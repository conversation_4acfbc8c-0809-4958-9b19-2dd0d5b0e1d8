# 优选

[![pipeline status](https://gitlab.corp.youdao.com/ead/youxuan/badges/master/pipeline.svg)](https://gitlab.corp.youdao.com/ead/youxuan/commits/master)

> 目前品牌投放系统较为老旧，已不太适应目前新形态广告资源的接入和投放流程的执行，需对品牌系统进行重构，以提供更顺畅、便捷和灵活的品牌广告投放后台，以提升整体品牌广告投放效率和广告资源管理效率。

### 部署方式

实体机
```shell
# 启动项目
./bin/start.sh env
# env表示环境 dev test online
# 关闭项目
./bin/stop.sh [-f]
```
容器部署
```
dev 将代码推送至 dev分支 自动部署
test 将代码推送至 test分支 自动部署
sandbox 将代码推送至 sandbox分支 自动部署
```

## 登陆控制

采用网易URS登陆校验，需要配置出口IP白名单。[详见](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=169736976)

## 相关链接

[设计文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=194524743)

**[快速开发](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=165772090)**

[数据库文档](https://gitlab.corp.youdao.com/ead/youxuan/-/tree/master/src/main/resources/db)

## Swagger

在 dev profile 下部署后，可以通过 http://host:port/v1.0.0/swagger-ui/index.html 访问。

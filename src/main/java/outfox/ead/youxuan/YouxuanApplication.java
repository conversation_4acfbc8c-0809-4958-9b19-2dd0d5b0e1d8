package outfox.ead.youxuan;

import com.mzt.logapi.starter.annotation.EnableLogRecord;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@EnableAsync
@EnableMongoRepositories
@EnableLogRecord(tenant = "outfox.ead.youxuan")
@SpringBootApplication(scanBasePackages = "outfox.ead.youxuan", exclude = {SecurityAutoConfiguration.class})
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
public class YouxuanApplication {
    public static void main(String[] args) {
        SpringApplication.run(YouxuanApplication.class, args);
    }
}

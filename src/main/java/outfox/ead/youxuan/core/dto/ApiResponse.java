package outfox.ead.youxuan.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import outfox.ead.youxuan.constants.ResponseType;

/**
 * <AUTHOR> <EMAIL>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Accessors(chain = true)
@ApiModel(value = "通用PI接口返回", description = "Common Api Response")
public class ApiResponse {
    /**
     * 通用返回状态
     */
    @ApiModelProperty(value = "通用返回状态", required = true)
    private Integer code;
    /**
     * 通用返回信息
     */
    @ApiModelProperty(value = "通用返回信息", required = true)
    private String message;
    /**
     * 通用返回数据
     */
    @ApiModelProperty(value = "通用返回数据", required = true)
    private Object data;

    public static ApiResponse success() {
        return new ApiResponse().setCode(ResponseType.SUCCESS.getCode()).setMessage(ResponseType.SUCCESS.getMsg());
    }

    public static ApiResponse success(Object data) {
        return new ApiResponse().setCode(ResponseType.SUCCESS.getCode()).setMessage(ResponseType.SUCCESS.getMsg()).setData(data);
    }

    public static ApiResponse error() {
        return new ApiResponse().setCode(ResponseType.SERVICE_ERROR.getCode()).setMessage(ResponseType.SERVICE_ERROR.getMsg());
    }

    public ApiResponse(ResponseType responseType) {
        this.setCode(responseType.getCode()).setMessage(responseType.getMsg());
    }

    public ApiResponse(ResponseType responseType, Object data) {
        this.setCode(responseType.getCode()).setMessage(responseType.getMsg()).setData(data);
    }

    public ApiResponse(ResponseType responseType, String msg, Object data) {
        this.setCode(responseType.getCode()).setMessage(msg).setData(data);
    }
}

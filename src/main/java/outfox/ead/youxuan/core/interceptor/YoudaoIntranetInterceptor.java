package outfox.ead.youxuan.core.interceptor;

import com.youdao.luna.ipfilter.WebIpUtils;
import lombok.AllArgsConstructor;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import outfox.ead.youxuan.core.annotation.YoudaoIntranet;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022年06月14日 17:20
 */
@AllArgsConstructor
public class YoudaoIntranetInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod h = (HandlerMethod) handler;
            YoudaoIntranet methodAnnotation = AnnotationUtils.findAnnotation(h.getMethod(), YoudaoIntranet.class);
            YoudaoIntranet classAnnotation = AnnotationUtils.findAnnotation(h.getMethod().getDeclaringClass(), YoudaoIntranet.class);
            if (Objects.nonNull(methodAnnotation) || Objects.nonNull(classAnnotation)) {
                return WebIpUtils.isInIpFilterWhiteList(request);
            }
        }
        return true;
    }
}

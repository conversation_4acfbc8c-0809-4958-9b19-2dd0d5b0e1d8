package outfox.ead.youxuan.core.interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.HandlerInterceptor;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.util.SecurityUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * 权限控制校验
 *
 * <AUTHOR>
 * @date 2022年03月02日 4:10 下午
 */
public class AccessControlInterceptor implements HandlerInterceptor {
    @Qualifier("handlerExceptionResolver")
    @Autowired
    private HandlerExceptionResolver resolver;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod h = (HandlerMethod) handler;
            AccessControl methodAnnotation = AnnotationUtils.findAnnotation(h.getMethod(), AccessControl.class);
            AccessControl classAnnotation = AnnotationUtils.findAnnotation(h.getMethod().getDeclaringClass(), AccessControl.class);
            if (accessDeny(request, response, methodAnnotation)) {
                return false;
            } else if (Objects.isNull(methodAnnotation) && accessDeny(request, response, classAnnotation)) {
                return false;
            }
        }
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

    private boolean accessDeny(HttpServletRequest request, HttpServletResponse response, AccessControl annotation) {
        if (Objects.nonNull(annotation) && !annotation.exclude() && !SecurityUtil.checkCurrentRole(annotation.roles())) {
            resolver.resolveException(request, response, null, new AccessDeniedException("权限不足"));
            return true;
        }
        return false;
    }
}

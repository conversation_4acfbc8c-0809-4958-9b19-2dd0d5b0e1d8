package outfox.ead.youxuan.core.interceptor;

import lombok.AllArgsConstructor;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import outfox.ead.youxuan.core.annotation.LDAP;
import outfox.ldapserver.filter.LDAPBackAuthFilter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022年06月14日 17:20
 */
@AllArgsConstructor
public class LdapInterceptor implements HandlerInterceptor {
    private final LDAPBackAuthFilter ldapBackAuthFilter = new LDAPBackAuthFilter();
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod h = (HandlerMethod) handler;
            LDAP methodAnnotation = AnnotationUtils.findAnnotation(h.getMethod(), LDAP.class);
            LDAP classAnnotation = AnnotationUtils.findAnnotation(h.getMethod().getDeclaringClass(), LDAP.class);
            if (Objects.nonNull(methodAnnotation) || Objects.nonNull(classAnnotation)) {
                ldapBackAuthFilter.doFilter(request, response, (request1, response1) -> {});
            }
        }
        return true;
    }
}

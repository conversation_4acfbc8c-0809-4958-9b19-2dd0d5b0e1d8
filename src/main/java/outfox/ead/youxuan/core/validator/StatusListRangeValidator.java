package outfox.ead.youxuan.core.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/3 20:42
 */
public class StatusListRangeValidator implements ConstraintValidator<StatusListRange, List<Integer>> {
    private int max;
    private int min;

    @Override
    public void initialize(StatusListRange constraintAnnotation) {
        max = constraintAnnotation.max();
        min = constraintAnnotation.min();
    }

    @Override
    public boolean isValid(List<Integer> value, ConstraintValidatorContext context) {
        if (Objects.isNull(value)) {
            return true;
        }
        for (Integer o : value) {
            if (o > max || o < min) {
                return false;
            }
        }
        return true;
    }
}

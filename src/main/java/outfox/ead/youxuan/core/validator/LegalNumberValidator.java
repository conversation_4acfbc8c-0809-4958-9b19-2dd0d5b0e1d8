package outfox.ead.youxuan.core.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/7 18:36
 */
public class LegalNumberValidator implements ConstraintValidator<LegalNumber, String> {

    long min;
    long max;

    @Override
    public void initialize(LegalNumber constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
        min = constraintAnnotation.min();
        max = constraintAnnotation.max();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        try {
            long l = Long.parseLong(value);
            return l >= min && l <= max;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}

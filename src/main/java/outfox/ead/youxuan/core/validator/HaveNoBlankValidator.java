package outfox.ead.youxuan.core.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import static outfox.ead.youxuan.constants.Constants.BLANK;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/3 10:35
 */
public class HaveNoBlankValidator implements ConstraintValidator<HaveNoBlank, String> {
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        return !value.contains(BLANK);
    }
}

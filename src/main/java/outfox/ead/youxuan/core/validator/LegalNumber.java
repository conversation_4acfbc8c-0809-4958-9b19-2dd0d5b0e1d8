package outfox.ead.youxuan.core.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 判断字符串是否是整形，如果不是则抛出异常
 *
 * <AUTHOR> <EMAIL>
 * @date 2021/9/7 18:35
 */
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = {LegalNumberValidator.class})
public @interface LegalNumber {
    String message() default "数字不合法";

    long max() default Long.MAX_VALUE;

    long min() default Long.MIN_VALUE;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    @Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
    @Retention(RUNTIME)
    @Documented
    @interface List {
        LegalNumber[] value();
    }
}

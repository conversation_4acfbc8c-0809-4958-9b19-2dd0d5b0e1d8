package outfox.ead.youxuan.core.validator;


import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ListLengthValidator implements ConstraintValidator<ListLength, List<?>> {

    int minLength;
    int maxLength;

    @Override
    public void initialize(ListLength constraintAnnotation) {
        minLength = constraintAnnotation.minLength();
        maxLength = constraintAnnotation.maxLength();
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(List<?> list, ConstraintValidatorContext context) {
        if (Objects.isNull(list)) {
            return true;
        }
        int size = list.size();
        return size >= minLength && size <= maxLength;
    }
}

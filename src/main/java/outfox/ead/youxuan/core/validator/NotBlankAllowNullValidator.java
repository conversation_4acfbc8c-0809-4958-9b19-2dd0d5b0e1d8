package outfox.ead.youxuan.core.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/10/20 15:10
 */
public class NotBlankAllowNullValidator implements ConstraintValidator<NotBlankAllowNull, String> {
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        return !"".equals(value.trim());
    }
}

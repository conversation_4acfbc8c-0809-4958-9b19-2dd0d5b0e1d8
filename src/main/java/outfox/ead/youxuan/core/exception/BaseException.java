package outfox.ead.youxuan.core.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import outfox.ead.youxuan.constants.ResponseType;

/**
 * 业务异常类，继承运行时异常，确保事务正常回滚
 *
 * <AUTHOR>  <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BaseException extends RuntimeException {

    private ResponseType code;

    public BaseException(ResponseType code) {
        this.code = code;
    }

    public BaseException(Throwable cause, ResponseType code) {
        super(cause);
        this.code = code;
    }
}

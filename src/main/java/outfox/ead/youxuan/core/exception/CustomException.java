package outfox.ead.youxuan.core.exception;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.logging.LogLevel;
import outfox.ead.youxuan.constants.ResponseType;

import static org.springframework.boot.logging.LogLevel.WARN;


/**
 * 异常类 error 将报警
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"stackTrace", "localizedMessage", "cause", "detailMessage", "suppressedExceptions", "suppressed"})
public class CustomException extends RuntimeException {

    final int code;

    final String message;

    @JsonIgnore
    LogLevel level = WARN;

    public CustomException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public CustomException(int code, String message, LogLevel level) {
        super(message);
        this.code = code;
        this.message = message;
        this.level = level;
    }

    public CustomException(ResponseType exception) {
        this(exception.getCode(), exception.getMsg());
    }

    public CustomException(ResponseType exception, String additionalMessage) {
        this(exception.getCode(), additionalMessage);
    }

    public CustomException(ResponseType exception, String additionalMessage, LogLevel level) {
        this(exception.getCode(), additionalMessage, level);
    }

    public CustomException(ResponseType exception, Exception e) {
        this.code = exception.getCode();
        this.message = exception.getMsg() + ": " + e.getMessage();
    }

    public CustomException(ResponseType responseType, LogLevel level) {
        this(responseType.getCode(), responseType.getMsg(), level);
    }

    public CustomException(ResponseType exception, String additionalMessage, Exception e) {
        super(e);
        this.code = exception.getCode();
        this.message = additionalMessage;
    }
}

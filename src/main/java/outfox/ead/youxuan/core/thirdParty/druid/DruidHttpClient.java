package outfox.ead.youxuan.core.thirdParty.druid;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.zapr.druid.druidry.client.DruidClient;
import in.zapr.druid.druidry.client.exception.ConnectionException;
import in.zapr.druid.druidry.client.exception.QueryException;
import in.zapr.druid.druidry.query.DruidQuery;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.Future;

/**
 * Use {@link CloseableHttpAsyncClient} to query druid.
 *
 * <AUTHOR> on 2018/10/09
 */
@Component
@Slf4j
public class DruidHttpClient implements DruidClient {

    private CloseableHttpAsyncClient client;
    @Autowired
    private final ObjectMapper mapper;
    @Setter
    @Value(value = "${druid.address}")
    private String druidAddr;

    @Override
    public void connect() throws ConnectionException {
        try {
            client = HttpAsyncClients.createDefault();
            client.start();
        } catch (Exception e) {

            throw new ConnectionException(e);
        }
    }

    public DruidHttpClient() throws ConnectionException {
        try {
            mapper = new ObjectMapper();
            client = HttpAsyncClients.createDefault();
            client.start();
        } catch (Exception e) {
            throw new ConnectionException(e);
        }
    }

    @Override
    public void close() throws ConnectionException {
        try {
            client.close();
        } catch (Exception e) {
            throw new ConnectionException(e);
        }
    }

    @Override
    public String query(DruidQuery druidQuery) throws QueryException {
        String query = convertDruidQuery(druidQuery);
        HttpPost post = new HttpPost(druidAddr);
        post.setEntity(new ByteArrayEntity(query.getBytes(StandardCharsets.UTF_8)));
        try {
            Future<HttpResponse> responseFuture = client.execute(post, null);
            HttpResponse response = responseFuture.get();
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK && response.getEntity() != null) {
                return IOUtils.toString(response.getEntity().getContent(), StandardCharsets.UTF_8);
            }else{
                throw new QueryException("查询Druid失败");
            }
        } catch (Exception e) {
            throw new QueryException(e);
        }
    }

    @Override
    public <T> List<T> query(DruidQuery druidQuery, Class<T> className) throws QueryException {
        String json = query(druidQuery);
        try {
            return mapper.readValue(json, mapper.getTypeFactory().constructCollectionType(List.class, className));
        } catch (IOException e) {
            throw new QueryException(e);
        }
    }

    /**
     * Convert druid query to json string.
     *
     * @param druidQuery druid query
     * @return json string
     * @throws QueryException if conversion fails
     */
    private String convertDruidQuery(DruidQuery druidQuery) throws QueryException {
        try {
            return mapper.writeValueAsString(druidQuery);
        } catch (JsonProcessingException e) {
            throw new QueryException(e);
        }
    }
}

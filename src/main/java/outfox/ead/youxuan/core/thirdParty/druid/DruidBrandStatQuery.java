package outfox.ead.youxuan.core.thirdParty.druid;

import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import in.zapr.druid.druidry.aggregator.DruidAggregator;
import in.zapr.druid.druidry.aggregator.HyperUniqueAggregator;
import in.zapr.druid.druidry.aggregator.LongSumAggregator;
import in.zapr.druid.druidry.dataSource.TableDataSource;
import in.zapr.druid.druidry.dimension.DefaultDimension;
import in.zapr.druid.druidry.dimension.DruidDimension;
import in.zapr.druid.druidry.dimension.ExtractionDimension;
import in.zapr.druid.druidry.dimension.enums.OutputType;
import in.zapr.druid.druidry.filter.AndFilter;
import in.zapr.druid.druidry.filter.DruidFilter;
import in.zapr.druid.druidry.filter.InFilter;
import in.zapr.druid.druidry.filter.havingSpec.GreaterThanHaving;
import in.zapr.druid.druidry.granularity.Granularity;
import in.zapr.druid.druidry.granularity.PeriodGranularity;
import in.zapr.druid.druidry.granularity.PredefinedGranularity;
import in.zapr.druid.druidry.granularity.SimpleGranularity;
import in.zapr.druid.druidry.postAggregator.ArithmeticFunction;
import in.zapr.druid.druidry.postAggregator.ArithmeticPostAggregator;
import in.zapr.druid.druidry.postAggregator.DruidPostAggregator;
import in.zapr.druid.druidry.postAggregator.FieldAccessPostAggregator;
import in.zapr.druid.druidry.query.DruidQuery;
import in.zapr.druid.druidry.query.aggregation.DruidGroupByQuery;
import in.zapr.druid.druidry.query.aggregation.DruidTimeSeriesQuery;
import in.zapr.druid.druidry.query.config.Context;
import in.zapr.druid.druidry.query.config.Interval;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.web.ad.controller.vo.AccountStatementsVO;

import java.util.*;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.Constants.*;

/**
 * 从brand_stat中查询数据
 *
 * <AUTHOR>
 */
public class DruidBrandStatQuery {
    /**
     * druid tables
     */
    private static final String BRAND_STAT = "brand_stat_v2";

    /**
     * 防止druid查询时间过长，设置最大查询31天
     */
    private static final int DRUID_MAX_DAYS = 100;
    private static final long DRUID_QUERY_TIMEOUT_MS = 20000;

    private static final String CLICK = "click";
    private static final String IMPR = "impr";
    private static final String UI = "ui";
    private static final String UC = "uc";
    private static final String CLICK_RATE = "clickRate";

    /**
     * 推广计划id
     */
    public static final String CAMPAIGN_ID = "campaignId";
    public static final String CAMPAIGN_NAME = "youxuan_campaignId_name";
    /**
     * 推广组id
     */
    public static final String GROUP_ID = "adGroupId";
    public static final String GROUP_NAME = "youxuan_groupId_name";
    /**
     * 推广内容
     */
    public static final String AD_VARIATION_ID = "adVariationId";
    /**
     * 媒体id
     */
    public static final String MEDIA_ID = "mediaId";
    public static final String MEDIA_NAME = "youxuan_mediaId_name";

    /**
     * 广告位
     */
    public static final String IMPR_POS_ID = "imprPos";
    public static final String IMPR_POS_NAME = "youxuan_imprPos_name";

    /**
     * 样式id
     */
    public static final String STYLE_ID = "styleId";
    public static final String STYLE_NAME = "youxuan_styleId_name";

    /**
     * 客户id
     */
    public static final String SPONSOR_ID = "sponsorId";
    public static final String SPONSOR_NAME = "youxuan_customerId_name";
    /**
     * 推广目标
     */
    public static final String BRAND_CLK_TYPE = "brandClkType";
    /**
     * 投放方式
     */
    public static final String DELIVERY_TYPE = "deliveryType";
    /**
     * 计费方式 0-CPT 1-CPM
     */
    public static final String BILLING_TYPE = "billingType";

    public static DruidQuery queryBuilder(AccountStatementsVO vo) {
        AndFilter filter = getFilter(vo.getPromotionTarget(),
                vo.getBillingType(),
                vo.getDeliveryType(),
                vo.getMediaIds(),
                vo.getAdPositionIds(),
                vo.getStyleIds(),
                vo.getAdPlanIds(),
                vo.getAdGroupIds());
        List<DruidDimension> druidDimensions = getDruidDimensions(vo.getDimension(), vo.getStatement());

        if (ObjectUtils.isNotEmpty(druidDimensions)) {
            return DruidGroupByQuery.builder()
                    .dataSource(new TableDataSource(BRAND_STAT))
                    .granularity(getGranularity(vo.getGranularity()))
                    .intervals(getInterval(vo.getStartTime(), vo.getEndTime()))
                    .aggregators(getAggregators())
                    .postAggregators(getDruidPostAggregator())
                    .having(new GreaterThanHaving(IMPR, 0))
                    .dimensions(druidDimensions)
                    .filter(Objects.nonNull(filter) ? filter : null)
                    .context(Context.builder()
                            .timeoutInMilliSeconds(DRUID_QUERY_TIMEOUT_MS)
                            .build())
                    .build();
        } else {
            return DruidTimeSeriesQuery.builder()
                    .dataSource(new TableDataSource(BRAND_STAT))
                    .granularity(getGranularity(vo.getGranularity()))
                    .intervals(getInterval(vo.getStartTime(), vo.getEndTime()))
                    .aggregators(getAggregators())
                    .postAggregators(getDruidPostAggregator())
                    .filter(Objects.nonNull(filter) ? filter : null)
                    .context(Context.builder()
                            .timeoutInMilliSeconds(DRUID_QUERY_TIMEOUT_MS)
                            .build())
                    .build();
        }
    }

    public static DruidQuery queryBuilder(List<Long> ids, DateTime startTime, DateTime endTime, int dimension) {
        SimpleGranularity simpleGranularity = new SimpleGranularity(PredefinedGranularity.ALL);
        DruidFilter druidFilter = new InFilter(mapDimension(dimension), ids.stream().map(Functions.toStringFunction()).collect(Collectors.toList()));
        return DruidGroupByQuery.builder()
                .dataSource(new TableDataSource(BRAND_STAT))
                .granularity(simpleGranularity)
                .filter(druidFilter)
                .intervals(getInterval(startTime, endTime))
                .aggregators(getAggregators())
                .postAggregators(getDruidPostAggregator())
                .dimensions(getDruidDimensions(dimension, null))
                .context(Context.builder()
                        .timeoutInMilliSeconds(DRUID_QUERY_TIMEOUT_MS)
                        .build())
                .build();
    }

    private static Granularity getGranularity(Integer granularity) {
        Granularity period;
        if (granularity.equals(GRANULARITY_DAY)) {
            period = PeriodGranularity.builder()
                    .timeZone(DATE_TIME_ZONE_SHANGHAI)
                    .period("P1D")
                    .build();
        } else if (granularity.equals(GRANULARITY_ALL)) {
            period = new SimpleGranularity(PredefinedGranularity.ALL);
        } else {
            period = PeriodGranularity.builder()
                    .timeZone(DATE_TIME_ZONE_SHANGHAI)
                    .period("PT1H")
                    .build();
        }
        return period;
    }

    private static List<Interval> getInterval(DateTime startTime, DateTime endTime) {
        DateTimeFormatter fmt2 = DateTimeFormat.forPattern(TIMESTAMP_FORMAT);
        DateTime startDateTime = DateTime.parse(fmt2.print(startTime));
        DateTime endDateTime = DateTime.parse(fmt2.print(endTime));
        DateTime minStartDateTime = endDateTime.minusDays(DRUID_MAX_DAYS);
        Interval interval = new Interval(minStartDateTime.isAfter(startDateTime) ?
                minStartDateTime : startDateTime, endDateTime);
        return Collections.singletonList(interval);
    }

    private static List<DruidDimension> getDruidDimensions(Integer dimension, Integer statement) {
        // builder
        Set<DruidDimension> druidDimensions = new HashSet<>();
        if (Objects.nonNull(dimension) && dimension != DIMENSION_ALL) {
            druidDimensions.addAll(getDimensions(dimension));
        }
        if (Objects.nonNull(statement) && statement != DIMENSION_ALL) {
            druidDimensions.addAll(getDimensions(statement));
        }
        return new ArrayList<>(druidDimensions);
    }

    private static List<DruidPostAggregator> getDruidPostAggregator() {
        // post aggregator
        FieldAccessPostAggregator clickPost = new FieldAccessPostAggregator(CLICK, CLICK);
        FieldAccessPostAggregator imprPost = new FieldAccessPostAggregator(IMPR, IMPR);

        return Collections.singletonList(ArithmeticPostAggregator.builder()
                .name(CLICK_RATE)
                .function(ArithmeticFunction.DIVIDE)
                .fields(Arrays.asList(clickPost, imprPost))
                .build());
    }

    private static AndFilter getFilter(List<Integer> promotionTarget, List<Integer> billingType, List<Integer> deliveryType, List<Long> mediaIds, List<Long> adPositionIds, List<Long> styleIds, List<Long> adPlanIds, List<Long> adGroupIds) {
        //filter
        List<DruidFilter> druidFilters = getFilters(promotionTarget, billingType, deliveryType, mediaIds, adPositionIds, styleIds, adPlanIds, adGroupIds);
        return ObjectUtils.isEmpty(druidFilters) ? null : new AndFilter(druidFilters);
    }

    private static List<DruidAggregator> getAggregators() {
        // aggregators
        DruidAggregator click = LongSumAggregator.builder()
                .name(CLICK)
                .fieldName(CLICK)
                .build();
        DruidAggregator imp = LongSumAggregator.builder()
                .name(IMPR)
                .fieldName(IMPR)
                .build();
        DruidAggregator ui = HyperUniqueAggregator.builder()
                .name(UI)
                .fieldName(UI)
                .build();
        DruidAggregator uc = HyperUniqueAggregator.builder()
                .name(UC)
                .fieldName(UC)
                .build();
        return Lists.newArrayList(click, imp, ui, uc);
    }

    private static List<DruidFilter> getFilters(List<Integer> promotionTarget,
                                                List<Integer> billingType,
                                                List<Integer> deliveryType,
                                                List<Long> mediaIds,
                                                List<Long> adPositionIds,
                                                List<Long> styleIds,
                                                List<Long> adPlanIds,
                                                List<Long> adGroupIds) {
        List<DruidFilter> druidFilters = new ArrayList<>();
        addInFilter(promotionTarget, BRAND_CLK_TYPE, druidFilters);
        addInFilter(billingType, BILLING_TYPE, druidFilters);
        addInFilter(deliveryType, DELIVERY_TYPE, druidFilters);
        addInFilter(mediaIds, MEDIA_ID, druidFilters);
        addInFilter(adPositionIds, IMPR_POS_ID, druidFilters);
        addInFilter(styleIds, STYLE_ID, druidFilters);
        addInFilter(adGroupIds, GROUP_ID, druidFilters);
        addInFilter(adPlanIds, CAMPAIGN_ID, druidFilters);
        return druidFilters;
    }

    private static <T extends Number> void addInFilter(List<T> values, String dimension, List<DruidFilter> druidFilters) {
        if (ObjectUtils.isNotEmpty(values)) {
            druidFilters.add(new InFilter(dimension,
                    values.stream()
                            .map(Functions.toStringFunction())
                            .collect(Collectors.toList())));
        }
    }

    private static List<DruidDimension> getDimensions(Integer dimension) {
        if (dimension.equals(DIMENSION_ALL)) {
            return Collections.emptyList();
        } else {
            List<DruidDimension> dimensions = new ArrayList<>();
            if (dimension.equals(DIMENSION_PROMOTION_TARGET)) {
                dimensions.add(getDimension(dimension));
            } else {
                if (dimension.equals(DIMENSION_AD_GROUP)) {
                    dimensions.add(getDimension(dimension));
                    dimensions.add(getExtractionDimension(dimension));
                    dimensions.add(getDimension(DIMENSION_AD_PLAN));
                    dimensions.add(getExtractionDimension(DIMENSION_AD_PLAN));
                } else if (dimension >= DIMENSION_MEDIA && dimension <= DIMENSION_STYLE) {
                    for (int i = dimension; i >= DIMENSION_MEDIA; i--) {
                        dimensions.add(getDimension(i));
                        dimensions.add(getExtractionDimension(i));
                    }
                } else {
                    dimensions.add(getDimension(dimension));
                    dimensions.add(getExtractionDimension(dimension));
                }
            }
            return dimensions;
        }
    }

    private static DefaultDimension getDimension(Integer dimension) {
        return DefaultDimension.builder()
                .dimension(mapDimension(dimension))
                .outputName(mapDimension(dimension))
                .outputType(OutputType.STRING)
                .build();
    }

    private static ExtractionDimension getExtractionDimension(Integer dimension) {
        return ExtractionDimension.builder()
                .dimension(mapDimension(dimension))
                .outputName(mapLookup(dimension))
                .outputType(OutputType.STRING)
                .extractionFunction(RegisteredLookUpExtractionFunction
                        .builder()
                        .lookup(mapLookup(dimension))
                        .injective(true)
                        .retainMissingValue(false)
                        .build())
                .build();
    }

    private static final Map<Integer, String> DIMENSION_2_LOOKUP_NAME = new HashMap<>();

    private static final Map<Integer, String> DIMENSION_2_NAME = new HashMap<>();

    static {
        DIMENSION_2_NAME.put(DIMENSION_CUSTOMER_NAME, SPONSOR_ID);
        DIMENSION_2_NAME.put(DIMENSION_MEDIA, MEDIA_ID);
        DIMENSION_2_NAME.put(DIMENSION_AD_POSITION, IMPR_POS_ID);
        DIMENSION_2_NAME.put(DIMENSION_STYLE, STYLE_ID);
        DIMENSION_2_NAME.put(DIMENSION_AD_PLAN, CAMPAIGN_ID);
        DIMENSION_2_NAME.put(DIMENSION_AD_GROUP, GROUP_ID);
        DIMENSION_2_NAME.put(DIMENSION_PROMOTION_TARGET, BRAND_CLK_TYPE);
        DIMENSION_2_LOOKUP_NAME.put(DIMENSION_CUSTOMER_NAME, SPONSOR_NAME);
        DIMENSION_2_LOOKUP_NAME.put(DIMENSION_MEDIA, MEDIA_NAME);
        DIMENSION_2_LOOKUP_NAME.put(DIMENSION_AD_POSITION, IMPR_POS_NAME);
        DIMENSION_2_LOOKUP_NAME.put(DIMENSION_STYLE, STYLE_NAME);
        DIMENSION_2_LOOKUP_NAME.put(DIMENSION_AD_PLAN, CAMPAIGN_NAME);
        DIMENSION_2_LOOKUP_NAME.put(DIMENSION_AD_GROUP, GROUP_NAME);
    }

    private static String mapLookup(Integer dimension) {
        String lookupName = DIMENSION_2_LOOKUP_NAME.get(dimension);
        if (lookupName==null){
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "没有该维度");
        }
        return lookupName;
    }
    private static String mapDimension(Integer dimension) {
        String name = DIMENSION_2_NAME.get(dimension);
        if (name == null) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "没有该维度");
        }
        return name;
    }
}

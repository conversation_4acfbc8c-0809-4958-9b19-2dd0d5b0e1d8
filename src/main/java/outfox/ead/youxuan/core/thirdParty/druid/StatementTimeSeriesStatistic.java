package outfox.ead.youxuan.core.thirdParty.druid;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.joda.time.LocalDateTime;
import outfox.ead.youxuan.util.MultiDateDeserializer;



/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/16 15:43
 */
@Data
@NoArgsConstructor
public class StatementTimeSeriesStatistic {
    @JsonDeserialize(using = MultiDateDeserializer.class)
    private LocalDateTime timestamp;

    private Event result;

    /**
     * 广告组 id 统计信息
     */
    @Data
    public static class Event {
        /**
         * 展示数
         */
        private long impr;
        /**
         * 点击数
         */
        private long click;
        /**
         * 展示独立设备数
         */
        private long ui;
        /**
         * 点击独立设备数
         */
        private long uc;
        /**
         * 点击率
         */
        private Double clickRate;
        /**
         * 推广计划id
         */
        @JsonProperty(value = "campaignId")
        private Long adPlanId;
        /**
         * 推广组id
         */
        private Long adGroupId;
        /**
         * 推广内容
         */
        private Long adVariationId;
        /**
         * 媒体id
         */
        private Long mediaId;
        /**
         * 广告位
         */
        private Long imprPos;
        /**
         * 样式id
         */
        private Long styleId;
        /**
         * 客户id
         */
        private Long sponsorId;
        /**
         * 推广目标
         */
        private Integer brandClkType;
        /**
         * 投放方式
         */
        private Integer deliveryType;
        /**
         * 计费方式 0-CPT 1-CPM
         */
        private Integer billingType;
        @JsonProperty(value = "youxuan_campaignId_name")
        private String adPlanName;
        @JsonProperty(value = "youxuan_groupId_name")
        private String adGroupName;
        @JsonProperty(value = "youxuan_mediaId_name")
        private String mediaName;
        @JsonProperty(value = "youxuan_imprPos_name")
        private String adPositionName;
        @JsonProperty(value = "youxuan_styleId_name")
        private String styleName;
        @JsonProperty(value = "youxuan_customerId_name")
        private String customerName;
    }
}


package outfox.ead.youxuan.core.thirdParty.leader;

import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.leader.LeaderSelector;
import org.apache.curator.framework.recipes.leader.LeaderSelectorListenerAdapter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.Closeable;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 优选多台选主，leader负责运行定时任务。
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class YouxuanLeaderSelector extends LeaderSelectorListenerAdapter implements Closeable {
    private final String path;
    private final LeaderSelector leaderSelector;

    private AtomicBoolean isLeader = new AtomicBoolean(false);

    /**
     * 所有节点抢{@link outfox.ead.youxuan.core.thirdParty.leader.YouxuanLeaderSelector#path}，抢到之后作为leader
     * 当放弃leader权限之后，自动重新排队抢leader权限
     */
    public YouxuanLeaderSelector(CuratorFramework client,
                                 @Value("${zookeeper.leader.path}") String path) {
        this.path = path;
        leaderSelector = new LeaderSelector(client, this.path, this);
        leaderSelector.autoRequeue();
    }


    /**
     * 只有调用了 {@link LeaderSelector#start()} 才真正开始竞选leader。
     * 竞选在后台进行，所以该方法会立即返回。
     */
    @PostConstruct
    public void start() throws IOException {
        leaderSelector.start();
    }

    @PreDestroy
    @Override
    public void close() throws IOException {
        leaderSelector.close();
    }

    /**
     * 当前服务被选为leader的时候将会调用本方法
     */
    @Override
    public void takeLeadership(CuratorFramework client) {
        // 获得leader权限，标注leader
        isLeader.set(true);
        after();
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            log.error("Current server was interrupted.");
            Thread.currentThread().interrupt();
        } finally {
            log.info("Current server is relinquishing leadership.");
            // 释放leader权限，标注非leader
            isLeader = new AtomicBoolean(false);
        }
    }

    private void after() {
        log.info("Current server has become leader.");
    }

    /**
     * 获取当前是否为leader
     */
    public boolean isLeader() {
        return isLeader.get();
    }
}

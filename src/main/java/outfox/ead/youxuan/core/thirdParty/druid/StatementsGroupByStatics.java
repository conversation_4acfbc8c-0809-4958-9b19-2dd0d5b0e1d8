package outfox.ead.youxuan.core.thirdParty.druid;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import org.joda.time.LocalDateTime;
import outfox.ead.youxuan.util.MultiDateDeserializer;


/**
 * <AUTHOR>
 * @date 2021/9/26/15:43
 */
@Data
public class StatementsGroupByStatics {
    private String version;
    @JsonDeserialize(using = MultiDateDeserializer.class)
    private LocalDateTime timestamp;
    private StatementTimeSeriesStatistic.Event event;
}

package outfox.ead.youxuan.core.thirdParty.druid;

import com.fasterxml.jackson.annotation.JsonInclude;
import in.zapr.druid.druidry.extractionFunctions.ExtractionFunction;
import lombok.Builder;
import lombok.Getter;

@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RegisteredLookUpExtractionFunction extends ExtractionFunction {

    private String lookup;
    private Boolean retainMissingValue;
    private Boolean injective;
    private String replaceMissingValueWith;
    private Boolean optimize;

    @Builder
    private RegisteredLookUpExtractionFunction(String lookup, Boolean retainMissingValue, Boolean injective,
                                               String replaceMissingValueWith, Boolean optimize) {
        this.type = ExtractionFunction.REGISTERED_LOOKUP_TYPE;
        this.lookup = lookup;
        this.retainMissingValue = retainMissingValue;
        this.injective = injective;
        this.replaceMissingValueWith = replaceMissingValueWith;
        this.optimize = optimize;
    }
}

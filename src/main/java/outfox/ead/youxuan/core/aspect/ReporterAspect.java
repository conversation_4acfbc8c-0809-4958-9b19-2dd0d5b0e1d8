package outfox.ead.youxuan.core.aspect;

import com.codahale.metrics.*;
import com.codahale.metrics.graphite.Graphite;
import com.codahale.metrics.graphite.GraphiteReporter;
import com.codahale.metrics.jmx.JmxReporter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import outfox.ead.youxuan.core.annotation.Report;
import outfox.ead.youxuan.core.config.ReporterConfig;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.codahale.metrics.MetricRegistry.name;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/11/4 10:41
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "youxuan.reporter", name = "enable", havingValue = "true")
public class ReporterAspect {
    private final MetricRegistry metrics = new MetricRegistry();

    private final ReporterConfig config;

    private static final String GRAPHITE = "graphite";
    private static final String JMX = "jmx";
    private static final String DEFAULT = "console";

    @PostConstruct
    public void init() {
        if (GRAPHITE.equals(config.getType())) {
            final Graphite graphite = new Graphite(new InetSocketAddress(config.getGraphiteHost(), config.getGraphitePort()));
            GraphiteReporter.forRegistry(metrics)
                    .prefixedWith(config.getPrefix())
                    .convertRatesTo(TimeUnit.SECONDS)
                    .convertDurationsTo(TimeUnit.MILLISECONDS)
                    .filter(MetricFilter.ALL)
                    .build(graphite)
                    .start(1, TimeUnit.MINUTES);
        } else if (JMX.equals(config.getType())) {
            JmxReporter.forRegistry(metrics)
                    .convertRatesTo(TimeUnit.SECONDS)
                    .convertDurationsTo(TimeUnit.MILLISECONDS)
                    .build()
                    .start();
        } else {
            ConsoleReporter.forRegistry(metrics)
                    .convertRatesTo(TimeUnit.SECONDS)
                    .convertDurationsTo(TimeUnit.MILLISECONDS)
                    .build()
                    .start(1, TimeUnit.MINUTES);
        }
    }

    @Pointcut("@annotation(report)")
    public void reporter(Report report) {
    }

    @Around(value = "reporter(report)", argNames = "proceedingJoinPoint,report")
    public Object computeTime(ProceedingJoinPoint proceedingJoinPoint, Report report) throws Throwable {
        switch (report.metrics()) {
            case meter:
                return meter(proceedingJoinPoint);
//            case "queue": return queue(proceedingJoinPoint,jmxReporter);
            case counter:
                return counter(proceedingJoinPoint);
            case timer:
                return timer(proceedingJoinPoint);
            default:
                throw new RuntimeException("没有这种统计方式");
        }
    }

    private Object timer(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Timer timer = metrics.timer(getName(proceedingJoinPoint));
        try (final Timer.Context context = timer.time()) {
            return proceedingJoinPoint.proceed();
        }
    }

    private String getName(ProceedingJoinPoint proceedingJoinPoint) throws UnknownHostException {
        return name(InetAddress.getLocalHost().getHostName(), proceedingJoinPoint.getTarget().getClass().getSimpleName(), proceedingJoinPoint.getSignature().getName());
    }

    private final Map<String, Integer> counter = new HashMap<>();

    private Object counter(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Integer integer = counter.getOrDefault(getName(proceedingJoinPoint), 0);
        counter.put(getName(proceedingJoinPoint), ++integer);
        Object proceed = proceedingJoinPoint.proceed();
        counter.put(getName(proceedingJoinPoint), --integer);
        return proceed;
    }

    private Object meter(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Meter meter = metrics.meter(getName(proceedingJoinPoint));
        meter.mark();
        return proceedingJoinPoint.proceed();
    }
}

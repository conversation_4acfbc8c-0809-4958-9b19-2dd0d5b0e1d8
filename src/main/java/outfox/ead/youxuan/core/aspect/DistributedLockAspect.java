package outfox.ead.youxuan.core.aspect;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import outfox.ead.youxuan.core.annotation.DistributedLock;
import outfox.ead.youxuan.core.annotation.DistributedLockKey;
import outfox.ead.youxuan.web.ad.service.DistributedLockService;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022年07月21日 16:35
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class DistributedLockAspect implements Ordered {
    private final DistributedLockService distributedLockService;

    @Pointcut("@annotation(distributedLock)")
    public void distributedLockAspect(DistributedLock distributedLock) {
    }

    @Around(value = "distributedLockAspect(distributedLock)", argNames = "joinPoint,distributedLock")
    public Object distributedLock(ProceedingJoinPoint joinPoint, DistributedLock distributedLock) throws Throwable {
        String lockKey = getLockKey(joinPoint, distributedLock.namespace());
        try {
            if (distributedLock.time() != -1) {
                if (distributedLock.lockType() == DistributedLock.MUTEX_LOCK) {
                    distributedLockService.mutexLock(lockKey, distributedLock.time(), distributedLock.timeUnit());
                } else {
                    distributedLockService.readWriteLock(lockKey, distributedLock.time(), distributedLock.timeUnit(), distributedLock.lockType());
                }
            } else {
                if (distributedLock.lockType() == DistributedLock.MUTEX_LOCK) {
                    distributedLockService.mutexLock(lockKey);
                } else {
                    distributedLockService.readWriteLock(lockKey, distributedLock.lockType());
                }
            }
            return joinPoint.proceed();
        } finally {
            if (distributedLock.lockType() == DistributedLock.MUTEX_LOCK) {
                distributedLockService.unlock(lockKey);
            } else {
                distributedLockService.readWriteUnLock(lockKey, distributedLock.lockType());
            }
        }
    }

    private String getLockKey(ProceedingJoinPoint joinPoint, String namespace) throws IllegalAccessException {
        Map<Integer, List<String>> order2Keys = new TreeMap<>(Integer::compareTo);
        Object[] params = joinPoint.getArgs();
        if (params.length == 0) {
            return namespace;
        }
        //获取方法，此处可将signature强转为MethodSignature
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        Annotation[][] annotations = method.getParameterAnnotations();
        for (int i = 0; i < annotations.length; i++) {
            Object param = params[i];
            Annotation[] paramAnn = annotations[i];
            if (param == null || paramAnn.length == 0) {
                continue;
            }
            for (Annotation annotation : paramAnn) {
                if (annotation.annotationType().equals(DistributedLockKey.class)) {
                    DistributedLockKey d = (DistributedLockKey) annotation;
                    if (param instanceof String) {
                        order2Keys.computeIfAbsent(d.order(), k -> new ArrayList<>()).add((String) param);
                    } else {
                        recursionFindLockKey(order2Keys, param);
                    }
                }
            }
        }
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<Integer, List<String>> next : order2Keys.entrySet()) {
            for (String s : next.getValue()) {
                sb.append(s);
            }
        }
        return StringUtils.joinWith("_", "youxuan", namespace, sb);
    }

    private void recursionFindLockKey(Map<Integer, List<String>> order2Keys, Object param) throws IllegalAccessException {
        if (Objects.isNull(param)) {
            return;
        }
        Class<?> clazz = param.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            DistributedLockKey annotation = field.getAnnotation(DistributedLockKey.class);
            if (Objects.nonNull(annotation)) {
                field.setAccessible(true);
                Object o = field.get(param);
                if (o instanceof String) {
                    order2Keys.computeIfAbsent(annotation.order(), k -> new ArrayList<>()).add((String) o);
                } else {
                    recursionFindLockKey(order2Keys, o);
                }
            }
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }
}

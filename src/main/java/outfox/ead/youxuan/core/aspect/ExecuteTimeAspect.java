package outfox.ead.youxuan.core.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import outfox.ead.youxuan.core.annotation.ExecuteTime;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class ExecuteTimeAspect {
    @Pointcut("@annotation(executeTime)")
    public void serviceExecutionTimeLog(ExecuteTime executeTime) {
    }

    @Around(value = "serviceExecutionTimeLog(executeTime)", argNames = "proceedingJoinPoint,executeTime")
    public Object computeTime(ProceedingJoinPoint proceedingJoinPoint, ExecuteTime executeTime) throws Throwable {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Object proceed = proceedingJoinPoint.proceed();
        stopWatch.stop();

        log.info("execute-time-name : [{}], execution-time : [{}], class-method : [{}]",
                executeTime.value(),
                stopWatch.getTotalTimeMillis(),
                proceedingJoinPoint.getTarget().getClass().getName() + "#" + proceedingJoinPoint.getSignature().getName());
        return proceed;
    }
}

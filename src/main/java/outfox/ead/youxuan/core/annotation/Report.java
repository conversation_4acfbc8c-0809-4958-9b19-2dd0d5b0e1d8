package outfox.ead.youxuan.core.annotation;

import outfox.ead.youxuan.constants.MetricsEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/11/4 10:35
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Report {
    MetricsEnum metrics();
}

package outfox.ead.youxuan.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 分布式锁 lock key
 * 需要配合{@link DistributedLock}使用
 *
 * <pre>
 *     {@code
 *     @DistributedLock(namespace="obj")
 *     createObj(
 *              @DistributedLockKey(order=0) String key1,
 *              @DistributedLockKey(order=1) String key2,
 *              @DistributedLockKey String key3
 *              ){
 *                  // code
 *              }
 *     }
 * </pre>
 * 在执行code前，会使用以下字符串作为lock_key加锁，basePath/obj-key1key2key3
 *
 * <pre>
 *     {@code
 *     class Data{
 *         @DistributedLockKey
 *         private String key1 = "key1";
 *         @DistributedLockKey
 *         private String key2 = "key2";
 *     }
 *     @DistributedLock(namespace="obj")
 *     createObj(
 *              @DistributedLockKey(order=0) Data obj,
 *              ){
 *                  // code
 *              }
 *     }
 * </pre>
 * {@link DistributedLockKey} 支持递归查找,在执行code前，会使用以下字符串作为lock_key加锁，basePath/obj-key1key2
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.PARAMETER, ElementType.FIELD})
public @interface DistributedLockKey {
    /**
     * order 用于拼接 lock key，order越小排序越前面
     */
    int order() default Integer.MAX_VALUE;
}

package outfox.ead.youxuan.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁注解<p>
 * 可以配合{@link DistributedLockKey}使用
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface DistributedLock {
    int MUTEX_LOCK = 0;
    int READ_LOCK = 1;
    int WRITE_LOCK = 2;

    String namespace() default "default-lock";

    int time() default -1;

    TimeUnit timeUnit() default TimeUnit.SECONDS;

    int lockType() default MUTEX_LOCK;
}

package outfox.ead.youxuan.core.annotation;

import java.lang.annotation.*;

/**
 * 用于ldap验证
 * <ol>
 *     <li>为什么不用filterchain，因为filterchain是url patterns 不方便，每次写一个接口就得加patterns，所以搞个注解</li>
 *     <li>ldap的接口url中必须带有 'ldap' ，否则会被spring security拦截</li>
 * </ol>
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@AccessControl(exclude = true)
@Inherited
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface LDAP {
}

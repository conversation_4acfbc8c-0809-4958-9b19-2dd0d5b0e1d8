package outfox.ead.youxuan.core.config.security;

import io.jsonwebtoken.impl.DefaultClaims;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年08月04日 11:05
 */
public class YouxuanClaims extends DefaultClaims {
    private static final String USER_ID = "id";
    private static final String USERNAME = "username";
    private static final String DICT_UID = "dictUid";
    private static final String ROLES = "roles";
    private static final String ROLE_KEY = "roleKey";
    private static final String CURRENT_ROLE_KEY = "currentRoleKey";

    public YouxuanClaims() {
    }

    public YouxuanClaims(Map<String, Object> map) {
        super(map);
    }

    public YouxuanClaims setUserId(Long userId) {
        setValue(USER_ID, userId);
        return this;
    }

    public Long getUserId() {
        return get(USER_ID, Long.class);
    }

    public YouxuanClaims setUsername(String username) {
        setValue(USERNAME, username);
        return this;
    }

    public String getUsername() {
        return getString(USERNAME);
    }

    public YouxuanClaims setDictUid(String dictUid) {
        setValue(DICT_UID, dictUid);
        return this;
    }

    public String getDictUid() {
        return getString(DICT_UID);
    }

    public YouxuanClaims setRoles(String roles) {
        setValue(ROLES, roles);
        return this;
    }

    public String getRoles() {
        return getString(ROLES);
    }

    public YouxuanClaims setRoleKey(String roleKey) {
        setValue(ROLE_KEY, roleKey);
        return this;
    }

    public String getRoleKey() {
        return getString(ROLE_KEY);
    }

    public String getCurrentRoleKey() {
        return getString(CURRENT_ROLE_KEY);
    }

    public YouxuanClaims setCurrentRoleKey(String currentRoleKey) {
        setValue(CURRENT_ROLE_KEY, currentRoleKey);
        return this;
    }
}

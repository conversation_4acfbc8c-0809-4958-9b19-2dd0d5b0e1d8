package outfox.ead.youxuan.core.config.security;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import outfox.ead.youxuan.web.ad.controller.mapper.UserMapper;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserService;

/**
 * 线上配置，上线前设置为true
 * 权限校验的配置，仅限配置的路径不需要校验token
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@Profile("online")
@EnableGlobalMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfigForScopeProduct extends WebSecurityConfigurerAdapter {
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    private final RoleService roleService;
    private final UserService userService;
    private final UserMapper userMapper;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                .csrf().disable()
                .exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint).and()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
                .authorizeRequests()
                .requestMatchers(EndpointRequest.toAnyEndpoint()).permitAll()
                .antMatchers(HttpMethod.OPTIONS).permitAll()
                .antMatchers(
                        "/**/isBindYouxuan/**",
                        "/**/user/**",
                        "/**/login/**",
                        "/**/area",
                        "/**/booking",
                        "/**/health",
                        "/**/ldap/**",
                        "/showcase_component/dict/passed",
                        "/**/intranet/**",
                        "/**/register/**",
                        "/**/goods/**"
                ).permitAll()
                .anyRequest().authenticated();

        http.addFilterBefore(new JwtAuthenticationTokenFilter(roleService, userService, userMapper), UsernamePasswordAuthenticationFilter.class);
        http.headers().cacheControl();
    }

    @Override
    public void configure(WebSecurity web) {
        web.ignoring()
                .antMatchers(HttpMethod.OPTIONS)
                .antMatchers(
                        HttpMethod.GET,
                        "/favicon.ico",
                        "/**/*.html",
                        "/**/*.css",
                        "/**/*.js",
                        "/**/*.png",
                        "/**/*.gif",
                        "/**/*.ttf",
                        "/**/swagger-resources/**",
                        "/**/swagger-ui/**",
                        "/v3/api-docs**",
                        "/webjars/**"
                );
    }

}

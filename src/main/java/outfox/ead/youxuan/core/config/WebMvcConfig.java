package outfox.ead.youxuan.core.config;

import lombok.NonNull;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.joda.JodaTimeFormatterRegistrar;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.http.HttpHeaders;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import outfox.ead.youxuan.core.interceptor.AccessControlInterceptor;
import outfox.ead.youxuan.core.interceptor.LdapInterceptor;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/12 20:43
 */
@Configuration
class WebMvcConfig implements WebMvcConfigurer {
    /**
     * get请求类型转换
     *
     * @param registry 注册表
     */
    @Override
    public void addFormatters(@NonNull FormatterRegistry registry) {
        DateTimeFormatterRegistrar registrar = new DateTimeFormatterRegistrar();
        registrar.setTimeFormatter(DateTimeFormatter.ofPattern("HH:mm:ss"));
        registrar.setDateFormatter(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        registrar.setDateTimeFormatter(DateTimeFormatter.ofPattern("yyyy-MM-dd HH`:mm:ss"));
        registrar.registerFormatters(registry);

        JodaTimeFormatterRegistrar jodaTimeFormatterRegistrar = new JodaTimeFormatterRegistrar();
        jodaTimeFormatterRegistrar.setTimeFormatter(org.joda.time.format.DateTimeFormat.forPattern("HH:mm:ss"));
        jodaTimeFormatterRegistrar.setDateFormatter(org.joda.time.format.DateTimeFormat.forPattern("yyyy-MM-dd"));
        jodaTimeFormatterRegistrar.setDateTimeFormatter(org.joda.time.format.DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss"));
        jodaTimeFormatterRegistrar.registerFormatters(registry);
    }


    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                //放行哪些原始域
                .allowedOrigins("*")
                //是否发送Cookie信息
                .allowCredentials(true)
                //放行哪些原始域(请求方式)
                .allowedMethods("GET", "POST", "PUT", "DELETE")
                //放行哪些原始域(头部信息)
                .allowedHeaders("*");
        WebMvcConfigurer.super.addCorsMappings(registry);
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 解决 String 统一封装RestBody的问题
        HttpMessageConverter<?> httpMessageConverter = converters.get(7);
        if (!(httpMessageConverter instanceof MappingJackson2HttpMessageConverter)) {
            // 确保正确，如果有改动就重新debug
            throw new RuntimeException("MappingJackson2HttpMessageConverter is not here");
        }
        converters.add(0, httpMessageConverter);
    }

    @Bean
    public AccessControlInterceptor accessControlInterceptor() {
        return new AccessControlInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(accessControlInterceptor());
        registry.addInterceptor(new LdapInterceptor());
    }
}

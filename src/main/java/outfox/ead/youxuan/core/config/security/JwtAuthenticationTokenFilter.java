package outfox.ead.youxuan.core.config.security;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.OncePerRequestFilter;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.web.ad.controller.bo.LoginUser;
import outfox.ead.youxuan.web.ad.controller.mapper.UserMapper;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserService;

import javax.annotation.Nonnull;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collection;
import java.util.Collections;

import static outfox.ead.youxuan.constants.Constants.*;

/**
 * 如果不是permit的url，根据用户的Authentication鉴权
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    private final RoleService roleService;
    private final UserService userService;
    private final UserMapper userMapper;

    @Override
    protected void doFilterInternal(@Nonnull HttpServletRequest httpServletRequest,
                                    @Nonnull HttpServletResponse httpServletResponse,
                                    @Nonnull FilterChain filterChain) throws ServletException, IOException {
        String token = null, userIdStr = null, currentRoleKey = null;
        try {
            token = httpServletRequest.getHeader(AUTH_HEADER);
            // 通过token来校验用户，访问必须携带token
            if (StringUtils.isNotBlank(token) && !UNDEFINED.equals(token)) {
                YouxuanClaims claims = JwtSecurityToken.getClaimsFromToken(token);
                Long userId = claims.getUserId();
                Collection<Role> roles = roleService.listByUserId(userId);
                currentRoleKey = claims.getCurrentRoleKey();
                String finalCurrentRole = currentRoleKey;
                LoginUser loginUser = userMapper.do2LoginUser(userService.getById(userId), roles, roles.stream()
                        .filter(role -> role.getRoleKey().equals(finalCurrentRole))
                        .findAny()
                        .orElse(null));
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(loginUser, null, Collections.emptyList());
                authentication.setDetails(
                        new WebAuthenticationDetailsSource().buildDetails(httpServletRequest)
                );
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        } catch (Exception e) {
            log.warn("user authentication failed, token is {}, userId is {}, currentRole id {}", token, userIdStr, currentRoleKey, e);
        }finally {
            filterChain.doFilter(httpServletRequest, httpServletResponse);
        }
    }
}

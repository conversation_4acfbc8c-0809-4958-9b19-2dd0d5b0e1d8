package outfox.ead.youxuan.core.config.db;

import com.atomikos.jdbc.AtomikosDataSourceBean;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.mysql.cj.jdbc.MysqlXADataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import outfox.ead.youxuan.core.handler.mybatis.MyMetaObjectHandler;

import javax.sql.DataSource;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2021年12月07日 7:45 下午
 */
@Configuration
@MapperScan(basePackages = "outfox.ead.youxuan.mapper.youxuan", sqlSessionFactoryRef = "youxuanSqlSessionFactory")
public class YouxuanDatasourceConfig {
    @Primary
    @Bean(name = "youxuanDatasourceProperties")
    @ConfigurationProperties(prefix = "spring.datasource.youxuan")
    public DataSourceProperties youxuanDatasourceProperties() {
        return new DataSourceProperties();
    }

    @Primary
    @Bean(name = "youxuanDatasource")
    public DataSource youxuanDatasource(@Qualifier("youxuanDatasourceProperties") DataSourceProperties properties) throws SQLException {
        MysqlXADataSource mysqlXADataSource = new MysqlXADataSource();
        mysqlXADataSource.setUrl(properties.getUrl());
        mysqlXADataSource.setPinGlobalTxToPhysicalConnection(true);
        mysqlXADataSource.setUser(properties.getUsername());
        mysqlXADataSource.setPassword(properties.getPassword());

        AtomikosDataSourceBean ds = new AtomikosDataSourceBean();
        ds.setConcurrentConnectionValidation(false);
        ds.setXaDataSource(mysqlXADataSource);
        ds.setMaxLifetime(540);
        ds.setMaxPoolSize(30);
        ds.setTestQuery("SELECT 1 FROM DUAL");
        ds.setUniqueResourceName("youxuanDatasource");
        return ds;
    }

    @Primary
    @Bean("youxuanSqlSessionFactory")
    public SqlSessionFactory youxuanSqlSessionFactory(@Qualifier("youxuanDatasource") DataSource youxuanDataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(youxuanDataSource);
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        bean.setPlugins(interceptor);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/youxuan/*.xml"));
        GlobalConfig config = new GlobalConfig();
        config.setMetaObjectHandler(new MyMetaObjectHandler());
        config.setBanner(false);
        bean.setGlobalConfig(config);
        return bean.getObject();
    }


    @Primary
    @Bean("youxuanSqlSessionTemplate")
    public SqlSessionTemplate storySqlSessionTemplate(@Qualifier("youxuanSqlSessionFactory") SqlSessionFactory youxuanSqlSessionFactory) {
        return new SqlSessionTemplate(youxuanSqlSessionFactory);
    }
}

package outfox.ead.youxuan.core.config;

import in.zapr.druid.druidry.client.exception.QueryException;
import in.zapr.druid.druidry.query.DruidQuery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.core.thirdParty.druid.DruidBrandStatQuery;
import outfox.ead.youxuan.core.thirdParty.druid.DruidHttpClient;
import outfox.ead.youxuan.core.thirdParty.druid.StatementTimeSeriesStatistic;
import outfox.ead.youxuan.core.thirdParty.druid.StatementsGroupByStatics;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.AccountStatementsVO;
import outfox.ead.youxuan.web.ad.service.AdPlanService;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;

import static outfox.ead.youxuan.constants.ResponseType.SERVICE_ERROR;

/**
 * <AUTHOR>
 * @date 2021/11/2/11:34
 */
@Component
@Slf4j
@AllArgsConstructor
public class AsyncMethod {
    private final DruidHttpClient druidClient;
    private final AdPlanService adPlanService;

    DruidQuery getDruidQuery(AccountStatementsVO accountStatementsVO) {
        return DruidBrandStatQuery.queryBuilder(accountStatementsVO);
    }

    @Async
    public Future<List<StatementsGroupByStatics>> getDruidGroupByData(AccountStatementsVO accountStatementsVO) {
        if (!SecurityUtil.isAdmin()) {
            adPlanFilter(accountStatementsVO);
            if (Objects.nonNull(accountStatementsVO.getAdPlanIds()) && accountStatementsVO.getAdPlanIds().isEmpty()) {
                return new AsyncResult<>(Lists.emptyList());
            }
        }
        try {
            return new AsyncResult<>(druidClient.query(getDruidQuery(accountStatementsVO), StatementsGroupByStatics.class));
        } catch (QueryException e) {
            log.error("DRUID QUERY FAIL", e);
            throw new CustomException(SERVICE_ERROR, "druid查询失败");
        }
    }

    @Async
    public Future<List<StatementTimeSeriesStatistic>> getDruidTimeSeriesData(AccountStatementsVO accountStatementsVO) {
        if (!SecurityUtil.isAdmin()) {
            adPlanFilter(accountStatementsVO);
            if (Objects.nonNull(accountStatementsVO.getAdPlanIds()) && accountStatementsVO.getAdPlanIds().isEmpty()) {
                return new AsyncResult<>(Lists.emptyList());
            }
        }
        try {
            return new AsyncResult<>(druidClient.query(getDruidQuery(accountStatementsVO), StatementTimeSeriesStatistic.class));
        } catch (QueryException e) {
            log.error("DRUID QUERY FAIL", e);
            throw new CustomException(SERVICE_ERROR, "druid查询失败");
        }
    }

    /**
     * 过滤推广计划
     *
     * @param accountStatementsVO -
     */
    private void adPlanFilter(AccountStatementsVO accountStatementsVO) {
        List<Long> adPlanIds = accountStatementsVO.getAdPlanIds();
        List<Long> ids = adPlanService.listId(SecurityUtil.getUserId(), SecurityUtil.getCurrentRole().getId());
        if (Objects.nonNull(adPlanIds)) {
            ids.retainAll(adPlanIds);
        }
        accountStatementsVO.setAdPlanIds(ids);
    }

}

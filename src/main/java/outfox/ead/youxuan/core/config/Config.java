package outfox.ead.youxuan.core.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xxl.job.core.autoconfig.EnableXxlJobCore;
import com.youdao.luna.ipfilter.WebIpFilter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import outfox.ead.annotation.EnableCentralDogmaConfig;
import outfox.ead.youxuan.web.kol.service.LoginApiEndpointInterface;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/16 17:00
 */
@Configuration
@EnableTransactionManagement
@EnableScheduling
@EnableAsync
@EnableRetry
@EnableCentralDogmaConfig
@EnableXxlJobCore
@Slf4j
public class Config implements EnvironmentAware, AsyncConfigurer {
    private static Environment environment;

    /**
     * 获取CuratorFramework.
     * 这里显式设置了sessionTimeoutMs和connectTimeoutMs，默认时间是60s，会造成服务被杀死之后，隔很久才选主
     */
    @Bean
    public CuratorFramework curatorFramework() {
        String connectString = environment.getProperty("zookeeper.uri");
        CuratorFramework curatorFramework = CuratorFrameworkFactory.newClient(connectString, 10000, 5000,
                new ExponentialBackoffRetry(1000, 3));
        curatorFramework.start();
        return curatorFramework;
    }

    @Override
    public void setEnvironment(@NonNull Environment environment) {
        Config.environment = environment;
    }

    public static String env() {
        return environment.getActiveProfiles()[0];
    }

    public static boolean isLocal() {
        return "local".equals(Config.env());
    }

    @Bean(name = "loginRetrofit")
    public Retrofit loginRetrofit(@Value("${dict.login.baseUrl}") String loginUrl) {
        return new Retrofit.Builder()
                .baseUrl(loginUrl)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
    }

    @Bean("loginApiService")
    public LoginApiEndpointInterface loginApiService(@Qualifier("loginRetrofit") Retrofit retrofit) {
        return retrofit.create(LoginApiEndpointInterface.class);
    }

    @Bean("threadPoolTaskExecutor")
    @Override
    public TaskExecutor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(200);
        executor.setMaxPoolSize(400);
        executor.setQueueCapacity(400);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("Async-");
        executor.initialize();
        return new DelegatingSecurityContextAsyncTaskExecutor(executor);
    }

    @Bean
    public TypeReference<List<String>> stringListTypeReference() {
        return new TypeReference<List<String>>() {
        };
    }

    @Bean
    public TypeReference<List<Long>> longListTypeReference() {
        return new TypeReference<List<Long>>() {
        };
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> log.error("Async error method {},params {}", method, Arrays.toString(params), ex);
    }

    public static final String OVERMIND_CONFIG = "overmind";

    @ConditionalOnProperty(name = "intranet", havingValue = "true")
    @Bean
    public FilterRegistrationBean<WebIpFilter> ipFilterFilter() {
        FilterRegistrationBean<WebIpFilter> registrationBean = new FilterRegistrationBean<>();
        WebIpFilter ipFilter= new WebIpFilter();
        registrationBean.addInitParameter("ipconfig", OVERMIND_CONFIG);
        registrationBean.addInitParameter("nameSpace", "luna:dict:ip-filter:prod");
        registrationBean.addInitParameter("overmindAddr", "overmind.youdao.com");
        registrationBean.addInitParameter("timeout", "3000");
        registrationBean.setName("ipFilter");
        registrationBean.addUrlPatterns("/*");
        registrationBean.setFilter(ipFilter);
        registrationBean.setOrder(1);
        return registrationBean;
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(RedisSerializer.json());
        return template;
    }
}

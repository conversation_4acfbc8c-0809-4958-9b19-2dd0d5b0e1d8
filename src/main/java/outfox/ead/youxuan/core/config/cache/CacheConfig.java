package outfox.ead.youxuan.core.config.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022年09月28日 11:32
 */
@Configuration
@EnableCaching
@Setter
@Getter
@RequiredArgsConstructor
public class CacheConfig {
    @Bean
    public RedisCacheManager redisCacheManager(@Value("${cache.prefix}") String prefix,
                                               LettuceConnectionFactory lettuceConnectionFactory) {
        RedisSerializer<String> redisSerializer = new StringRedisSerializer();
        GenericJackson2JsonRedisSerializer jackson2JsonRedisSerializer = new GenericJackson2JsonRedisSerializer();

        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofSeconds(60))
                .prefixCacheNameWith(prefix)
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(redisSerializer))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jackson2JsonRedisSerializer))
                .disableCachingNullValues();

        return RedisCacheManager.builder(lettuceConnectionFactory)
                .cacheDefaults(config)
                .build();
    }

    @Bean
    public CaffeineCacheManager caffeineCacheManager() {
        // 使用Caffeine做本地缓存
        // 支持配置最大容量,超时时间等,可参考com.github.benmanes.caffeine.cache.CaffeineSpec
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
        Caffeine<Object, Object> caffeine = Caffeine
                .newBuilder()
                .softValues()
                .maximumSize(100)
                .expireAfterWrite(60, TimeUnit.SECONDS);
        caffeineCacheManager.setCaffeine(caffeine);

        return caffeineCacheManager;
    }

    @Bean
    @Primary
    public MultiCacheManager multiCacheManager(CaffeineCacheManager caffeineCacheManager, RedisCacheManager redisCacheManager) {
        return new MultiCacheManager(caffeineCacheManager, redisCacheManager);
    }
}

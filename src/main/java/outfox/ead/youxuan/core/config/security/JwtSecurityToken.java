package outfox.ead.youxuan.core.config.security;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.config.Config;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.web.ad.controller.bo.LoginUser;

import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

/**
 * 生成token
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Slf4j
public class JwtSecurityToken {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    private static final SignatureAlgorithm SIGNATURE_ALGORITHM = SignatureAlgorithm.HS512;
    private static final String SECRET = "asTiSHiDhsKrfSiKE";
    private static final long EXPIRATION = 60 * 60 * 10L;
    private static final long DAY = 24 * 60 * 60L;

    public static YouxuanClaims getClaimsFromToken(String token) {
        YouxuanClaims claims = new YouxuanClaims(Jwts.parser()
                .setSigningKey(SECRET)
                .parseClaimsJws(token)
                .getBody());
        String issuer = claims.getIssuer();
        if (issuer != null && !Config.env().equals(issuer)) {
            // some tolerant
            if (!"local".equals(Config.env())) {
                throw new JwtException("Jwt env error,expected issuer is " + Config.env());
            }
        }
        return claims;
    }


    public static String generateToken(LoginUser loginUser) {
        return generateToken(loginUser, EXPIRATION);
    }

    /**
     * @param expirationSec 有效时间，单位s
     */
    public static String generateToken(LoginUser loginUser, Long expirationSec) {
        YouxuanClaims claims = new YouxuanClaims();
        claims.setUserId(loginUser.getId());
        claims.setUsername(loginUser.getUsername());
        claims.setDictUid(loginUser.getDictUid());
        claims.setRoles(Arrays.toString(loginUser.getRoles().stream().map(Role::getRoleKey).toArray()));
        claims.setCurrentRoleKey(Optional.ofNullable(loginUser.getCurrentRole()).orElseGet(Role::new).getRoleKey());
        return jwt(claims, expirationSec);
    }

    public static String generateActiveToken(Long id, String roleKey, String email, String subject, long expired) {
        YouxuanClaims claims = new YouxuanClaims();
        claims.setUserId(id)
                .setUsername(email)
                .setRoleKey(roleKey)
                .setSubject(subject);
        return jwt(claims, expired);
    }

    private static String jwt(YouxuanClaims claims, long exp) {
        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(new Date())
                .setIssuer(Config.env())
                .setExpiration(new Date(System.currentTimeMillis() + exp * 1000))
                .signWith(SIGNATURE_ALGORITHM, SECRET)
                .compact();
    }

    public static <T> T of(String activeToken, Class<? extends T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(OBJECT_MAPPER.writeValueAsString(getClaimsFromToken(activeToken)), clazz);
        } catch (ExpiredJwtException e) {
            throw new CustomException(ResponseType.TOKEN_INVALID, "token expired");
        } catch (Exception e) {
            log.warn("activeToken is {}", activeToken, e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "active token error");
        }
    }
}

package outfox.ead.youxuan.core.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "youxuan.reporter")
@ConditionalOnProperty(prefix = "youxuan.reporter", name = "enable", havingValue = "true")
@Configuration
@Data
public class ReporterConfig {
    private boolean enabled;
    private String type;
    private String graphiteHost;
    private int graphitePort;
    private String prefix;
}


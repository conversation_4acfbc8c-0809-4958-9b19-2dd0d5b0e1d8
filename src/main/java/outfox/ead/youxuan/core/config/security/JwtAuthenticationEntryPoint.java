package outfox.ead.youxuan.core.config.security;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerExceptionResolver;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.core.handler.global.GlobalExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static outfox.ead.youxuan.constants.ResponseType.AUTHENTICATION_FAILED;

/**
 * 当一个未授权的用户请求非公有资源时, commence方法将会被调用，这里返回用户未登录
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {
    @Qualifier("handlerExceptionResolver")
    @Autowired
    private HandlerExceptionResolver resolver;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException e) {
        GlobalExceptionHandler.writeCorsHeaders(request, response);
        resolver.resolveException(request, response, null, new CustomException(AUTHENTICATION_FAILED));
    }
}

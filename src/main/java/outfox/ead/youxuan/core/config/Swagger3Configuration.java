package outfox.ead.youxuan.core.config;

import com.google.common.collect.ImmutableSet;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Collections;
import java.util.List;

import static outfox.ead.youxuan.constants.Constants.AUTH_HEADER;

/**
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/12 20:43
 */
@Configuration
public class Swagger3Configuration {
    @Value("${app.version}")
    public String version;
    @Value("${app.wiki.url}")
    public String wikiUrl;

    @Bean
    public Docket commonDocket() {
        return new Docket(DocumentationType.OAS_30)
                .securitySchemes(securitySchemes())
                .securityContexts(securityContexts())
                .groupName("ead")
                .apiInfo(commonApiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("outfox.ead.youxuan"))
                .build()
                .forCodeGeneration(true)
                .protocols(ImmutableSet.of("https", "http"));
    }

    /**
     * 设置授权信息
     */
    private List<SecurityScheme> securitySchemes() {
        ApiKey apiKey = new ApiKey(AUTH_HEADER, AUTH_HEADER, In.HEADER.toValue());
        return Collections.singletonList(apiKey);
    }

    /**
     * 授权信息全局应用
     */
    private List<SecurityContext> securityContexts() {
        return Collections.singletonList(
                SecurityContext.builder()
                        .securityReferences(Collections.singletonList(new SecurityReference(AUTH_HEADER, new AuthorizationScope[]{new AuthorizationScope("global", "")})))
                        .build()
        );
    }


    private ApiInfo commonApiInfo() {
        return new ApiInfoBuilder()
                .title("有道优选")
                .description("品牌广告投放")
                .contact(new Contact("wiki链接", wikiUrl, "<EMAIL>"))
                .version(version)
                .build();
    }
}

package outfox.ead.youxuan.core.handler.mybatis;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import outfox.ead.youxuan.util.LocalDateUtil;
import outfox.ead.youxuan.util.SecurityUtil;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/08/15/19:40
 */
@Slf4j
public class MyMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        if (SecurityUtil.isLogin()) {
            this.strictInsertFill(metaObject, "creator", Long.class, SecurityUtil.getUserIdIfPresent());
            this.strictInsertFill(metaObject, "modifier", Long.class, SecurityUtil.getUserIdIfPresent());
            this.strictInsertFill(metaObject, "roleId", Long.class, SecurityUtil.getCurrentRole().getId());
            this.strictInsertFill(metaObject, "creatorRole", Long.class, SecurityUtil.getCurrentRole().getId());
            this.strictInsertFill(metaObject, "modifierRole", Long.class, SecurityUtil.getCurrentRole().getId());
        }
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now(LocalDateUtil.CHINA));
        this.strictInsertFill(metaObject, "lastModTime", LocalDateTime.class, LocalDateTime.now(LocalDateUtil.CHINA));
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (SecurityUtil.isLogin()) {
            this.setFieldValByName("modifier", SecurityUtil.getUserIdIfPresent(), metaObject);
            this.setFieldValByName("modifierRole", SecurityUtil.getCurrentRole().getId(), metaObject);
        }
        this.setFieldValByName("lastModTime", LocalDateTime.now(LocalDateUtil.CHINA), metaObject);
    }
}

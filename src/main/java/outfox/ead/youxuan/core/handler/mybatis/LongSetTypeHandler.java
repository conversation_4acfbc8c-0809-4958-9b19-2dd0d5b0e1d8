package outfox.ead.youxuan.core.handler.mybatis;

import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/8 20:11
 */
public class LongSetTypeHandler extends CustomJacksonTypeHandler {
    public LongSetTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<Set<Long>>() {
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

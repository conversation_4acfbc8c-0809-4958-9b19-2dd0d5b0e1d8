package outfox.ead.youxuan.core.handler.mybatis;

import com.fasterxml.jackson.core.type.TypeReference;
import outfox.ead.youxuan.web.ad.controller.dto.StyleElement;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/8 20:11
 */
public class StyleElementTypeHandler extends CustomJacksonTypeHandler {
    public StyleElementTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<List<StyleElement>>() {
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

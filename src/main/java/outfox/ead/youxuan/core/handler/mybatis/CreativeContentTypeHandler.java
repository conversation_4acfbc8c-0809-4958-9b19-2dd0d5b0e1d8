package outfox.ead.youxuan.core.handler.mybatis;

import com.fasterxml.jackson.core.type.TypeReference;
import outfox.ead.youxuan.web.ad.controller.dto.CreativeContent;

import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/8 20:11
 */
public class CreativeContentTypeHandler extends CustomJacksonTypeHandler {
    public CreativeContentTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, new TypeReference<Set<CreativeContent>>() {
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

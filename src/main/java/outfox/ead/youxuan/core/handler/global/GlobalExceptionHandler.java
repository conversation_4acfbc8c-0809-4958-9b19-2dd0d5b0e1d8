package outfox.ead.youxuan.core.handler.global;

import io.jsonwebtoken.JwtException;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import outfox.ead.youxuan.core.dto.ApiResponse;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.util.SecurityUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;

import static org.springframework.boot.logging.LogLevel.*;
import static outfox.ead.youxuan.constants.ResponseType.ACCESS_DENIED;


/**
 * 此类可以统一拦截、处理所有{@link org.springframework.validation.annotation.Validated}和 Controller 中的异常。
 * <b>Service 层的异常不会捕获。</b>
 *
 * <AUTHOR> <EMAIL>
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 最终拦截
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse handleException(Exception e) {
        log.error("userId:{},Caught UnknownException:{}", SecurityUtil.getUserId(), e.getMessage(), e);
        return ApiResponse.error();
    }

    /**
     * 1.处理 form data方式调用接口校验失败抛出的异常<p>
     * 2.处理 json 请求体调用接口校验失败抛出的异常
     */
    @ExceptionHandler(BindException.class)
    public ApiResponse bindExceptionHandler(BindException e) {
        log.warn("Caught Exception:{}", e.getMessage(), e);
        return ApiResponse.error().setMessage("请求参数错误");
    }

    @ExceptionHandler(JwtException.class)
    public ApiResponse jwtExceptionHandler(JwtException e) {
        log.warn("jwt error", e);
        return ApiResponse.error().setMessage(e.getMessage());
    }

    /**
     * 处理 json 请求体调用接口校验失败抛出的异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        String msg = e.getBindingResult().getFieldErrors()
                .stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.toList()).toString();
        log.warn("Caught Exception:{}", e.getMessage(), e);
        return ApiResponse.error().setMessage(StringUtils.removeStart(StringUtils.removeEnd(msg, "]"), "["));
    }

    /**
     * 方法入参错误处理
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ApiResponse methodArgumentTypeMismatchExceptionHandler(MethodArgumentTypeMismatchException e) {
        log.warn("Caught Exception:{}", e.getMessage(), e);
        return ApiResponse.error().setMessage(StringUtils.removeStart(StringUtils.removeEnd(e.getMessage(), "]"), "["));
    }

    @ExceptionHandler(ServletException.class)
    public ApiResponse servletExceptionHandler(ServletException e) {
        log.warn("Caught ServletException", e);
        return ApiResponse.error().setMessage("请检查请求是否错误");
    }

    /**
     * 处理单个参数校验失败抛出的异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ApiResponse constraintViolationExceptionHandler(ConstraintViolationException e) {
        String msg = e.getConstraintViolations()
                .stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.toList()).toString();
        log.warn("Caught Exception:[{}],LoginUser:[{}]", e.getMessage(), SecurityUtil.isLogin() ? SecurityUtil.getLoginUser() : null, e);
        return ApiResponse.error().setMessage(StringUtils.removeStart(StringUtils.removeEnd(msg, "]"), "["));
    }

    @ExceptionHandler(CustomException.class)
    public ApiResponse customExceptionHandler(CustomException e) {
        if (e.getLevel() == DEBUG) {
            log.debug("Caught Exception:[{}],Principal:[{}]", e.getMessage(), SecurityUtil.isLogin() ? SecurityUtil.getPrincipal() : null, e);
        } else if (e.getLevel() == INFO) {
            log.info("Caught Exception:[{}],Principal:[{}]", e.getMessage(), SecurityUtil.isLogin() ? SecurityUtil.getPrincipal() : null, e);
        } else if (e.getLevel() == WARN) {
            log.warn("Caught Exception:[{}],Principal:[{}]", e.getMessage(), SecurityUtil.isLogin() ? SecurityUtil.getPrincipal() : null, e);
        } else if (e.getLevel() == ERROR) {
            log.error("Caught Exception:[{}],Principal:[{}]", e.getMessage(), SecurityUtil.isLogin() ? SecurityUtil.getPrincipal() : null, e);
        }
        return new ApiResponse().setCode(e.getCode()).setMessage(e.getMessage());
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ApiResponse accessDeniedExceptionHandler(AccessDeniedException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}',权限校验失败'{}'", requestURI, e.getMessage());
        return new ApiResponse().setCode(ACCESS_DENIED.getCode()).setMessage("没有权限");
    }

    @ExceptionHandler(ClientAbortException.class)
    public ApiResponse clientAbortExceptionHandler(ClientAbortException e, HttpServletRequest request) {
        log.warn("client abort", e);
        return ApiResponse.success();
    }

    /**
     * 处理跨域且未添加跨域消息头的请求
     */
    public static void writeCorsHeaders(HttpServletRequest request, HttpServletResponse response) {
        if (!response.containsHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN)) {
            String origin = request.getHeader("Origin");
            if (StringUtils.isNotEmpty(origin)) {
                response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
            }
        }
        if (!response.containsHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS)) {
            response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
        }
    }
}


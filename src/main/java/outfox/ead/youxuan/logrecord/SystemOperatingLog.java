package outfox.ead.youxuan.logrecord;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import javax.annotation.Nullable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Document
@NoArgsConstructor
@AllArgsConstructor
public class SystemOperatingLog {
    @MongoId
    private String id;
    /**
     * 操作用户名
     */
    @Field(name = "userName")
    private String userName;
    /**
     * 操作用户ID
     */
    @Field(name = "userId")
    private String userId;

    @Field(name = "roleId")
    private String roleId;
    /**
     * 操作时间
     */
    @Field(name = "dateTime")
    private LocalDateTime dateTime;
    /**
     * 展示用时间
     */
    @Field(name = "displayDateTime")
    private String displayDateTime;
    /**
     * 操作类型/模块名
     */
    @Field(name = "type")
    private String type;
    /**
     * 子类型
     */
    @Field(name = "subName")
    private String subType;
    /**
     * 执行状态
     */
    @Field(name = "success")
    private boolean success;
    /**
     * 变更内容
     */
    @Nullable
    @Field(name = "summary")
    private String summary;
    /**
     * 备注信息
     */
    @Field(name = "extra")
    private String extra;
    /**
     * 操作的对象的ID
     */
    @Field(name = "bizNo")
    private String bizNo;
    /**
     * 类信息与方法名
     */
    @Field(name = "className")
    private String className;
    @Field(name = "methodName")
    private String methodName;
}

package outfox.ead.youxuan.logrecord;

import com.mzt.logapi.beans.CodeVariableType;
import com.mzt.logapi.beans.LogRecord;
import com.mzt.logapi.service.ILogRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.util.SecurityUtil;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class DbLogRecordServiceImpl implements ILogRecordService {

    private final SystemOperatingLogRepository systemOperatingLogRepository;

    @Override
    public void record(LogRecord logRecord) {
        SystemOperatingLog systemOperatingLog = buildSystemOperatingLog(logRecord);
        systemOperatingLogRepository.save(systemOperatingLog);
    }

    @Override
    public List<LogRecord> queryLog(String bizNo, String type) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<LogRecord> queryLogByBizNo(String bizNo, String type, String subType) {
        return null;
    }

    private SystemOperatingLog buildSystemOperatingLog(LogRecord logRecord) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(logRecord.getCreateTime().toInstant(), ZoneId.of("+8"));
        return SystemOperatingLog.builder()
                .userId(Objects.requireNonNull(SecurityUtil.getUserId()).toString())
                .roleId(SecurityUtil.getCurrentRole().getId().toString())
                .userName(logRecord.getOperator())
                .dateTime(localDateTime)
                .type(logRecord.getType())
                .subType(logRecord.getSubType())
                .success(!logRecord.isFail())
                .summary(logRecord.getAction())
                .extra(logRecord.getExtra())
                .bizNo(logRecord.getBizNo())
                .className(logRecord.getCodeVariable().get(CodeVariableType.ClassName).toString())
                .methodName((String) logRecord.getCodeVariable().get(CodeVariableType.MethodName))
                .build();
    }
}
package outfox.ead.youxuan.logrecord;

import com.mzt.logapi.beans.Operator;
import com.mzt.logapi.service.IOperatorGetService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import outfox.ead.youxuan.util.SecurityUtil;

import java.util.Optional;

@Configuration
public class LogRecordConfiguration {
    @Bean
    public IOperatorGetService operatorGetService() {
        return () -> Optional.ofNullable(SecurityUtil.getLoginUser().toString())
                .map(Operator::new)
                .orElseThrow(() -> new IllegalArgumentException("user is null"));
    }
}
package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.apache.commons.lang3.ObjectUtils;
import outfox.ead.youxuan.entity.AdPosition;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import static outfox.ead.youxuan.constants.ResourceStatusEnum.DELETED;
import static outfox.ead.youxuan.constants.ResourceStatusEnum.VALID;

/**
 * <AUTHOR>
 */
public interface AdPositionMapper extends BaseMapper<AdPosition> {
    /**
     * 通过样式id列表查询广告位
     *
     * @param styleIds 样式id列表
     * @return 广告位列表
     */
    List<AdPosition> listByStyleIds(List<Long> styleIds);

    /**
     * 查看未删除的广告位个数
     *
     * @return 个数
     */
    default long countAllValid() {
        return new LambdaQueryChainWrapper<>(this).ne(AdPosition::getStatus, DELETED.getCode()).count();
    }

    /**
     * 通过媒体Id查询未删除的广告位
     *
     * @param mediaId 媒体id
     * @return 广告位列表
     */
    default List<AdPosition> listNotDeleteByMediaId(Long mediaId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AdPosition::getMediaId, mediaId)
                .list();
    }

    /**
     * 通过名字模糊查询广告位
     * 通过媒体id查询广告位
     *
     * @param name     名字
     * @param mediaIds 媒体id
     * @return 广告位列表
     */
    default List<AdPosition> listNotDeletedPositionsLikeNameOrInMediaIds(String name, Set<Long> mediaIds) {
        return new LambdaQueryChainWrapper<>(this)
                .ne(AdPosition::getStatus, DELETED.getCode())
                .and(wrapper ->
                        wrapper
                                .like(Objects.nonNull(name), AdPosition::getName, name)
                                .or()
                                .in(ObjectUtils.isNotEmpty(mediaIds), AdPosition::getMediaId, mediaIds))
                .list();
    }

    default List<AdPosition> listValid(Integer promotionTarget) {
        return new LambdaQueryChainWrapper<>(this)
                .like(AdPosition::getPromotionType, promotionTarget)
                .eq(AdPosition::getStatus, VALID.getCode())
                .list();
    }

}





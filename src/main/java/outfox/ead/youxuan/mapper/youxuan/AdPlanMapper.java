package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import outfox.ead.youxuan.entity.AdPlan;
import outfox.ead.youxuan.util.SecurityUtil;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.PromotionStatusEnum.*;

/**
 * <AUTHOR>
 */
public interface AdPlanMapper extends BaseMapper<AdPlan> {
    /**
     * 通过名字查询推广计划
     *
     * @param adPlanName 推广计划名称
     * @return 推广计划列表
     */
    default List<AdPlan> listByName(String adPlanName) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(!SecurityUtil.isAdmin(), AdPlan::getCreator, SecurityUtil.getUserId())
                .like(AdPlan::getName, adPlanName)
                .list();
    }

    /**
     * 通过名字查询有效的推广计划个数，用于名字判重
     *
     * @param name 名字
     * @param id   id
     * @return num
     */
    default long countByNameExcludeId(String name, Long id) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AdPlan::getName, name)
                .ne(AdPlan::getStatus, DELETED.getCode())
                .ne(Objects.nonNull(id) && id != 0, AdPlan::getId, id)
                .count();
    }

    /**
     * 查询有效的推广计划
     *
     * @return 推广计划列表
     */
    default List<AdPlan> listValid() {
        return new LambdaQueryChainWrapper<>(this)
                .ne(AdPlan::getStatus, DELETED.getCode())
                .ne(AdPlan::getStatus, DELIVER_FINISH.getCode())
                .ne(AdPlan::getStatus, DELIVER_PAUSE.getCode())
                .list();
    }

    /**
     * 通过主键以及计费方式查询未删除的推广计划
     *
     * @param ids         推广计划id列表
     * @param billingType 计费方式
     * @return 推广计划列表
     */
    default List<AdPlan> listNotDeleteByIdsAndBillingType(Collection<Long> ids, Integer billingType) {
        return new LambdaQueryChainWrapper<>(this)
                .ne(AdPlan::getStatus, DELETED.getCode())
                .in(AdPlan::getId, ids)
                .eq(AdPlan::getBillingType, billingType)
                .list();
    }

    /**
     * 通过名字模糊查询未删除推广计划
     *
     * @param name         名字
     * @param deliveryType
     * @return 未删除推广计划列表
     */
    default List<AdPlan> listNotDeleteByNameAndDeliveryType(String name, List<Long> deliveryType) {
        return new LambdaQueryChainWrapper<>(this)
                .like(Objects.nonNull(name), AdPlan::getName, name)
                .ne(AdPlan::getStatus, DELETED.getCode())
                .in(Objects.nonNull(deliveryType), AdPlan::getDeliveryType, deliveryType)
                .eq(!SecurityUtil.isAdmin(), AdPlan::getCreator, SecurityUtil.getUserId())
                .eq(!SecurityUtil.isAdmin(), AdPlan::getRoleId, SecurityUtil.getCurrentRole().getId())
                .list();
    }

    /**
     * 通过推广计划id分页查找
     *
     * @param adPlanIds     推广计划id列表
     * @param deliveryTypes
     * @param current       页数
     * @param size          行数
     * @return 推广计划分页
     */
    default Page<AdPlan> pageInIdsAndDeliveryType(Set<Long> adPlanIds, List<Long> deliveryTypes, Long current, Long size) {
        return new LambdaQueryChainWrapper<>(this)
                .in(AdPlan::getId, adPlanIds)
                .in(Objects.nonNull(deliveryTypes), AdPlan::getDeliveryType, deliveryTypes)
                .eq(!SecurityUtil.isAdmin(), AdPlan::getCreator, SecurityUtil.getUserId())
                .orderByDesc(AdPlan::getId)
                .page(new Page<>(current, size));
    }

    default List<Long> listId(Long id, Long roleId) {
        return new LambdaQueryChainWrapper<>(this)
                .select(AdPlan::getId)
                .eq(AdPlan::getCreator, id)
                .eq(AdPlan::getRoleId,roleId)
                .list()
                .stream()
                .map(AdPlan::getId)
                .collect(Collectors.toList());
    }

    /**
     * 设置联合频控的推广计划idList
     * @param adPlanIdList
     * @param unionFrequencyRuleId
     */
    default void setBindUnionFrequencyRuleId(Set<Long> adPlanIdList, Long unionFrequencyRuleId){
        //防止全表更新
        if(CollectionUtils.isEmpty(adPlanIdList)){
            return;
        }

        new LambdaUpdateChainWrapper<>(this)
                .in(CollectionUtils.isNotEmpty(adPlanIdList), AdPlan::getId, adPlanIdList)
                .set(AdPlan::getValidUnionFrequencyRuleId, unionFrequencyRuleId)
                .update();
    }
}





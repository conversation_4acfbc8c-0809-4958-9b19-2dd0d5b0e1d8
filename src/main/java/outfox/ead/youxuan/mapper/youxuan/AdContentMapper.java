package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import outfox.ead.youxuan.entity.AdContent;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdContentMapper extends BaseMapper<AdContent> {
    int VALID = 0;
    int DELETED = 1;

    /**
     * 通过推广组id删除
     *
     * @param id 推广组id
     */
    default void deleteByAdGroupId(Long id) {
        new LambdaUpdateChainWrapper<>(this)
                .eq(AdContent::getAdGroupId, id)
                .set(AdContent::getStatus, DELETED)
                .update();
    }

    /**
     * 通过推广组id查询有效的adContent
     *
     * @param id 推广组id
     * @return list
     */
    default List<AdContent> listValidByAdGroupId(Long id) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AdContent::getAdGroupId, id)
                .eq(AdContent::getStatus, VALID)
                .list();
    }

    /**
     * 通过推广组id列表查询有效的adContent
     *
     * @param adGroupIds 推广组id列表
     * @return list
     */
    default List<AdContent> listByAdGroupIds(List<Long> adGroupIds) {
        return new LambdaQueryChainWrapper<>(this)
                .in(AdContent::getAdGroupId, adGroupIds)
                .eq(AdContent::getStatus, VALID)
                .list();
    }

    default AdContent getByGroupIdAndType(Long id, Integer type){
        return new LambdaQueryChainWrapper<>(this)
                .eq(AdContent::getAdGroupId, id)
                .eq(AdContent::getType, type)
                .one();
    }
}





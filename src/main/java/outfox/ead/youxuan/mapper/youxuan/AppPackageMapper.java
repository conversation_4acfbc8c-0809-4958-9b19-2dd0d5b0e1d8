package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.AppPackage;
import java.util.Collection;
import java.util.List;

public interface AppPackageMapper extends BaseMapper<AppPackage> {

    /**
     * 获取全部有效的应用包信息
     * @return
     */
    default List<AppPackage> listValid() {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AppPackage::getStatus, 1)
                .list();
    }
}

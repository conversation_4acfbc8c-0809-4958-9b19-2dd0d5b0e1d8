package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.PlatformTask;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【PlatformTask】的数据库操作Mapper
 * @createDate 2022-02-18 10:53:58
 * @Entity outfox.ead.youxuan.entity.PlatformTask
 */
public interface PlatformTaskMapper extends BaseMapper<PlatformTask> {

    default List<PlatformTask> getByPlatformId(Long platformId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(PlatformTask::getPlatformId, platformId)
                .list();
    }

    default List<PlatformTask> getByPlatformIds(List<Long> platformIds) {
        return new LambdaQueryChainWrapper<>(this)
                .in(PlatformTask::getPlatformId, platformIds)
                .list();
    }

    default List<PlatformTask> getByIds(List<Long> ids) {
        return new LambdaQueryChainWrapper<>(this)
                .in(PlatformTask::getId, ids)
                .list();
    }


}





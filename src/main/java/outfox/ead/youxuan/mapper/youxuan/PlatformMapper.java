package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.Platform;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【Platform(平台信息)】的数据库操作Mapper
 * @createDate 2022-02-11 14:13:04
 * @Entity outfox.ead.youxuan.entity.Platform
 */
public interface PlatformMapper extends BaseMapper<Platform> {

    default Platform selectOneByName(String name) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(Platform::getName, name)
                .one();
    }

    Platform getByPlatformContentId(Long platformContentId);

    default List<Platform> getById(Collection<Long> platformIds) {
        return new LambdaQueryChainWrapper<>(this)
                .in(Platform::getId, platformIds)
                .list();
    }
}





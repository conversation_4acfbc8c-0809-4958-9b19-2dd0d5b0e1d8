package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.PlatformContent;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【PlatformContent】的数据库操作Mapper
 * @createDate 2022-02-18 10:55:53
 * @Entity outfox.ead.youxuan.entity.PlatformContent
 */
public interface PlatformContentMapper extends BaseMapper<PlatformContent> {

    default List<PlatformContent> getListByPlatformTaskId(Long platformTaskId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(PlatformContent::getPlatformTaskId, platformTaskId)
                .list();
    }

    default List<PlatformContent> getListByPlatformTaskIds(Collection<Long> platformTaskIds) {
        return new LambdaQueryChainWrapper<>(this)
                .in(PlatformContent::getPlatformTaskId, platformTaskIds)
                .list();
    }

    default List<PlatformContent> getByIds(List<Long> ids) {
        return new LambdaQueryChainWrapper<>(this)
                .in(PlatformContent::getId, ids)
                .list();
    }

    default PlatformContent getByPlatformTaskIdAndContentName(Long platformTaskId, String platformContentName) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(PlatformContent::getPlatformTaskId, platformTaskId)
                .eq(PlatformContent::getName, platformContentName)
                .one();
    }
}





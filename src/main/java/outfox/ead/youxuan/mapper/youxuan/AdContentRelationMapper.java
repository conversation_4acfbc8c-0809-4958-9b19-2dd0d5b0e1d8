package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import io.jsonwebtoken.lang.Collections;
import org.assertj.core.util.Lists;
import outfox.ead.youxuan.entity.AdContentRelation;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface AdContentRelationMapper extends BaseMapper<AdContentRelation> {

    int VALID = 0;
    int DELETED = 1;

    /**
     * 通过推广组id列表查询购买关联
     *
     * @param adGroupIds 推广组id列表
     * @return relaition列表
     */
    default List<AdContentRelation> listByAdGroupIds(Collection<Long> adGroupIds) {
        if (Collections.isEmpty(adGroupIds)) {
            return Lists.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .in(AdContentRelation::getAdGroupId, adGroupIds)
                .ne(AdContentRelation::getStatus, DELETED)
                .list();
    }

    /**
     * 通过adGroupIds列表查找有效的relation
     *
     * @param adGroupIds 推广组id列表
     * @return relation列表
     */
    List<AdContentRelation> listValidByAdGroupIds(List<Long> adGroupIds);

    /**
     * 根据样式id获取有效的AdContentRelation，可以指定排除某个推广组的AdContentRelation
     *
     * @param styleIds  样式id列表
     * @param adGroupId 推广组id
     * @return List<AdContentRelation>
     */
    List<AdContentRelation> listValidByStyleIdsAndFilterByAdGroupId(List<Long> styleIds, Long adGroupId);

    /**
     * 根据样式id获取有效的AdContentRelation
     *
     * @param styleId 样式id
     * @return List<AdContentRelation>
     */
    default List<AdContentRelation> listByStyleId(Long styleId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AdContentRelation::getStyleId, styleId)
                .ne(AdContentRelation::getStatus, DELETED)
                .list();
    }

    /**
     * 通过推广组id查询有效relation
     *
     * @param adGroupId 推广组id
     * @return relaition
     */
    default List<AdContentRelation> listByAdGroupId(Long adGroupId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AdContentRelation::getAdGroupId, adGroupId)
                .ne(AdContentRelation::getStatus, DELETED)
                .list();
    }

    /**
     * 通过推广组id删除
     *
     * @param adGroupId 推广组id
     */
    default void deleteByAdGroupId(Long adGroupId) {
        new LambdaUpdateChainWrapper<>(this)
                .eq(AdContentRelation::getAdGroupId, adGroupId)
                .set(AdContentRelation::getStatus, DELETED)
                .update();
    }

    default List<AdContentRelation> listByStyleIdsAndFilterByAdGroupId(Collection<Long> styleIds, Long adGroupId) {
        return new LambdaQueryChainWrapper<>(this)
                .in(AdContentRelation::getStyleId, styleIds)
                .ne(Objects.nonNull(adGroupId), AdContentRelation::getAdGroupId, adGroupId)
                .eq(AdContentRelation::getStatus, VALID)
                .list();
    }

    default List<AdContentRelation> listByStyleIdsAndGroupIds(Set<Long> styleIds, Set<Long> adGroupIds) {
        return new LambdaQueryChainWrapper<>(this)
                .in(AdContentRelation::getStyleId, styleIds)
                .in(AdContentRelation::getAdGroupId, adGroupIds)
                .eq(AdContentRelation::getStatus, VALID)
                .list();
    }
}





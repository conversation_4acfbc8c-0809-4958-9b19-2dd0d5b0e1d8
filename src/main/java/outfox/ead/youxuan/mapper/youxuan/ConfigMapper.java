package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import outfox.ead.youxuan.entity.Config;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【Config(配置表)】的数据库操作Mapper
 * @createDate 2022-07-25 10:59:17
 * @Entity generator.domain.Config
 */
public interface ConfigMapper extends BaseMapper<Config> {

    /**
     * 批量查询配置项
     * @param configKeys
     * @return
     */
    default List<Config> queryByKey(Set<String> configKeys) {
        if (CollectionUtils.isEmpty(configKeys)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .in(Config::getConfigKey, configKeys)
                .list();
    }

    default Config queryByKey(String configKey) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(Config::getConfigKey, configKey)
                .one();
    }
}





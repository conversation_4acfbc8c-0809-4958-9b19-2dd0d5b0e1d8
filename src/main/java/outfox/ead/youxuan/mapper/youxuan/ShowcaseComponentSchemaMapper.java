package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.constants.ContentMarketingConstants;
import outfox.ead.youxuan.entity.ShowcaseComponentSchema;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ShowcaseComponentSchema(橱窗组件样式)】的数据库操作Mapper
* @createDate 2022-05-30 19:55:45
* @Entity outfox.ead.youxuan.entity.ShowcaseComponentSchema
*/
public interface ShowcaseComponentSchemaMapper extends BaseMapper<ShowcaseComponentSchema> {

    default List<ShowcaseComponentSchema> listValidByPromotionType(Integer promotionType) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ShowcaseComponentSchema::getPromotionType, promotionType)
                .eq(ShowcaseComponentSchema::getStatus, ContentMarketingConstants.SHOWCASE_COMPONENT_SCHEMA_VALID)
                .list();
    }
}





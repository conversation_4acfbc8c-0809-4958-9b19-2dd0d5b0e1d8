package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import org.apache.commons.lang3.ObjectUtils;
import outfox.ead.youxuan.constants.PromotionStatusEnum;
import outfox.ead.youxuan.entity.AdGroup;
import outfox.ead.youxuan.util.SecurityUtil;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import static outfox.ead.youxuan.constants.PromotionStatusEnum.DELETED;

/**
 * <AUTHOR>
 */
public interface AdGroupMapper extends BaseMapper<AdGroup> {
    /**
     * 名称数量
     *
     * @param name     推广组名称
     * @param id       推广组id
     * @param adPlanId 推广计划id
     * @return int
     */
    default long nameCount(String name, Long id, Long adPlanId) {
        return new LambdaQueryChainWrapper<>(this)
                .ne(Objects.nonNull(id), AdGroup::getId, id)
                .eq(Objects.nonNull(adPlanId) && adPlanId != 0, AdGroup::getAdPlanId, adPlanId)
                .eq(AdGroup::getName, name)
                .ne(AdGroup::getStatus, DELETED.getCode())
                .count();
    }

    /**
     * 查询有效广告组
     *
     * @param id 推广计划id
     * @return 推广组列表
     */
    default List<AdGroup> listNoteDeleteByAdPlanId(Long id) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AdGroup::getAdPlanId, id)
                .ne(AdGroup::getStatus, DELETED.getCode())
                .list();
    }

    /**
     * @param id     推广计划id
     * @param status 状态
     */
    default void updateValidStatusByAdPlanId(Long id, Integer status) {
        new LambdaUpdateChainWrapper<>(this)
                .eq(AdGroup::getAdPlanId, id)
                .ne(AdGroup::getStatus, DELETED.getCode())
                .ne(AdGroup::getStatus, PromotionStatusEnum.DELIVER_PAUSE.getCode())
                .set(AdGroup::getStatus, status)
                .update();
    }

    /**
     * 通过推广计划id查找推广组数量
     *
     * @param id 推广计划id
     * @return 数量
     */
    default long countNotDeleteByAdPlanId(Long id) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AdGroup::getAdPlanId, id)
                .ne(AdGroup::getStatus, DELETED.getCode())
                .count();
    }

    default List<AdGroup> listNotDeletedAdGroupsLikeNameOrInAdPlanIds(String name, Set<Long> adPlanIds) {
        return new LambdaQueryChainWrapper<>(this)
                .ne(AdGroup::getStatus, DELETED.getCode())
                .eq(!SecurityUtil.isAdmin(), AdGroup::getCreator, SecurityUtil.getUserId())
                .eq(!SecurityUtil.isAdmin(), AdGroup::getRoleId, SecurityUtil.getCurrentRole().getId())
                .and(wrapper ->
                        wrapper
                                .like(Objects.nonNull(name), AdGroup::getName, name)
                                .or()
                                .in(ObjectUtils.isNotEmpty(adPlanIds), AdGroup::getAdPlanId, adPlanIds))
                .list();
    }
}





package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.AdDeliveryTime;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface AdDeliveryTimeMapper extends BaseMapper<AdDeliveryTime> {

    /**
     * 找到具有时间交集的投放时间
     *
     * @param adDeliveryTimes 时间段列表
     * @param excludeAdPlanId 需要排除的推广计划id
     * @return 投放时间列表
     */
    List<AdDeliveryTime> listByTimeIntervalExcludeAdPlan(Collection<AdDeliveryTime> adDeliveryTimes, Long excludeAdPlanId);

    /**
     * 通过planIds查询已投放过的plan
     *
     * @param adPlanIds 推广计划id列表
     * @return 推广计划id
     */
    default Set<Long> listDeliveringByAdPlanIds(Collection<Long> adPlanIds) {
        return new LambdaQueryChainWrapper<>(this)
                .in(AdDeliveryTime::getAdPlanId, adPlanIds)
                .le(AdDeliveryTime::getStartTime, LocalDateTime.now())
                .list()
                .stream()
                .map(AdDeliveryTime::getAdPlanId)
                .collect(Collectors.toSet());
    }

    /**
     * 通过planIds查询已投放过的plan
     *
     * @param adPlanIds 推广计划id列表
     * @return 推广计划id
     */
    default long countDeliveredByAdPlanIds(Collection<Long> adPlanIds) {
        return new LambdaQueryChainWrapper<>(this)
                .in(AdDeliveryTime::getAdPlanId, adPlanIds)
                .le(AdDeliveryTime::getStartTime, LocalDateTime.now())
                .count();
    }

    /**
     * 通过planIds查询在投的plan
     * 在投 = 投放中 + 即将开始
     *
     * @param adPlanIds 推广计划id列表
     * @return 推广计划id
     */
    default long countToDeliveryByAdPlanIds(Set<Long> adPlanIds) {
        if (adPlanIds.isEmpty()) {
            return 0;
        }
        return new LambdaQueryChainWrapper<>(this)
                .in(AdDeliveryTime::getAdPlanId, adPlanIds)
                .ge(AdDeliveryTime::getEndTime, LocalDateTime.now())
                .count();
    }
}





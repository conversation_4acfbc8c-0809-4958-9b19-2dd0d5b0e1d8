package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import outfox.ead.youxuan.entity.UnionFrequencyRule;

import java.util.List;
import java.util.Objects;

import static outfox.ead.youxuan.constants.UnionFrequencyRuleStatusEnum.DELETED;
import static outfox.ead.youxuan.constants.UnionFrequencyRuleStatusEnum.VALID;

public interface UnionFrequencyRuleMapper extends BaseMapper<UnionFrequencyRule> {

    /**
     * 查询有效的联合频控
     *
     * @return 联合频控
     */
    default List<UnionFrequencyRule> listValid() {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UnionFrequencyRule::getStatus, VALID.getCode())
                .list();
    }

    /**
     * 逻辑删除联合频控
     *
     * @param id
     */
    default void deleteById(Long id) {
        new LambdaUpdateChainWrapper<>(this)
                .eq(UnionFrequencyRule::getId, id)
                .set(UnionFrequencyRule::getStatus, DELETED.getCode())
                .update();
    }

    /**
     * 分页查询联合频控
     * @param unionFrequencyRuleId
     * @param unionFrequencyRuleName
     * @param statusList
     * @param current
     * @param size
     * @return
     */
    default IPage<UnionFrequencyRule> pageDO(Long adPlanId, Long unionFrequencyRuleId, String unionFrequencyRuleName, List<Integer> statusList, long current, long size) {
        return new LambdaQueryChainWrapper<>(this)
                .apply(Objects.nonNull(adPlanId), "JSON_CONTAINS(AD_PLAN_ID_LIST, CAST({0} AS JSON))", adPlanId)
                .eq(Objects.nonNull(unionFrequencyRuleId), UnionFrequencyRule::getId, unionFrequencyRuleId)
                .like(Objects.nonNull(unionFrequencyRuleName), UnionFrequencyRule::getName, unionFrequencyRuleName)
                .in(CollectionUtils.isNotEmpty(statusList), UnionFrequencyRule::getStatus, statusList)
                .orderByDesc(UnionFrequencyRule::getLastModTime)
                .page(new Page<>(current, size));

    }
}





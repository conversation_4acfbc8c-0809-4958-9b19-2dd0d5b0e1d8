package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.ShowcaseComponentWhiteList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【ShowcaseComponentWhiteList(橱窗组件特殊功能白名单)】的数据库操作Mapper
* @createDate 2022-05-25 19:47:02
* @Entity outfox.ead.youxuan.entity.ShowcaseComponentWhiteList
*/
public interface ShowcaseComponentWhiteListMapper extends BaseMapper<ShowcaseComponentWhiteList> {

    default Long countByUserId(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ShowcaseComponentWhiteList::getUserId, userId)
                .count();
    }

}





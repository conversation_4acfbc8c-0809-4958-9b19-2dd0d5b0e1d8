package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.constants.Constants;
import outfox.ead.youxuan.entity.AppAccount;
import outfox.ead.youxuan.entity.KolPlatformContentRelation;
import outfox.ead.youxuan.web.kol.controller.vo.TaskOrderInitQueryVO;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【KolPlatformContentRelation(达人与平台内容类型绑定关系)】的数据库操作Mapper
 * @createDate 2022-02-17 18:31:07
 * @Entity outfox.ead.youxuan.entity.KolPlatformContentRelation
 */
public interface KolPlatformContentRelationMapper extends BaseMapper<KolPlatformContentRelation> {

    default List<KolPlatformContentRelation> getByUserIdAndContentPlatformId(Long AppAccountId, List<Long> platformContentIds) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(KolPlatformContentRelation::getAppAccountId, AppAccountId)
                .in(KolPlatformContentRelation::getPlatformContentId, platformContentIds)
                .list();
    }

    default List<KolPlatformContentRelation> getByTaskOrderInitQueryVOList(Collection<TaskOrderInitQueryVO> taskOrderInitQueryVOList) {
        return new LambdaQueryChainWrapper<>(this)
                .or(wrapper -> {
                    taskOrderInitQueryVOList.forEach(item -> {
                        wrapper.eq(KolPlatformContentRelation::getAppAccountId, item.getAppAccountId())
                                .eq(KolPlatformContentRelation::getPlatformContentId, item.getPlatformContentId()).or();
                    });
                })
                .list();
    }

    default KolPlatformContentRelation getByUserIdAndContentPlatformId(Long AppAccountId, Long platformContentId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(KolPlatformContentRelation::getAppAccountId, AppAccountId)
                .eq(KolPlatformContentRelation::getPlatformContentId, platformContentId)
                .one();
    }

    default List<KolPlatformContentRelation> getByAppAccountIdsAndConditions(List<Long> appAccountIds, Integer order,
                                                                             Long priceMin, Long priceMax) {
        return new LambdaQueryChainWrapper<>(this)
                .in(KolPlatformContentRelation::getAppAccountId, appAccountIds)
                .orderByDesc(null != order && Constants.DESC == order, KolPlatformContentRelation::getPrice)
                .orderByAsc(null != order && Constants.ASC == order, KolPlatformContentRelation::getPrice)
                .ge(null != priceMin, KolPlatformContentRelation::getPrice, priceMin)
                .le(null != priceMax, KolPlatformContentRelation::getPrice, priceMax)
                .list();
    }


    default List<KolPlatformContentRelation> listByAppAccounts(List<AppAccount> appAccount) {
        return new LambdaQueryChainWrapper<>(this)
                .in(KolPlatformContentRelation::getAppAccountId, appAccount.stream().map(AppAccount::getId).collect(Collectors.toList()))
                .list();
    }
}





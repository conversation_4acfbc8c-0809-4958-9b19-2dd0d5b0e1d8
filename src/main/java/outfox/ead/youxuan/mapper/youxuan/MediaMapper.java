package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import outfox.ead.youxuan.entity.Media;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static outfox.ead.youxuan.constants.ResourceStatusEnum.DELETED;
import static outfox.ead.youxuan.constants.ResourceStatusEnum.VALID;

/**
 * <AUTHOR>
 */
public interface MediaMapper extends BaseMapper<Media> {
    /**
     * 通过id列表查询有效的媒体
     *
     * @param ids id列表
     * @return 媒体列表
     */
    default List<Media> listValidByIds(Collection<Long> ids) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(Media::getStatus, VALID.getCode())
                .in(Media::getId, ids)
                .list();
    }

    /**
     * 通过名字查询未删除的媒体
     *
     * @param name 名字
     * @return 媒体列表
     */
    default List<Media> listNotDeletedByName(String name) {
        return new LambdaQueryChainWrapper<>(this)
                .like(Objects.nonNull(name), Media::getName, name)
                .ne(Media::getStatus, DELETED.getCode())
                .list();
    }

    /**
     * 通过媒体id分页查找
     *
     * @param mediaIds 媒体id列表
     * @param current  页码
     * @param size     行数
     * @return 媒体分页
     */
    default Page<Media> pageListInIds(Set<Long> mediaIds, Long current, Long size) {
        return new LambdaQueryChainWrapper<>(this)
                .in(Media::getId, mediaIds)
                .page(new Page<>(current, size));
    }

    List<Media> listByStyleIds(List<Long> styleIds);

    Integer getOsByStyleId(Long styleId);
}





package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import outfox.ead.youxuan.entity.AppAccount;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static outfox.ead.youxuan.constants.Constants.BIND;

/**
 * <AUTHOR>
 * @description 针对表【AppAccount(媒体账户)】的数据库操作Mapper
 * @createDate 2022-02-09 15:49:35
 * @Entity outfox.ead.youxuan.entity.AppAccount
 */
public interface AppAccountMapper extends BaseMapper<AppAccount> {
    List<AppAccount> listByConditions(Page<AppAccount> page, Long platformId, String nickName,
                                      List<Long> famousTeacherTagIds, List<Long> contentTagIds, Integer gender,
                                      Long fansNumMin, Long fansNumMax,
                                      Long priceMin, Long priceMax,
                                      Integer fansOrder, Integer priceOrder);


    default Page<AppAccount> pageByStatusAndUserId(Set<Integer> status, Long userId, Long current, Long size) {
        return new LambdaQueryChainWrapper<>(this)
                .in(CollectionUtils.isNotEmpty(status), AppAccount::getStatus, status)
                .eq(AppAccount::getUserId, userId)
                .orderByDesc(AppAccount::getCreateTime)
                .page(new Page<>(current, size));
    }

    default List<AppAccount> listByUserId(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AppAccount::getUserId, userId)
                .list();
    }

    default Long countValidByUserAndPlatform(Long userId, Long platformId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AppAccount::getUserId, userId)
                .eq(AppAccount::getPlatformId, platformId)
                .eq(AppAccount::getStatus, BIND)
                .count();
    }

    default AppAccount getBindByUserIdAndPlatformId(Long userId, Long platformId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AppAccount::getUserId, userId)
                .eq(AppAccount::getPlatformId, platformId)
                .eq(AppAccount::getStatus, BIND)
                .one();
    }

    default List<AppAccount> listByAppUserIdAndPlatformId(String appUserId, Long platformId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(Objects.nonNull(appUserId), AppAccount::getAppUserId, appUserId)
                .eq(Objects.nonNull(platformId), AppAccount::getPlatformId, platformId)
                .list();
    }

    default AppAccount getByAppUserIdAndPlatformIdAndUserId(String appUserId, Long platformId, Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AppAccount::getAppUserId, appUserId)
                .eq(AppAccount::getUserId, userId)
                .eq(AppAccount::getPlatformId, platformId)
                .one();
    }

    default int updateInServiceByAppUserIdAndUserId(Long userId, Long appAccountId, boolean isInService) {
        return new LambdaUpdateChainWrapper<>(this)
                .set(AppAccount::getInService, isInService)
                .eq(AppAccount::getUserId, userId)
                .eq(AppAccount::getId, appAccountId)
                .update() ? 1 : 0;
    }

    AppAccount getBindByUserIdAndPlatformName(Long userId, String platformName);

    List<AppAccount> getBindByUserIdListAndPlatformName(List<Long> userIdList, String platformName);

    AppAccount getBindByPlatformNameAndAppUserId(String platformName, String uid);

    default AppAccount getBindById(Long id) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AppAccount::getId, id)
                .eq(AppAccount::getStatus, BIND)
                .one();
    }

    default List<AppAccount> getBindByIds(List<Long> ids) {
        return new LambdaQueryChainWrapper<>(this)
                .in(AppAccount::getId, ids)
                .eq(AppAccount::getStatus, BIND)
                .list();
    }

    default List<AppAccount> getBindAndInServiceByIds(List<Long> ids) {
        return new LambdaQueryChainWrapper<>(this)
                .in(AppAccount::getId, ids)
                .eq(AppAccount::getStatus, BIND)
                .eq(AppAccount::getInService, true)
                .list();
    }

    default List<AppAccount> listBindByUserId(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AppAccount::getUserId, userId)
                .eq(AppAccount::getStatus, BIND)
                .list();
    }

    default AppAccount getBindByAppUserIdAndPlatformId(String appUserId, Long platformId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(Objects.nonNull(appUserId), AppAccount::getAppUserId, appUserId)
                .eq(Objects.nonNull(platformId), AppAccount::getPlatformId, platformId)
                .eq(AppAccount::getStatus, BIND)
                .one();
    }

    default List<AppAccount> getByUserId(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AppAccount::getUserId, userId)
                .list();
    }

    default Boolean updateNotifySchemaUpgrade2False(Long appAccountId) {
        return new LambdaUpdateChainWrapper<>(this)
                .eq(AppAccount::getId, appAccountId)
                .set(AppAccount::getNotifySchemaUpgrade, false)
                .update();
    }

    default Collection<AppAccount> getByKolOperatorUserId(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(AppAccount::getKolOperatorUserId, userId)
                .list();
    }
}





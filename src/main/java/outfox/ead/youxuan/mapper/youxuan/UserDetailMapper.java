package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.UserDetail;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【UserDetail】的数据库操作Mapper
 * @date 2022-01-28 14:10:32
 * @Entity outfox.ead.youxuan.entity.UserDetail
 */
public interface UserDetailMapper extends BaseMapper<UserDetail> {

    int NORMAL = 0;

    /**
     * 通过userId查找
     *
     * @param userId      用户id
     * @param currentRole
     * @return UserDetail
     */
    default UserDetail selectByUserId(Long userId, Role currentRole) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserDetail::getUserId, userId)
                .eq(UserDetail::getRoleId, currentRole.getId())
                .eq(UserDetail::getStatus, NORMAL)
                .one();
    }

    default Collection<UserDetail> listByUserIdsAndRoles(Collection<Long> userIds, Collection<Role> roles) {
        if (CollectionUtils.isEmpty(userIds) || CollectionUtils.isEmpty(roles)) {
            return Lists.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .in(UserDetail::getUserId, userIds)
                .in(UserDetail::getRoleId, roles.stream().map(Role::getId).collect(Collectors.toList()))
                .eq(UserDetail::getStatus, NORMAL)
                .list();
    }

    default List<UserDetail> listByUserIdAndSyncStatus(List<Long> kolUserIds, Boolean syncStatus, Role role) {
        if (CollectionUtils.isEmpty(kolUserIds)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .in(UserDetail::getUserId, kolUserIds)
                .eq(UserDetail::getRoleId, role.getId())
                .eq(UserDetail::getSyncShowcaseComponent, syncStatus)
                .list();
    }

    default List<UserDetail> listUserIdByNickName(String name) {
        return new LambdaQueryChainWrapper<>(this)
                .select(UserDetail::getUserId)
                .like(UserDetail::getNickname, name)
                .list();
    }
}





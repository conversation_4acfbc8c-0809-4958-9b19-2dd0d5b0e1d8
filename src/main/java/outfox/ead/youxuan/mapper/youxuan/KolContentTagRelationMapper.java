package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.KolContentTagRelation;
import outfox.ead.youxuan.web.kol.controller.dto.ContentTagUsageCountDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【KolContentTagRelation(达人与内容标签绑定关系)】的数据库操作Mapper
 * @createDate 2022-02-10 17:45:52
 * @Entity outfox.ead.youxuan.entity.KolContentTagRelation
 */
public interface KolContentTagRelationMapper extends BaseMapper<KolContentTagRelation> {

    List<ContentTagUsageCountDTO> getUsageCountByContentTagIds(List<Long> contentTagIdList);

    default Long getCountByCountTagId(Long contentTagId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(KolContentTagRelation::getContentTagId, contentTagId)
                .count();
    }

    default Integer delByAppAccountId(Long appAccountId) {
        LambdaQueryWrapper<KolContentTagRelation> lq = Wrappers.lambdaQuery();
        return delete(
                lq.eq(KolContentTagRelation::getAppAccountId, appAccountId)
        );
    }

    default List<KolContentTagRelation> getByAppAccountIds(List<Long> appAccountIds) {
        return new LambdaQueryChainWrapper<>(this)
                .in(KolContentTagRelation::getAppAccountId, appAccountIds)
                .list();
    }
}

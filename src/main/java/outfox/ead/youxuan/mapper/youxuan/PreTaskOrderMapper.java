package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import org.apache.commons.collections4.CollectionUtils;
import outfox.ead.youxuan.entity.PreTaskOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【PreTaskOrder(已选达人表)】的数据库操作Mapper
 * @createDate 2022-02-23 12:34:32
 * @Entity outfox.ead.youxuan.entity.PreTaskOrder
 */
public interface PreTaskOrderMapper extends BaseMapper<PreTaskOrder> {
    default PreTaskOrder getOne(Long appAccountId, Long platformContentId, Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(PreTaskOrder::getAppAccountId, appAccountId)
                .eq(PreTaskOrder::getPlatformContentId, platformContentId)
                .eq(PreTaskOrder::getCreator, userId)
                .eq(PreTaskOrder::isDeleted, false)
                .one();
    }

    default List<PreTaskOrder> getByCreator(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(PreTaskOrder::getCreator, userId)
                .eq(PreTaskOrder::isDeleted, false)
                .list();
    }

    default Long getCountByCreator(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(PreTaskOrder::getCreator, userId)
                .eq(PreTaskOrder::isDeleted, false)
                .count();
    }

    default boolean deleteById(Long id, Long userId) {
        return new LambdaUpdateChainWrapper<>(this)
                .eq(PreTaskOrder::getId, id)
                .eq(PreTaskOrder::getCreator, userId)
                .set(PreTaskOrder::isDeleted, true)
                .update();
    }

    default List<PreTaskOrder> getListByPlatformContentIdsAndUserId(List<Long> platformContentIds, Long UserId) {
        return new LambdaQueryChainWrapper<>(this)
                .in(CollectionUtils.isNotEmpty(platformContentIds), PreTaskOrder::getPlatformContentId, platformContentIds)
                .eq(PreTaskOrder::isDeleted, false)
                .eq(PreTaskOrder::getCreator, UserId)
                .list();
    }

    default List<PreTaskOrder> getValidByIds(List<Long> ids) {
        return new LambdaQueryChainWrapper<>(this)
                .in(CollectionUtils.isNotEmpty(ids), PreTaskOrder::getId, ids)
                .eq(PreTaskOrder::isDeleted, false)
                .list();
    }

    default boolean deleteByIds(List<Long> ids) {
        return new LambdaUpdateChainWrapper<>(this)
                .in(PreTaskOrder::getId, ids)
                .set(PreTaskOrder::isDeleted, true)
                .update();
    }

    default boolean updateCommentById(Long id, String comment, Long showcaseComponentId) {
        return new LambdaUpdateChainWrapper<>(this)
                .eq(PreTaskOrder::getId, id)
                .set(PreTaskOrder::getComment, comment)
                .set(PreTaskOrder::getShowcaseComponentId, showcaseComponentId)
                .update();
    }

}





package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.Customer;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CustomerMapper extends BaseMapper<Customer> {
    /**
     * 获取客户id name 列表
     *
     * @return 客户列表
     */
    default List<Customer> listIdToName() {
        return new LambdaQueryChainWrapper<>(this)
                .select(Customer::getId, Customer::getName)
                .list();
    }
}





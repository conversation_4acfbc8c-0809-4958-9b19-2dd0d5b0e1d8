package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import outfox.ead.youxuan.entity.UserRelation;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【UserRelation(用户单向绑定表)】的数据库操作Mapper
 * @createDate 2022-02-09 19:44:19
 * @Entity outfox.ead.youxuan.entity.UserRelation
 */
public interface UserRelationMapper extends BaseMapper<UserRelation> {
    int NORMAL = 0;
    int UNBIND = 1;

    /**
     * 查询账户是否正在绑定状态中
     *
     * @param userId 用户id
     * @return true-是 false-否
     */
    default boolean existsValidByKolUserId(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserRelation::getKolUserId, userId)
                .eq(UserRelation::getStatus, NORMAL)
                .exists();
    }

    default Page<UserRelation> pageByKolUserId(Long kolUserId, Long current, Long size) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserRelation::getKolUserId, kolUserId)
                .page(new Page<>(current, size));
    }

    default Page<UserRelation> pageBySponsorUserId(Long sponsorUserId, Long current, Long size) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserRelation::getSponsorUserId, sponsorUserId)
                .page(new Page<>(current, size));
    }

    /**
     * 通过userId和boundUserId查找relation
     * <br/>
     * @param kolUserId      用户id
     * @param sponsorUserId 被绑定用户id
     * @return relation
     */
    default UserRelation getByUserId(Long kolUserId, Long sponsorUserId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserRelation::getKolUserId, kolUserId)
                .eq(UserRelation::getSponsorUserId, sponsorUserId)
                .one();
    }

    default Boolean existsValid(Long sponsorUserId, Long kolUserId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserRelation::getSponsorUserId, sponsorUserId)
                .eq(UserRelation::getKolUserId, kolUserId)
                .eq(UserRelation::getStatus, NORMAL)
                .exists();
    }

    default List<UserRelation> listValidByKolUserId(Long kolUserId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserRelation::getKolUserId, kolUserId)
                .eq(UserRelation::getStatus,NORMAL)
                .list();
    }

    default Collection<UserRelation> listValidBySponsorUserId(Long sponsorId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserRelation::getSponsorUserId, sponsorId)
                .eq(UserRelation::getStatus,NORMAL)
                .list();
    }

}





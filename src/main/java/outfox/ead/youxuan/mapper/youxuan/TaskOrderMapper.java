package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Select;
import outfox.ead.youxuan.entity.TaskOrder;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.ContentMarketingConstants.*;


/**
 * <AUTHOR>
 * @description 针对表【TaskOrder(任务订单表)】的数据库操作Mapper
 * @createDate 2022-02-21 10:57:49
 * @Entity outfox.ead.youxuan.entity.TaskOrder
 */
public interface TaskOrderMapper extends BaseMapper<TaskOrder> {

    default Long count(Long appAccountId, List<Integer> status, int type) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getAppAccountId, appAccountId)
                .in(CollectionUtils.isNotEmpty(status), TaskOrder::getStatus, status)
                .eq(TaskOrder::getType, type)
                .count();
    }

    /**
     * 按橱窗组件ID计数
     * type/status不传代表 查所有
     *
     * @param showcaseComponentId -
     * @param type                任务类型
     * @param status              状态
     * @return 橱窗组件ID计数
     */
    default Long countShowcaseComponentByTypeAndExcludeStatus(@NotNull Long showcaseComponentId, Integer type, Integer status) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getShowcaseComponentId, showcaseComponentId)
                .eq(Objects.nonNull(type), TaskOrder::getType, type)
                .ne(Objects.nonNull(status), TaskOrder::getStatus, status)
                .count();
    }

    <T> Page<TaskOrder> pagePostTask(Collection<Long> showcaseComponentIdList,
                                     Collection<Long> ids,
                                     Collection<String> orderIds,
                                     String name,
                                     Integer status,
                                     Integer billingType,
                                     Long userId,
                                     Integer type,
                                     Integer order,
                                     Page<T> tPage);

    /**
     * 获取可投稿的投稿任务列表
     * <br/>
     * 这里的可投稿指的是: 参与过的任务 和 未参与过但是还可以被领取的任务
     *
     * @param participatedPostTaskIds 参与过的任务ID
     * @param onlyParticipated
     */
    <T> Page<TaskOrder> pagePostTaskCanPost(Long id,
                                            String orderId,
                                            Collection<Long> showcaseComponentIdList,
                                            Set<Long> participatedPostTaskIds,
                                            Integer billingType,
                                            Integer order,
                                            Boolean onlyParticipated,
                                            Page<T> tPage);

    /**
     * 获取已参与的订单 投稿任务id
     *
     * @param appAccountId
     * @param userId
     * @return
     */
    default Set<Long> getParticipatedPostOrder(Long appAccountId, Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getAppAccountId, appAccountId)
                .eq(TaskOrder::getCreator, userId)
                .select(TaskOrder::getParentOrderId)
                .list()
                .stream().map(TaskOrder::getParentOrderId).collect(Collectors.toSet());
    }

    /**
     * 查询达人已参与的任务
     *
     * @param appAccountId
     * @return
     */
    default List<TaskOrder> listPickedUp(Long appAccountId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getAppAccountId, appAccountId)
                .eq(TaskOrder::getType, TASK_ORDER_TYPE_SUB_POST)
                .list();
    }


    default TaskOrder getByOrderId(String orderId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getOrderId, orderId)
                .one();
    }

    default List<TaskOrder> listByParentIds(List<Long> postTaskIdList) {
        if (CollectionUtils.isEmpty(postTaskIdList)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .in(TaskOrder::getParentOrderId, postTaskIdList)
                .list();
    }

    default Long countTaskOrderExcludeId(String name, Long id, Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getName, name)
                .ne(Objects.nonNull(id), TaskOrder::getId, id)
                .eq(TaskOrder::getType, TASK_ORDER_TYPE_POST)
                .eq(TaskOrder::getCreator, userId)
                .count();
    }

    default Long countByParentIdAndType(Long parentId, Integer type) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getParentOrderId, parentId)
                .eq(TaskOrder::getType, type)
                .count();
    }

    default Boolean updateStatusById(Long id, Integer status) {
        return new LambdaUpdateChainWrapper<>(this)
                .eq(TaskOrder::getId, id)
                .set(TaskOrder::getStatus, status)
                .update();
    }

    /**
     * @param type
     * @param status
     * @return
     */
    default List<TaskOrder> listByTypeAndStatus(Integer type, List<Integer> status) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getType, type)
                .in(TaskOrder::getStatus, status)
                .list();
    }

    /**
     * 行锁
     *
     * @param id 主键
     */
    @Select("select ID from TaskOrder where id=#{id} for update")
    Long lock(Long id);

    /**
     * 查询可以投稿的投稿任务
     *
     * @param name   投稿任务名称
     * @param userId
     * @return list
     */
    default List<TaskOrder> listPostTask(String name, Long userId) {
        return listPostTask(name, Collections.singletonList(userId));
    }

    default List<TaskOrder> listPostTask(String name, List<Long> userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getType, TASK_ORDER_TYPE_POST)
                .eq(TaskOrder::getStatus, TASK_ORDER_IN_PROGRESS)
                .in(Objects.nonNull(userId), TaskOrder::getCreator, userId)
                .like(Objects.nonNull(name), TaskOrder::getName, name)
                .and(taskOrderLambdaQueryWrapper -> taskOrderLambdaQueryWrapper
                        .eq(TaskOrder::getPostLimit, 0)
                        .or()
                        .gt(TaskOrder::getPostRemain, 0))
                .list();
    }

    default List<TaskOrder> listByShowcaseComponentId(Long showcaseComponentId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getShowcaseComponentId, showcaseComponentId)
                .list();
    }

    default TaskOrder getSubOrder(Long id, Long appAccountId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(TaskOrder::getParentOrderId, id)
                .eq(TaskOrder::getAppAccountId, appAccountId)
                .eq(TaskOrder::getType, TASK_ORDER_TYPE_SUB_POST)
                .one();
    }

    /**
     * 更新对应记录的剩余投稿数，可以更新成null
     */
    default Boolean updatePostRemainAllowNull(Long id, Long postRemain) {
        return new LambdaUpdateChainWrapper<>(this)
                .eq(TaskOrder::getId, id)
                .set(TaskOrder::getPostRemain, postRemain)
                .update();
    }

}






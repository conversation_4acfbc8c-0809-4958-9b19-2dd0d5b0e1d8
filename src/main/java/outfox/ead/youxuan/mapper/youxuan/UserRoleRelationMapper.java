package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.UserRoleRelation;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【UserRole】的数据库操作Mapper
 * @date 2022-01-27 18:09:38
 * @Entity outfox.ead.youxuan.entity.UserRole
 */
public interface UserRoleRelationMapper extends BaseMapper<UserRoleRelation> {
    int NORMAL = 0;
    int PAUSE = 1;
    int DELETED = 2;

    /**
     * 逻辑删除
     *
     * @param userId -
     */
    default void logicDeleteByUserId(Long userId) {
        new LambdaUpdateChainWrapper<>(this)
                .set(UserRoleRelation::getStatus, DELETED)
                .eq(UserRoleRelation::getUserId, userId)
                .update();
    }

    default List<UserRoleRelation> listByUserIdsAndRoles(Collection<Long> userIds, Collection<Role> roles) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserRoleRelation::getStatus, NORMAL)
                .in(UserRoleRelation::getUserId, userIds)
                .in(UserRoleRelation::getRoleId, roles.stream().map(Role::getId).collect(Collectors.toSet()))
                .list();
    }

    default UserRoleRelation getByUserId(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserRoleRelation::getStatus, NORMAL)
                .in(UserRoleRelation::getUserId, userId)
                .one();
    }

    default List<UserRoleRelation> listByUserid(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(UserRoleRelation::getUserId, userId)
                .eq(UserRoleRelation::getStatus, NORMAL)
                .list();
    }
}





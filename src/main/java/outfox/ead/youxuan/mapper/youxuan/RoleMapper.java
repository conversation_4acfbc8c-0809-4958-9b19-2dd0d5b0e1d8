package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.Role;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【Role】的数据库操作Mapper
 * @date 2022-01-27 11:33:40
 * @Entity outfox.ead.youxuan.entity.Role
 */
public interface RoleMapper extends BaseMapper<Role> {

    Collection<Role> listByUserId(Long userId);

    default List<Role> listByRoleKeys(List<String> roleKeys) {
        return new LambdaQueryChainWrapper<>(this)
                .in(Role::getRoleKey, roleKeys)
                .list();
    }

    default Role getByRoleKey(String roleKey) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(Role::get<PERSON><PERSON><PERSON><PERSON>, role<PERSON><PERSON>)
                .one();
    }
}





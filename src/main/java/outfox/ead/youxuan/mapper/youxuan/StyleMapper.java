package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.ObjectUtils;
import org.assertj.core.util.Lists;
import outfox.ead.youxuan.entity.Style;
import outfox.ead.youxuan.web.ad.controller.vo.StyleCriteriaQueryVO;
import outfox.ead.youxuan.web.ad.controller.vo.StyleListVO;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static outfox.ead.youxuan.constants.ResourceStatusEnum.DELETED;
import static outfox.ead.youxuan.constants.ResourceStatusEnum.VALID;

/**
 * <AUTHOR>
 */
public interface StyleMapper extends BaseMapper<Style> {
    /**
     * 通过组id查找样式
     *
     * @param adGroupId 组id
     * @return 样式列表
     */
    List<Style> listByAdGroupId(Long adGroupId);

    /**
     * 通过推广组列表查询样式
     *
     * @param adGroupIds 推广组id列表
     * @return 样式列表
     */
    List<Style> listByAdGroupIds(Collection<Long> adGroupIds);

    /**
     * 通过广告位id查询样式
     *
     * @param adPositionId 广告位id
     * @return 样式列表
     */
    default List<Style> listNotDeletedByAdPositionId(Long adPositionId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(Style::getAdPositionId, adPositionId)
                .ne(Style::getStatus, DELETED.getCode())
                .list();
    }

    /**
     * 通过广告位id列表查询有效的样式
     *
     * @param adPositionIds 广告位id列表
     * @return 有效的样式
     */
    default List<Style> listValidByAdPositionIds(Collection<Long> adPositionIds) {
        return new LambdaQueryChainWrapper<>(this)
                .in(Style::getAdPositionId, adPositionIds)
                .eq(Style::getStatus, VALID.getCode())
                .list();
    }

    /**
     * 通过样式id列表查询未删除样式
     *
     * @param styleIds 样式id列表
     * @return 样式列表
     */
    default List<Style> listNotDeletedByIds(Collection<Long> styleIds) {
        if (styleIds.isEmpty()) {
            return Lists.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .in(Style::getId, styleIds)
                .ne(Style::getStatus, DELETED.getCode())
                .list();
    }

    /**
     * 通过样式id列表以及广告位id列表查询未删除样式
     *
     * @param ids           样式id列表
     * @param adPositionIds 广告位id列表
     * @return 样式列表
     */
    default List<Style> listNotDeletedByIdsAndAdPositionIds(Set<Long> ids, Collection<Long> adPositionIds) {
        if (ObjectUtils.isEmpty(ids) || ObjectUtils.isEmpty(adPositionIds)) {
            return Lists.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .in(Style::getId, ids)
                .in(Style::getAdPositionId, adPositionIds)
                .list();
    }

    default List<Style> list(String name, Set<Long> adPositionIds, List<Integer> status) {
        return new LambdaQueryChainWrapper<>(this)
                .in(Style::getStatus, status)
                .and(wrapper ->
                        wrapper
                                .like(Objects.nonNull(name), Style::getName, name)
                                .or()
                                .in(ObjectUtils.isNotEmpty(adPositionIds), Style::getAdPositionId, adPositionIds))
                .list();
    }

    Page<StyleListVO> pageStyle(StyleCriteriaQueryVO styleVO, Page<StyleListVO> styleListVOPage);
}





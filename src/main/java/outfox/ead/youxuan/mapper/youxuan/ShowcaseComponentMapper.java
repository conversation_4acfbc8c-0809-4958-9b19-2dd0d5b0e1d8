package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.ShowcaseComponent;
import outfox.ead.youxuan.web.kol.controller.vo.AuditPageQuery;
import outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentAuditListVO;
import outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentCriteriaQueryVO;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static outfox.ead.youxuan.constants.ContentMarketingConstants.SHOWCASE_COMPONENT_DELETED;

/**
* <AUTHOR>
* @description 针对表【ShowcaseComponent(橱窗组件)】的数据库操作Mapper
* @createDate 2022-05-27 17:44:43
* @Entity outfox.ead.youxuan.entity.ShowcaseComponent
*/
public interface ShowcaseComponentMapper extends BaseMapper<ShowcaseComponent> {

    default List<ShowcaseComponent> listByName(String name, Long userId, Role currentRole) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ShowcaseComponent::getName, name)
                .eq(ShowcaseComponent::getCreator, userId)
                .list();
    }


    Page<ShowcaseComponentAuditListVO> pageAudit(Page<ShowcaseComponentAuditListVO> page, AuditPageQuery auditPageQuery);

    default Boolean updateStatus(Long id, Integer status) {
        return new LambdaUpdateChainWrapper<>(this)
                .eq(ShowcaseComponent::getId, id)
                .set(ShowcaseComponent::getStatus, status)
                .update();
    }

    default Page<ShowcaseComponent> pageByCriteria(ShowcaseComponentCriteriaQueryVO query, Long appAccountId, Long userId, Role currentRole) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ShowcaseComponent::getCreator, userId)
                .eq(Objects.nonNull(query.getId()), ShowcaseComponent::getId, query.getId())
                .eq(Objects.nonNull(query.getComponentId()), ShowcaseComponent::getComponentId, query.getComponentId())
                .eq(Objects.nonNull(query.getSwitchType()), ShowcaseComponent::getSwitchType, query.getSwitchType())
                .eq(Objects.nonNull(query.getStatus()), ShowcaseComponent::getStatus, query.getStatus())
                .eq(Objects.nonNull(appAccountId), ShowcaseComponent::getAppAccountId, appAccountId)
                .eq(Objects.nonNull(query.getDeleted()), ShowcaseComponent::getDeleted, query.getDeleted())
                .eq(Objects.nonNull(query.getAuditStatus()), ShowcaseComponent::getAuditStatus, query.getAuditStatus())
                .eq(ShowcaseComponent::getRoleId, currentRole.getId())
                .like(Objects.nonNull(query.getName()), ShowcaseComponent::getName, query.getName())
                .isNull(Objects.nonNull(query.getIsSyncFromSponsor()) && BooleanUtils.isFalse(query.getIsSyncFromSponsor()), ShowcaseComponent::getSourceId)
                .isNotNull(Objects.nonNull(query.getIsSyncFromSponsor()) && query.getIsSyncFromSponsor(), ShowcaseComponent::getSourceId)
                .orderByDesc(ShowcaseComponent::getCreateTime)
                .page(new Page<>(query.getCurrent(), query.getSize()));
    }

    default Page<ShowcaseComponent> pagePassed(Long id,
                                               String componentId,
                                               String name,
                                               String promotionTitle,
                                               Integer switchType,
                                               Boolean deleted,
                                               Long userId,
                                               Role currentRole,
                                               Integer status,
                                               Long current,
                                               Long size) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(Objects.nonNull(id), ShowcaseComponent::getId, id)
                .eq(Objects.nonNull(componentId), ShowcaseComponent::getComponentId, componentId)
                .eq(Objects.nonNull(switchType), ShowcaseComponent::getSwitchType, switchType)
                .isNotNull(ShowcaseComponent::getAuditedContent)
                .like(Objects.nonNull(name), ShowcaseComponent::getName, name)
                .like(Objects.nonNull(promotionTitle), ShowcaseComponent::getAuditedPromoteTitle, promotionTitle)
                .eq(Objects.nonNull(status), ShowcaseComponent::getStatus, status)
                .eq(ShowcaseComponent::getDeleted, deleted)
                .eq(ShowcaseComponent::getCreator, userId)
                .eq(ShowcaseComponent::getRoleId, currentRole.getId())
                .orderByDesc(ShowcaseComponent::getCreateTime)
                .page(new Page<>(current, size));
    }


    default Boolean logicDelete(Long id) {
        return new LambdaUpdateChainWrapper<>(this)
                .eq(ShowcaseComponent::getId, id)
                .set(ShowcaseComponent::getDeleted, SHOWCASE_COMPONENT_DELETED)
                .update();
    }

    default List<ShowcaseComponent> listByPromoteTitleLikely(String auditedPromoteTitle) {
        if (Objects.isNull(auditedPromoteTitle)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .like(ShowcaseComponent::getAuditedPromoteTitle, auditedPromoteTitle)
                .list();
    }

    default List<ShowcaseComponent> listBySourceId(List<Long> sourceId) {
        if (CollectionUtils.isEmpty(sourceId)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .in(ShowcaseComponent::getSourceId, sourceId)
                .list();
    }

    default List<ShowcaseComponent> listByPromotionType(Integer promotionType) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ShowcaseComponent::getPromotionType, promotionType)
                .list();
    }

    default List<ShowcaseComponent> list(Boolean isDeleted, Long creatorUserId, Role currentRole, Boolean existAuditedContent) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ShowcaseComponent::getDeleted, isDeleted)
                .eq(ShowcaseComponent::getCreator, creatorUserId)
                .isNotNull(existAuditedContent, ShowcaseComponent::getAuditedContent)
                .eq(ShowcaseComponent::getRoleId, currentRole.getId())
                .isNull(BooleanUtils.isFalse(existAuditedContent), ShowcaseComponent::getAuditedContent)
                .list();
    }

    default ShowcaseComponent getByComponentId(String componentId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ShowcaseComponent::getComponentId, componentId)
                .one();
    }

    default List<ShowcaseComponent> listByIds(List<String> ids) {
        return new LambdaQueryChainWrapper<>(this)
                .in(ShowcaseComponent::getId, ids)
                .list();
    }
}





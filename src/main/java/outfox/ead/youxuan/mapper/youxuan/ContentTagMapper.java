package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.jsonwebtoken.lang.Collections;
import org.assertj.core.util.Lists;
import outfox.ead.youxuan.entity.ContentTag;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【ContentTag(达人内容标签)】的数据库操作Mapper
 * @createDate 2022-02-09 18:03:39
 * @Entity outfox.ead.youxuan.entity.ContentTag
 */
public interface ContentTagMapper extends BaseMapper<ContentTag> {

    default Page<ContentTag> pageListByStatusAndType(Integer status, Integer type, Long current, Long size) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(Objects.nonNull(status), ContentTag::getStatus, status)
                .eq(ContentTag::getType, type)
                .page(new Page<>(current, size));
    }

    default List<ContentTag> selectAllByStatus(Integer status) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(Objects.nonNull(status), ContentTag::getStatus, status)
                .list();
    }

    default List<ContentTag> selectByTypeAndStatus(Integer type, Integer status) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ContentTag::getType, type)
                .eq(ContentTag::getStatus, status)
                .list();
    }

    default Long countByName(String name) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ContentTag::getName, name)
                .count();
    }

    default List<ContentTag> selectByIds(List<Long> ids) {
        if (Collections.isEmpty(ids)) {
            return Lists.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .in(ContentTag::getId, ids)
                .list();
    }
}





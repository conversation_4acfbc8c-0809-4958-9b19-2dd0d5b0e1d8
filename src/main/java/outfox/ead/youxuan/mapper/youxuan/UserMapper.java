package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.constants.UserStatusEnum;
import outfox.ead.youxuan.entity.User;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserMapper extends BaseMapper<User> {
    /**
     * 施加行锁
     *
     * @param userIds
     */
    List<Long> lockUser(List<Long> userIds);

    default Boolean exsitsById(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(User::getId, userId)
                .eq(User::getStatus, UserStatusEnum.NORMAL.getCode())
                .exists();
    }

    List<User> listByRoleKey(String roleKey);
}





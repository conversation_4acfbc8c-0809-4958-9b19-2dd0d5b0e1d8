package outfox.ead.youxuan.mapper.youxuan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.ConvModule;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ConvModule(主页转化工具)】的数据库操作Mapper
 * @createDate 2022-02-08 17:36:46
 * @Entity outfox.ead.youxuan.entity.ConvModule
 */
public interface ConvModuleMapper extends BaseMapper<ConvModule> {

    default ConvModule getByTypeAppAccountIdAndCreator(Integer type, Long appAccountId, Long creatorUserId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ConvModule::getType, type)
                .eq(ConvModule::getAppAccountId, appAccountId)
                .eq(ConvModule::getCreator, creatorUserId)
                .one();
    }


    default List<ConvModule> getByAppAccountIdAndUserId(Long appAccountId, Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ConvModule::getAppAccountId, appAccountId)
                .eq(ConvModule::getCreator, userId)
                .list();

    }

}





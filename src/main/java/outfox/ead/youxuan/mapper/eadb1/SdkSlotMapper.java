package outfox.ead.youxuan.mapper.eadb1;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.SdkSlot;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface SdkSlotMapper extends BaseMapper<SdkSlot> {
    /**
     * 通过广告位id反查slotIds
     *
     * @param adPositionIds 广告位id列表
     * @return slotIds
     */
    List<SdkSlot> listByAdPositionIds(Collection<Long> adPositionIds);

    /**
     * 通过slotUdid查询相关数据
     *
     * @param slotUdids slotUdid列表
     * @return ignore
     */
    default Collection<? extends SdkSlot> listBySlotUdid(Set<String> slotUdids) {
        return new LambdaQueryChainWrapper<>(this)
                .select(SdkSlot::getSdkSlotUdid, SdkSlot::getBidFloorYex)
                .eq(SdkSlot::getSdkStatus, 0)
                .in(SdkSlot::getSdkSlotUdid, slotUdids)
                .list();
    }
}





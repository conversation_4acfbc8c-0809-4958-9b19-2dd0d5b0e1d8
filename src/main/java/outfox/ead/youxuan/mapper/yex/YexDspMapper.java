package outfox.ead.youxuan.mapper.yex;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import outfox.ead.youxuan.entity.YexDsp;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface YexDspMapper extends BaseMapper<YexDsp> {

    /**
     * 查询dsp列表
     *
     * @param multipleMainImage
     * @return dsp列表
     */
    default List<YexDsp> listId2DspName(Boolean multipleMainImage) {
        return new LambdaQueryChainWrapper<>(this)
                .select(YexDsp::getDspId, YexDsp::getDspName)
                .eq(YexDsp::getStatus, 1)
                .eq(YexDsp::getDspAdType, 2)
                .eq(multipleMainImage, YexDsp::getMultipleMainImage, multipleMainImage)
                .list();
    }

    /**
     * 查询有效
     *
     * @param dsps dspId
     * @return
     */
    default long countValidById(String dsps) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(YexDsp::getDspId, dsps)
                .eq(YexDsp::getStatus, 1)
                .count();
    }
}





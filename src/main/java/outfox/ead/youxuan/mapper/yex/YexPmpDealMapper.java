package outfox.ead.youxuan.mapper.yex;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import outfox.ead.youxuan.entity.YexPmpDeal;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface YexPmpDealMapper extends BaseMapper<YexPmpDeal> {
    int DELETED = 0;
    int VALID = 1;

    /**
     * 通过订单id查询有效的 PmpDeal
     *
     * @param dealId 订单id
     * @return YexPmpDeal
     */
    default List<YexPmpDeal> listValidBrandAdByDealId(String dealId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(YexPmpDeal::getDealId, dealId)
                .eq(YexPmpDeal::getStatus, VALID)
                .list();
    }

    /**
     * 通过订单名称删除订单
     *
     * @param dealName 订单名称
     */
    default void deleteByDealName(String dealName) {
        new LambdaUpdateChainWrapper<>(this)
                .eq(YexPmpDeal::getDealName, dealName)
                .set(YexPmpDeal::getStatus, DELETED)
                .update();
    }
}





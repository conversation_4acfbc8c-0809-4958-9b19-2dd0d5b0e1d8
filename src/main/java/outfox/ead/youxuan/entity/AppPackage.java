package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 应用及其包名信息
 */
@Data
@TableName(value = "AppPackage")
public class AppPackage implements Serializable {
    /**
     * 应用Id
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    /**
     * 应用名称
     */
    @TableField(value = "NAME")
    private String name;

    /**
     * 应用在android系统中的包名
     */
    @TableField(value = "ANDROID_PACKAGE_NAME")
    private String androidPkgName;

    /**
     * 应用在ios系统中的包名
     */
    @TableField(value = "IOS_PACKAGE_NAME")
    private String iosPkgName;

    /**
     * 应用信息有效性，0-无效，1-有效
     */
    @TableField(value = "STATUS")
    private Integer status;
}

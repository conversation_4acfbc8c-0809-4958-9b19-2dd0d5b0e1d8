package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import outfox.ead.youxuan.core.handler.mybatis.LongSetTypeHandler;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

@TableName(value = "UnionFrequencyRule", autoResultMap=true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class UnionFrequencyRule implements Serializable {

    /**
     * 联合频控主键，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 联合频控名称
     */
    private String name;

    /**
     * 联合频控方式 0-不频控 1-按天频控 2-按周频控 3-按投放周期频控
     */
    private Integer frequencyType;

    /**
     * 联合频控次数
     */
    private Integer frequencyLimit;

    /**
     * 联合频控状态 1-生效 2-失效 3-已删除
     */
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 失效时间
     */
    @TableField(value = "END_TIME")
    private LocalDateTime endTime;

    /**
     * 更新人
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    /**
     * 最近更新时间
     */
    @TableField(value = "LAST_MOD_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;

    /**
     * 关联的广告计划ids
     */
    @TableField(value = "AD_PLAN_ID_LIST", typeHandler = LongSetTypeHandler.class)
    private Set<Long> adPlanIdList;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}

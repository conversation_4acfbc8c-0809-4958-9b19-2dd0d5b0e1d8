package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @TableName UserDetail
 */
@TableName(value = "UserDetail")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class UserDetail implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 账户id
     */
    private Long userId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 联系人姓名
     */
    private String name;

    /**
     * 联系人电话
     */
    private String phone;

    /**
     * 联系人邮箱
     */
    private String email;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 行业信息
     */
    private Integer industry;

    /**
     * 信息完善步骤
     */
    private Integer stage;

    /**
     * 指派任务权限
     */
    private Boolean taskPermission;

    /**
     * 投稿任务权限
     */
    private Boolean postTaskPermission;

    /**
     * 是否需要同步已绑定广告主的橱窗组件
     */
    private Boolean syncShowcaseComponent;

    /**
     * 广告主和机构创作者是否资质认证通过 0-否 1-是
     */
    private Boolean verified;

    /**
     * 状态 0-正常，1-暂停，2-删除
     */
    private Integer status;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;


    private LocalDateTime createTime;


    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

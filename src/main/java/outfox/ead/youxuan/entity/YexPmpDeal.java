package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @TableName yex_pmp_deal
 */
@TableName(value = "yex_pmp_deal")
@Data
public class YexPmpDeal implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String dealId;

    private String dealName;

    private Object dealType;

    /**
     * slot UDID
     */
    private String slotId;

    /**
     * DSP ID
     */
    private String dsps;

    /**
     * 底价  SdkSlot:BID_FLOOR_YEX
     */
    private Long price;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    /**
     * 打给效果广告DSP的流量比例系数，均为0
     */
    private Integer ratio;

    /**
     * 1是有效，0是无效，默认为1有效
     */
    private Integer status;

    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

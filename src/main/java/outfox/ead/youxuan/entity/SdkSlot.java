package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @TableName SdkSlot
 */
@TableName(value = "SdkSlot")
@Data
public class SdkSlot implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long sdkSlotId;

    /**
     * 根据对应应用包名和自身ID的MD5码
     */
    private String sdkSlotUdid;


    private Long sdkAppId;


    private String sdkSlotName;


    private String sdkSlotDescription;

    /**
     * 0：文字广告
     * 1：图片广告
     * 2：图文广告
     * 若SDKTYPE为nativeAd则该字段无实际意义 /////// 该字段未找到在现有系统的使用情况，旧开发者逻辑中给的、每个新建的广告位默认0，这里直接设默认为0. 2018-11-27 王默
     */
    private Integer type;

    /**
     * 0: BannerAd
     * 1: 插屏广告
     * 2: 原生广告
     * 3: 视频广告
     */
    private Integer sdkType;

    /**
     * 状态:
     * 0为有效
     * -1为未提交
     * 0为有效
     * 1为待审核
     * 2为审核不通过
     * <p>
     * 复用EADSTATUS设计：https://dev.corp.youdao.com/outfoxwiki/EADDataStatus
     */
    private Integer sdkStatus;

    /**
     * 0：IOS
     * 1：安卓
     * 2：WP
     */
    private Integer sdkOsType;


    private String sdkImageRules;


    private String sdkTextRules;


    private Integer sdkIconHeight;


    private Integer sdkIconWidth;


    private Integer sdkMainimageHeight;


    private Integer sdkMainimageWidth;


    private Integer sdkTitleLength;


    private Integer sdkSummaryLength;


    private Integer sdkDescriptionLength;


    private Long createTime;


    private Long lastModTime;


    private String sdkResultImage;


    private String sdkPackage;

    /**
     * 0-有效 1-暂停 2-删除 update at 2018-11-27 by 王默
     */
    private Integer sdkOpStatus;


    private String sdkSlotPosition;


    private Integer sdkSlotRepeat;


    private String sdkSlotDesc;

    /**
     * this position will show brand AD prior,it is the magic nubmer
     */
    private String brandAdSeq;

    /**
     * SDK_SLOT_POSITION's ad schema ids
     */
    private String schemaIds;

    /**
     * Type of showConfirmDialog, 0:do not show dialogi, 1(default):show dialog when non-wif , 2: show dialog in any network state.
     */
    private Integer showConfirmDialogType;

    /**
     * filter size of the repeat advariant, -2: filter all, -1/NULL: not set.
     */
    private Integer filterSize;

    /**
     * reqeust AD from:bourse(x%)/zhixuan(0 or (1-x)%)
     */
    private Integer adProviderRatio;

    /**
     * 激励视频广告位的回调地址
     */
    private String sdkSlotCallbackUrl;

    /**
     * 激励视频广告回调加密秘钥
     */
    private String callbackUrlSecretKey;

    /**
     * 视频广告是否在媒体端预加载
     */
    private Integer videoPrefetch;

    /**
     * 广告位样式:0-unknown(对应新开发者系统的自定义);1-Banner;2-插屏;3-开屏;4-信息流;5-激励视频;6-文字链;7-焦点图 | 用于yex和新版开发者系统
     */
    private Integer sdkSlotStyle;

    /**
     * 审核要求，运营手工录入
     */
    private String requirements;

    /**
     * 是否支持deeplink,-1-默认值，表示未设置;1-支持;2-不支持
     */
    private Integer isDeeplink;

    /**
     * 媒体广告位的底价，单位分，给智选的广告主用
     */
    private Long bidFloor;

    /**
     * tesst
     */
    private Long bidFloorYex;

    /**
     * 媒体广告位接入方式:-1-默认值，表示未设置;1-sdk接入,2-API接入
     */
    private Integer entryMethod;

    /**
     * track from client, -1-默认值，表示未设置;1-支持;2-不支持
     */
    private Integer tfc;

    /**
     * true: 从此效果广告位对应的所有品牌广告位上提名广告。
     */
    private Byte isNominateFromAllBrandPosition;

    /**
     * ture: 表识此广告位只提名品牌广告，如果没有品牌广告返回，不走提名效果广告流程。 false: 表识此广告位如果提名品牌广告，在没有品牌广告返回时，走提名效果广告流程。
     */
    private Byte isOnlyNominateBrand;

    /**
     * 标识该广告位轮播次数和支持的最低版本，json格式表示，例如[{"num":6,"miniVersion":"7.7.7"}]（huqj 2018.7.5）
     */
    private String carouselNum;

    /**
     * 该广告位对应的品牌广告位和样式,json数组格式（huqj 2018.7.6）
     */
    private String brandAdSlotMap;

    /**
     * 广告位同意接受的最小CPC，单位：分
     */
    private Integer minCpc;

    /**
     * 广告位（一般是劣质广告位）所能出价的最大CPC，单位：分 由模板定向cpc广告组调价引入
     */
    private Integer maxCpc;

    /**
     * 禁止展示其广告的广告主列表，以英文,隔开，如:123,456,789
     */
    private String blockSponsorIds;

    /**
     * 禁止展示的广告类别，以英文,隔开，如:123,456,789
     */
    private String blockCategoryIds;

    /**
     * 是否开启平滑消费
     */
    private Byte isOpenSmoothConsumption;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

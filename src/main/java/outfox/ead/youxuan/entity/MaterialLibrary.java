package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 素材库
 *
 * <AUTHOR>
 * @create 2025-05-19 18:06
 **/
@TableName(value = "MaterialLibrary")
@Data
public class MaterialLibrary implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 媒体主键
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;
    /**
     * 文件名称
     */
    @TableField("NAME")
    private String name;
    /**
     * 文件url
     */
    @TableField("URL")
    private String url;
    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "LAST_MOD_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;
    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 更新人
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    /**
     * 是否删除：false-未删除，true-已删除
     */
    @TableField("IS_DELETED")
    @TableLogic
    private Boolean isDeleted;
}
package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户单向绑定表
 *
 * @TableName UserRelation
 */
@TableName(value = "UserRelation")
@Data
public class UserRelation implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创作者user id
     */
    private Long kolUserId;

    /**
     * 广告主user id
     */
    private Long sponsorUserId;

    /**
     * 状态 0-正常，1-暂停，2-删除
     */
    private Integer status;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;


    private LocalDateTime createTime;


    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

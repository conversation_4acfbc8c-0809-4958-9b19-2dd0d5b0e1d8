package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import outfox.ead.youxuan.constants.InteractionTypeEnum;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 开屏灵敏度配置
 *
 * <AUTHOR>
 * @create 2024-03-21
 **/
@TableName(value = "AdSensitivity")
@Data
public class AdSensitivity implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    /**
     * 广告点击交互类型，1：摇一摇，2：滑动互动，5：扭一扭, 6:三合一
     *
     * @see InteractionTypeEnum
     */
    @TableField("CLICK_TYPE")
    private Integer clickType;

    /**
     * 广告位ID
     */
    @TableField("AD_POSITION_ID")
    private Long adPositionId;

    /**
     * 广告组ID
     */
    @TableField("AD_GROUP_ID")
    private Long adGroupId;

    /**
     * 灵敏度配置-摇动加速度
     */
    @TableField("SHAKE_SPEED")
    private Integer shakeSpeed;

    /**
     * 灵敏度配置-滑动角度
     */
    @TableField("SLIDE_ANGLE")
    private Integer slideAngle;

    /**
     * 灵敏度配置-摇动角度/扭转角度
     */
    @TableField("ROTATION_ANGLE")
    private Integer rotationAngle;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "LAST_MOD_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 更新人
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    /**
     * 是否删除：false-未删除，true-已删除
     */
    @TableField("IS_DELETED")
    private Boolean isDeleted;
} 
package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @TableName Role
 */
@TableName(value = "Role")
@Data
public class Role implements Serializable {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(hidden = true)
    private Long id;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色唯一标识
     */
    private String roleKey;

    /**
     * 状态 0-正常，1-暂停，3-删除
     */
    private Integer status;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(hidden = true)
    private Long creator;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(hidden = true)
    private Long modifier;

    @ApiModelProperty(hidden = true)
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true)
    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 橱窗组件样式
 * @TableName ShowcaseComponentSchema
 */
@TableName(value ="ShowcaseComponentSchema")
@Data
public class ShowcaseComponentSchema implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 样式名称
     */
    private String name;

    /**
     * 推广类型；1-品牌宣传，2-商品售卖，3-线索留资，4-应用推广
     */
    private Integer promotionType;

    /**
     * 状态；0-可选，1-不可选
     */
    private Integer status;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long modifier;

    /**
     * 
     */
    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 达人与平台内容类型绑定关系
 * @TableName KolPlatformContentRelation
 */
@TableName(value ="KolPlatformContentRelation")
@Data
public class KolPlatformContentRelation implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 达人ID
     */
    private Long appAccountId;

    /**
     * 平台内容类型ID
     */
    private Long platformContentId;

    /**
     * 服务报价，以分为单位
     */
    private Long price;

    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 
     */
    private LocalDateTime createTime;

    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    /**
     * 
     */
    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

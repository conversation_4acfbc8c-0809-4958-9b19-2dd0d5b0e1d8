package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 自定义人群包
 *
 * @TableName CustomCrowdPack
 */
@TableName(value = "CustomCrowdPack")
@Data
public class CustomCrowdPack implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 名字
     */
    private String name;
    private String filePath;
    private Integer deviceIdType;
    private String errMsg;
    /**
     * 状态 0-有效 1-校验中 2-校验失败 3-生成中 4-已失效 5-已删除
     */
    private Integer status;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 失效时间
     */
    private LocalDateTime expiryTime;
    /**
     * 有效数量
     */
    private Integer validCount;

    private LocalDateTime createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    private LocalDateTime lastModTime;
    /**
     * 创建者角色
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorRole;
    /**
     * 修改者角色
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifierRole;

    public static final int VALID = 0;
    public static final int CHECKING = 1;
    public static final int CHECK_FAIL = 2;
    public static final int GENERATING = 3;
    public static final int EXPIRED = 4;
    public static final int DELETED = 5;
}
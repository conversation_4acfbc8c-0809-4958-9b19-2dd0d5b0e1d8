package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @TableName UserRole
 */
@TableName(value = "UserRoleRelation")
@Data
@NoArgsConstructor
public class UserRoleRelation implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 账户id
     */
    private Long userId;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 状态 0-正常，1-暂停，2-删除
     */
    private Integer status;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;


    private LocalDateTime createTime;


    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public UserRoleRelation(Long userId, Long role) {
        this.userId = userId;
        this.roleId = role;
    }
}

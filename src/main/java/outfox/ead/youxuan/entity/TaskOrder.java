package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务订单表
 *
 * @TableName TaskOrder
 */
@TableName(value = "TaskOrder")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskOrder implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String orderId;

    /**
     * 父订单ID
     */
    private Long parentOrderId;

    /**
     * 推广内容类型ID
     */
    private Long platformContentId;

    /**
     * 达人ID
     */
    private Long appAccountId;

    /**
     * 备注（仅运营可见）
     */
    private String comment;

    /**
     * 价格，以人民币分为单位
     */
    private Long price;

    /**
     * 状态;0待接收、1待付款、2进行中、3已完成、4已取消、5待开启、6已下架、7已结束
     */
    private Integer status;

    /**
     * 任务类型；1-指派任务，2-投稿任务
     */
    private Integer type;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 橱窗组件ID
     */
    private Long showcaseComponentId;

    /**
     * 推广开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime beginDate;
    /**
     * 推广结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endDate;

    /**
     * 投稿上限；0为无上限，其他值为上限
     */
    private Long postLimit;

    /**
     * 投稿剩余数
     * <br/>
     * 投稿数无上限时该值为null
     */
    private Long postRemain;

    /**
     * 计费类型；0-其他，1-CPM，2-CPC，3-CPA，4-CPS
     */
    private Integer billingType;

    /**
     * 佣金单价，以人民币分为单位
     */
    private Long commissionPrice;

    /**
     * 结算周期;0-其他，1-月结，2-周结，3-日结
     */
    private Integer settlementInterval;

    /**
     * 考核说明
     */
    private String assessmentDescription;

    /**
     * 自动挂窗投稿任务权重
     */
    private Integer weight;

    /**
     * 自动挂窗
     */
    private Boolean autoShowcase;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

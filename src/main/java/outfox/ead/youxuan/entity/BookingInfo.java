package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @TableName BookingInfo
 */
@TableName(value = "BookingInfo")
@Data
public class BookingInfo implements Serializable {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(hidden = true)
    private Long id;

    @ApiModelProperty("企业名称")
    @NotNull(message = "请输入企业名称")
    private String companyName;

    @ApiModelProperty("名字")
    @NotNull(message = "请输入姓名")
    private String name;

    @ApiModelProperty("手机号")
    @Size(max = 11, min = 11)
    @NotNull(message = "请输入手机号")
    private String phoneNum;

    @ApiModelProperty("地区")
    @NotNull(message = "请选择地区")
    private String city;

    @ApiModelProperty("行业")
    @NotNull(message = "请选择行业")
    private String industry;

    @ApiModelProperty("介绍")
    @NotNull(message = "请输入产品介绍")
    private String description;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import outfox.ead.youxuan.core.handler.mybatis.CreativeContentTypeHandler;
import outfox.ead.youxuan.web.ad.controller.dto.CreativeContent;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.TreeSet;

/**
 * 推广组的子层级推广内容
 *
 * <AUTHOR>
 * @TableName AdContent
 */
@TableName(value = "AdContent", autoResultMap = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class AdContent implements Serializable {
    /**
     * 推广内容主键
     */
    @TableId(type = IdType.AUTO)
    @EqualsAndHashCode.Exclude
    private Long id;
    /**
     * 推广组主键
     */
    private Long adGroupId;
    /**
     * 创意内容类型
     * 0-信息流
     * 1-开屏
     * 2-插屏
     * 3-焦点图
     * 4-激励视频
     * 5-横幅
     * 6-自定义
     */
    private Integer type;
    /**
     * 创意内容文字类型
     */
    @TableField(typeHandler = CreativeContentTypeHandler.class)
    @EqualsAndHashCode.Exclude
    private Set<CreativeContent> textCreativeContents = new TreeSet<>();
    /**
     * 创意内容图片类型
     */
    @TableField(typeHandler = CreativeContentTypeHandler.class)
    @EqualsAndHashCode.Exclude
    private Set<CreativeContent> imageCreativeContents = new TreeSet<>();
    /**
     * 创意内容视频类型
     */
    @TableField(typeHandler = CreativeContentTypeHandler.class)
    @EqualsAndHashCode.Exclude
    private Set<CreativeContent> videoCreativeContents = new TreeSet<>();

    /**
     * 展示时长
     */
    private Integer showTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @EqualsAndHashCode.Exclude
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @EqualsAndHashCode.Exclude
    private LocalDateTime lastModTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @EqualsAndHashCode.Exclude
    private Long creator;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @EqualsAndHashCode.Exclude
    private Long modifier;

    /**
     * 0-有效，1-删除
     */
    private Integer status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @TableName User
 */
@TableName(value = "User")
@Data
public class User {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(hidden = true)
    private Long id;

    /**
     * 用户名
     */
    @Pattern(regexp = "(\\w){4,20}@163\\.com$", message = "请使用网易163邮箱注册")
    private String username;

    @ApiModelProperty("词典uid")
    private String dictUid;

    @ApiModelProperty("状态，默认为1-有效，其他值都无效")
    @NotNull
    private Integer status;

    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

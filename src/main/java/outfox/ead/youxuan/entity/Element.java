package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @TableName Element
 */
@TableName(value = "Element")
@Data
public class Element implements Serializable {
    /**
     * 样式元素主键，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 样式元素名称,用于开发者和发布系统前端展示
     */
    private String name;

    /**
     * 样式元素key，用于解析
     */
    private String elementKey;

    /**
     * 图片比例
     */
    private String ratio;

    /**
     * 文字样式元素元素长度
     */
    private Integer length;

    /**
     * 图片样式元素高度
     */
    private Integer height;

    /**
     * 图片样式元素宽度
     */
    private Integer width;

    /**
     * 视频格式 0:MP4
     */
    private String mimeType;
    /**
     * 样式元素类型，1:文字;2:图片;3:视频
     */
    private Integer type;

    /**
     * 样式元素状态，0：无效（可扩展）1：有效
     */
    private Integer status;

    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
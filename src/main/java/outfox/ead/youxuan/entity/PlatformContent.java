package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @TableName PlatformContent
 */
@TableName(value = "PlatformContent")
@Data
public class PlatformContent implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 平台任务ID
     */
    private Long platformTaskId;

    /**
     * 内容类型名字
     */
    private String name;

    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    private LocalDateTime createTime;

    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

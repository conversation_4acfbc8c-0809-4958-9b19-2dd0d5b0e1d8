package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 查词定向广告规则
 *
 * <AUTHOR>
 */
@TableName(value = "AdvertisingByKeywordsRule", autoResultMap = true)
@Data
public class AdvertisingByKeywordsRule implements Serializable {

    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    private Long adGroupId;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> keywordList;

    @TableField(value = "AUDIO_URLS", typeHandler = JacksonTypeHandler.class)
    private List<String> audioUrls;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

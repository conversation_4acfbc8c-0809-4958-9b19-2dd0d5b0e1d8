package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @TableName PlatformTask
 */
@TableName(value = "PlatformTask")
@Data
public class PlatformTask implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务类型
     */
    private String name;

    /**
     * 平台ID
     */
    private Long platformId;

    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;

    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    private LocalDateTime createTime;

    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

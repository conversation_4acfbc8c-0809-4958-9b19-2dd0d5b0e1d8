package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @TableName yex_dsp
 */
@TableName(value = "yex_dsp")
@Data
public class YexDsp implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long dspId;

    private String dspName;

    private String url;

    private String cookieMatchUrl;

    private String selectRule;

    private String protocol;

    /**
     * 1=valid
     */
    private Integer status;

    /**
     * 0=false,1=true
     */
    private Byte test;

    private Object serde;

    private Integer maxLatencyMs;

    private String encryptionKeyString;

    private String integrityKeyString;

    private String testMdid;

    private String neteasePassport;

    private Integer maxQps;

    private Integer priority;

    private Integer standardVersion;

    private Byte enableCompositeLink;

    private Integer dspAdType;

    private Integer timeout;

    private Boolean multipleMainImage;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.util.LocalDateUtil;
import outfox.ead.youxuan.web.ad.controller.dto.DateSlot;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static outfox.ead.youxuan.constants.Constants.DATE_CONTINUOUS;

/**
 * <AUTHOR>
 * @TableName AdPlan
 */
@TableName(value = "AdPlan", autoResultMap = true)
@Data
public class AdPlan implements Serializable {
    /**
     * 推广计划主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 推广目标
     */
    private Integer promotionTarget;

    /**
     * 推广计划名称
     */
    private String name;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 计费模式 0-CPT 1-CPM
     */
    private Integer billingType;

    /**
     * 投放方式
     */
    private Integer deliveryType;

    /**
     * 日期是否连续 0-连续 1-不连续
     */
    private Integer dateContinuous;
    /**
     * 投放日期
     * 存储不连续的日期
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> adDeliveryDate;

    /**
     * 投放开始日期
     * 连续日期只需要存储开始和结束时间
     * 不连续日期要通过adDeliveryDate推出
     */
    private LocalDateTime adOpenDate;

    /**
     * 投放结束时间
     */
    private LocalDateTime adCloseDate;

    /**
     * 投放时段 0-不限 1-指定时段
     * <p>
     * 指定 星期几几点投放
     */
    private Integer timeOrientation;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<List<Integer>> timeDest;
    /**
     * 地域定向 0-不限 1-按区域
     */
    private Integer regionalOrientation;

    /**
     * 地域定向流量范围 0-全部 1-内部流量
     */
    private Integer regionalOrientationScope;
    /**
     * 投放目标地址国际
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Integer> internationalRegionalDest;

    /**
     * 投放目标地址国内
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Integer> inlandRegionalDest;

    /**
     * 计划状态 0-投放中，1-投放结束，2-即将开始，3-暂停，4已删除
     */
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;

    /**
     * 创建人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 修改人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(fill = FieldFill.INSERT)
    private Long roleId;

    /**
     * 频控方式 0-不频控 1-按天频控 2-按周频控 3-按投放周期频控
     */
    private Integer frequencyType;

    /**
     * 频控次数
     */
    private Integer frequencyLimit;

    /**
     * 关联的有效联合频控id
     * {@link UnionFrequencyRule}
     */
    private Long validUnionFrequencyRuleId;

    /**
     * 返回投放时间段用于前端展示
     *
     * <p> 如果时间连续那么直接拿到AdOpenDate和AdCloseDate组成时间段即可
     * <p> 如果时间不连续那么要拿adDeliveryDate，尝试将散点时间进行拼接
     *
     * @return 投放时间段
     */
    public List<DateSlot> getAdDeliveryInterval() {
        List<DateSlot> dateSlots = new ArrayList<>();
        if (this.getDateContinuous().equals(DATE_CONTINUOUS)) {
            DateSlot dateSlot = new DateSlot();
            dateSlot.setStartDate(this.getAdOpenDate().toLocalDate());
            dateSlot.setEndDate(this.getAdCloseDate().toLocalDate());
            dateSlots.add(dateSlot);
        } else {
            List<String> adDeliveryDate = this.getAdDeliveryDate();
            List<LocalDate> localDates = new ArrayList<>();
            for (String s : adDeliveryDate) {
                String[] split = s.split("-");
                localDates.add(LocalDate.of(Integer.parseInt(split[0]), Integer.parseInt(split[1]), Integer.parseInt(split[2])));
            }
            dateSlots = LocalDateUtil.aggregationDate(localDates);
        }
        return dateSlots;
    }
}

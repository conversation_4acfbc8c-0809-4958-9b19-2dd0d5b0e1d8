package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import outfox.ead.youxuan.constants.InteractionTypeEnum;
import outfox.ead.youxuan.core.handler.mybatis.LongSetTypeHandler;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @TableName AdGroup
 */
@TableName(value = "AdGroup", autoResultMap = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class AdGroup implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 推广组名称
     */
    private String name;

    /**
     * 推广计划主键
     */
    private Long adPlanId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * cpm 出价
     */
    private Integer cpmPrice;

    /**
     * 总展示量
     */
    private Integer sumDisplayCount;

    /**
     * 日展示量上限
     */
    private Integer dailyDisplayLimit;

    /**
     * 投放速度,0代表快速，1代表均匀
     */
    private Integer deliverySpeed;

    /**
     * 开机首刷，0不开启，1开启
     */
    private Integer bootFirstRefresh;

    /**
     * 开屏回收，0不开启，1开启
     */
    private Integer openScreenRecycle;

    /**
     * 跳转添加类型
     */
    private Integer brandClkType;
    /**
     * 落地页链接
     */
    private String landingPageLink;
    /**
     * 深链接
     */
    private String deeplinkUrl;
    /**
     * 小程序APPID
     */
    private String wechatAppId;
    /**
     * 小程序原始id
     */
    private String wechatOriginalId;
    /**
     * 目标路径
     */
    private String wechatPath;
    /**
     * 落地页链接1
     */
    private String landingPageLink1;
    /**
     * 深链接1
     */
    private String deeplinkUrl1;
    /**
     * 小程序原始id1
     */
    private String wechatOriginalId1;
    /**
     * 目标路径1
     */
    private String wechatPath1;
    /**
     * 落地页链接2
     */
    private String landingPageLink2;
    /**
     * 深链接2
     */
    private String deeplinkUrl2;
    /**
     * 小程序原始id2
     */
    private String wechatOriginalId2;
    /**
     * 目标路径2
     */
    private String wechatPath2;

    /**
     * 订单id
     */
    private String dealId;

    /**
     * 订单备注
     */
    private String dealRemark;

    /**
     * dsp id
     */
    private String dspId;

    /**
     * 打底广告，0不填充，1填充
     */
    private Integer basePadding;

    /**
     * 曝光监测链接
     */
    private String expoDeteLink;

    /**
     * 点击监测链接
     */
    private String clickDeteLink;

    /**
     * 青少年模式，0-否，1-是
     */
    private Boolean youthMode;

    /**
     * 性别定向 0-无 1-男 2-女
     */
    private Integer genderOrientation;

    /**
     * 年龄定向信息第二版，存储格式为年龄区间列表。
     * 年龄区间格式为以下两种：
     *1、[22,34]表示22岁到34岁的闭区间
     *2、[35]表示35岁
     *
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<List<Integer>> ageOrientationV2;

    /**
     * 定向模式 0-无  1-扩展人群定向 2-精准人群定向
     */
    private Integer orientationMode;

    /**
     * 人群标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> crowdLabel;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    /**
     * 无设备号过滤
     */
    private Boolean mustDeviceId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(fill = FieldFill.INSERT)
    private Long roleId;

    /**
     * 监测链接是否需要宏替换
     */
    private Boolean replaceMacro;

    /**
     * 是否开启扩量推广
     */
    private Boolean expansionPromotion;

    /**
     * 交互类型{@link InteractionTypeEnum#getCode()}
     */
    private Integer interactionType;

    /**
     * 是否开启app定向
     */
    private Boolean appInstalledOrientation;

    /**
     * 应用包名id列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> installedAppPackageIds;

    /**
     * 微信小程序定向
     */
    private Boolean wechatOrientation;

    /**
     * 测试直达状态
     */
    private Boolean testDirect;

    /**
     * 直达有效开始时间
     */
    private LocalDateTime directStartTime;
    /**
     * 直达有效结束时间
     */
    private LocalDateTime directEndTime;

    /**
     * 是否开启自定义人群包
     */
    private Boolean openCustomCrowdPack;

    /**
     * true-定向人群投放 false-排除人群投放
     */
    private Boolean includeCrowdPack;

    /**
     * 人群包id列表
     */
    @TableField(typeHandler = LongSetTypeHandler.class)
    private Set<Long> crowdPackIds;

    /**
     * 是否开启h5合成链接
     */
    private Boolean h5TransitUrl;

    /**
     * 是否开启deeplink合成链接
     */
    private Boolean dpTransitUrl;

    /**
     * 合成链接中目标小程序的appId
     */
    private String transitTargetWechatAppId;

    /**
     * 品牌cpm广告消费类型，0-正常消费，1-匀速消费，2-加速消费(默认0)
     */
    private Integer cpmCostType;

    /**
     * cpm广告加速系数，当广告模式为加速模式时生效，范围为1-100，默认为1
     */
    private Integer cpmAccelerateRatio;

    /**
     * 是否允许caid作为有效设备号类型
     */
    private Boolean allowCaid;

    /**
     * 是否支持全链路投放
     */
    private Boolean fullChannel;

    /**
     * 是否需要上报第三方曝光检测
     */
    private Boolean reportExpoDeteLink;

    /**
     * 全屏点击：0表示关，1表示开
     */
    private Boolean fullClickIsOpen;

    /**
     * 全屏点击支持的城市等级列表
     * 包含"-1"则为不限制地域
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> fullClickClassCities;
}

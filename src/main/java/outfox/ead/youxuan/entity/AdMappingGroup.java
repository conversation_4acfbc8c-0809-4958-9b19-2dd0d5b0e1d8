package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import outfox.ead.youxuan.core.handler.mybatis.LongSetTypeHandler;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 广告映射组
 *
 * @TableName AdMappingGroup
 */
@TableName(value = "AdMappingGroup", autoResultMap = true)
@Data
public class AdMappingGroup implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 推广组id
     */
    private Long adGroupId;
    /**
     * 原始样式id
     */
    @TableField(typeHandler = LongSetTypeHandler.class)
    private Set<Long> sourceStyleIds;
    /**
     * 映射样式id
     */
    @TableField(typeHandler = LongSetTypeHandler.class)
    private Set<Long> mappingStyleIds;
    /**
     * 状态；0-开启，1-暂停，2-删除
     */
    private Integer status;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     *
     */
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    private LocalDateTime lastModTime;
    @TableField(fill = FieldFill.INSERT)
    private Long roleId;
}
package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import outfox.ead.youxuan.core.annotation.DistributedLockKey;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 媒体账户
 *
 * <AUTHOR>
 * @TableName AppAccount
 */
@TableName(value = "AppAccount")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppAccount implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 媒体账户id
     */
    @DistributedLockKey
    private String appUserId;
    /**
     * 媒体平台id
     */
    private Long platformId;

    /**
     * 媒体账户昵称
     */
    private String name;

    /**
     * 媒体账户头像
     */
    private String avatar;

    /**
     * 性别，0-男 1-女
     */
    private Integer gender;

    /**
     * 粉丝数
     */
    private Long fansNum;

    /**
     * 状态 0-正常，1-暂停，2-删除
     */
    private Integer status;

    /**
     * 地点
     */
    private Integer area;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;


    private LocalDateTime createTime;


    private LocalDateTime lastModTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 是否在接单
     */
    private Boolean inService;

    /**
     * 词典橱窗是否在优选开通过
     */
    private boolean productWindowPermission;

    /**
     * 提示样式迁移（目前只有词典需要用这个字段）？0-不提示，1-提示
     */
    private Boolean notifySchemaUpgrade;

    /**
     * 所属mcn
     */
    private String mcn;

    /**
     * 所属运营账户id
     */
    private Long kolOperatorUserId;

    /**
     * 主页链接
     */
    private String homepageLink;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

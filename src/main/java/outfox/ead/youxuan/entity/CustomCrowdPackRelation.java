package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 自定义人群包关联表
 *
 * @TableName CustomCrowdPackRelation
 */
@TableName(value = "CustomCrowdPackRelation")
@Data
public class CustomCrowdPackRelation implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 推广组id
     */
    private Long adGroupId;
    /**
     * 自定义人群包id
     */
    private Long customCrowdPackId;
}
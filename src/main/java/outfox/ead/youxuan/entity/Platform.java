package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 平台信息
 *
 * @TableName Platform
 */
@TableName(value = "Platform")
@Data
public class Platform implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;


    private String name;

    /**
     * 绑定类型 0-不验证 1-需要验证
     */
    private Integer bindType;

    /**
     * 验证URL
     */
    private String verifyUrl;


    private String userInfoUrl;


    private String icon;




    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    private LocalDateTime createTime;

    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

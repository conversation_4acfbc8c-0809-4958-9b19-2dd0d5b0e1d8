package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 广告映射
 *
 * @TableName AdMapping
 */
@TableName(value = "AdMapping")
@Data
public class AdMapping implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 所属映射组 id
     */
    private Long adMappingGroupId;

    /**
     * 推广组id
     */
    private Long adGroupId;

    /**
     * 原始样式id
     */
    private Long sourceStyleId;

    /**
     * 映射样式id
     */
    private Long mappingStyleId;

    /**
     * 状态；0-开启，1-暂停，2-删除
     */
    private Integer status;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    private LocalDateTime lastModTime;

    @TableField(fill = FieldFill.INSERT)
    private Long roleId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
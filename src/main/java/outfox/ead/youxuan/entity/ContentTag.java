package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 达人内容标签
 *
 * @TableName ContentTag
 */
@TableName(value = "ContentTag")
@Data
public class ContentTag implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签类型：0-内容标签 1-名师标签
     */
    private Integer type;

    /**
     * 0-有效 1-无效
     */
    private Integer status;


    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;


    private LocalDateTime createTime;


    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;


    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

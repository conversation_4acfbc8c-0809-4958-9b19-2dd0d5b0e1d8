package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 配置表
 *
 * @TableName Config
 */
@Data
@TableName(value = "Config")
public class Config implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 配置名
     */
    private String configKey;

    /**
     * 配置值
     */
    private String configValue;

    /**
     *
     */
    private Long creator;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Long modifier;

    /**
     *
     */
    private Date lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
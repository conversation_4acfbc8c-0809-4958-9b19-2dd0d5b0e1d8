package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 主页转化工具
 *
 * @TableName ConvModule
 */
@TableName(value = "ConvModule")
@Data
public class ConvModule implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 启用？0-关闭 1-启用
     */
    private boolean enabled;

    /**
     * 0-官方网站；1-应用下载；2-联系电话；3-推广活动
     */
    private Integer type;

    private Long appAccountId;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 官方网站和推广活动的链接地址
     */
    private String url;

    /**
     * 安卓应用包名
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String androidPackageName;

    /**
     * iOS下载地址
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String iosDownloadUrl;

    /**
     * 电话；需要支持400或区号开头的电话
     */
    private String phone;

    /**
     * 跳转微信小程序：0-关闭 1-开启
     */
    private Boolean enabledWechatMicroProgramSwitch;

    /**
     * 微信小程序原始ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String wechatMicroProgramRawId;

    /**
     * 微信小程序路径
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String wechatMicroProgramPath;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;


    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;


    private LocalDateTime lastModTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

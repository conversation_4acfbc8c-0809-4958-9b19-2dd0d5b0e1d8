package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 达人与内容标签绑定关系
 *
 * @TableName KolContentTagRelation
 */
@TableName(value = "KolContentTagRelation")
@Builder
@Data
public class KolContentTagRelation implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 达人ID
     */
    private Long appAccountId;

    /**
     * 内容标签ID
     */
    private Long contentTagId;


    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;


    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

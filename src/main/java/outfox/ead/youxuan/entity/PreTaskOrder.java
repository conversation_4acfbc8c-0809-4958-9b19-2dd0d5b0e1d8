package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 已选达人表
 *
 * @TableName PreTaskOrder
 */
@TableName(value = "PreTaskOrder")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreTaskOrder implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 达人ID
     */
    private Long appAccountId;

    /**
     * 推广内容类型ID
     */
    private Long platformContentId;

    /**
     * 加入已选达人列表时的媒体账户昵称
     */
    private String kolName;

    /**
     * 加入已选达人列表时的媒体账户头像
     */
    private String kolAvatar;

    /**
     * 备注（仅运营可见）
     */
    private String comment;

    /**
     * 加入已选达人列表时的价格，以人民币分为单位
     */
    private Long price;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;


    private LocalDateTime createTime;


    private LocalDateTime lastModTime;

    /**
     * 0-未删除 1-删掉了
     */
    private boolean deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 橱窗组件id
     */
    private Long showcaseComponentId;
}

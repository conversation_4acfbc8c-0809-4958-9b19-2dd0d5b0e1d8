package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@TableName(value = "Media")
@Data
public class Media implements Serializable {
    /**
     * 媒体主键
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    /**
     * 平台
     */
    @TableField(value = "OS_TYPE")
    private Integer osType;

    /**
     * 状态,0-有效，1-暂停,2-已删除
     */
    @TableField(value = "STATUS")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(value = "LAST_MOD_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;

    /**
     * 媒体名称
     */
    @TableField(value = "NAME")
    private String name;

    /**
     * 备注
     */
    @TableField(value = "NOTE")
    private String note;

    /**
     * 青少年模式，0-否，1-是
     */
    private Boolean youthMode;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 修改人
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
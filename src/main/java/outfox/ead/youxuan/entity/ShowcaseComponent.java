package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.jd.open.api.sdk.domain.kplunion.GoodsService.response.query.CategoryInfo;
import com.jd.open.api.sdk.domain.kplunion.GoodsService.response.query.CommissionInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;

import java.io.Serializable;
import java.time.LocalDateTime;

import static outfox.ead.youxuan.constants.ContentMarketingConstants.*;

/**
 * 橱窗组件
 *
 * @TableName ShowcaseComponent
 */
@TableName(value = "ShowcaseComponent", autoResultMap = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShowcaseComponent implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组件ID，为10位大小写字母组成的字符串
     */
    private String componentId;

    /**
     * App Account ID
     */
    private Long appAccountId;

    /**
     * 来源橱窗组件ID，广告主创建的组件需要同步到绑定了的品牌号上。
     */
    private Long sourceId;

    /**
     * 组件名称
     */
    private String name;

    /**
     * 推广类型；1-品牌宣传，2-商品售卖，3-线索留资，4-应用推广
     */
    private Integer promotionType;

    /**
     * 推广品类，复用行业信息类目
     */
    private Integer category;

    /**
     * 跳转类型；1-落地页，2-应用直达，3-微信小程序，4-应用商店
     */
    private Integer switchType;

    /**
     * deeplink链接
     */
    private String deepLink;

    /**
     * 落地页跳转链接
     */
    private String landingPageUrl;

    /**
     * 备用落地页链接
     */
    private String backupLandingPageUrl;

    /**
     * 是否自动为跳转链接添加来源参数配置项
     */
    private Boolean appendOutVendor;

    /**
     * 微信小程序原始ID
     */
    private String microProgramId;

    /**
     * 微信小程序目标页面路径
     */
    private String microProgramPath;

    /**
     * 应用平台；0-不限，1-Android，2-iOS
     */
    private Integer appPlatform;

    /**
     * 安卓应用包名
     */
    private String androidPackageName;

    /**
     * iOS应用ID
     */
    private String iosAppId;

    /**
     * 样式ID
     */
    private Long schemaId;

    /**
     * 引导文案
     */
    private String leadText;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品价格
     */
    private Long itemPrice;

    /**
     * 划线价
     */
    private Long strikeThroughPrice;

    /**
     * 按钮文案
     */
    private String buttonText;

    /**
     * 推广图片的URL
     */
    private String promoteImageUrl;

    /**
     * 推广标题
     */
    private String promoteTitle;

    /**
     * 推广文案
     */
    private String promoteText;

    /**
     * 应用名称
     */
    private String appName;


    /**
     * 已经审核通过的组件信息，以JSON格式存储。
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ShowcaseComponent auditedContent;

    /**
     * 已经审核通过的推广标题
     */
    private String auditedPromoteTitle;

    /**
     * 审核时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditDatetime;

    /**
     * 是否标记为广告；0-否，1是
     */
    private Boolean markAsAd;

    /**
     * 状态；1-审核中（待审核），2-已上架，3-已下架
     */
    private Integer status;

    /**
     * 状态；1-审核中（待审核），2-已通过，3-已下架，4-未通过
     */
    private Integer auditStatus;
    /**
     * 已删除？0-未删除，1-删掉了
     */
    private Boolean deleted;

    private Long roleId;

    /**
     * 创建人
     */
    private Long creator;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 原始商品类别信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    @JsonIgnore
    private CategoryInfo originalCategoryInfo;

    /**
     * 原始商品佣金信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    @JsonIgnore
    private CommissionInfo originalCommissionPrice;

    /**
     * 京东商品id
     */
    private Long skuId;
    /**
     * 修改人
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastModTime;

    private Long auditor;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public void setAuditedPromoteTitle() {
        this.setAuditedPromoteTitle(mapAuditedPromoteTitle());
    }

    private String mapAuditedPromoteTitle() {
        switch (this.getPromotionType()) {
            case SHOWCASE_COMPONENT_PROMOTION_TYPE_BRANDING:
                return this.getLeadText();
            case SHOWCASE_COMPONENT_PROMOTION_TYPE_SALES:
                return this.getItemName();
            case SHOWCASE_COMPONENT_PROMOTION_TYPE_LEADS:
                return this.getPromoteTitle();
            case SHOWCASE_COMPONENT_PROMOTION_TYPE_APP:
                return this.getAppName();
            default:
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "推广样式参数错误");
        }
    }
}

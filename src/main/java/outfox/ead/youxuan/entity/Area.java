package outfox.ead.youxuan.entity;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 地区列表
 *
 * <AUTHOR>
 */
@Data
public final class Area {
    @JacksonXmlProperty(localName = "ID")
    private int id;
    @JacksonXmlProperty(localName = "NAME")
    private String name;
    @JacksonXmlProperty(localName = "PARENT_ID")
    private int parentId;
    private List<Area> cities = new ArrayList<>(0);
}

package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @TableName AdDeliveryTime
 */
@TableName(value = "AdDeliveryTime")
@Data
@EqualsAndHashCode
public class AdDeliveryTime implements Serializable, Cloneable {
    @TableId(type = IdType.AUTO)
    @EqualsAndHashCode.Exclude
    private Long id;

    private Long adPlanId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public AdDeliveryTime clone() throws CloneNotSupportedException {
        return (AdDeliveryTime) super.clone();
    }
}

package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @TableName AdContentRelation
 */
@TableName(value = "AdContentRelation", autoResultMap = true)
@Data
@EqualsAndHashCode
public class AdContentRelation implements Serializable {

    @TableId(type = IdType.AUTO)
    @EqualsAndHashCode.Exclude
    private Long id;

    private Long adGroupId;

    private Long styleId;

    private Integer displayWeight;

    private Integer status;

    /**
     * 词典视屏流的用户id
     */
    private String dictUid;

    /**
     * 词典视屏流的内容id
     */
    private String dictPostId;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<String> historyDictPostIds;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.core.handler.mybatis.StyleElementTypeHandler;
import outfox.ead.youxuan.web.ad.controller.dto.StyleElement;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 */
@TableName(value = "Style", autoResultMap = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Style implements Serializable {
    /**
     * 样式主键
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    /**
     * 广告位ID
     */
    @TableField(value = "AD_POSITION_ID")
    private Long adPositionId;

    /**
     * 样式名称
     */
    @TableField(value = "NAME")
    private String name;


    /**
     * 自定义展示时间，单位s
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Integer> showTime;

    /**
     * 状态,0-有效，1-暂停,2-已删除
     */
    @TableField(value = "STATUS")
    private Integer status;


    @ApiModelProperty(hidden = true)
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    @ApiModelProperty(hidden = true)
    @TableField(value = "LAST_MOD_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;

    /**
     * 可兼容的最低版本号
     */
    @TableField(value = "MIN_VERSION_SUPPORTED")
    private String minVersionSupported;


    /**
     * 样式类型，0-无，1，原生开屏样式，2，摇一摇样式
     */
    @TableField(value = "STYLE_TYPE")
    private Integer styleType;

    /**
     * 样式元素
     */
    @TableField(typeHandler = StyleElementTypeHandler.class)
    private List<StyleElement> picStyleContent;
    @TableField(typeHandler = StyleElementTypeHandler.class)
    private List<StyleElement> textStyleContent;
    @TableField(typeHandler = StyleElementTypeHandler.class)
    private List<StyleElement> videoStyleContent;

    /**
     * 开屏回收 0-不支持 1-支持
     */
    @TableField(value = "OPEN_SCREEN_RECYCLING")
    private Integer openScreenRecycling;

    /**
     * 开机首刷 0-不支持 1-支持
     */
    @TableField(value = "BOOT_FIRST_REFRESH")
    private Integer bootFirstRefresh;

    /**
     * 是否接受开屏广告 0-否 1-是
     */
    @TableField(value = "RECEIVE_OPEN_SCREEN_RECYCLING")
    private Integer receiveOpenScreenRecycling;
    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 修改人
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    /**
     * 是否全屏，0-否，1-是
     */
    @TableField(value = "FULL_SCREEN")
    private Boolean fullScreen;

    @TableField(value = "DICT_VIDEO_POST")
    private boolean dictVideoPost;

    /**
     * 是否开启摇一摇
     */
    @TableField(value = "SHAKABLE")
    private Boolean shakable;

    /**
     * 是否支持滑动交互
     */
    @TableField(value = "SLIDE_INTERACT")
    private Boolean slideInteract;

    /**
     * 是否支持双link
     */
    @TableField(value = "DOUBLE_LINK")
    private Boolean doubleLink;

    /**
     * 是否支持三link
     */
    @TableField(value = "THREE_LINK")
    private Boolean threeLink;

    @TableField(value = "HAS_TWIST")
    private Boolean hasTwist;

    /**
     * 是否支持全屏点击
     */
    @TableField(value = "FULL_CLICK")
    private Boolean fullClick;

    /**
     * 是否支持三合一交互
     */
    @TableField(value = "THREE_IN_ONE")
    private Boolean threeInOne;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

package outfox.ead.youxuan.util;

import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.kplunion.CategoryService.request.get.CategoryReq;
import com.jd.open.api.sdk.domain.kplunion.CategoryService.response.get.CategoryResp;
import com.jd.open.api.sdk.domain.kplunion.GoodsService.request.query.GoodsReq;
import com.jd.open.api.sdk.domain.kplunion.GoodsService.request.query.JFGoodsReq;
import com.jd.open.api.sdk.domain.kplunion.promotioncommon.PromotionService.request.get.PromotionCodeReq;
import com.jd.open.api.sdk.request.kplunion.UnionOpenCategoryGoodsGetRequest;
import com.jd.open.api.sdk.request.kplunion.UnionOpenGoodsJingfenQueryRequest;
import com.jd.open.api.sdk.request.kplunion.UnionOpenGoodsQueryRequest;
import com.jd.open.api.sdk.request.kplunion.UnionOpenPromotionCommonGetRequest;
import com.jd.open.api.sdk.response.kplunion.UnionOpenCategoryGoodsGetResponse;
import com.jd.open.api.sdk.response.kplunion.UnionOpenGoodsJingfenQueryResponse;
import com.jd.open.api.sdk.response.kplunion.UnionOpenGoodsQueryResponse;
import com.jd.open.api.sdk.response.kplunion.UnionOpenPromotionCommonGetResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年08月30日 15:52
 */
@Slf4j
public class JdItemUtil {
    final static String SERVER_URL = "https://api.jd.com/routerjson";
    final static String ANDROID_APP_KEY = "9a12aadb195059e5847ffc126e1ee8d7";
    final static String ANDROID_APP_SECRET = "83ef078482264f97af07c1419e9ea4b8";
    final static String ANDROID_APP_ID = "4100885304";
    final static String ACCESS_TOKEN = "";
    // 选品库
    final static Integer ELITE_ID = 1001;

    final static String IOS_APP_KEY = "9a12aadb195059e5847ffc126e1ee8d7";
    final static String IOS_APP_SECRET = "83ef078482264f97af07c1419e9ea4b8";


    private static final JdClient CLIENT = new DefaultJdClient(SERVER_URL, "", "0e28024bd18a4927e82bd0908a422027", "19f0a61f8a1949c6b472fa9042de5755");

    // todo 该接口需要申请权限，临时找了一个有权限的appKey和appSecret 后续pm改回有道的appKey和appSecret
    public static UnionOpenGoodsQueryResponse getUnionOpenGoodsQueryResponse(Long[] skuIds) {
        if (skuIds.length > 20) {
            throw new UnsupportedOperationException("skuIds length must less than 20");
        }
        UnionOpenGoodsQueryRequest request = new UnionOpenGoodsQueryRequest();
        GoodsReq goodsReqDTO = new GoodsReq();
        goodsReqDTO.setSkuIds(skuIds);
        goodsReqDTO.setPid("1003359339_4100885304_3005038212");
        request.setGoodsReqDTO(goodsReqDTO);
        request.setVersion("1.0");
        try {
            return CLIENT.execute(request);
        } catch (Exception e) {
            log.error("getUnionOpenGoodsQueryResponse error", e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "getUnionOpenGoodsJingfenQueryResponse error");
        }
    }

    private static final JdClient CLIENT2 = new DefaultJdClient(SERVER_URL, ACCESS_TOKEN, ANDROID_APP_KEY, ANDROID_APP_SECRET);

    public static UnionOpenPromotionCommonGetResponse getUnionOpenPromotionCommonGetResponse(String materialId) {
        UnionOpenPromotionCommonGetRequest request = new UnionOpenPromotionCommonGetRequest();
        PromotionCodeReq promotionCodeReq = new PromotionCodeReq();
        promotionCodeReq.setSiteId(ANDROID_APP_ID);
        promotionCodeReq.setPositionId(3005038212L);
        promotionCodeReq.setMaterialId(materialId);
        request.setPromotionCodeReq(promotionCodeReq);
        request.setVersion("1.0");
        try {
            return CLIENT2.execute(request);
        } catch (Exception e) {
            log.error("获取推广位信息失败,materialId: {}", materialId, e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "getUnionOpenPromotionCommonGetResponse error");
        }
    }

    private static final JdClient CLIENT3 = new DefaultJdClient(SERVER_URL, ACCESS_TOKEN, "0cf6b55c585c5ead77361772a937bbc7", "619475fec05447d1a8041adddc022647");

    public static UnionOpenGoodsJingfenQueryResponse getUnionOpenGoodsJingfenQueryResponse(Long groupId) {
        UnionOpenGoodsJingfenQueryRequest request = new UnionOpenGoodsJingfenQueryRequest();
        JFGoodsReq goodsReq = new JFGoodsReq();
        goodsReq.setEliteId(ELITE_ID);
        goodsReq.setOwnerUnionId(1003359339L);
        goodsReq.setGroupId(groupId);
        request.setGoodsReq(goodsReq);
        request.setVersion("1.0");
        try {
            return CLIENT3.execute(request);
        } catch (Exception e) {
            log.error("获取商品信息失败，groupId：{}", groupId, e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "getUnionOpenGoodsJingfenQueryResponse error");
        }
    }

    public static Set<String> getGradeTwoCategory() {
        CategoryResp[] gradeZereCategory = JdItemUtil.getUnionOpenCategoryGoodsGetResponse(0, 0).getGetResult().getData();
        List<CategoryResp> gradeOneCategory = new ArrayList<>();
        for (CategoryResp categoryResp : gradeZereCategory) {
            gradeOneCategory.addAll(Arrays.asList(JdItemUtil.getUnionOpenCategoryGoodsGetResponse(categoryResp.getId(), 1).getGetResult().getData()));
        }
        List<CategoryResp> gradeTwoCategory = new ArrayList<>();
        for (CategoryResp categoryResp : gradeOneCategory) {
            gradeTwoCategory.addAll(Arrays.asList(JdItemUtil.getUnionOpenCategoryGoodsGetResponse(categoryResp.getId(), 2).getGetResult().getData()));
        }
        return gradeTwoCategory.stream().map(CategoryResp::getName).collect(Collectors.toSet());
    }

    public static UnionOpenCategoryGoodsGetResponse getUnionOpenCategoryGoodsGetResponse(Integer parent, Integer grade) {
        JdClient client = new DefaultJdClient(SERVER_URL, ACCESS_TOKEN, ANDROID_APP_KEY, ANDROID_APP_SECRET);
        UnionOpenCategoryGoodsGetRequest request = new UnionOpenCategoryGoodsGetRequest();
        CategoryReq req = new CategoryReq();
        req.setParentId(parent);
        req.setGrade(grade);
        request.setReq(req);
        request.setVersion("1.0");
        try {
            return client.execute(request);
        } catch (Exception e) {
            log.error("获取商品品类数据失败", e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "getUnionOpenCategoryGoodsGetResponse error");
        }
    }

    public static void main(String[] args) throws IOException {
        FileUtils.writeLines(new File("/Users/<USER>/jdcategory.txt"), JdItemUtil.getGradeTwoCategory());
    }
}


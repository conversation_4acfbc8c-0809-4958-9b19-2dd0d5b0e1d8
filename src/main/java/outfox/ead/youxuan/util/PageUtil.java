package outfox.ead.youxuan.util;

import org.assertj.core.util.Lists;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年11月21日 18:05
 */
public class PageUtil {
    public static <T> Collection<T> page(Integer page, Integer size, List<T> list) {
        int fromIndex = (page - 1) * size;
        if (fromIndex >= list.size()) {
            return Lists.emptyList();
        } else {
            return list.subList(fromIndex, Math.min(list.size(), fromIndex + size));
        }
    }
}

package outfox.ead.youxuan.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import outfox.ead.youxuan.constants.Constants;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.dto.CookieVerifyResult;
import outfox.ead.youxuan.core.exception.CustomException;

import java.net.URI;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static outfox.ead.youxuan.constants.Constants.NTES_SESS;


/**
 * cookie远程校验工具类
 * urs cookie远程校验接口文档，见https://ursdoc.hz.netease.com/urs-doc/docs/index.php?t=cookie&md=cookie&pos=toc-0-5的1.6.2
 *
 * <AUTHOR>
 **/
@Slf4j
public class CookieVerifyUtil {
    /**
     * 优选的产品id，urs cookie远程校验使用，通过提交工单到urs部门获取
     */
    private static final String PRODUCT_ID = "8084fd601719410090797034d9b5a396";
    /**
     * 163邮箱后缀
     */
    private static final String NTES_163_COM = "@163.com";
    private static final RestTemplate restTemplate = new RestTemplate();

    static {
        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setSupportedMediaTypes(Collections.singletonList(MediaType.ALL));
        messageConverters.add(converter);
        restTemplate.setMessageConverters(messageConverters);
    }

    /**
     * 创建新用户时，校验cookie与邮箱的合法性
     *
     * @param cookie   使用其中已加密的NTES_SESS值进行远程cookie校验,解密后得到的用户邮箱为小写字母
     * @return true 合法， false 非法
     */
    public static String verifyCookieWithEmailAndGetEmail(String cookie) {
        if (cookie == null) {
            throw new CustomException(ResponseType.URS_COOKIE_EMPTY, "URS COOKIE为空，请重新登陆邮箱");
        }
        int indexStart = cookie.indexOf(Constants.NTES_SESS);
        int indexEnd = cookie.indexOf(';', indexStart);
        if (indexStart == -1 || indexEnd == -1) {
            throw new CustomException(ResponseType.URS_COOKIE_EMPTY, "URS COOKIE为空，请重新登陆邮箱");
        }
        String ntesSessCookie = cookie.substring(indexStart + Constants.NTES_SESS.length() + 1, indexEnd);
        // urs提供的北京和杭州机房cookie校验地址
        String[] ursHttpUrl = {"https://cookiebj.reg.163.com/validate", "https://cookiehz.reg.163.com/validate"};
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        for (String s : ursHttpUrl) {
            URI uri = UriComponentsBuilder.fromHttpUrl(s)
                    //产品id，系统部署位置更换时需向urs部门重新提该产品id的工单，增加ip白名单
                    .queryParam("productid", PRODUCT_ID)
                    //是否重新生成cookie，0为否，1为是
                    .queryParam("recreate", 0)
                    .queryParam("cookieName", NTES_SESS)
                    .queryParam("cookie", ntesSessCookie)
                    .build().encode().toUri();
            ResponseEntity<CookieVerifyResult> resp = restTemplate.exchange(uri, HttpMethod.POST, entity, CookieVerifyResult.class);
            CookieVerifyResult cookieVerifyResult = resp.getBody();
            CookieVerifyResult.InnerData data = cookieVerifyResult.getData();
            log.info("urs cookie校验开始：ntesSessCookie=" + ntesSessCookie);
            if (cookieVerifyResult.getRetCode() == 200) {
                log.info("urs cookie校验成功，resp=" + resp);
                return data.getSsn() + NTES_163_COM;
            } else if (cookieVerifyResult.getRetCode() == 400) {
                log.warn("urs cookie校验失败，resp=" + resp);
                throw new CustomException(ResponseType.URS_COOKIE_REJECT, "URS COOKIE为空，请重新登陆");
            } else {
                log.error("urs cookie校验服务状态异常：" + resp);
                throw new CustomException(ResponseType.URS_COOKIE_REJECT, "urs cookie校验服务状态异常");
            }
        }
        throw new CustomException(ResponseType.SERVICE_ERROR);
    }
}

package outfox.ead.youxuan.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.web.ad.controller.bo.CommonInputStreamResource;
import outfox.ead.youxuan.web.ad.controller.response.NosResponse;
import outfox.ead.youxuan.core.exception.CustomException;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @date 2022年03月14日 7:24 下午
 */
@Slf4j
public class FileUtil {
    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    private static final String LUNA_NOS_URL = "https://luna-nos.youdao.com/backend/upload";
    public static void download(String filePath, HttpServletResponse response) throws IOException {
        File file = new File(filePath);
        InputStream is;
        is = new BufferedInputStream(new FileInputStream(file));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(filePath, "UTF-8"));
        FileCopyUtils.copy(is, response.getOutputStream());
    }
    /**
     * 将oImage图片上传到NOS
     *
     * @param url oImage图片链接
     * @return nos url
     */
    public static String transferNosUrl(String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add("file", new CommonInputStreamResource(getInputStream(url)));
        param.add("userCdn",true);
        param.add("useHttps",true);
        param.add("product","ad");
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(param, headers);
        ResponseEntity<NosResponse> response = REST_TEMPLATE.postForEntity(LUNA_NOS_URL, request, NosResponse.class);
        if (response.getStatusCode().is2xxSuccessful() && response.getBody().getCode().equals(0)){
            return (String) response.getBody().getData().get("url");
        }else{
            throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "NOS上传图片失败");
        }
    }

    public static InputStream getInputStream(String url) {
        try {
            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setReadTimeout(30000);
            conn.setConnectTimeout(30000);
            //设置应用程序要从网络连接读取数据
            conn.setDoInput(true);
            conn.setRequestMethod("GET");
            if (conn.getResponseCode() == HttpURLConnection.HTTP_OK) {
                return conn.getInputStream();
            }else{
                throw new RuntimeException();
            }
        } catch (Exception e) {
            throw new RuntimeException("文件下载失败", e);
        }
    }
}

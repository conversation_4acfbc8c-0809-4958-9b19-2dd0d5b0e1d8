package outfox.ead.youxuan.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;

/**
 * 刷新CDN工具类
 * https://confluence.inner.youdao.com/pages/viewpage.action?pageId=375674374
 */
@Slf4j
public class SyncToYoudaoCDNUtil {

    private static final RestTemplate restTemplate = new RestTemplate();

    // CDN同步相关常量
    private static final String CDN_SYNC_URL = "http://updatecdn.iyoudao.net/sync.php";
    private static final String USER_AGENT = "Mozilla/5.0 (X11; Fedora; L Firefox/86.0";
    private static final String ACCEPT = "application/json";
    private static final String ACCEPT_LANGUAGE = "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2";
    private static final String X_REQUESTED_WITH = "XMLHttpRequest";
    private static final String ORIGIN = "http://updatecdn.iyoudao.net";
    private static final String REFERER = "http://updatecdn.iyoudao.net/";
    private static final String CONNECTION = "keep-alive";
    private static final String PRAGMA = "no-cache";
    private static final String CACHE_CONTROL = "no-cache";
    private static final String URL_PARAM_NAME = "url";
    private static final String URL_SEPARATOR = ";";

    /**
     * 刷新CDN
     *
     * @param urlList 需要刷新的URL列表
     * @return true 刷新成功，false 刷新失败
     */
    public static boolean syncToYoudaoCDN(List<String> urlList) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("User-Agent", USER_AGENT);
        headers.set("Accept", ACCEPT);
        headers.set("Accept-Language", ACCEPT_LANGUAGE);
        headers.set("X-Requested-With", X_REQUESTED_WITH);
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Origin", ORIGIN);
        headers.set("Connection", CONNECTION);
        headers.set("Referer", REFERER);
        headers.set("Pragma", PRAGMA);
        headers.set("Cache-Control", CACHE_CONTROL);

        // 将URL列表拼接成字符串
        String urls = String.join(URL_SEPARATOR, urlList);

        // 设置请求参数
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add(URL_PARAM_NAME, urls);

        // 创建请求实体
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);

        // 发送请求
        ResponseEntity<String> response = restTemplate.postForEntity(CDN_SYNC_URL, request, String.class);
        HttpStatus statusCode = response.getStatusCode();
        String body = response.getBody();
        if (statusCode != HttpStatus.OK) {
            log.error("SyncToYoudaoCDN error,urls: {} ,statusCode: {}, body: {}", urls, statusCode, body);
        }
        return statusCode == HttpStatus.OK;
    }

    /**
     * 使用示例
     * curl 'http://updatecdn.iyoudao.net/sync.php' -H 'User-Agent: Mozilla/5.0 (X11; Fedora; L Firefox/86.0' -H 'Accept: application/json' -H 'Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2' --compressed -H 'X-Requested-With: XMLHttpRequest' -H 'Content-Type: application/x-www-form-urlencoded' -H 'Origin: http://updatecdn.iyoudao.net' -H 'Connection: keep-alive' -H 'Referer: http://updatecdn.iyoudao.net/' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' --data 'url=adpublish.ydstatic.com/youxuan/dev/material/86/84c6058cea999b0a737b38e43437fe68.txt;adpublish.ydstatic.com/youxuan/dev/material/28/be282b4494ec7156ea4b84856e9f3b78.css'
     */
    public static void main(String[] args) {
        List<String> urlList = Arrays.asList(
                "adpublish.ydstatic.com/youxuan/dev/material/86/84c6058cea999b0a737b38e43437fe68.txt",
                "adpublish.ydstatic.com/youxuan/dev/material/28/be282b4494ec7156ea4b84856e9f3b78.css"
        );
        System.out.println("Response: " + syncToYoudaoCDN(urlList));
    }
} 
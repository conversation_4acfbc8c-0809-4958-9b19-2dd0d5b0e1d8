package outfox.ead.youxuan.util;


import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import outfox.ead.youxuan.constants.ContentMarketingConstants;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.entity.UserDetail;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class MailTemplateUtil {
    private static final String VERIFY_NOTICE_MAIL_TEMPLATE;
    private static final String ACTIVE_REGISTER_MAIL_TEMPLATE;

    private static final String VERIFY_USER_NAME_MARCO = "{{userName}}";

    private static final String VERIFY_ROLE_NAME_MACRO = "{{roleName}}";

    private static final String VERIFY_USER_DETAIL_NAME_MARCO = "{{userDetail}}";

    private static final String ACTIVE_URL = "\\{\\{ACTIVE_URL}}";

    private static final String EMAIL = "{{EMAIL}}";

    public static final String BUTTON_TEXT = "{{buttonText}}";

    private static final String BREAK_LINE_TAG = "<br>";

    static {
        try {
            VERIFY_NOTICE_MAIL_TEMPLATE = IOUtils.toString(new ClassPathResource("/mail/verifyNoticeMailTemplate.html").getInputStream(), StandardCharsets.UTF_8);
            ACTIVE_REGISTER_MAIL_TEMPLATE = IOUtils.toString(new ClassPathResource("/mail/activeRegisterTemplate.html").getInputStream(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String getActiveRegisterMailContent(String email ,String activeUrl, String buttonText) {
        return ACTIVE_REGISTER_MAIL_TEMPLATE.replaceAll(ACTIVE_URL, activeUrl).replace(EMAIL, email).replace(BUTTON_TEXT, buttonText);
    }

    public static String getVerifyEmailContent(UserDetail userDetail, String roleKey) {
        RoleEnum roleEnum = RoleEnum.roleKeyOf(roleKey);
        String roleName = roleEnum.getName();
        StringBuilder parsedUserDetail = parseUserDetailCommon(userDetail);
        if (RoleEnum.SPONSOR == roleEnum) {
            String industryName = "无";
            if (Objects.nonNull(userDetail.getIndustry())) {
                industryName = IndustryUtil.getById(userDetail.getIndustry()).getName();
            }
            parsedUserDetail.append("行业信息: ").append(industryName).append(BREAK_LINE_TAG);
        } else if (RoleEnum.BRAND_KOL == roleEnum) {
            roleName = ContentMarketingConstants.BRAND_KOL_ALIAS;
        }
        return VERIFY_NOTICE_MAIL_TEMPLATE.replace(VERIFY_USER_NAME_MARCO, userDetail.getNickname())
                .replace(VERIFY_ROLE_NAME_MACRO, roleName)
                .replace(VERIFY_USER_DETAIL_NAME_MARCO, parsedUserDetail);
    }



    /**
     * 拼接出公共的审核信息
     * @param userDetail
     * @return
     */
    private static StringBuilder parseUserDetailCommon(UserDetail userDetail) {
        return new StringBuilder()
                .append("优选昵称: ").append(userDetail.getNickname()).append(BREAK_LINE_TAG)
                .append("优选ID: ").append(userDetail.getUserId()).append(BREAK_LINE_TAG)
                .append("公司名称: ").append(userDetail.getCompanyName()).append(BREAK_LINE_TAG)
                .append("联系人: ").append(userDetail.getName()).append(BREAK_LINE_TAG)
                .append("联系电话: ").append(userDetail.getPhone()).append(BREAK_LINE_TAG)
                .append("联系邮箱: ").append(userDetail.getEmail()).append(BREAK_LINE_TAG);
    }
}

package outfox.ead.youxuan.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.ffmpeg.ffprobe;
import org.bytedeco.javacpp.Loader;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class FfprobeUtil {

    private static String ffprobe;

    static {
        try {
            ffprobe = Loader.load(ffprobe.class);
        } catch (Throwable e) {
            log.error("load ffprobe error", e);
        }
    }

    /**
     * 获取视频编码 Profile 和 Level，格式：Profile@Level，如：Main@L4.1
     *
     * @param videoPath 视频文件路径
     * @return 视频编码 Profile 和 Level
     */
    public static String getVideoProfile(String videoPath) {
        Process process = null;
        try {
            if (ffprobe == null) {
                throw new NullPointerException("ffprobe not found");
            }

            // ffprobe -v quiet -select_streams v:0 -show_entries stream=profile,level -of json input.mp4
            ProcessBuilder pb = new ProcessBuilder(
                    ffprobe,
                    "-v", "quiet",
                    "-select_streams", "v:0",
                    "-show_entries", "stream=profile,level",
                    "-of", "json",
                    videoPath);
            // 将 InputStream 和 ErrorStream 合并到一个 Stream 中，减少一个线程
            pb.redirectErrorStream(true);
            log.info("run ffprobe command: {}", StringUtils.join(pb.command(), " "));

            process = pb.start();
            String stdout = IOUtils.toString(process.getInputStream(), StandardCharsets.UTF_8);
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                log.error("ffprobe returned non-zero exit status, check stdout: {}", stdout);
                throw new RuntimeException("ffprobe returned non-zero exit status, check stdout: " + stdout);
            }

            Map<String, Object> stdoutMap = JacksonUtil.jsonToMap(stdout);
            Map<String, Object> stream = ((List<Map<String, Object>>) stdoutMap.get("streams")).get(0);

            String profile = (String) stream.get("profile");
            // Main@L6 -> profile: Main  level: 60
            // Main@L4.1 -> profile: Main  level: 41
            // https://en.wikipedia.org/wiki/Advanced_Video_Coding#Levels
            int level = (int) stream.get("level");
            if (level % 10 == 0) {
                return profile + "@L" + level / 10;
            }
            int tensDigit = level / 10;
            int unitsDigit = level % 10;
            return profile + "@L" + tensDigit + "." + unitsDigit;
        } catch (Exception e) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "获取视频编码格式配置失败", e);
        } finally {
            if (Objects.nonNull(process)) {
                try {
                    process.getErrorStream().close();
                    process.getInputStream().close();
                    process.getOutputStream().close();
                    process.destroy();
                } catch (IOException ignored) {
                }
            }
        }
    }

}

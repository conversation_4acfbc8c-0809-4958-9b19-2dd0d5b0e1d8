package outfox.ead.youxuan.util;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.TreeNode;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.node.TextNode;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.io.IOException;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 解析多种日期格式
 */
public class MultiDateDeserializer extends StdDeserializer<LocalDateTime> {
    private static final long serialVersionUID = 1L;

    private static final int UTC_TIME_OFFSET = 8;

    private static final DateTimeFormatter[] DATE_FORMATTERS = new DateTimeFormatter[]{
            DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSS+08:00").withZone(DateTimeZone.forID("Asia/Shanghai")),
            DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ").withZoneUTC()
    };

    public MultiDateDeserializer(Class<?> vc) {
        super(vc);
    }

    public MultiDateDeserializer() {
        this(null);
    }

    @Override
    public LocalDateTime deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        TreeNode treeNode = jp.getCodec().readTree(jp);
        TextNode textNode = treeNode instanceof TextNode
                ? (TextNode) treeNode
                : ((TextNode) treeNode.get(""));
        final String date = textNode.textValue();

        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                if (DateTimeZone.UTC.equals(formatter.getZone())) {
                    return LocalDateTime.parse(date, formatter).plusHours(UTC_TIME_OFFSET);
                } else {
                    return LocalDateTime.parse(date, formatter);
                }
            } catch (Exception ignored) {
            }
        }
        throw new JsonParseException(jp, "Unparseable date: \"" + date + "\". Supported formats: " +
                Arrays.stream(DATE_FORMATTERS).map(DateTimeFormatter::toString).collect(Collectors.joining("; ")));
    }
}

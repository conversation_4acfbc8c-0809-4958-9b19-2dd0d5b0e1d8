package outfox.ead.youxuan.util;

import org.apache.commons.lang3.ObjectUtils;
import outfox.ead.youxuan.web.ad.controller.dto.DateSlot;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.Temporal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/2 11:42
 */
public class LocalDateUtil {
    public static final ZoneId CHINA = ZoneId.of("GMT+08:00");

    /**
     * 是否是昨天
     *
     * @param pre  preDate
     * @param post postDate
     * @return true 是
     */
    public static Boolean isYesterday(Temporal pre, Temporal post) {
        return LocalDate.from(pre).plusDays(1).compareTo(LocalDate.from(post)) == 0;
    }

    /**
     * 离散时间聚合时间段
     *
     * @param dates 离散时间列表
     * @return 时间段列表
     */
    public static List<DateSlot> aggregationDate(List<LocalDate> dates) {
        Collections.sort(dates);
        List<DateSlot> dateSlots = new ArrayList<>();
        for (int i = 0; i < dates.size(); i++) {
            for (int j = i + 1; j <= dates.size(); j++) {
                if (j == dates.size() || !isYesterday(dates.get(j - 1), dates.get(j))) {
                    DateSlot dateSlot = new DateSlot();
                    dateSlot.setStartDate(dates.get(i));
                    dateSlot.setEndDate(dates.get(j - 1));
                    dateSlots.add(dateSlot);
                    i = j - 1;
                    break;
                }
            }
        }
        return dateSlots;
    }

    public static LocalDateTime getFirstTimeOfDay(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.MIN);
    }

    /**
     * 判断时间是否在区间内
     *
     * @param time      判断的时间
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return true-在区间内
     */
    public static boolean inTheInterval(LocalDateTime time, LocalDateTime startTime, LocalDateTime endTime) {
        if (ObjectUtils.allNotNull(startTime, endTime)) {
            return time.compareTo(startTime) >= 0 && time.compareTo(endTime) <= 0;
        } else if (Objects.nonNull(startTime)) {
            return time.compareTo(startTime) >= 0;
        } else if (Objects.nonNull(endTime)) {
            return time.compareTo(endTime) <= 0;
        } else {
            return true;
        }
    }

    public static LocalDateTime getEarlierTime(LocalDateTime a, LocalDateTime b) {
        return a.isBefore(b) ? a : b;
    }

    public static LocalDateTime getLaterTime(LocalDateTime a, LocalDateTime b) {
        return a.isBefore(b) ? b : a;
    }
}

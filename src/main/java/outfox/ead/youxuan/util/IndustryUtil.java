package outfox.ead.youxuan.util;

import com.opencsv.bean.CsvToBeanBuilder;
import outfox.ead.youxuan.entity.Area;
import outfox.ead.youxuan.web.ad.controller.dto.Industry;

import java.io.InputStreamReader;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年02月14日 11:20 上午
 */
public class IndustryUtil {
    private static final Collection<Industry> INDUSTRY_LIST;
    private static final Map<String, Industry> NAME2INDUSTRY_MAP;
    private static final Map<Integer, Industry> ID2INDUSTRY_MAP;

    static {
        List<Industry> industryList = new CsvToBeanBuilder(new InputStreamReader(Area.class.getResourceAsStream("/industry.csv")))
                .withType(Industry.class)
                .build()
                .parse();
        NAME2INDUSTRY_MAP = Collections.unmodifiableMap(industryList.stream().collect(Collectors.toMap(Industry::getName, Function.identity())));
        ID2INDUSTRY_MAP = Collections.unmodifiableMap(industryList.stream().collect(Collectors.toMap(Industry::getId, Function.identity())));

        Map<Integer, Industry> id2Industry = new HashMap<>();
        Map<Integer, Industry> topId2Industry = new HashMap<>();
        Map<Integer, List<Industry>> parentId2Industry = new HashMap<>();

        for (Industry industry : industryList) {
            id2Industry.put(industry.getId(), industry);
            if (industry.getParentId().equals(0)) {
                topId2Industry.put(industry.getId(), industry);
            } else {
                parentId2Industry.computeIfAbsent(industry.getParentId(), k -> new ArrayList<>()).add(industry);
            }
        }

        for (Map.Entry<Integer, Industry> entry : id2Industry.entrySet()) {
            entry.getValue().setIndustryList(parentId2Industry.get(entry.getKey()));
        }
        INDUSTRY_LIST = Collections.unmodifiableCollection(topId2Industry.values());
    }

    public static Collection<Industry> getIndustryList() {
        return INDUSTRY_LIST;
    }

    public static Map<String, Industry> getName2IndustryMap() {
        return NAME2INDUSTRY_MAP;
    }

    private static final Industry emptyIndustry = new Industry();

    public static Industry getById(Integer id) {
        return ID2INDUSTRY_MAP.getOrDefault(id, emptyIndustry);
    }

    public static Industry getByIdOrDefault(Integer id, Industry industry) {
        return ID2INDUSTRY_MAP.getOrDefault(id, industry);
    }
}

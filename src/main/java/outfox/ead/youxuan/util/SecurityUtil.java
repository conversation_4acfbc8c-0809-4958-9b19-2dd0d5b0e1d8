package outfox.ead.youxuan.util;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.web.ad.controller.bo.LoginUser;

import java.util.Arrays;
import java.util.Objects;

import static outfox.ead.youxuan.constants.ResponseType.TOKEN_INFO_ERROR;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
public class SecurityUtil {
    /**
     * 用户ID
     **/
    public static Long getUserId() {
        try {
            return getLoginUser().getId();
        } catch (Exception e) {
            return null;
        }
    }

    public static Long getUserIdIfPresent() {
        try {
            return getLoginUser().getId();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getUsername();
        } catch (Exception e) {
            throw new CustomException(TOKEN_INFO_ERROR, "获取用户账户异常");
        }
    }

    /**
     * 尽量在controller层调用该方法，获取登录用户信息<p>
     * 因为在controller层调用该方法时，spring security的上下文已经初始化完毕，所以可以直接获取用户信息<p>
     * service可能是由内部服务直接调用，导致该值为空，发生错误
     *
     * @return 登录用户信息
     */
    public static Role getCurrentRole() {
        return getLoginUser().getCurrentRole();
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new CustomException(TOKEN_INFO_ERROR, "用户未登录");
        }
    }

    public static Object getPrincipal() {
        try {
            return getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new CustomException(TOKEN_INFO_ERROR, "用户未登录");
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 是否为管理员
     *
     * @return 结果
     */
    public static boolean isAdmin() {
        return getLoginUser().isAdmin();
    }

    public static boolean hasRole(RoleEnum role) {
        LoginUser loginUser = SecurityUtil.getLoginUser();
        if (Objects.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getRoles())) {
            return false;
        }
        return isAdmin()
                || loginUser
                .getRoles()
                .stream()
                .anyMatch(
                        r -> r.getRoleKey().equals(role.getRoleKey())
                );
    }

    public static Boolean checkCurrentRole(RoleEnum roleEnum) {
        return roleEnum.getRoleKey().equals(SecurityUtil.getCurrentRole().getRoleKey());
    }

    public static Boolean checkCurrentRole(RoleEnum[] roles) {
        LoginUser loginUser = SecurityUtil.getLoginUser();
        if (roles.length == 0 || Objects.isNull(loginUser)) {
            return false;
        }
        return loginUser.isAdmin()
                || Arrays.stream(roles).anyMatch(roleEnum -> roleEnum.getRoleKey().equals(SecurityUtil.getCurrentRole() == null ? null : SecurityUtil.getCurrentRole().getRoleKey()));
    }

    public static boolean hasAnyRole(RoleEnum[] roles) {
        if (roles.length == 0) {
            return false;
        }
        LoginUser loginUser = SecurityUtil.getLoginUser();
        if (Objects.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getRoles())) {
            return false;
        }
        return Arrays.stream(roles).anyMatch(SecurityUtil::hasRole);
    }

    public static boolean isLogin() {
        return getUserIdIfPresent() != null;
    }
}

package outfox.ead.youxuan.util;

import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

public class MailUtil {
    public static final String FROM_USER_OUTER = "<EMAIL>";
    public static final String FROM_USER_INNER = "<EMAIL>";
    public static final String FROM_NAME = "有道优选";
    private static final String MAIL_SERVER_HOST = "ydsmtp.rd.netease.com";
    private static final int MAIL_SERVER_PORT = 25;
    private static final JavaMailSenderImpl JAVA_MAIL_SENDER = new JavaMailSenderImpl();

    public static final String RD_MAIL_SUFFIX = "rd.netease.com";

    public static final String CORP_MAIL_SUFFIX = "corp.netease.com";

    static {
        JAVA_MAIL_SENDER.setHost(MAIL_SERVER_HOST);
        JAVA_MAIL_SENDER.setPort(MAIL_SERVER_PORT);
        Properties javaMailProperties = new Properties();
        JAVA_MAIL_SENDER.setJavaMailProperties(javaMailProperties);
        JAVA_MAIL_SENDER.setDefaultEncoding("UTF-8");
    }

    /**
     * 获取JavaMailSenderImpl
     */
    private static JavaMailSenderImpl getMailSender() {
        return JAVA_MAIL_SENDER;
    }

    /**
     * 发送邮件
     *
     * @param toUser  收件方的邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     */
    public static void sendMail(String toUser, String subject, String content)
            throws MessagingException, UnsupportedEncodingException {
        JavaMailSenderImpl senderImpl = getMailSender();

        String fromUser;
        if (isInnerDomain(extractDomain(toUser))) {
            fromUser = FROM_USER_INNER;
        } else {
            fromUser = FROM_USER_OUTER;
        }

        MimeMessage mailMessage = getMailMessage(senderImpl, Collections.singletonList(toUser), fromUser, FROM_NAME, subject, content);
        senderImpl.send(mailMessage);
    }

    public static void sendMail(List<String> toUser, String fromUser, String fromName, String subject, String content)
            throws MessagingException, UnsupportedEncodingException {
        JavaMailSenderImpl senderImpl = getMailSender();
        MimeMessage mailMessage = getMailMessage(senderImpl, toUser, fromUser, fromName, subject, content);
        senderImpl.send(mailMessage);
    }

    private static MimeMessage getMailMessage(JavaMailSenderImpl senderImpl, List<String> toUser, String fromUser,
                                              String fromName, String subject, String content) throws MessagingException, UnsupportedEncodingException {
        MimeMessage mailMessage = senderImpl.createMimeMessage();
        MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage, false, "UTF-8");
        messageHelper.setTo(toUser.toArray(new String[0]));
        messageHelper.setFrom(fromUser, fromName);
        messageHelper.setSubject(subject);
        messageHelper.setText(content, true);
        return mailMessage;
    }

    private static String extractDomain(String emailAddress) {
        return emailAddress.substring(emailAddress.indexOf("@") + 1);
    }

    private static Boolean isInnerDomain(String mailDomain) {
        return RD_MAIL_SUFFIX.equals(mailDomain) || CORP_MAIL_SUFFIX.equals(mailDomain);
    }
}

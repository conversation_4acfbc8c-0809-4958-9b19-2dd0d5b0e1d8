package outfox.ead.youxuan.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年02月26日 10:28 下午
 */
@Slf4j
public class ExcelUtil {
    public static void writeExcel(HttpServletResponse response,
                                  List<?> data,
                                  String fileName,
                                  String sheetName,
                                  Class clazz) throws Exception {
        EasyExcel.write(getOutputStream(fileName, response), clazz)
                .sheet(sheetName)
                .doWrite(data);
    }

    private static OutputStream getOutputStream(String fileName, HttpServletResponse response) throws Exception {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        return response.getOutputStream();
    }

    private static final int EXCEL_MAX_ROW = 1000;

    public static <T> List<T> read(MultipartFile file, Class<T> clazz) throws IOException {
        List<T> data = EasyExcel
                .read(new BufferedInputStream(file.getInputStream()))
                .head(clazz)
                .sheet()
                .doReadSync();
        data = data.stream().filter(ExcelUtil::isNotEmptyRow).collect(Collectors.toList());
        if (data.size() > EXCEL_MAX_ROW) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "excel数据不得超过" + EXCEL_MAX_ROW + "行");
        }
        return data;
    }

    public static <T> boolean isNotEmptyRow(T o) {
        try {
            Class<?> clazz = o.getClass();
            if (fieldHasValue(o, clazz)) return true;
        } catch (IllegalAccessException e) {
            log.warn("Excel Parse Error", e);
            return true;
        }
        return false;
    }

    private static <T> boolean fieldHasValue(T o, Class<?> clazz) throws IllegalAccessException {
        if (clazz==null) {
            return false;
        }
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(ExcelProperty.class)
                    && Objects.nonNull(field.get(o))
                    && StringUtils.isNotBlank(field.get(o).toString())) {
                return true;
            }
        }
        return fieldHasValue(o,clazz.getSuperclass());
    }
}

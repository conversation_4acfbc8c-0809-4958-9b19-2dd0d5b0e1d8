package outfox.ead.youxuan.util;

/**
 * <AUTHOR>
 * @date 2022年02月17日 5:13 下午
 */


import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 功能描述：
 * <p>
 * 版权所有：网易科技
 * <p>
 * 未经本公司许可，不得以任何方式复制或使用本程序任何部分
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0 created on: 2009
 */

public class AESUtil {

    public static String getEncryptResultNoResultStr(String keySource,
                                                     String content) throws Exception {
        return AESUtil.byte2hex(AESUtil.encrypt(content
                .getBytes(StandardCharsets.UTF_8), AESUtil.hex2byte(keySource)));
    }

    public static String getDecryptResultNoResultStr(String keySource,
                                                     String content) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        return new String(AESUtil.decrypt(
                AESUtil.hex2byte(content), AESUtil.hex2byte(keySource)));
    }

    public static byte[] encrypt(byte[] src, byte[] kv) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        SecretKey key = new SecretKeySpec(kv, "AES");
        Cipher cp = Cipher.getInstance("AES");
        cp.init(Cipher.ENCRYPT_MODE, key);
        return cp.doFinal(src);
    }

    public static byte[] decrypt(byte[] src, byte[] kv) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        SecretKey key = new SecretKeySpec(kv, "AES");
        Cipher cp = Cipher.getInstance("AES");
        cp.init(Cipher.DECRYPT_MODE, key);
        return cp.doFinal(src);
    }

    public static String byte2hex(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp = "";
        for (byte value : b) {
            stmp = (Integer.toHexString(value & 0XFF));
            if (stmp.length() == 1) {
                hs.append("0").append(stmp);
            } else {
                hs.append(stmp);
            }
        }
        return hs.toString().toUpperCase();
    }

    public static byte[] hex2byte(String s) {
        byte[] b = s.getBytes();
        if ((b.length % 2) != 0) {
            throw new IllegalArgumentException("??????????");
        }
        byte[] b2 = new byte[b.length / 2];
        for (int n = 0; n < b.length; n += 2) {
            String item = new String(b, n, 2);
            b2[n / 2] = (byte) Integer.parseInt(item, 16);
        }
        return b2;
    }

    public static void main(String[] args) {
        // AES key
        String key = "59FDA97DC9AE2760ACEC6F85C014B798";
        try {
            // 待加密的字符串
            String val = "123456";
            // 加密
            String encryptStr = AESUtil.getEncryptResultNoResultStr(key, val);
            System.out.println(encryptStr);
            // 解密
            String decryptStr = new String(AESUtil.decrypt(
                    AESUtil.hex2byte(encryptStr), AESUtil.hex2byte(key)),
                    StandardCharsets.UTF_8);
            System.out.println(decryptStr);
        } catch (Exception e) {
        }
    }
}


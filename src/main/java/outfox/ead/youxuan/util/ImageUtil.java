package outfox.ead.youxuan.util;


import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022年12月02日 17:06
 */
public class ImageUtil {
    public static Optional<FastImageInfo> parse(String url) {
        try {
            FastImageInfo fastImageInfo = new FastImageInfo(url);
            return Optional.of(fastImageInfo);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
}

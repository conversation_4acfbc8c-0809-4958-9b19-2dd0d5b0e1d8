package outfox.ead.youxuan.util;

import com.opencsv.bean.CsvToBeanBuilder;
import outfox.ead.youxuan.entity.Area;
import outfox.ead.youxuan.web.ad.controller.dto.CrowdLabel;

import java.io.InputStreamReader;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年02月14日 11:20 上午
 */
public class CrowdLabelUtil {
    private static final Collection<CrowdLabel> CROWD_LABELS;

    static {
        List<CrowdLabel> crowdLabels = (List<CrowdLabel>) new CsvToBeanBuilder(new InputStreamReader(Area.class.getResourceAsStream("/CrowdLabel.csv")))
                .withType(CrowdLabel.class)
                .build()
                .parse().stream().filter(crowdLabel -> ((CrowdLabel) crowdLabel).getDisplayable() == 1).collect(Collectors.toList());

        Map<Integer, CrowdLabel> id2CrowdLabel = new HashMap<>();
        Map<Integer, CrowdLabel> topId2CrowdLabel = new HashMap<>();

        for (CrowdLabel crowdLabel : crowdLabels) {
            id2CrowdLabel.put(crowdLabel.getId(), crowdLabel);
            if (crowdLabel.getParentId().equals(0)) {
                topId2CrowdLabel.put(crowdLabel.getId(), crowdLabel);
            }
        }
        crowdLabels.stream()
                .filter(c -> !c.getParentId().equals(0))
                .forEach(c -> Optional.ofNullable(id2CrowdLabel.get(c.getParentId())).ifPresent(cr -> cr.getCrowdLabelList().add(c)));
        CROWD_LABELS = topId2CrowdLabel.values();
    }

    public static Collection<CrowdLabel> getCrowdLabels() {
        return CROWD_LABELS;
    }
}

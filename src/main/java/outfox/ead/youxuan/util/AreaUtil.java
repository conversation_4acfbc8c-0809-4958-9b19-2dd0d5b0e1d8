package outfox.ead.youxuan.util;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.google.common.base.Functions;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.Area;

import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.Constants.LEVEL_INLAND;
import static outfox.ead.youxuan.constants.Constants.LEVEL_INTERNATION;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/22 19:29
 */
@Slf4j
public class AreaUtil {
    /**
     * 所有的地域，包括各直辖市下的各个区
     */
    private static final List<Area> ALL_INLAND_CITIES = new ArrayList<>();
    private static Map<String, Area> INLAND_NAME_2_AREA;
    private static final List<Area> INLAND_CITIES = new ArrayList<>();
    @Getter
    public static Map<Integer, Area> ALL_INLAND_CITY_MAP;
    private static final List<Area> INTERNATION_CITIES = new ArrayList<>();
    public static final List<Integer> ALL_INLAND_SUB_CITY_IDS;
    public static final List<Integer> ALL_INTERNATION_SUB_CITY_IDS;
    public static final List<Area> ALL_INLAND_SUB_CITY;
    public static final List<Area> ALL_INTERNATION_SUB_CITY;

    static {
        populateAreas(LEVEL_INLAND, ALL_INLAND_CITIES, true);
        populateAreas(LEVEL_INLAND, INLAND_CITIES, false);
        populateAreas(LEVEL_INTERNATION, INTERNATION_CITIES, false);
        ALL_INLAND_SUB_CITY_IDS = getSubCityIds(LEVEL_INLAND);
        ALL_INTERNATION_SUB_CITY_IDS = getSubCityIds(LEVEL_INTERNATION);
        ALL_INLAND_SUB_CITY = getSubCity(LEVEL_INLAND);
        ALL_INTERNATION_SUB_CITY = getSubCity(LEVEL_INTERNATION);
    }

    private static List<Area> getSubCity(Integer level) {
        List<Area> areas = new ArrayList<>();
        if (level.equals(LEVEL_INLAND)) {
            recursionPopulateSubCity(ALL_INLAND_CITIES, areas);
        } else {
            recursionPopulateSubCity(INTERNATION_CITIES, areas);
        }
        return areas;
    }

    private static void recursionPopulateSubCity(List<Area> areaCities, List<Area> areas) {
        for (Area areaCity : areaCities) {
            if (Objects.isNull(areaCity.getCities()) || areaCity.getCities().isEmpty()) {
                areas.add(areaCity);
            } else {
                recursionPopulateSubCity(areaCity.getCities(), areas);
            }
        }
    }

    /**
     * @param level 国内/海外
     * @param all   是否包含直辖市下的区
     */
    private static void populateAreas(Integer level, List<Area> areas, boolean all) {
        List<Area> areaList = getArea(level, all);
        Map<Integer, List<Area>> map = new HashMap<>();
        for (Area area : areaList) {
            map.computeIfAbsent(area.getParentId(), x-> new ArrayList<>()).add(area);
        }
        for (Area area : areaList) {
            if (map.containsKey(area.getId())) {
                area.getCities().addAll(map.get(area.getId()));
            }
        }
        areas.addAll(map.get(0));
    }

    /**
     * 获取省份 -- 城市 或者 洲 -- 国家 列表
     * 由于Country类完全继承Area,使用hibernate查询时，返回的Area对象会包含Country对象。
     * 为了保持通用，统一从xml文件中读取
     *
     * @param all 是否包含直辖市下的区
     */
    private static List<Area> getArea(Integer level, boolean all) {
        List<Area> list;
        list = getAreaFromBackup(level, all);
        log.info(level + " list size is : " + list.size());
        return list;
    }

    /**
     * 判断是否为主流省市
     *
     * @param areaName 地区名称
     */
    private static boolean isFirstArea(String areaName) {
        return null == areaName || !areaName.startsWith("香港") && !areaName.startsWith("澳门") && !areaName.startsWith("台湾") && !areaName.startsWith("其他");
    }


    /**
     * 从备份文件中获取省份，城市列表
     *
     * @param level 国内/海外
     * @param all   是否包含直辖市下的区
     */
    private static List<Area> getAreaFromBackup(Integer level, boolean all) {
        log.debug("Use xml file to get Area Info!");
        List<Area> tList = new ArrayList<>();

        try {
            XmlMapper xmlMapper = new XmlMapper();
            Data data = xmlMapper.readValue(getInputStream(level), Data.class);
            List<Area> list = data.getRows();
            if (level.equals(LEVEL_INTERNATION)) {
                tList.addAll(list);
            } else {
                if (Objects.isNull(ALL_INLAND_CITY_MAP)) {
                    ALL_INLAND_CITY_MAP = Collections.unmodifiableMap(list.stream().collect(Collectors.toMap(Area::getId, Function.identity())));
                }
                if (all) {
                    tList.addAll(list);
                    INLAND_NAME_2_AREA = list.stream().collect(Collectors.toMap(Area::getName, Functions.identity()));
                } else {
                    List<Integer> municipalityIds = list.stream().filter(x -> x.getParentId() == 0)
                            .filter(x -> isFirstArea(x.getName()))
                            .filter(x -> x.getName().endsWith("市"))
                            .map(Area::getId).collect(Collectors.toList());
                    tList.addAll(
                            list.stream().filter(x -> ! municipalityIds.contains(x.getParentId())).collect(Collectors.toList())
                    );
                }
            }
            log.info("The tlist size is " + tList.size());

        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return tList;
    }

    private static InputStream getInputStream(Integer level) {
        if (LEVEL_INLAND.equals(level)) {
            return Area.class.getResourceAsStream("/area/area_value.xml");
        } else {
            return Area.class.getResourceAsStream("/area/country_value.xml");
        }
    }

    /**
     * 获得国内或者海外所有城市tree结构
     *
     * @param level 国内/海外
     * @param all   是否包含直辖市的区
     * @return 城市列表
     */
    public static List<Area> getAllCities(Integer level, boolean all) {
        if (level.equals(LEVEL_INLAND)) {
            if (all) {
                return ALL_INLAND_CITIES;
            } else {
                return INLAND_CITIES;
            }
        } else {
            return INTERNATION_CITIES;
        }
    }

    /**
     * 获得id列表中的最低级城市id
     *
     * @param level   国内海外区分
     * @param cityIds 需要的城市id列表
     * @return 城市id列表
     */
    public static Set<Integer> getSubCityIds(Integer level, Collection<Integer> cityIds) {
        Set<Integer> ids = new HashSet<>();
        if (level.equals(LEVEL_INLAND)) {
            getCityIds(INLAND_CITIES, cityIds, ids);
        } else {
            getCityIds(INTERNATION_CITIES, cityIds, ids);
        }
        return ids;
    }

    private static void getCityIds(Collection<Area> areaCities, Collection<Integer> subCityIds, Collection<Integer> ids) {
        for (Area city : areaCities) {
            if (subCityIds.contains(city.getParentId())) {
                subCityIds.add(city.getId());
            }
        }
        for (Area areaCity : areaCities) {
            if (subCityIds.contains(areaCity.getId())) {
                if (Objects.isNull(areaCity.getCities()) || areaCity.getCities().isEmpty()) {
                    ids.add(areaCity.getId());
                } else {
                    populateIds(areaCity.getCities(), ids);
                }
            }
        }
    }

    private static void populateIds(Collection<Area> cities, Collection<Integer> ids) {
        for (Area city : cities) {
            if (Objects.isNull(city.getCities()) || city.getCities().isEmpty()) {
                ids.add(city.getId());
            } else {
                populateIds(city.getCities(), ids);
            }
        }
    }


    /**
     * 获得所有的三级城市
     *
     * @param level 国内海外
     * @return 所有无子级城市的id
     */
    public static List<Integer> getSubCityIds(Integer level) {
        List<Integer> ids = new ArrayList<>();
        if (level.equals(LEVEL_INLAND)) {
            recursionPopulateSubCityIds(INLAND_CITIES, ids);
        } else {
            recursionPopulateSubCityIds(INTERNATION_CITIES, ids);
        }
        return ids;
    }

    private static void recursionPopulateSubCityIds(List<Area> areaCities, List<Integer> ids) {
        for (Area areaCity : areaCities) {
            if (Objects.isNull(areaCity.getCities()) || areaCity.getCities().isEmpty()) {
                ids.add(areaCity.getId());
            } else {
                recursionPopulateSubCityIds(areaCity.getCities(), ids);
            }
        }
    }

    public static List<Area> getAllSubCities(Integer level) {
        if (level.equals(LEVEL_INLAND)) {
            return ALL_INLAND_SUB_CITY;
        } else {
            return ALL_INTERNATION_SUB_CITY;
        }
    }

    public static Map<Integer, Area> getAllInlandCityMap() {
        return ALL_INLAND_CITY_MAP;
    }

    public static Set<Integer> getInlandIdByName(Set<String> names) {
        Set<Integer> res = new HashSet<>();
        for (String name : names) {
            Area area = INLAND_NAME_2_AREA.get(name);
            if (area == null) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, name + " :不存在该城市");
            }
            res.add(area.getId());
        }
        return res;
    }
}

@lombok.Data
class Data {
    @JacksonXmlProperty(localName = "row")
    @JacksonXmlElementWrapper(useWrapping = false)
    List<Area> rows;
}
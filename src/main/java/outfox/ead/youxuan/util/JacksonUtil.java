package outfox.ead.youxuan.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import outfox.ead.youxuan.core.exception.CustomException;

import java.util.List;
import java.util.Map;

import static outfox.ead.youxuan.constants.ResponseType.CONVERT_DATE_ERROR;

/**
 * jackson 转换类
 *
 * <AUTHOR> from Jackson
 */
public class JacksonUtil {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(Feature.ALLOW_COMMENTS, true)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);
    private static final ObjectWriter WRITER;
    private static final ObjectWriter PRETTY_WRITER;

    static {
        WRITER = OBJECT_MAPPER.writer();
        PRETTY_WRITER = OBJECT_MAPPER.writerWithDefaultPrettyPrinter();
    }

    private JacksonUtil() {
    }

    public static String toJsonPrettyString(Object value) {
        if (value == null) {
            return null;
        }
        try {
            return PRETTY_WRITER.writeValueAsString(value);
        } catch (Exception var2) {
            throw new CustomException(CONVERT_DATE_ERROR, var2);
        }
    }

    public static String toJsonString(Object value) {
        if (value == null) {
            return null;
        }
        try {
            return WRITER.writeValueAsString(value);
        } catch (Exception var2) {
            throw new CustomException(CONVERT_DATE_ERROR, var2);
        }
    }

    public static <T> T toObject(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            throw new CustomException(CONVERT_DATE_ERROR, e);
        }
    }

    /**
     * json字符串转成list
     *
     * @param json 字符串
     * @param cls  类型
     * @param <T>  泛型
     * @return 转换结果
     */
    public static <T> List<T> jsonToList(String json, Class<T> cls) {
        try {
            JavaType t = OBJECT_MAPPER.getTypeFactory().constructParametricType(List.class, cls);
            return OBJECT_MAPPER.readValue(json, t);
        } catch (Exception e) {
            throw new CustomException(CONVERT_DATE_ERROR, e);
        }
    }

    /**
     * json字符串转成map
     *
     * @param json json字符串
     * @return Map 对象
     */
    public static Map<String, Object> jsonToMap(String json) {
        try {
            JavaType t = OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, String.class, Object.class);
            return OBJECT_MAPPER.readValue(json, t);
        } catch (JsonProcessingException e) {
            throw new CustomException(CONVERT_DATE_ERROR, e);
        }
    }


    /**
     * object to map
     * null values will convert
     *
     * @param o object
     * @return map
     */
    public static Map<String, Object> objectToMap(Object o) {
        try {
            JavaType t = OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, String.class, Object.class);
            return OBJECT_MAPPER.convertValue(o, t);
        } catch (Exception e) {
            throw new CustomException(CONVERT_DATE_ERROR, e);
        }
    }


    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }

    public static ObjectWriter getWriter() {
        return WRITER;
    }

    public static ObjectWriter getPrettyWriter() {
        return PRETTY_WRITER;
    }

    public static <T> T deepCopy(Object o, Class<T> clazz) {
        return toObject(toJsonString(o), clazz);
    }
}

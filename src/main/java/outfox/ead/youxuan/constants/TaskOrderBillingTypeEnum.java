package outfox.ead.youxuan.constants;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR> Li
 */
public enum TaskOrderBillingTypeEnum {
    OTHER(0, "other"),
    CPM(1, "CPM"),
    CPC(2, "CPC"),
    CPA(3, "CPA"),
    CPS(4, "CPS");

    private static Map<Integer, TaskOrderBillingTypeEnum> VALUE_MAP;

    static {
        final Map<Integer, TaskOrderBillingTypeEnum> tmpMap = Maps.newHashMap();
        for (final TaskOrderBillingTypeEnum en : TaskOrderBillingTypeEnum.values()) {
            tmpMap.put(en.code, en);
        }
        VALUE_MAP = ImmutableMap.copyOf(tmpMap);
    }

    public static TaskOrderBillingTypeEnum getEnum(Integer code) {
        return VALUE_MAP.getOrDefault(code, OTHER);
    }

    private final int code;

    private final String description;

    TaskOrderBillingTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}

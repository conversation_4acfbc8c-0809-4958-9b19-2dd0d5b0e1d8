package outfox.ead.youxuan.constants;

/**
 * <AUTHOR>
 */

public enum RegisterRoleEnum {
    /**
     * 广告主
     */
    SPONSOR("sponsor", "广告主"),
    /**
     * 个人创作者
     */
    KOL("kol","个人创作者"),
    /**
     * 机构创作者
     */
    BRAND_KOL("brandKol", "机构创作者");
    private final String roleKey;
    private final String name;

    RegisterRoleEnum(String roleKey, String name) {
        this.roleKey = roleKey;
        this.name = name;
    }

    public String getRoleKey() {
        return roleKey;
    }

    public String getName() {
        return name;
    }
}

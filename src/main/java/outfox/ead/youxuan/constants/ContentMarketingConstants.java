package outfox.ead.youxuan.constants;

/**
 * 和内容营销相关的常量
 *
 * <AUTHOR> Li
 */
public final class ContentMarketingConstants {

    /**
     * 机构创作者的另一个名字，品牌号和机构创作者指的都是brand kol.
     */
    public static final String BRAND_KOL_ALIAS = "品牌号";

    // 主页转化工具
    /**
     * 转化工具权限
     */
    public static final int CONV_MODULE_PERMISSION = -1;
    /**
     * 官方网站
     */
    public static final int OFFICIAL_WEBSITE = 0;

    public static final String OFFICIAL_WEBSITE_DISPLAY_NAME = "官方网站";


    /**
     * 应用下载
     */
    public static final int APPS_DOWNLOAD = 1;

    public static final String APPS_DOWNLOAD_DISPLAY_NAME = "立即下载";

    /**
     * 联系电话
     */
    public static final int CONTACT = 2;

    public static final String[] CONTACT_DISPLAY_NAMES = {"联系方式", "官方电话", "联系我们"};

    /**
     * 推广活动
     */
    public static final int PROMOTION_ACTIVITY = 3;

    public static final String PROMOTION_ACTIVITY_DISPLAY_NAME = "推广活动";

    public static final int COMPANY_NAME = 4;

    // 内容标签类型
    /**
     * 内容标签
     */
    public static final int CONTENT_TAG_CONTENT = 0;

    /**
     * 名师标签
     */
    public static final int CONTENT_TAG_FAMOUS_TEACHER = 1;

    /**
     * 内容标签：正常
     */
    public static final int CONTENT_TAG_STATUS_NORMAL = 0;

    /**
     * 内容标签：无效
     */
    public static final int CONTENT_TAG_STATUS_INVALID = 1;
    //任务订单
    /**
     * 任务订单状态：待接收
     */
    public static final int TASK_ORDER_WAITING_FOR_ACCEPT = 0;
    /**
     * 任务订单状态：待付款
     */
    public static final int TASK_ORDER_WAITING_FOR_PAYMENT = 1;
    /**
     * 任务订单状态：进行中/已上架
     */
    public static final int TASK_ORDER_IN_PROGRESS = 2;
    /**
     * 任务订单状态：已完成
     */
    public static final int TASK_ORDER_FINISHED = 3;
    /**
     * 任务订单状态：已取消
     */
    public static final int TASK_ORDER_CANCELED = 4;
    /**
     * 待开启
     */
    public static final int TASK_ORDER_READY = 5;
    /**
     * 已下架
     */
    public static final int TASK_ORDER_TAKE_DOWN = 6;
    /**
     * 已结束
     */
    public static final int TASK_ORDER_END = 7;

    // 达人状态
    /**
     * 达人状态：正常
     */
    public static final int APP_ACCOUNT_STATUS_NORMAL = 0;
    /**
     * 达人状态：暂停
     */
    public static final int APP_ACCOUNT_STATUS_PAUSE = 1;
    /**
     * 达人状态：删除
     */
    public static final int APP_ACCOUNT_STATUS_DELETED = 2;
    /**
     * 达人服务状态，在服务
     */
    public static final int APP_ACCOUNT_IN_SERVICE_TRUE = 1;
    /**
     * 达人服务状态，暂停接单
     */
    public static final int APP_ACCOUNT_IN_SERVICE_FALSE = 0;
    // 词典接口状态
    /**
     * 正常
     */
    public static final int DICT_API_OK = 200;
    /**
     * 正常
     */
    public static final int DICT_PROFILE_API_OK = 0;
    /**
     * 当前用户的某个模块存在未审核的记录
     */
    public static final int DICT_API_USER_STATUS_AUDITING = 400;
    // 任务订单
    /**
     * 添加已选达人重复，覆盖之前的备注
     */
    public static final long PRE_TASK_ORDER_REPETITION = -1;
    /**
     * 任务类型，指派任务
     */
    public static final int TASK_ORDER_TYPE_APPOINTMENT = 1;
    /**
     * 任务类型，投稿任务
     */
    public static final int TASK_ORDER_TYPE_POST = 2;
    /**
     * 投稿任务子任务
     */
    public static final int TASK_ORDER_TYPE_SUB_POST = 3;

    // 橱窗组件
    public static final int SHOWCASE_COMPONENT_DELETED = 1;
    /**
     * 有效/可选
     */
    public static final int SHOWCASE_COMPONENT_SCHEMA_VALID = 0;
    /**
     * 无效/不可选
     */
    public static final int SHOWCASE_COMPONENT_SCHEMA_INVALID = 1;
    /**
     * 品牌宣传
     */
    public static final int SHOWCASE_COMPONENT_PROMOTION_TYPE_BRANDING = 1;
    /**
     * 商品售卖
     */
    public static final int SHOWCASE_COMPONENT_PROMOTION_TYPE_SALES = 2;
    /**
     * 线索留资
     */
    public static final int SHOWCASE_COMPONENT_PROMOTION_TYPE_LEADS = 3;
    /**
     * 应用推广
     */
    public static final int SHOWCASE_COMPONENT_PROMOTION_TYPE_APP = 4;
    /**
     * 落地页
     */
    public static final int SHOWCASE_COMPONENT_SWITCH_LANDING_PAGE = 1;
    /**
     * 应用直达
     */
    public static final int SHOWCASE_COMPONENT_SWITCH_DEEPLINK = 2;
    /**
     * 微信小程序
     */
    public static final int SHOWCASE_COMPONENT_SWITCH_WECHAT_MP = 3;
    /**
     * 应用商店
     */
    public static final int SHOWCASE_COMPONENT_SWITCH_APP_STORE = 4;
    /**
     * 应用平台 不限
     */
    public static final int SHOWCASE_COMPONENT_APP_PLATFORM_BOTH = 0;
    /**
     * 应用平台 Android
     */
    public static final int SHOWCASE_COMPONENT_APP_PLATFORM_ANDROID = 1;
    /**
     * 应用平台 iOS
     */
    public static final int SHOWCASE_COMPONENT_APP_PLATFORM_IOS = 2;

    /**
     * 来源参数配置key
     */
    public static final String SHOWCASE_COMPONENT_OUT_VENDOR_KEY = "outvendor";

    /**
     * 来源参数配置value
     */
    public static final String SHOWCASE_COMPONENT_OUT_VENDOR_VALUE = "wowshop_";
    /**
     * 品牌推广样式1
     */
    public static final long SHOWCASE_COMPONENT_BRANDING_SCHEMA_ID = 1;
    /**
     * 商品售卖样式1
     * 样式1为带价格样式，样式2为不带价格样式
     */
    public static final long SHOWCASE_COMPONENT_SALES_SCHEMA_1_ID = 3;
    /**
     * 商品售卖样式2
     * 样式1为带价格样式，样式2为不带价格样式
     */
    public static final long SHOWCASE_COMPONENT_SALES_SCHEMA_2_ID = 5;
    /**
     * 线索留资样式1
     */
    public static final long SHOWCASE_COMPONENT_LEADS_SCHEMA_ID = 7;
    /**
     * 应用推广样式1
     */
    public static final long SHOWCASE_COMPONENT_APP_SCHEMA_ID = 9;
    /**
     * 1-审核中（待审核）
     */
    public static final int SHOWCASE_COMPONENT_STATUS_AUDITING = 1;
    /**
     * 2-已上架（已通过）
     */
    public static final int SHOWCASE_COMPONENT_STATUS_NORMAL = 2;
    /**
     * 3-已下架
     */
    public static final int SHOWCASE_COMPONENT_STATUS_TAKE_DOWN = 3;
    /**
     * 4-未通过
     */
    public static final int SHOWCASE_COMPONENT_STATUS_AUDIT_REJECT = 4;

    // 与词典API交互时使用的source字段
    /**
     * 个人商品
     */
    public static final int DICT_API_SOURCE_PERSONAL_GOOD = 0;
    /**
     * 指派任务
     */
    public static final int DICT_API_SOURCE_APPOINTMENT_TASK = 1;
    /**
     * 投稿任务
     */
    public static final int DICT_API_SOURCE_POST_TASK = 2;
    /**
     * 自动挂窗投稿任务
     */
    public static final int DICT_API_SOURCE_AUTO_SHOWCASE_POST_TASK = 3;

}

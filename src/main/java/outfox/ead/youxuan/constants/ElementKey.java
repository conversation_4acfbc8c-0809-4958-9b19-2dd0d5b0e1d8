package outfox.ead.youxuan.constants;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
public enum ElementKey {
    MAIN_IMAGE("mainimage", 2, 0, 0),
    MAIN_IMAGE1("mainimage1", 2, 0, 1),
    MAIN_IMAGE2("mainimage2", 2, 0, 2),
    MAIN_IMAGE3("mainimage3", 2, 0, 3),
    MAIN_IMAGE4("mainimage4", 2, 0, 4),
    MULTI_MAIN_IMAGE("multiMainImage", 2, 0, 5),
    GIF_IMAGE("gifimage", 2, 1, 5),
    COVER_IMAGE("coverimage", 2, 2, 0),
    COVER_IMAGE1("coverimage1", 2, 2, 1),
    COVER_IMAGE2("coverimage2", 2, 2, 2),
    COVER_IMAGE3("coverimage3", 2, 2, 3),
    MULTI_COVER_IMAGE("multiCoverImage", 2, 2, 4),
    ICON_IMAGE("iconimage", 2, 3, 0),
    VIDEO("video", 3, 0, 0),
    VIDEO1("video1", 3, 0, 1),
    VIDEO2("video2", 3, 0, 2),
    VIDEO3("video3", 3, 0, 3),
    VIDEO4("video4", 3, 0, 4),
    VIDEO_URL("videourl", 3, 1, 0),
    VIDEO_URL1("videourl1", 3, 1, 1),
    VIDEO_URL2("videourl2", 3, 1, 2),
    VIDEO_URL3("videourl3", 3, 1, 3),
    VIDEO_URL4("videourl4", 3, 1, 4),
    MULTI_VIDEO("multiVideo", 3, 2, 0),
    UNKNOWN("unknown", -1, 100, 100),
    ;

    private final String value;
    private static final Map<String, ElementKey> VALUE_MAP;
    private final int level;
    private final int subLevel;

    static {
        final Map<String, ElementKey> tmpMap = Maps.newHashMap();
        for (final ElementKey en : ElementKey.values()) {
            tmpMap.put(en.value, en);
        }
        VALUE_MAP = ImmutableMap.copyOf(tmpMap);
    }

    /**
     * 1-文字
     * 2-图片
     * 3-视频
     */
    private final int type;

    ElementKey(String value, int type, int level, int subLevel) {
        this.value = value;
        this.type = type;
        this.level = level;
        this.subLevel = subLevel;
    }

    public static ElementKey getEnum(String value) {
        return VALUE_MAP.getOrDefault(value, UNKNOWN);
    }
}

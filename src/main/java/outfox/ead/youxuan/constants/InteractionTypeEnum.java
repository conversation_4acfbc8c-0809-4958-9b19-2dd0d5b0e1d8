package outfox.ead.youxuan.constants;

/**
 * 广告组的点击交互类型
 * <AUTHOR>
 * @date 2023/4/21
 */
public enum InteractionTypeEnum {

    /**
     * 无
     */
    NONE(0),

    /**
     * 摇一摇
     */
    SHAKABLE(1),

    /**
     * 滑动互动
     */
    SLIDE_INTERACT(2),

    /**
     * 双link
     */
    DOUBLE_LINK(3),

    /**
     * 三link
     */
    THREE_LINK(4),

    /**
     * 扭一扭
     */
    TWIST(5),

    /**
     * 三合一
     */
    THREE_IN_ONE(6);

    private final int code;

    InteractionTypeEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static InteractionTypeEnum fromCode(int code) {
        for (InteractionTypeEnum type : InteractionTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}

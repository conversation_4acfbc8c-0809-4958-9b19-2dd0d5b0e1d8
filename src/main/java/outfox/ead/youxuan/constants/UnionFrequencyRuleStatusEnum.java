package outfox.ead.youxuan.constants;

import lombok.Getter;

/**
 * 联合频控规则状态
 */
@Getter
public enum UnionFrequencyRuleStatusEnum {

    VALID("生效中", 1),

    EXPIRED("已失效", 2),

    DELETED("已删除", 3);

    /**
     * 信息
     *
     */
    private final String msg;
    private final Integer code;

    UnionFrequencyRuleStatusEnum(String msg, Integer code) {
        this.msg = msg;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}

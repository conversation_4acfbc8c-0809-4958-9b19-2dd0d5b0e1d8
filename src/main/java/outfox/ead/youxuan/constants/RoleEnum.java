package outfox.ead.youxuan.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public enum RoleEnum {
    /**
     * 广告主
     */
    SPONSOR("sponsor", "广告主"),
    /**
     * 个人创作者
     */
    KOL("kol","个人创作者"),
    /**
     * 机构创作者
     */
    BRAND_KOL("brandKol", "机构创作者"),
    /**
     * 创作运营人员
     */
    KOL_OPERATOR("kolOperator","创作运营人员"),
    /**
     * 投放运营人员
     */
    AD_OPERATOR("adOperator","投放运营人员"),
    /**
     * 投放超管运营
     */
    ADMIN("admin", "投放超管运营"),
    /**
     * 审核运营人员
     */
    AUDIT_OPERATOR("auditOperator", "审核运营人员"),;
    private final String roleKey;
    private final String name;

    private static final Map<String, RoleEnum> ROLE_KEY_2_ENUM = new HashMap<>();
    private static final Map<String, RoleEnum> NAME_2_ENUM = new HashMap<>();
    static {
        for (RoleEnum roleEnum :RoleEnum.values()){
            ROLE_KEY_2_ENUM.put(roleEnum.getRoleKey(), roleEnum);
            NAME_2_ENUM.put(roleEnum.getName(), roleEnum);
        }
    }
    RoleEnum(String roleKey, String name) {
        this.roleKey = roleKey;
        this.name = name;
    }

    public String getRoleKey() {
        return roleKey;
    }

    public String getName() {
        return name;
    }

    public static RoleEnum nameOf(String name) {
        return NAME_2_ENUM.get(name);
    }

    public static RoleEnum roleKeyOf(String roleKey) {
        return ROLE_KEY_2_ENUM.get(roleKey);
    }
}

package outfox.ead.youxuan.constants;

import org.joda.time.DateTimeZone;

/**
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/13 18:28
 */
public final class Constants {
    public static final int INSERT_REPEAT = 0;
    public static final int UPDATE_REPEAT = 1;
    public static final int ASC = 1;
    public static final int DESC = 0;
    /**
     * 空格
     */
    public static final String BLANK = " ";
    public static final String COMMA = ",";
    /**
     * 上限1亿
     */
    public static final long UPPER_LIMIT = 100000000;
    /**
     * 存放Authorization的header字段
     */
    public static final String AUTH_HEADER = "youxuan-user";

    public static final String CURRENT_ROLE = "current-role";


    public static final String USER_ID = "userId";

    public static final String UNDEFINED = "undefined";

    public static final String SUCCESS = "成功";

    public static final String ERROR = "失败";

    // >>>>>>>URS<<<<<<<
    /**
     * cookie校验时使用的网易urs cookie类型
     */
    public static final String NTES_SESS = "NTES_SESS";

    // >>>>>>>Area<<<<<<<
    /**
     * 海外级别
     */
    public static final Integer LEVEL_INTERNATION = 0;
    /**
     * 国内级别
     */
    public static final Integer LEVEL_INLAND = 1;

    // >>>>>>>AdPlan<<<<<<<
    /**
     * 不限时间段
     */
    public static final Integer NO_TIME_ORIENTATION = 0;
    /**
     * 时间定向
     */
    public static final Integer TIME_ORIENTATION = 1;
    /**
     * 不限地域
     */
    public static final Integer NO_REGIONAL_ORIENTATION = 0;
    /**
     * 地域限制
     */
    public static final Integer REGIONAL_ORIENTATION = 1;
    /**
     * 日期连续
     */
    public static final Integer DATE_CONTINUOUS = 0;
    /**
     * 日期不连续
     */
    public static final Integer NOT_DATE_CONTINUOUS = 1;

    // Promotion type
    /**
     * 落地页推广
     */
    public static final Integer LANDING_PAGE = 1;
    /**
     * 应用直达
     */
    public static final Integer APP_DIRECT = 2;
    /**
     * 小程序推广
     */
    public static final Integer WECHAT_APP = 3;

    // 计费方式 BillingType
    /**
     * Cost per time
     */
    public static final Integer CPT = 0;
    /**
     * Cost per mille
     */
    public static final Integer CPM = 1;


    // 投放方式 deliveryType
    /**
     * 标准投放
     */
    public static final Integer DELIVERY_STANDARD = 0;
    /**
     * PD
     */
    public static final Integer DELIVERY_PD = 1;
    /**
     * PDB
     */
    public static final Integer DELIVERY_PDB = 2;

    // >>>>>>>AdPositionSchedule<<<<<<<
    // Resource Status
    /**
     * 全部售出
     */
    public static final Integer RESOURCE_SOLD_ALL = 0;
    /**
     * 部分售出
     */
    public static final Integer RESOURCE_PARTIALLY_SOLD = 1;
    /**
     * 暂停
     */
    public static final Integer RESOURCE_PAUSE = 2;
    /**
     * 空闲
     */
    public static final Integer RESOURCE_SPARE = 3;

    // >>>>>>>Media<<<<<<<
    /**
     * 安卓
     */
    public static final Integer ANDROID = 0;
    /**
     * ios
     */
    public static final Integer IOS = 1;
    /**
     * pc
     */
    public static final Integer PC = 2;

    // >>>>>>>AdPosition<<<<<<<
    //广告位类型TYPE
    /**
     * 信息流
     */
    public static final Integer INFO_STREAM = 0;
    /**
     * 开屏
     */
    public static final Integer OPEN_SCREEN = 1;
    /**
     * 插屏
     */
    public static final Integer TABLE_SCREEN = 2;
    /**
     * 焦点图
     */
    public static final Integer FOCUS_MAP = 3;
    /**
     * 激励视频
     */
    public static final Integer INCENTIVE_VIDEO = 4;
    /**
     * 横幅
     */
    public static final Integer BANNER = 5;
    /**
     * 自定义
     */
    public static final Integer USER_DEFINED = 6;

    /**
     * 不支持
     */
    public static final Integer NOT_SUPPORT = 0;
    /**
     * 支持
     */
    public static final Integer SUPPORT = 1;

    // >>>>>>>Element<<<<<<<
    /**
     * 文字
     */
    public static final Integer TEXT_ELEMENT = 1;

    public static final String CLK_TEXT = "clkText";
    public static final String CLK_TEXT_1 = "clkText1";
    public static final String CLK_TEXT_2 = "clkText2";


    public static final Integer STATUS_INVALID = 0;
    public static final Integer STATUS_VALID = 1;
    // >>>>>>>Statements<<<<<<<


    // >>>>>>>数据细分<<<<<<<

    public static final int DIMENSION_ALL = 0;
    public static final int DIMENSION_CUSTOMER_NAME = 1;
    public static final int DIMENSION_PROMOTION_TARGET = 2;
    public static final int DIMENSION_MEDIA = 3;
    public static final int DIMENSION_AD_POSITION = 4;
    public static final int DIMENSION_STYLE = 5;
    public static final int DIMENSION_AD_PLAN = 6;
    public static final int DIMENSION_AD_GROUP = 7;

    public static final int GRANULARITY_DAY = 0;
    public static final int GRANULARITY_HOUR = 1;

    public static final int GRANULARITY_ALL = 2;

    public static final int ALL_PROMOTION_TARGET = -1;
    public static final String ALL = "全部";

    public static final int FILTER_ZERO = 1;
    public static final int NOT_FILTER_ZERO = 0;

    /**
     * 上海时区的字符串常量
     */
    public static final String ASIA_SHANGHAI = "Asia/Shanghai";
    public static final DateTimeZone DATE_TIME_ZONE_SHANGHAI = DateTimeZone.forID(ASIA_SHANGHAI);

    /**
     * 日期format
     */
    public static final String TIME_FORMAT = "HH:mm:ss";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String MIN_FORMAT = "yyyy-MM-dd HH:mm";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS+08:00";

    /**
     * 是否有投放中、投放暂停、即将投放的推广组
     * <p>0-无
     * 1-有
     */
    public static final Integer AD_GROUP_NOT_DELIVERING = 0;
    public static final Integer AD_GROUP_DELIVERING = 1;

    /**
     * 历史是否有投放中、投放暂停、即将投放的推广组
     * <p>0-无
     * 1-有
     */
    public static final Integer GROUP_IS_DELIVERED = 1;
    public static final Integer GROUP_IS_NOT_DELIVERED = 0;

    public static final int YEX_VALID = 1;

    public static final int USER_STATUS_NORMAL = 1;
    public static final int USER_STATUS_PAUSE = 2;
    public static final int BIND = 0;
    public static final int UNBIND = 1;

    public static final int BOOT_FIRST_REFRESH_CLOSE = 0;
    public static final int BOOT_FIRST_REFRESH_OPEN = 1;
    public static final int GENDER_ORIENTATION_NO = 0;
    public static final int GENDER_ORIENTATION_MALE = 1;
    public static final int GENDER_ORIENTATION_FEMALE = 2;

    public static final String TOKEN_REGISTER = "register";
    public static final String TOKEN_ADD_ROLE = "add_role";
    public static final String TOKEN_BIND_MAIL = "bind_mail";

    /**
     * 全屏点击支持的城市等级，如果包含-1，则表示不限制地域
     */
    public static final int FULL_CLICK_CLASS_CITIES_IS_ALL_CITY_ID = -1;

    /**
     * 文件上传：mfs挂载的根目录
     */
    public static final String MATERIAL_PATH = "material";
    public static final String VIDEO_PATH = "video";
    public static final String AUDIO_PATH = "audio";
}

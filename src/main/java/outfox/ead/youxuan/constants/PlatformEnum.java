package outfox.ead.youxuan.constants;

import java.util.HashSet;
import java.util.Set;

public enum PlatformEnum {
    YOUDAO_DICT("有道词典"),
    WEIBO("新浪微博"),
    DOUYIN("抖音"),
    XIAOHONGSHU("小红书"),
    KUAISHOU("快手"),
    BILIBILI("B站"),
    WX_VIDEO("微信视频号");
    private final String name;

    PlatformEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    private static final Set<String> SET = new HashSet<>();

    static {
        for (PlatformEnum value : values()) {
            SET.add(value.getName());
        }
    }

    public boolean contains(String name) {
        return SET.contains(name);
    }
}

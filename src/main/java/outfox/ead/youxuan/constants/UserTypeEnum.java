package outfox.ead.youxuan.constants;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 用户类型
 *
 * <AUTHOR>
 */
@Getter
public enum UserTypeEnum {

    /**
     * 用户类型
     */
    OTHERS(0, "其它, 普通用户");

    @EnumValue
    @JsonValue
    private final int code;

    private final String name;

    UserTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}

package outfox.ead.youxuan.constants;

/**
 * @author: 李梦杰
 * @date: 2021/8/26/11:48
 * @description:
 */
public enum ResourceStatusEnum {
    /**
     * 有效,0
     */
    VALID("有效", 0),

    /**
     * 暂停,1
     */
    PAUSE("暂停", 1),

    /**
     * 删除,2
     */
    DELETED("删除", 2),
    /**
     * 未删除,3
     */
    NOT_DELETED("未删除", 3),
    /**
     * 错误的状态,-1
     */
    ERROR("错误状态", -1);

    private final String msg;
    private final Integer code;

    ResourceStatusEnum(String msg, Integer code) {
        this.msg = msg;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}

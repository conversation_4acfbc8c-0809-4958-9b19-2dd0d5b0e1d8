package outfox.ead.youxuan.constants;

/**
 * <AUTHOR>
 */
public enum PromotionStatusEnum {
    /**
     * 投放中
     */
    DELIVERING("投放中", 0),

    DELIVER_FINISH("投放结束", 1),

    DELIVER_READY("即将开始", 2),

    DELIVER_PAUSE("投放暂停", 3),

    DELIVER_NOT_PAUSE("未暂停", 4),

    DELETED("已删除", 5),

    NOT_DELETED("未删除", 6),

    ERROR("错误状态", 7);

    /**
     * 信息
     */
    private final String msg;
    private final Integer code;

    PromotionStatusEnum(String msg, Integer code) {
        this.msg = msg;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}

package outfox.ead.youxuan.web.kol.service.impl;

import outfox.ead.youxuan.entity.ShowcaseComponentWhiteList;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.service.ShowcaseComponentWhiteListService;
import outfox.ead.youxuan.mapper.youxuan.ShowcaseComponentWhiteListMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【ShowcaseComponentWhiteList(橱窗组件特殊功能白名单)】的数据库操作Service实现
* @createDate 2022-05-25 19:47:02
*/
@Service
public class ShowcaseComponentWhiteListServiceImpl extends YouxuanServiceImpl<ShowcaseComponentWhiteListMapper, ShowcaseComponentWhiteList>
implements ShowcaseComponentWhiteListService{

    @Override
    public Boolean isUserInWhiteList(Long userId) {
        return baseMapper.countByUserId(userId) > 0;
    }
}
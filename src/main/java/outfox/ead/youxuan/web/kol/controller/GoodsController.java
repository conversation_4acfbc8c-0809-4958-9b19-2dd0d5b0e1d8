package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.MetricsEnum;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.annotation.Report;
import outfox.ead.youxuan.web.kol.controller.query.GuessYouLikeQuery;
import outfox.ead.youxuan.web.kol.controller.query.KolGoodsQuery;
import outfox.ead.youxuan.web.kol.controller.vo.IsShoppingVO;
import outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentDetail2DictVO;
import outfox.ead.youxuan.web.kol.service.GoodsService;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年10月18日 14:52
 */
@RestController
@BaseResponse
@Validated
@RequiredArgsConstructor
@RequestMapping("/goods")
@Api(tags = "商品接口")
public class GoodsController {
    private final GoodsService goodsService;

    @ApiOperation("达人推荐好物")
    @GetMapping("/kolGoods")
    @Report(metrics = MetricsEnum.timer)
    public List<ShowcaseComponentDetail2DictVO> kolGoods(KolGoodsQuery kolGoodsQuery) {
        return goodsService.kolRecommendGoods(kolGoodsQuery);
    }

    @ApiOperation("猜你喜欢")
    @GetMapping("/guessYouLike")
    @Report(metrics = MetricsEnum.timer)
    public List<ShowcaseComponentDetail2DictVO> guessYouLike(GuessYouLikeQuery guessYouLikeQuery) {
        return goodsService.guessYouLike(guessYouLikeQuery);
    }

    @ApiOperation("查询正在购买的用户")
    @GetMapping("/isShopping")
    public Collection<String> isShopping(@RequestParam String componentId) {
        return goodsService.isShopping(componentId);
    }

    @ApiOperation("上报正在购买的用户")
    @PostMapping("/isShopping")
    public void isShopping(@Valid IsShoppingVO isShoppingVO, @RequestHeader(value = HttpHeaders.COOKIE, required = false) String cookie) {
        goodsService.addIsShopping(isShoppingVO, cookie);
    }
}

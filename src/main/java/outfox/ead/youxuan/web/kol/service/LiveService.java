package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.web.kol.controller.bo.Good;
import outfox.ead.youxuan.web.kol.controller.query.UserGoodInfoQuery;
import outfox.ead.youxuan.web.kol.controller.vo.GoodInfoVO;
import outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentDetail2DictVO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022年07月20日 15:20
 */
public interface LiveService {
    /**
     * 开通用户 指派任务/投稿任务/橱窗权限 <p>
     * 如果没有用户，进行注册后再开通
     *
     * @param uid -
     * @param name 联系人姓名
     * @param phone 手机
     * @param email 邮箱
     */
    void openAllPermission(String uid, String name, String phone, String email);

    /**
     * 获取直播橱窗数据
     * @param goods -
     * @return -
     */
    List<ShowcaseComponentDetail2DictVO> getGoodInfoDetails(List<Good> goods);

    /**
     * 查询用户商品
     * @param userGoodInfoQuery -
     * @return -
     */
    List<GoodInfoVO> listUserGoodInfo(UserGoodInfoQuery userGoodInfoQuery);

    /**
     * 查询商品列表
     * @param queryGoodsList -
     * @return -
     */
    List<GoodInfoVO> listGoodInfo(Set<Good> queryGoodsList);
}

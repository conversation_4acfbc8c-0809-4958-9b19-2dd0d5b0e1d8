package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.KolContentTagRelation;
import outfox.ead.youxuan.web.ad.service.YouxuanService;
import outfox.ead.youxuan.web.kol.controller.dto.ContentTagUsageCountDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【KolContentTagRelation(达人与内容标签绑定关系)】的数据库操作Service
 * @createDate 2022-02-10 17:45:52
 */
public interface KolContentTagRelationService extends YouxuanService<KolContentTagRelation> {

    List<ContentTagUsageCountDTO> getContentTagUsageCountByTagIds(List<Long> ids);

    ContentTagUsageCountDTO getContentTagUsageCountByContentTagId(Long id);

    Integer delByAppAccountId(Long appAccountId);

    List<KolContentTagRelation> getByAppAccountIds(List<Long> appAccountIds);

    void batchBuildAndSave(Long appAccountId, List<Long> tagIds);
}
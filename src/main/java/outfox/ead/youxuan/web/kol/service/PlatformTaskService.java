package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.PlatformTask;
import outfox.ead.youxuan.web.ad.service.YouxuanService;
import outfox.ead.youxuan.web.kol.controller.vo.PlatformTaskVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【PlatformTask】的数据库操作Service
 * @createDate 2022-02-18 10:53:58
 */
public interface PlatformTaskService extends YouxuanService<PlatformTask> {

    List<PlatformTaskVO> getListByPlatformId(Long platformId);

    List<PlatformTaskVO> getListByPlatformIds(List<Long> platformIds);

    List<PlatformTaskVO> getListByIds(List<Long> ids);
}

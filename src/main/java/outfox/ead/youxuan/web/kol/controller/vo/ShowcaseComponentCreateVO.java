package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShowcaseComponentCreateVO {


    @ApiModelProperty("推广样式ID")
    private Long schemaId;


    @NotNull
    @ApiModelProperty("组件名称")
    private String name;


    @ApiModelProperty("推广类型")
    @NotNull
    @Range(min = 1, max = 4, message = "推广类型不合法")
    private Integer promotionType;


    @ApiModelProperty("推广品类")
    @NotNull
    private Integer category;


    @ApiModelProperty("跳转类型")
    private Integer switchType;


    @ApiModelProperty("deeplink链接")
    private String deepLink;


    @ApiModelProperty("落地页链接")
    private String landingPageUrl;


    @ApiModelProperty("备用落地页链接")
    private String backupLandingPageUrl;


    @ApiModelProperty("是否自动为跳转链接添加来源参数配置项")
    private Boolean appendOutVendor;


    @ApiModelProperty("微信小程序原始ID")
    private String microProgramId;


    @ApiModelProperty("微信小程序目标页面路径")
    private String microProgramPath;


    @ApiModelProperty("应用平台；0-不限，1-Android，2-iOS")
    private Integer appPlatform;


    @ApiModelProperty("安卓应用包名")
    private String androidPackageName;


    @ApiModelProperty("iOS应用ID")
    private String iosAppId;


    @ApiModelProperty("引导文案")
    private String leadText;

    @ApiModelProperty("商品名称")
    private String itemName;


    @ApiModelProperty("商品价格")
    @Range(min = 0, max = 99_999_999_00L, message = "商品价格不合法")
    private Long itemPrice;


    @ApiModelProperty("划线价")
    @Range(min = 0, max = 99_999_999_00L, message = "商品价格不合法")
    private Long strikeThroughPrice;


    @ApiModelProperty("按钮文案")
    private String buttonText;


    @ApiModelProperty("推广图片的URL")
    private String promoteImageUrl;


    @ApiModelProperty("推广标题")
    private String promoteTitle;


    @ApiModelProperty("推广文案")
    private String promoteText;


    @ApiModelProperty("应用名称")
    private String appName;


    @ApiModelProperty("是否标记为广告")
    private Boolean markAsAd;
}

package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年03月01日 7:18 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskOrderVO {
    @ApiModelProperty("任务id")
    private Long parentOrderId;
    @ApiModelProperty("字符串子任务ID")
    private String orderId;
    @ApiModelProperty("子任务id")
    private Long id;
    @ApiModelProperty("广告主信息")
    private TaskOrderUserDetailVO taskOrderUserDetailVO;
    @ApiModelProperty("达人昵称")
    private String name;
    @ApiModelProperty("达人头像")
    private String avatar;
    @ApiModelProperty("媒体名称")
    private String platformName;
    @ApiModelProperty("媒体ID")
    private Long platformId;
    @ApiModelProperty("任务类型")
    private String taskType;
    @ApiModelProperty("内容类型")
    private String platformContent;
    @ApiModelProperty("价格")
    private Long price;
    @ApiModelProperty("备注")
    private String comment;
    @ApiModelProperty("状态;0待接收、1待付款、2进行中、3已完成、4已取消、5待开启、6已下架、7已结束")
    private Integer status;
    @ApiModelProperty("任务创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("状态更新时间")
    private LocalDateTime lastModTime;
    @ApiModelProperty("子订单")
    private List<TaskOrderVO> orders;
    @ApiModelProperty("icon")
    private String icon;
    // 投稿任务专有的一些参数
    @ApiModelProperty("橱窗组件ID")
    private Long showcaseComponentId;
    @ApiModelProperty("橱窗组件的简要信息，供列表显示和渲染预览图用")
    private ShowcaseComponentListVO showcaseComponent;
    @ApiModelProperty("推广开始日期")
    private LocalDateTime beginDate;
    @ApiModelProperty("推广结束日期")
    private LocalDateTime endDate;
    @ApiModelProperty("投稿上限；0为无上限，其他值为上限")
    private Long postLimit;
    @ApiModelProperty("处于已发布状态的动态数")
    private Long publishedPostCount;
    @ApiModelProperty("已参与当前任务的个人创作者账户数")
    private Long pickedUpUserCount;
    @ApiModelProperty("计费类型；0-其他，1-CPM，2-CPC，3-CPA，4-CPS")
    private Integer billingType;
    @ApiModelProperty("佣金单价，以人民币分为单位")
    private Long commissionPrice;
    @ApiModelProperty("结算周期;0-其他，1-月结，2-周结，3-日结")
    private Integer settlementInterval;
    @ApiModelProperty("是否已经领取过该任务")
    private Boolean isPickedUp;
    @ApiModelProperty("剩余量")
    private Long postRemain;
    @ApiModelProperty("权重")
    private String weight;
    @ApiModelProperty("自动挂窗")
    private Boolean autoShowcase;
}

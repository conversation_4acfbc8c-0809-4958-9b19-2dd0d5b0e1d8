package outfox.ead.youxuan.web.kol.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.hibernate.validator.constraints.Range;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.CacheNameConstant;
import outfox.ead.youxuan.constants.MetricsEnum;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.annotation.Report;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.kol.controller.vo.*;
import outfox.ead.youxuan.web.kol.service.ShowcaseComponentSchemaService;
import outfox.ead.youxuan.web.kol.service.ShowcaseComponentService;
import outfox.ead.youxuan.web.kol.service.ShowcaseComponentWhiteListService;

import javax.validation.Valid;
import java.net.URISyntaxException;
import java.util.List;

/**
 * <AUTHOR> Li
 */
@Validated
@BaseResponse
@RestController
@AllArgsConstructor
@Api(tags = "橱窗组件")
@RequestMapping("/showcase_component")
public class ShowcaseComponentController {
    private final ShowcaseComponentService showcaseComponentService;

    private final ShowcaseComponentWhiteListService showcaseComponentWhiteListService;

    private final ShowcaseComponentSchemaService showcaseComponentSchemaService;

    @ApiOperation("修改审核状态")
    @PutMapping("/audit")
    @AccessControl(roles = RoleEnum.AUDIT_OPERATOR)
    @CacheEvict(cacheNames = CacheNameConstant.SHOWCASE_ID_CACHE_NAME, key = "#id")
    public void audit(String id, int status) {
        showcaseComponentService.audit(id, status, SecurityUtil.getUserId());
    }

    @ApiOperation("批量修改审核状态")
    @PutMapping("/batch/audit")
    @AccessControl(roles = RoleEnum.AUDIT_OPERATOR)
    public void batchAudit(@RequestBody BatchAuditVO batchAuditVO) {
        showcaseComponentService.batchAudit(batchAuditVO.getIds(), batchAuditVO.getStatus(), SecurityUtil.getUserId());
    }

    @ApiOperation("修改组件状态")
    @PutMapping("/status")
    public Boolean updateStatus(Long id, Integer status, Boolean confirmUpdate) {
        return showcaseComponentService.updateStatus(id, status, confirmUpdate, SecurityUtil.getUserId());
    }


    @ApiOperation("审核列表")
    @GetMapping("/audit/page")
    @AccessControl(roles = RoleEnum.AUDIT_OPERATOR)
    public Page<ShowcaseComponentAuditListVO> pageAudit(@Valid AuditPageQuery auditPageQuery) {
        return showcaseComponentService.pageAudit(auditPageQuery);
    }

    @AccessControl(roles = {RoleEnum.SPONSOR})
    @ApiOperation("当前登录用户是否可见部分特殊功能")
    @GetMapping("/has_extra_permission")
    public Boolean hasExtraPermission() {
        return showcaseComponentWhiteListService.isUserInWhiteList(SecurityUtil.getUserId());
    }

    @ApiOperation("通过推广类型获取可用的样式列表")
    @GetMapping("/list_valid_schema/{promotionType}")
    public List<ShowcaseComponentSchemaListVO> listValidSchema(
            @PathVariable
            @Range(min = 1, max = 4, message = "非法参数")
            @ApiParam("推广类型；1-品牌宣传，2-商品售卖，3-线索留资，4-应用推广") Integer promotionType) {
        return showcaseComponentSchemaService.listValidByPromotionType(promotionType);
    }

    @ApiOperation("创建新的橱窗组件")
    @PostMapping("/create")
    public Long create(@RequestBody @Valid ShowcaseComponentCreateVO showcaseComponentCreateVO) throws URISyntaxException {
        return showcaseComponentService.create(showcaseComponentCreateVO, SecurityUtil.getUserId(), SecurityUtil.getCurrentRole());
    }

    @ApiOperation("获取一个存在的橱窗组件")
    @GetMapping("/{id}")
    public ShowcaseComponentDetailVO detail(@PathVariable Long id) {
        return showcaseComponentService.detail(id);
    }

    @ApiOperation("获取最后一次审核通过的橱窗组件")
    @GetMapping("/{id}/passed")
    public ShowcaseComponentDetailVO latestPassedDetail(@PathVariable Long id) {
        return showcaseComponentService.latestPassedDetail(id);
    }

    /**
     * <ol>
     *     <li>source=0  goodId 是 ShowcaseComponent 的 componentId</li>
     *     <li>source=1  goodId 是 TaskOrder 的 orderId</li>
     *     <li>source=2  goodId 是 TaskOrder 的 orderId</li>
     * </ol>
     *
     * @param goodId 商品id
     * @param source 0个人商品/橱窗组件 1指派任务 2投稿任务
     * @return
     */
    @ApiOperation("词典用获取最后一次审核通过的橱窗组件，不需要鉴权")
    @GetMapping("/dict/passed")
    @Report(metrics = MetricsEnum.timer)
    public ShowcaseComponentDetail2DictVO latestPassedDetail4Dict(String goodId,
                                                                  @ApiParam("0-个人商品；1-指派任务；2-投稿任务") Integer source) {
        return showcaseComponentService.latestPassedDetail4Dict(goodId, source);
    }

    @ApiOperation("编辑一个存在的橱窗组件")
    @PostMapping("/modify")
    public Integer modify(@RequestBody @Valid ShowcaseComponentModifyVO showcaseComponentModifyVO) {
        return showcaseComponentService.modify(showcaseComponentModifyVO, SecurityUtil.getUserId(), SecurityUtil.getCurrentRole());
    }

    @ApiOperation("名字重复校验")
    @GetMapping("/repeat")
    public Boolean isNameExist(String name) {
        return showcaseComponentService.isNameRepeat(name, SecurityUtil.getUserId(), SecurityUtil.getCurrentRole());
    }

    @ApiOperation("删除一个存在的橱窗组件")
    @DeleteMapping("/delete")
    public Boolean delete(Long id) {
        return showcaseComponentService.delete(id, SecurityUtil.getUserId());
    }

    @ApiOperation("按条件分页查询当前用户下的橱窗组件")
    @GetMapping("/page")
    public PageVO<ShowcaseComponentListVO> pageByCriteria(ShowcaseComponentCriteriaQueryVO showcaseComponentCriteriaQueryVO) {
        return showcaseComponentService.pageByCriteria(showcaseComponentCriteriaQueryVO, SecurityUtil.getUserId(), SecurityUtil.getCurrentRole());
    }

    @ApiOperation("是否需要弹窗确认样式升级")
    @GetMapping("/notify_schema")
    public Boolean isNotifySchemaUpgrade() {
        return showcaseComponentService.isNotifySchemaUpgrade(SecurityUtil.getUserId());
    }


    @ApiOperation("不再弹窗确认样式升级")
    @PutMapping("/notify_schema")
    public Boolean disableNotifySchemaUpgrade() {
        return showcaseComponentService.disableNotifySchemaUpgrade(SecurityUtil.getUserId());
    }

    @ApiOperation("获取关联的动态")
    @GetMapping("/related_post")
    @AccessControl(roles = {RoleEnum.KOL, RoleEnum.BRAND_KOL})
    public PageVO<DictPostVO> getRelatedPost(Long id, @ApiParam("页码，注意：第一页是0") Long current, Long size) {
        return showcaseComponentService.getRelatedDictPost(id, current, size);
    }

    @ApiOperation("获取同步广告主组件按钮的状态；-1：未绑定广告主账号，0：关；1：开")
    @GetMapping("/sync_status")
    public Integer getSyncStatus() {
        return showcaseComponentService.getSyncStatus(SecurityUtil.getUserId(), SecurityUtil.getCurrentRole());
    }

    @ApiOperation("修改同步广告主组件按钮的状态")
    @PutMapping("/sync_status")
    public Boolean updateSyncButton(Boolean status) {
        return showcaseComponentService.updateSyncStatus(SecurityUtil.getUserId(), status);
    }

    @ApiOperation("分页获取过审的橱窗组件")
    @GetMapping("/page_passed")
    public PageVO<ShowcaseComponentListVO> pagePassed(ShowcaseComponentCriteriaQueryVO showcaseComponentCriteriaQueryVO) throws Exception {
        return showcaseComponentService.pagePassed(showcaseComponentCriteriaQueryVO, SecurityUtil.getUserId(), SecurityUtil.getCurrentRole());
    }
}

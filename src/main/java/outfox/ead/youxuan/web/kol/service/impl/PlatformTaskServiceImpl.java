package outfox.ead.youxuan.web.kol.service.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.PlatformTask;
import outfox.ead.youxuan.mapper.youxuan.PlatformTaskMapper;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.controller.vo.PlatformTaskVO;
import outfox.ead.youxuan.web.kol.service.PlatformTaskService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【PlatformTask】的数据库操作Service实现
 * @createDate 2022-02-18 10:53:58
 */
@Service
@AllArgsConstructor
public class PlatformTaskServiceImpl extends YouxuanServiceImpl<PlatformTaskMapper, PlatformTask>
        implements PlatformTaskService {

    private final outfox.ead.youxuan.web.kol.controller.mapper.PlatformTaskMapper platformTaskMapper;

    @Override
    public List<PlatformTaskVO> getListByPlatformId(Long platformId) {
        return platformTaskMapper.do2PlatformTaskVO(baseMapper.getByPlatformId(platformId));
    }

    @Override
    public List<PlatformTaskVO> getListByPlatformIds(List<Long> platformIds) {
        return platformTaskMapper.do2PlatformTaskVO(baseMapper.getByPlatformIds(platformIds));
    }

    @Override
    public List<PlatformTaskVO> getListByIds(List<Long> ids) {
        return platformTaskMapper.do2PlatformTaskVO(baseMapper.getByIds(ids));
    }
}

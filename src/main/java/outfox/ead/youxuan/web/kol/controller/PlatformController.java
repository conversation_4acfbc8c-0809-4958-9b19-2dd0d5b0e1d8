package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.entity.Platform;
import outfox.ead.youxuan.web.kol.service.PlatformService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年02月17日 6:23 下午
 */
@RestController
@BaseResponse
@AllArgsConstructor
@RequestMapping("/platform")
@Api(tags = "平台管理")
public class PlatformController {
    private final PlatformService platformService;

    @GetMapping
    public List<Platform> listPlatform() {
        return platformService.list();
    }
}

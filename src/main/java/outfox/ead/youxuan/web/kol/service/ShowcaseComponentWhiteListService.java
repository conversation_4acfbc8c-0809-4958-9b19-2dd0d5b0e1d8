package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.ShowcaseComponentWhiteList;
import outfox.ead.youxuan.web.ad.service.YouxuanService;

/**
* <AUTHOR>
* @description 针对表【ShowcaseComponentWhiteList(橱窗组件特殊功能白名单)】的数据库操作Service
* @createDate 2022-05-25 19:47:02
*/
public interface ShowcaseComponentWhiteListService extends YouxuanService<ShowcaseComponentWhiteList> {

    /**
     * 通过userId查询其是否在白名单中
     * @param userId
     * @return
     */
    Boolean isUserInWhiteList(Long userId);

}
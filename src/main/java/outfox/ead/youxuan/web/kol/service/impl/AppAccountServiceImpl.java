package outfox.ead.youxuan.web.kol.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.youxuan.constants.*;
import outfox.ead.youxuan.core.annotation.DistributedLock;
import outfox.ead.youxuan.core.annotation.DistributedLockKey;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.mapper.youxuan.AppAccountMapper;
import outfox.ead.youxuan.util.AreaUtil;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserDetailService;
import outfox.ead.youxuan.web.ad.service.UserRoleRelationService;
import outfox.ead.youxuan.web.ad.service.UserService;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.controller.dto.DictProfile;
import outfox.ead.youxuan.web.kol.controller.vo.*;
import outfox.ead.youxuan.web.kol.service.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static outfox.ead.youxuan.constants.Constants.*;
import static outfox.ead.youxuan.constants.ContentMarketingConstants.TASK_ORDER_TYPE_APPOINTMENT;
import static outfox.ead.youxuan.constants.ContentMarketingConstants.TASK_ORDER_TYPE_SUB_POST;
import static outfox.ead.youxuan.constants.PlatformEnum.YOUDAO_DICT;
import static outfox.ead.youxuan.constants.ResponseType.*;

/**
 * <AUTHOR>
 * @description 针对表【AppAccount(媒体账户)】的数据库操作Service实现
 * @date 2022-02-09 15:49:35
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AppAccountServiceImpl extends YouxuanServiceImpl<AppAccountMapper, AppAccount>
        implements AppAccountService {
    private final PlatformService platformService;
    private final RoleService roleService;
    @Lazy
    private final ShowcaseComponentService showcaseComponentService;
    private final ContentTagService contentTagService;
    @Lazy
    private final PlatformContentService platformContentService;
    @Lazy
    private final DictService dictService;
    @Lazy
    private final UserDetailService userDetailService;
    @Lazy
    private final TaskOrderService taskOrderService;
    private final UserRoleRelationService userRoleRelationService;
    @Lazy
    private final UserService userService;

    private final outfox.ead.youxuan.web.kol.controller.mapper.AppAccountMapper appAccountMapper;

    @Override
    public PageVO<AppAccountVO> pageByStatusAndUserId(Set<Integer> status, Long userId, Long current, Long size, Boolean sortByPlatform, Role currentRole) {
        UserDetail userDetail = userDetailService.getByUserId(userId, currentRole);
        if (userDetail == null || userDetail.getStage() < StageEnum.COMPLETE_USER_DETAIL.getStage()) {
            return PageVO.emptyPage(current, size);
        }
        Page<AppAccount> page = baseMapper.pageByStatusAndUserId(status, userId, current, size);
        Map<Long, Platform> id2Platform = platformService.list().stream().collect(Collectors.toMap(Platform::getId, Function.identity()));
        List<AppAccountVO> appAccountVOList = appAccountMapper.listAppAccount2VO(page.getRecords());
        for (AppAccountVO appAccountVO : appAccountVOList) {
            Platform platform = id2Platform.get(appAccountVO.getPlatformId());
            appAccountVO.setIcon(platform.getIcon());
            appAccountVO.setPlatformName(platform.getName());
        }
        if (sortByPlatform) {
            appAccountVOList = appAccountVOList.stream()
                    .sorted(Comparator.comparing(AppAccountVO::getPlatformId))
                    .collect(Collectors.toList());
        }
        return new PageVO<>(page.getCurrent(),
                page.getSize(),
                appAccountVOList,
                page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(namespace = "appAccount")
    public void bind(@DistributedLockKey AppAccount appAccount, Long userId, boolean noException, Role currentRole) {
        try {
            bindPreCheck(appAccount, userId, currentRole);
        } catch (CustomException ce) {
            if (ce.getCode() == REPEAT_BINDING.getCode() && noException) {
                return;
            } else {
                throw ce;
            }
        }
        if (currentRole.getRoleKey().equals(RoleEnum.BRAND_KOL.getRoleKey())
                && platformService.getByName(PlatformEnum.YOUDAO_DICT.getName()).getId().equals(appAccount.getPlatformId())) {
            grantDictBrandKol(appAccount, userId, currentRole);
        }
        this.saveOrUpdate(appAccount);
        userDetailService.calStageAndUpdate(userId, currentRole);
    }

    private void grantDictBrandKol(AppAccount appAccount, Long userId, Role currentRole) {
        // 角色为BrandKol 且 绑定的媒体账户为词典账户
        dictService.sendCompanyDataByAppAccountIdAndUserId(appAccount, userId, currentRole);
        dictService.grantBrandKolPermission(appAccount, userId);
        if (Objects.nonNull(appAccount.getId())) {
            dictService.resendAllInfoWhenRebind(appAccount.getId(), userId, currentRole);
        }
    }


    @Override
    public void specialBind(AppAccount appAccount, Long userId, Role currentRole) {
        if (Objects.isNull(appAccount.getId())) {
            try {
                specialBindPreCheck(appAccount, userId);
            } catch (CustomException ce) {
                if (ce.getCode() == REPEAT_BINDING.getCode()) {
                    this.saveOrUpdate(appAccount);
                    return;
                }
            }
            appAccount.setUserId(userId);
        }
        this.saveOrUpdate(appAccount);
        userDetailService.calStageAndUpdate(userId, currentRole);
    }

    @Override
    public List<Long> getIdByKolOperator(Long userId) {
        return baseMapper.getByKolOperatorUserId(userId).stream().map(AppAccount::getId).collect(Collectors.toList());
    }

    private void specialBindPreCheck(AppAccount appAccount, Long userId) {
        // 检测媒体平台，如果是词典账户需要补全信息，其他平台需要校验参数
        Platform platform = platformService.getById(appAccount.getPlatformId());
        if (Objects.nonNull(platform)) {
            if (platform.getName().equals(YOUDAO_DICT.getName())) {
                DictProfile profiles = dictService.getProfiles(appAccount.getAppUserId());
                appAccount.setGender(profiles.getGender());
                appAccount.setAvatar(profiles.getAvatar());
                appAccount.setName(profiles.getNickname());
                appAccount.setFansNum(profiles.getFansNum());
            } else {
                if (Stream.of(appAccount.getAvatar(),
                        appAccount.getName(),
                        appAccount.getAppUserId(),
                        appAccount.getFansNum())
                        .anyMatch(Objects::isNull)) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "媒体账户信息不全");
                }
            }
        } else {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "平台不存在");
        }
        // 该媒体账户是否被绑定过
        AppAccount account = baseMapper.getBindByAppUserIdAndPlatformId(appAccount.getAppUserId(), appAccount.getPlatformId());
        if (Objects.nonNull(account)) {
            if (account.getUserId().equals(userId)) {
                appAccount.setId(account.getId());
                throw new CustomException(REPEAT_BINDING, "当前创作者账户已绑定过，无需重复绑定");
            } else {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "该媒体账户已绑定其他优选创作者，不支持绑定当前优选账户");
            }
        }
        // 用户一个平台只能绑定一个账户
        if (baseMapper.countValidByUserAndPlatform(userId, appAccount.getPlatformId()) > 0) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "已经绑定过" + platform.getName() + "平台，不可重复绑定");
        }
        // 查看是否是自身之前解绑的用户，如果是则重新绑定
        AppAccount oldAppAccount = getByAppUserIdAndPlatformIdAndUserId(appAccount.getAppUserId(),
                appAccount.getPlatformId(), userId);
        if (Objects.nonNull(oldAppAccount)) {
            appAccount.setId(oldAppAccount.getId());
            appAccount.setStatus(BIND);
        } else {
            appAccount.setInService(true);
        }
    }

    /**
     * <ol>
     *     <li>检查stage是否合法</li>
     *     <li>校验参数合法性，如果平台是词典，去词典拉取媒体账户信息</li>
     *     <li>如果是brandKol,只能绑定词典账户,且必须通过资质认证</li>
     *     <li>检验媒体账户是否已经被自己绑定</li>
     *     <li>检验媒体账户是否被别的账户绑定</li>
     *     <li>检验自身是否解绑过该用户，如果是则重新绑定</li>
     *     <li>检验词典账户是否已经被别的优选账户绑定，如果没有同步词典账户到登陆账户</li>
     * </ol>
     *  @param appAccount -
     *
     * @param userId      -
     * @param currentRole
     */
    private void bindPreCheck(AppAccount appAccount, Long userId, Role currentRole) {
        appAccount.setUserId(userId);
        // 1.用户完善信息后才能进行媒体账户绑定
        UserDetail ud = userDetailService.getByUserId(userId, currentRole);
        if (Objects.isNull(ud)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "请先完善个人信息");
        }
        // 2.检测媒体平台，如果是词典账户需要补全信息，其他平台需要校验参数
        Platform platform = platformService.getById(appAccount.getPlatformId());
        if (Objects.nonNull(platform)) {
            if (platform.getName().equals(YOUDAO_DICT.getName())) {
                DictProfile profiles = dictService.getProfiles(appAccount.getAppUserId());
                appAccount.setGender(profiles.getGender());
                appAccount.setAvatar(profiles.getAvatar());
                appAccount.setName(profiles.getNickname());
                appAccount.setFansNum(profiles.getFansNum());
            } else {
                if (Stream.of(appAccount.getAvatar(),
                        appAccount.getName(),
                        appAccount.getAppUserId(),
                        appAccount.getFansNum())
                        .anyMatch(Objects::isNull)) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "媒体账户信息不全");
                }
            }
        } else {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "平台不存在");
        }
        Set<String> roleKeySet = roleService.listByUserId(userId).stream().map(Role::getRoleKey).collect(Collectors.toSet());
        // 3.如果是brandKol,只能绑定词典账户,且必须通过资质认证
        if (roleKeySet.contains(RoleEnum.BRAND_KOL.getRoleKey())) {
            if (!YOUDAO_DICT.getName().equals(platform.getName())) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "机构创作者只能绑定有道词典媒体平台");
            }
            if (!ud.getVerified()) {
                throw new CustomException(INVALID_PARAMETERS, "机构创作者未完成资质认证,不能进行媒体绑定");
            }
        }
        // 4.该媒体账户是否被绑定过
        AppAccount account = baseMapper.getBindByAppUserIdAndPlatformId(appAccount.getAppUserId(), appAccount.getPlatformId());
        if (Objects.nonNull(account)) {
            if (account.getUserId().equals(userId)) {
                appAccount.setId(account.getId());
                throw new CustomException(REPEAT_BINDING, "当前创作者账户已绑定过，无需重复绑定");
            } else {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "该媒体账户已绑定其他优选创作者，不支持绑定当前优选账户");
            }
        }
        // 5. 用户一个平台只能绑定一个账户
        if (baseMapper.countValidByUserAndPlatform(userId, appAccount.getPlatformId()) > 0) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "已经绑定过" + platform.getName() + "平台，不可重复绑定");
        }
        // 6.查看是否是自身之前解绑的用户，如果是则重新绑定
        AppAccount oldAppAccount = getByAppUserIdAndPlatformIdAndUserId(appAccount.getAppUserId(),
                appAccount.getPlatformId(), userId);
        if (Objects.nonNull(oldAppAccount)) {
            appAccount.setId(oldAppAccount.getId());
            appAccount.setStatus(BIND);
        } else {
            appAccount.setInService(true);
        }
        // 7. 查询该词典账户是否已经绑定了优选账户
        if (platform.getName().equals(YOUDAO_DICT.getName()) && !Objects.equals(userService.getById(userId).getDictUid(), appAccount.getAppUserId())) {
            userService.bindByDictUid(appAccount.getUserId(), appAccount.getAppUserId());
        }
    }

    @Override
    public AppAccount getByAppUserIdAndPlatformIdAndUserId(String appUserId, Long platformId, Long userId) {
        return baseMapper.getByAppUserIdAndPlatformIdAndUserId(appUserId, platformId, userId);
    }

    @Override
    public AppAccount getBindById(Long id) {
        return baseMapper.getBindById(id);
    }

    @Override
    public List<AppAccount> getBindAndInServiceByIds(List<Long> ids) {
        return baseMapper.getBindAndInServiceByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusById(Long id, int status, Role currentRole) {
        AppAccount appAccount = baseMapper.selectById(id);
        if (Objects.isNull(appAccount)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "没有该媒体账户");
        }
        if (appAccount.getStatus().equals(status)) {
            return;
        }
        appAccount.setStatus(status);
        this.updateById(appAccount);
        userDetailService.calStageAndUpdate(appAccount.getUserId(), currentRole);
    }

    @Override
    public List<AppAccount> listByUserId(Long userId) {
        return baseMapper.listByUserId(userId);
    }

    @Override
    public List<AppAccount> listBindByUserId(Long userId) {
        return baseMapper.listBindByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbind(Long id, Role currentRole) {
        Long userId = SecurityUtil.getUserId();
        unbindPreCheck(id);
        if (SecurityUtil.hasRole(RoleEnum.BRAND_KOL)
                || this.listByUserId(userId).stream().filter(a -> a.getStatus().equals(BIND)).count() > 1) {
            dictService.sendConvModulePermission(id, userId, false);
            dictService.cancelBrandKolPermission(id, userId);
            this.updateStatusById(id, UNBIND, currentRole);
            if (!isBindDict(userId)) {
                userDetailService.closePostTaskPermission(userId, currentRole);
            }
        } else {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "当前优选账户中只绑定了一个媒体账号，不支持解绑，需要再绑定一个其他媒体账号，当前关系方可解绑");
        }
    }

    /**
     * 解绑校验 <p/>
     * <ol>
     *     <li>是否有橱窗数据</li>
     *     <li>是否有投稿任务数据</li>
     *     <li>是否有指派任务数据</li>
     * </ol>
     */
    private void unbindPreCheck(Long id) {
        if (showcaseComponentService.count(id) > 0) {
            throw new CustomException(CAN_NOT_UNBIND, "当前账户已产生橱窗业务数据，无法解绑");
        }
        if (ObjectUtils.notEqual(taskOrderService.count(id, TASK_ORDER_TYPE_SUB_POST), 0L)) {
            throw new CustomException(ResponseType.APP_ACCOUNT_HAVE_TASK_ORDER, "当前账户已参与过任务，无法解绑");
        }
        if (ObjectUtils.notEqual(taskOrderService.count(id, TASK_ORDER_TYPE_APPOINTMENT), 0L)) {
            throw new CustomException(ResponseType.APP_ACCOUNT_HAVE_TASK_ORDER);
        }
        AppAccount appAccount = baseMapper.selectById(id);
        if (!appAccount.getCreator().equals(SecurityUtil.getUserId())) {
            throw new CustomException(ACCESS_DENIED);
        }
    }

    @Override
    public int updateInServiceStatus(Long userId, Long appAccountId, boolean isInService) {
        if (ObjectUtils.notEqual(taskOrderService.countUnfinishedAppointment(appAccountId, TASK_ORDER_TYPE_APPOINTMENT), 0L)) {
            throw new CustomException(ResponseType.APP_ACCOUNT_HAVE_UNFINISHED_TASK_ORDER);
        }
        return baseMapper.updateInServiceByAppUserIdAndUserId(userId, appAccountId, isInService);
    }

    @Override
    public AppAccount getByPlatformIdAndUserId(Long platformId, Long userId) {
        return baseMapper.getBindByUserIdAndPlatformId(userId, platformId);
    }


    @Override
    public PageVO<KolQueryListVO> kolCriteriaPageQuery(KolCriteriaQueryVO kolCriteriaQueryVO, Long size, Long current) {
        //famous teacher tag.
        List<Long> famousTeacherIds = Collections.emptyList();
        if (kolCriteriaQueryVO.getFamousTeacher()) {
            famousTeacherIds =
                    contentTagService.getValidByType(ContentMarketingConstants.CONTENT_TAG_FAMOUS_TEACHER)
                            .stream()
                            .map(ContentTagListVO::getId)
                            .collect(Collectors.toList());
        }
        if (Objects.nonNull(kolCriteriaQueryVO.getFansOrder()) && Objects.isNull(kolCriteriaQueryVO.getPriceOrder())) {
            kolCriteriaQueryVO.setPriceOrder(ASC);
        }
        //collect data.
        Page<AppAccount> appAccountPage = new Page<>(current, size);
        List<AppAccount> appAccounts = baseMapper.listByConditions(appAccountPage,
                kolCriteriaQueryVO.getPlatformId(), kolCriteriaQueryVO.getNickName(), famousTeacherIds,
                kolCriteriaQueryVO.getContentTagIds(), kolCriteriaQueryVO.getGender(),
                kolCriteriaQueryVO.getFansNumMin(), kolCriteriaQueryVO.getFansNumMax(),
                kolCriteriaQueryVO.getPriceMin(), kolCriteriaQueryVO.getPriceMax(),
                kolCriteriaQueryVO.getFansOrder(), kolCriteriaQueryVO.getPriceOrder());
        if (CollectionUtils.isEmpty(appAccounts)) {
            return new PageVO<>(current, size, Collections.emptyList(), appAccountPage.getTotal());
        }
        List<Long> appAccountIds = appAccounts.stream().map(AppAccount::getId).collect(Collectors.toList());

        // get contentTagId;
        Map<Long, List<ContentTagListVO>> appAccountId2ContentTagList = contentTagService.getContentTagByAppAccountIds(appAccountIds)
                .stream()
                .collect(Collectors.toMap(ContentTagByUserAppAccountIdVO::getAppAccountId,
                        ContentTagByUserAppAccountIdVO::getContentTagList)
                );
        // get platform icon;
        List<Platform> platforms = platformService.getById(
                appAccounts.stream()
                        .map(AppAccount::getPlatformId)
                        .collect(Collectors.toSet())
        );
        //get price tag
        Map<Long, List<FlatPlatformTaskVO>> appAccountId2PlatformTaskVOList =
                platformContentService.getPlatformTaskVOByAppAccountIds(
                        appAccountIds,
                        platforms.stream()
                                .map(Platform::getId)
                                .collect(Collectors.toList()),
                        kolCriteriaQueryVO.getPriceOrder(),
                        kolCriteriaQueryVO.getPriceMin(), kolCriteriaQueryVO.getPriceMax());
        // allow null value.
        Map<Long, String> platformId2Icon = platforms.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getIcon()), HashMap::putAll);
        List<KolQueryListVO> kolQueryListVOList = new ArrayList<>(appAccounts.size());
        for (AppAccount appAccount : appAccounts) {
            kolQueryListVOList.add(
                    KolQueryListVO.builder()
                            .appAccountId(appAccount.getId())
                            .name(appAccount.getName())
                            .avatar(appAccount.getAvatar())
                            .gender(appAccount.getGender())
                            .area(appAccount.getArea())
                            .fansNum(appAccount.getFansNum())
                            .tagIds(appAccountId2ContentTagList.get(appAccount.getId()))
                            .platformIcon(platformId2Icon.get(appAccount.getPlatformId()))
                            .platformId(appAccount.getPlatformId())
                            .contentPriceList(appAccountId2PlatformTaskVOList.get(appAccount.getId()))
                            .build());

        }
        return new PageVO<>(current, size, kolQueryListVOList, appAccountPage.getTotal());
    }

    @Override
    public List<AppAccount> listByPlatformId(Long id) {
        return baseMapper.listByAppUserIdAndPlatformId(null, id);
    }

    @Override
    public AppAccount getBindByPlatformNameAndUserId(String platformName, Long userId) {
        return baseMapper.getBindByUserIdAndPlatformName(userId, platformName);
    }

    @Override
    public List<AppAccount> getBindByPlatformNameAndUserId(String platformName, List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        return baseMapper.getBindByUserIdListAndPlatformName(userIdList, platformName);
    }

    @Override
    public AppAccount getBindByPlatformNameAndAppUserId(String platformName, String uid) {
        return baseMapper.getBindByPlatformNameAndAppUserId(platformName, uid);
    }

    @Override
    public Boolean isBindDict(Long userId) {
        return Objects.nonNull(baseMapper.getBindByUserIdAndPlatformName(userId, YOUDAO_DICT.getName()));
    }

    @Override
    public PageVO<KolDetailVO> pageKolDetail(KolDetailQueryVO kolDetailQueryVO) {
        List<Long> userIdList = null;
        if (Objects.nonNull(kolDetailQueryVO.getName())) {
            userIdList = userDetailService.listUserIdByNickName(kolDetailQueryVO.getName());
            if (CollectionUtils.isEmpty(userIdList)) {
                return PageVO.emptyPage(kolDetailQueryVO.getCurrent(), kolDetailQueryVO.getSize());
            }
        }

        Page<AppAccount> page = this.lambdaQuery()
                .eq(Objects.nonNull(kolDetailQueryVO.getUserId()), AppAccount::getUserId, kolDetailQueryVO.getUserId())
                .in(Objects.nonNull(userIdList), AppAccount::getUserId, userIdList)
                .eq(AppAccount::getStatus, BIND)
                .orderByDesc(AppAccount::getCreateTime)
                .eq(AppAccount::getKolOperatorUserId, SecurityUtil.getUserId())
                .page(new Page<>(kolDetailQueryVO.getCurrent(), kolDetailQueryVO.getSize()));

        List<AppAccount> appAccounts = page.getRecords();
        Map<Long, List<ContentTagListVO>> appAccountId2ContentTagList = contentTagService.getContentTagByAppAccountIds(appAccounts.stream().map(AppAccount::getId).collect(Collectors.toList())).stream().collect(Collectors.toMap(ContentTagByUserAppAccountIdVO::getAppAccountId, ContentTagByUserAppAccountIdVO::getContentTagList));
        Set<Long> userIds = appAccounts.stream().map(AppAccount::getUserId).collect(Collectors.toSet());
        List<Role> kolRoles = roleService.listByRoleKeys(Arrays.asList(RoleEnum.KOL.getRoleKey(), RoleEnum.BRAND_KOL.getRoleKey()));
        Map<Long, Long> userId2RoleId = userRoleRelationService
                .listByUserIdsAndRoles(userIds, kolRoles)
                .stream()
                .collect(Collectors.toMap(UserRoleRelation::getUserId, UserRoleRelation::getRoleId));
        Map<Long, String> id2RoleName = roleService.list().stream().collect(Collectors.toMap(Role::getId, Role::getName));
        Map<Long, Platform> id2Platform = platformService.list().stream().collect(Collectors.toMap(Platform::getId, Function.identity()));
        Map<Long, String> userId2Nickname = userDetailService.listByUserIdsAndRoles(userIds, kolRoles).stream().collect(Collectors.toMap(UserDetail::getUserId, UserDetail::getNickname));

        List<KolDetailVO> kolDetailVOList = new ArrayList<>();
        appAccounts.forEach(appAccount ->
                kolDetailVOList.add(
                        appAccountMapper.do2KolDetailVO(appAccount,
                                userId2Nickname,
                                id2Platform,
                                id2RoleName,
                                appAccountId2ContentTagList,
                                userId2RoleId,
                                getAreaName(appAccount.getArea())
                        )
                )
        );
        return new PageVO<>(page.getCurrent(), page.getSize(), kolDetailVOList, page.getTotal());
    }

    private String getAreaName(Integer areaId) {
        StringBuilder sb = new StringBuilder();
        Area area = AreaUtil.getAllInlandCityMap().getOrDefault(areaId, null);
        while (area != null) {
            sb.insert(0, area.getName() + " ");
            area = AreaUtil.getAllInlandCityMap().get(area.getParentId());
        }
        return sb.toString();
    }

    @Override
    public List<Long> getIdByUserId(Long userId) {
        return baseMapper.getByUserId(userId).stream().map(AppAccount::getId).collect(Collectors.toList());
    }

    @Override
    public void saveOrUpdateMcn(String mcn, Long id) {
        AppAccount appAccount = baseMapper.selectById(id);
        if (Objects.isNull(appAccount)) {
            throw new CustomException(INVALID_PARAMETERS, "达人不存在");
        }
        if (!appAccount.getKolOperatorUserId().equals(SecurityUtil.getUserId())) {
            throw new CustomException(ACCESS_DENIED);
        }
        appAccount.setMcn(mcn);
        baseMapper.updateById(appAccount);
    }

    @Override
    public Boolean disableNotifySchemaUpgrade(Long appAccountId) {
        return baseMapper.updateNotifySchemaUpgrade2False(appAccountId);
    }
}

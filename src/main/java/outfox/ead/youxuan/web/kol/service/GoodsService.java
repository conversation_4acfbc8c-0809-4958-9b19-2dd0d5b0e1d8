package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.web.kol.controller.bo.ShowcaseComponentDetail2DictBO;
import outfox.ead.youxuan.web.kol.controller.query.GuessYouLikeQuery;
import outfox.ead.youxuan.web.kol.controller.query.KolGoodsQuery;
import outfox.ead.youxuan.web.kol.controller.vo.IsShoppingVO;
import outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentDetail2DictVO;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年10月18日 14:53
 */
public interface GoodsService {
    List<ShowcaseComponentDetail2DictVO> kolRecommendGoods(KolGoodsQuery kolGoodsQuery);

    /**
     * 添加正在购买商品的用户数据
     *
     * @param isShoppingVO -
     * @param cookie
     */
    void addIsShopping(IsShoppingVO isShoppingVO, String cookie);

    /**
     * 获取正在购买商品的用户数据
     */
    Collection<String> isShopping(String componentId);

    /**
     * 猜你喜欢
     * 获取除了 当前商品所属的达人好物推荐列表 以外的所有上架的商品
     */
    List<ShowcaseComponentDetail2DictVO> guessYouLike(GuessYouLikeQuery guessYouLikeQuery);

    /**
     * <ol>
     *     <li>状态为2且ShowcaseComponent的PromotionType=2的PostTaskOrder
     *     <li>品牌号创建的状态为2、PromotionType=2且已经审核过的ShowcaseComponent
     * </ol>
     *
     * @return 正在售卖的商品
     */
    Collection<ShowcaseComponentDetail2DictBO> getInProcessGoods();

    /**
     * 更新在线商品缓存
     *
     * @return -
     */
    Collection<ShowcaseComponentDetail2DictBO> updateInProcessGoodsCache();
}


package outfox.ead.youxuan.web.kol.service.impl;

import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.Platform;
import outfox.ead.youxuan.mapper.youxuan.PlatformMapper;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.service.PlatformService;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【Platform(平台信息)】的数据库操作Service实现
 * @date 2022-02-11 14:13:04
 */
@Service
public class PlatformServiceImpl extends YouxuanServiceImpl<PlatformMapper, Platform>
        implements PlatformService {


    @Override
    public Platform getByPlatformContentId(Long id) {
        return baseMapper.getByPlatformContentId(id);
    }

    @Override
    public List<Platform> getById(Collection<Long> platformId) {
        return baseMapper.getById(platformId);
    }

    @Override
    public Platform getByName(String name) {
        return baseMapper.selectOneByName(name);
    }
}





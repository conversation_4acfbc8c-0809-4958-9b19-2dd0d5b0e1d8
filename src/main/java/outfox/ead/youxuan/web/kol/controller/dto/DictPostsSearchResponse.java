package outfox.ead.youxuan.web.kol.controller.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DictPostsSearchResponse {
    private Integer code;
    private String msg;
    private Data data;



    @lombok.Data
    public static class Data {
        private Integer totalPages;
        private Long totalElements;
        private Integer numberOfElements;
        private Boolean first;
        private Boolean last;
        private Integer size;
        private List<Content> content;
        private Integer number;
        private Boolean empty;
    }

    @lombok.Data
    public static class Content {
        private String postId;
        private String postTitle;
        private Long publishTime;
        private Long goodEndTime;
        /**
         * 帖子没有审核时为UGC，审核成功后才生成帖子
         */
        private String ugcStatus;
        private String status;
        private List<Community> community;
    }

    @lombok.Data
    public static class Community {
        private Integer id;
        private String name;
        private String status;
        private Integer displayOrder;
        private Integer channelId;
        private String icon;
        private String creator;
        private String editor;
        private Long createTime;
        private Long updateTime;
        private String channelName;
    }
}

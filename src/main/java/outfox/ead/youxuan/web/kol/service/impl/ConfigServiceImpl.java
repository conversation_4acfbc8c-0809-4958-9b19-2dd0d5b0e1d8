package outfox.ead.youxuan.web.kol.service.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.Config;
import outfox.ead.youxuan.mapper.youxuan.ConfigMapper;
import outfox.ead.youxuan.web.ad.controller.vo.QualificationInfoVO;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.service.ConfigService;

import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.ConfigKeyConstants.QUALIFICATION_MAIL;
import static outfox.ead.youxuan.constants.ConfigKeyConstants.QUALIFICATION_PHONE;

/**
 * <AUTHOR>
 * @description 针对表【Config(配置表)】的数据库操作Service实现
 * @createDate 2022-07-25 10:59:17
 */
@Service
@AllArgsConstructor
public class ConfigServiceImpl extends YouxuanServiceImpl<ConfigMapper, Config>
        implements ConfigService {

    outfox.ead.youxuan.web.kol.controller.mapper.ConfigMapper configMapper;

    @Override
    public QualificationInfoVO queryConfig(Set<String> configKeys) {
        Map<String, Config> configKey2Obj = baseMapper.queryByKey(configKeys)
                .stream()
                .collect(Collectors.toMap(Config::getConfigKey, Function.identity()));
        return QualificationInfoVO.builder()
                .qualificationPhone(configKey2Obj.get(QUALIFICATION_PHONE).getConfigValue())
                .qualificationMail(configKey2Obj.get(QUALIFICATION_MAIL).getConfigValue())
                .build();
    }

    @Override
    public String queryConfig(String configKey) {
        return baseMapper.queryByKey(configKey).getConfigValue();
    }
}
package outfox.ead.youxuan.web.kol.service;


import outfox.ead.youxuan.entity.Config;
import outfox.ead.youxuan.web.ad.controller.vo.QualificationInfoVO;
import outfox.ead.youxuan.web.ad.service.YouxuanService;

import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【Config(配置表)】的数据库操作Service
* @createDate 2022-07-25 10:59:17
*/
public interface ConfigService extends YouxuanService<Config> {

    QualificationInfoVO queryConfig(Set<String> configKeys);

    String queryConfig(String configKey);
}
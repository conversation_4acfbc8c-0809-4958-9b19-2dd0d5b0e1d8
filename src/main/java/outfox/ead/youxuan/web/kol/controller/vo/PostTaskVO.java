package outfox.ead.youxuan.web.kol.controller.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022年06月09日 11:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PostTaskVO {
    private Long id;

    @ApiModelProperty(value = "任务订单ID")
    private String orderId;

    @ApiModelProperty(value = "推广内容类型ID")
    private Long platformContentId;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "橱窗组件ID")
    private Long showcaseComponentId;

    @ApiModelProperty(value = "橱窗组件")
    private ShowcaseComponentListVO showcaseComponentListVO;

    @ApiModelProperty(value = "推广开始日期")
    private LocalDateTime beginDate;

    @ApiModelProperty(value = "推广结束日期")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "投稿剩余数")
    private Long postRemain;

    @ApiModelProperty(value = "计费类型；0-其他，1-CPM，2-CPC，3-CPA，4-CPS")
    private Integer billingType;

    @ApiModelProperty(value = "佣金单价，以人民币分为单位")
    private Long commissionPrice;

    @ApiModelProperty(value = "结算周期;0-其他，1-月结，2-周结，3-日结")
    private Integer settlementInterval;

    @ApiModelProperty(value = "发布时间")
    private LocalDateTime createTime;

    @ApiModelProperty("是否已经领取过该任务")
    private Boolean pickedUp;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("推广文案")
    @JsonProperty("recommendText")
    private String promoteText;
}

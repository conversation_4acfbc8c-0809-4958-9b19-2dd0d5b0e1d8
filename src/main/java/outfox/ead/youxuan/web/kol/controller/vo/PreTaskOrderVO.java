package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreTaskOrderVO {

    private Long preTaskOrderId;

    private String kolAvatarUrl;

    private String kolName;

    private Long platformTaskId;

    private String platformTaskName;

    private Long platformContentId;

    private String platformContentName;

    @ApiModelProperty(value = "橱窗组件id")
    private Long showcaseComponentId;

    @ApiModelProperty(value = "跳转类型")
    private Integer switchType;

    private Long price;

    private String comment;

    private boolean isModified;
}

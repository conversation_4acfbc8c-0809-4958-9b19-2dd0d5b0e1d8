package outfox.ead.youxuan.web.kol.service.impl;

import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.mapper.youxuan.PlatformContentMapper;
import outfox.ead.youxuan.web.ad.service.UserDetailService;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.controller.vo.FlatPlatformTaskVO;
import outfox.ead.youxuan.web.kol.controller.vo.PlatformContentWithPriceVO;
import outfox.ead.youxuan.web.kol.controller.vo.PlatformTaskVO;
import outfox.ead.youxuan.web.kol.service.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @description 针对表【PlatformContent】的数据库操作Service实现
 * @createDate 2022-02-17 16:06:08
 */
@Service
@RequiredArgsConstructor
public class PlatformContentServiceImpl extends YouxuanServiceImpl<PlatformContentMapper, PlatformContent>
        implements PlatformContentService {

    private final KolPlatformContentRelationService kolPlatformContentRelationService;

    private final outfox.ead.youxuan.web.kol.controller.mapper.PlatformContentMapper platformContentMapper;

    private final PlatformService platformService;

    private final PlatformTaskService platformTaskService;

    @Lazy
    private final AppAccountService appAccountService;

    private final UserDetailService userDetailService;

    @Override
    public List<PlatformTaskVO> getPlatformContentByPlatformId(Long userId, Long platformId) {
        AppAccount appAccount = appAccountService.getByPlatformIdAndUserId(platformId, userId);
        if (Objects.isNull(appAccount)) {
            //no account in binding
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "没有绑定状态的用户...");
        }
        return getPlatformContentByAppAccountIdAndPlatformId(appAccount.getId(), platformId);
    }

    @Override
    public List<PlatformTaskVO> getPlatformContentByAppAccountIdAndPlatformId(Long appAccountId, Long platformId) {
        //collect and mapping data.
        List<PlatformTaskVO> platformTaskVOs = platformTaskService.getListByPlatformId(platformId);
        if (CollectionUtils.isEmpty(platformTaskVOs)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "PLATFORM不存在，或PLATFORM下不存在任务");
        }
        List<PlatformContentWithPriceVO> platformContentWithPriceVOS =
                platformContentMapper.do2PlatformContentWithPriceVO(baseMapper.getListByPlatformTaskIds(
                        platformTaskVOs.stream()
                                .map(PlatformTaskVO::getId)
                                .collect(Collectors.toList())
                ));
        Map<Long, KolPlatformContentRelation> platformContentId2KolPlatformContentRelation =
                kolPlatformContentRelationService.getByAppAccountIdAndPlatformContentId(
                        appAccountId,
                        platformContentWithPriceVOS.stream()
                                .map(PlatformContentWithPriceVO::getId)
                                .collect(Collectors.toList())
                )
                        .stream()
                        .collect(Collectors.toMap(KolPlatformContentRelation::getPlatformContentId, Function.identity()));
        // reduce them.
        for (PlatformContentWithPriceVO platformContentWithPriceVO : platformContentWithPriceVOS) {
            KolPlatformContentRelation kolPlatformContentRelation =
                    platformContentId2KolPlatformContentRelation.get(platformContentWithPriceVO.getId());
            platformContentWithPriceVO.setPrice(
                    Objects.nonNull(kolPlatformContentRelation) ? kolPlatformContentRelation.getPrice() : null
            );
        }

        for (PlatformTaskVO platformTaskVO : platformTaskVOs) {
            platformTaskVO.setContentTypeList(
                    platformContentWithPriceVOS.stream()
                            .filter(item -> platformTaskVO.getId().equals(item.getPlatformTaskId()))
                            .collect(Collectors.toList())
            );
        }
        return platformTaskVOs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdatePrice(Long userId, Long platformContentId, Long price, Role currentRole) {
        Platform platform = platformService.getByPlatformContentId(platformContentId);
        AppAccount appAccount = appAccountService.getByPlatformIdAndUserId(platform.getId(), userId);
        if (Objects.isNull(appAccount)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "用户和平台没有正绑定的账户");
        }
        KolPlatformContentRelation kolPlatformContentRelation =
                kolPlatformContentRelationService.getByAppAccountIdAndPlatformContentId(appAccount.getId(), platformContentId);
        if (Objects.isNull(kolPlatformContentRelation)) {
            kolPlatformContentRelation = new KolPlatformContentRelation();
            kolPlatformContentRelation.setPlatformContentId(platformContentId);
            kolPlatformContentRelation.setAppAccountId(appAccount.getId());
        }
        kolPlatformContentRelation.setPrice(price);
        kolPlatformContentRelationService.saveOrUpdate(kolPlatformContentRelation);
        userDetailService.calStageAndUpdate(userId, currentRole);
        return kolPlatformContentRelation.getId();
    }

    @Override
    public List<PlatformContentWithPriceVO> getPlatformContentWithPriceVOByPlatformTaskIds(Collection<Long> platformTaskIds) {

        return platformContentMapper.do2PlatformContentWithPriceVO(baseMapper.getListByPlatformTaskIds(platformTaskIds));
    }

    @Override
    public List<PlatformContent> getPlatformContentByIds(List<Long> ids) {
        return baseMapper.getByIds(ids);
    }

    @Override
    public List<PlatformContent> getByPlatformTaskId(Long platformTaskId) {
        return baseMapper.getListByPlatformTaskId(platformTaskId);
    }


    @Override
    public Map<Long, List<FlatPlatformTaskVO>> getPlatformTaskVOByAppAccountIds(List<Long> appAccountIds, List<Long> platformIds,
                                                                                Integer priceOrder, Long priceMin, Long priceMax) {
        //collect data.
        Map<Long, List<KolPlatformContentRelation>> appAccountId2KolPlatformContentRelation =
                kolPlatformContentRelationService.getByAppAccountIds(appAccountIds, priceOrder, priceMin, priceMax)
                        .stream()
                        .collect(Collectors.groupingBy(KolPlatformContentRelation::getAppAccountId));

        Map<Long, PlatformTaskVO> id2PlatformTaskVOList = platformTaskService.getListByPlatformIds(platformIds)
                .stream().collect(Collectors.toMap(PlatformTaskVO::getId, Function.identity()));

        Map<Long, PlatformContentWithPriceVO> platformContentId2PlatformContentWithPriceVO =
                this.getPlatformContentWithPriceVOByPlatformTaskIds(
                        id2PlatformTaskVOList.keySet()
                ).stream()
                        .collect(Collectors.toMap(PlatformContentWithPriceVO::getId, Function.identity()));

        Map<Long, List<FlatPlatformTaskVO>> result = new HashMap<>(appAccountId2KolPlatformContentRelation.size());
        //process data
        for (Long appAccountId : appAccountId2KolPlatformContentRelation.keySet()) {
            List<KolPlatformContentRelation> kolPlatformContentRelationList = appAccountId2KolPlatformContentRelation.get(appAccountId);
            LinkedList<FlatPlatformTaskVO> flatPlatformTaskVOList = new LinkedList<>();

            for (KolPlatformContentRelation kolPlatformContentRelation : kolPlatformContentRelationList) {
                PlatformContentWithPriceVO platformContentWithPriceVO = platformContentId2PlatformContentWithPriceVO.get(kolPlatformContentRelation.getPlatformContentId());
                PlatformTaskVO platformTaskVO = id2PlatformTaskVOList.get(platformContentWithPriceVO.getPlatformTaskId());
                FlatPlatformTaskVO flatPlatformTaskVO = FlatPlatformTaskVO.builder().price(kolPlatformContentRelation.getPrice())
                        .name(platformContentWithPriceVO.getName())
                        .id(platformContentWithPriceVO.getId())
                        .platformTaskId(platformTaskVO.getId())
                        .platformTaskName(platformTaskVO.getName())
                        .build();
                flatPlatformTaskVOList.add(flatPlatformTaskVO);
            }
            result.put(appAccountId, flatPlatformTaskVOList);
        }
        return result;
    }

    @Override
    public PlatformContent getByPlatformTaskIdAndContentName(Long platformTaskId, String platformContentName) {
        return baseMapper.getByPlatformTaskIdAndContentName(platformTaskId, platformContentName);
    }
}

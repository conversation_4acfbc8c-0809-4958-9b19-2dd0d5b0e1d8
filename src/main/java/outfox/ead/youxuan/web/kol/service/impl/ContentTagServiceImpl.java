package outfox.ead.youxuan.web.kol.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.constants.ContentMarketingConstants;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.ContentTag;
import outfox.ead.youxuan.entity.KolContentTagRelation;
import outfox.ead.youxuan.mapper.youxuan.ContentTagMapper;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.controller.dto.ContentTagUsageCountDTO;
import outfox.ead.youxuan.web.kol.controller.vo.*;
import outfox.ead.youxuan.web.kol.service.ContentTagService;
import outfox.ead.youxuan.web.kol.service.KolContentTagRelationService;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR> Li
 * @description 针对表【ContentTag(达人内容标签)】的数据库操作Service实现
 * @createDate 2022-02-09 18:03:39
 */
@Service
public class ContentTagServiceImpl extends YouxuanServiceImpl<ContentTagMapper, ContentTag>
        implements ContentTagService {

    private final KolContentTagRelationService kolContentTagRelationService;

    private final outfox.ead.youxuan.web.kol.controller.mapper.ContentTagMapper contentTagMapper;

    public ContentTagServiceImpl(KolContentTagRelationService kolContentTagRelationService,
                                 outfox.ead.youxuan.web.kol.controller.mapper.ContentTagMapper contentTagMapper) {
        this.kolContentTagRelationService = kolContentTagRelationService;
        this.contentTagMapper = contentTagMapper;
    }

    @Override
    public PageVO<ContentTagListVO> getPageList(ContentTagCriteriaQueryVO contentTagCriteriaQueryVO) {
        Page<ContentTag> contentTags = baseMapper.pageListByStatusAndType(
                contentTagCriteriaQueryVO.getStatus(),
                ContentMarketingConstants.CONTENT_TAG_CONTENT,
                contentTagCriteriaQueryVO.getCurrent(), contentTagCriteriaQueryVO.getSize());
        List<ContentTag> contentTagList = contentTags.getRecords();
        if (CollectionUtils.isEmpty(contentTagList)) {
            return PageVO.emptyPage(contentTagCriteriaQueryVO.getCurrent(), contentTagCriteriaQueryVO.getSize());
        }
        List<Long> contentTagIds = contentTagList.stream().map(ContentTag::getId).collect(Collectors.toList());
        List<ContentTagUsageCountDTO> contentTagUsageCountById = kolContentTagRelationService.getContentTagUsageCountByTagIds(contentTagIds);
        return new PageVO<>(contentTagCriteriaQueryVO.getCurrent(),
                contentTagCriteriaQueryVO.getSize(),
                mergeContentTagAndContentTagUsageCountDTOToVo(contentTagList, contentTagUsageCountById),
                contentTags.getTotal());
    }

    @Override
    public List<ContentTagListVO> getAllValidContentTagList() {
        List<ContentTag> contentTagList = baseMapper.selectAllByStatus(ContentMarketingConstants.CONTENT_TAG_STATUS_NORMAL);
        return contentTagMapper.listDoToContentTagListVo(contentTagList);
    }

    @Override
    public Long saveOrUpdate(ContentTagSaveOrUpdateVO contentTagSaveOrUpdateVO, Long userId) {
        if (Objects.nonNull(contentTagSaveOrUpdateVO.getId())) {
            ContentTag contentTag = baseMapper.selectById(contentTagSaveOrUpdateVO.getId());
            updatePreCheck(contentTag, contentTagSaveOrUpdateVO);
            ContentTag newContentTag = contentTagMapper.contentTagSaveOrUpdateVOToDo(contentTagSaveOrUpdateVO);
            newContentTag.setModifier(userId);
            return (long) baseMapper.updateById(newContentTag);
        } else {
            nameRepeatCheck(contentTagSaveOrUpdateVO.getName());
            ContentTag contentTag = contentTagMapper.contentTagSaveOrUpdateVOToDo(contentTagSaveOrUpdateVO);
            contentTag.setModifier(userId);
            contentTag.setCreator(userId);
            baseMapper.insert(contentTag);
            return contentTag.getId();
        }
    }

    @Override
    public Long saveOrUpdateUserContentTag(Long appAccountId, List<ContentTagUserUpdateVO> contentTagUserUpdateVOList, Long opUserId) {
        kolContentTagRelationService.delByAppAccountId(appAccountId);
        List<Long> contentTagIds = contentTagUserUpdateVOList.stream()
                .map(ContentTagUserUpdateVO::getId)
                .collect(Collectors.toList());
        tagStatusPreCheck(contentTagIds);
        kolContentTagRelationService.batchBuildAndSave(
                appAccountId,
                contentTagIds);
        return (long) contentTagUserUpdateVOList.size();
    }

    @Override
    public List<ContentTagByUserAppAccountIdVO> getContentTagByAppAccountIds(List<Long> appAccountIds) {
        List<KolContentTagRelation> kolContentTagRelationList = kolContentTagRelationService.getByAppAccountIds(appAccountIds);
        if (CollectionUtils.isEmpty(kolContentTagRelationList)) {
            return Collections.emptyList();
        }
        Map<Long, List<KolContentTagRelation>> appAccountIdToKolContentTagRelations = kolContentTagRelationList
                .stream()
                .collect(groupingBy(KolContentTagRelation::getAppAccountId));
        List<ContentTag> contentTagList = baseMapper.selectByIds
                (kolContentTagRelationList
                        .stream()
                        .map(KolContentTagRelation::getContentTagId)
                        .collect(Collectors.toList())
                );
        List<ContentTagByUserAppAccountIdVO> contentTagByAppAccountIdVOs = new ArrayList<>(appAccountIdToKolContentTagRelations.size());
        for (Long appAccountId : appAccountIdToKolContentTagRelations.keySet()) {
            List<Long> contentTagIds = appAccountIdToKolContentTagRelations.get(appAccountId)
                    .stream()
                    .map(KolContentTagRelation::getContentTagId)
                    .collect(Collectors.toList());
            ContentTagByUserAppAccountIdVO contentTagByUserIdVO =
                    ContentTagByUserAppAccountIdVO
                            .builder()
                            .appAccountId(appAccountId)
                            .contentTagList(contentTagMapper.listDoToContentTagListVo(
                                    contentTagList.stream()
                                            .filter(item -> contentTagIds.contains(item.getId()))
                                            .collect(Collectors.toList()))).build();
            contentTagByAppAccountIdVOs.add(contentTagByUserIdVO);
        }
        return contentTagByAppAccountIdVOs;
    }

    @Override
    public List<ContentTagListVO> getValidByType(Integer type) {
        return contentTagMapper.listDoToContentTagListVo(
                baseMapper.selectByTypeAndStatus(type, ContentMarketingConstants.CONTENT_TAG_STATUS_NORMAL)
        );
    }

    private List<ContentTagListVO> mergeContentTagAndContentTagUsageCountDTOToVo(List<ContentTag> contentTags, List<ContentTagUsageCountDTO> contentTagUsageCountDTOs) {
        Map<Long, Long> contentTagIdToUsageCountMap = contentTagUsageCountDTOs
                .stream()
                .collect(Collectors.toMap(
                        ContentTagUsageCountDTO::getContentTagId,
                        ContentTagUsageCountDTO::getUsageCount)
                );
        List<ContentTagListVO> mergeResult = new ArrayList<>(contentTags.size());
        for (ContentTag contentTag : contentTags) {
            ContentTagListVO contentTagListVO = contentTagMapper.doToContentTagListVo(contentTag);
            contentTagListVO.setUsageCount(contentTagIdToUsageCountMap.get(contentTag.getId()));
            mergeResult.add(contentTagListVO);
        }
        return mergeResult;
    }

    private void updatePreCheck(ContentTag contentTag, ContentTagSaveOrUpdateVO contentTagSaveOrUpdateVO) {
        String name = contentTagSaveOrUpdateVO.getName();

        if (ContentMarketingConstants.CONTENT_TAG_STATUS_INVALID == contentTag.getStatus()
                && StringUtils.isNotBlank(name)) {
            throw new CustomException(ResponseType.CONTENT_TAG_IS_INVALID);
        }

        if (StringUtils.isNotBlank(name)) {
            if (name.length() <= 0 || name.length() > 6) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS);
            }
            //name not equal then check repeat
            if (!StringUtils.equals(contentTag.getName(), contentTagSaveOrUpdateVO.getName())) {
                nameRepeatCheck(name);
            }
        }

        Integer status = contentTagSaveOrUpdateVO.getStatus();
        if (Objects.nonNull(status)) {
            if (ContentMarketingConstants.CONTENT_TAG_STATUS_INVALID == status) {
                // Is still in usage?
                ContentTagUsageCountDTO contentTagUsageCountDTO = kolContentTagRelationService.getContentTagUsageCountByContentTagId(contentTagSaveOrUpdateVO.getId());
                if (contentTagUsageCountDTO.getUsageCount() != 0) {
                    throw new CustomException(ResponseType.CONTENT_TAG_STILL_IN_USAGE);
                }
            }
        }
    }

    private void nameRepeatCheck(String tagName) {
        // Does name of tag exist already?
        Long nameCount = baseMapper.countByName(tagName);
        if (nameCount > 0) {
            throw new CustomException(ResponseType.CONTENT_TAG_NAME_EXIST);
        }
    }

    private void tagStatusPreCheck(List<Long> tagIds) {
        List<ContentTag> contentTagList = baseMapper.selectByIds(tagIds);
        if (tagIds.size() != contentTagList.size()) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        for (ContentTag contentTag : contentTagList) {
            if (ContentMarketingConstants.CONTENT_TAG_STATUS_NORMAL != contentTag.getStatus()) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS);
            }
        }
    }
}

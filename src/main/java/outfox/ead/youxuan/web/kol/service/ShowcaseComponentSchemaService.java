package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.ShowcaseComponentSchema;
import outfox.ead.youxuan.web.ad.service.YouxuanService;
import outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentSchemaListVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ShowcaseComponentSchema(橱窗组件样式)】的数据库操作Service
* @createDate 2022-05-30 19:55:45
*/
public interface ShowcaseComponentSchemaService extends YouxuanService<ShowcaseComponentSchema> {

    /**
     * 通过推广类型查询有效的样式
     * @param promotionType 1-品牌宣传，2-商品售卖，3-线索留资，4-应用推广
     */
    List<ShowcaseComponentSchemaListVO> listValidByPromotionType(Integer promotionType);

}
package outfox.ead.youxuan.web.kol.service.impl;

import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.PreTaskOrder;
import outfox.ead.youxuan.mapper.youxuan.PreTaskOrderMapper;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.service.PreTaskOrderService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【PreTaskOrder(已选达人表)】的数据库操作Service实现
 * @createDate 2022-02-23 12:34:32
 */
@Service
public class PreTaskOrderServiceImpl extends YouxuanServiceImpl<PreTaskOrderMapper, PreTaskOrder>
        implements PreTaskOrderService {

    @Override
    public PreTaskOrder getOne(Long accountId, Long platformContentId, Long userId) {
        return baseMapper.getOne(accountId, platformContentId, userId);
    }

    @Override
    public List<PreTaskOrder> getByCreator(Long userId) {
        return baseMapper.getByCreator(userId);
    }

    @Override
    public Long getCountByCreator(Long userId) {
        return baseMapper.getCountByCreator(userId);
    }

    @Override
    public boolean deleteByIdAndCreatorId(Long id, Long userId) {
        return baseMapper.deleteById(id, userId);
    }

    @Override
    public List<PreTaskOrder> getListByPlatformContentIdsAndUserId(List<Long> platformContentIds, Long userId) {
        return baseMapper.getListByPlatformContentIdsAndUserId(platformContentIds, userId);
    }

    @Override
    public List<PreTaskOrder> getValidById(List<Long> ids) {
        return baseMapper.getValidByIds(ids);
    }

    @Override
    public boolean deleteByIds(List<Long> ids) {
        return baseMapper.deleteByIds(ids);
    }

    @Override
    public boolean updateCommentAndShowcaseComponentById(Long id, String comment, Long showcaseComponentId) {
        return baseMapper.updateCommentById(id, comment, showcaseComponentId);
    }
}

package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.annotation.YoudaoIntranet;
import outfox.ead.youxuan.web.ad.controller.vo.BatchSaveDetailVO;
import outfox.ead.youxuan.web.kol.service.ItemService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年08月30日 15:55
 */
@RestController
@BaseResponse
@Validated
@RequiredArgsConstructor
@RequestMapping("/item")
@Api(tags = "外部商品导入接口")
public class ItemController {
    private final ItemService itemService;
    @PostMapping("jd/groupIds")
    @AccessControl(roles = RoleEnum.SPONSOR)
    @YoudaoIntranet
    public BatchSaveDetailVO importJdItemByGroupIds(@RequestBody List<Long> groupIds) {
        return itemService.importJdItemByGroupIds(groupIds);
    }

    @PostMapping("jd/urls")
    @AccessControl(roles = RoleEnum.SPONSOR)
    @YoudaoIntranet
    public BatchSaveDetailVO importJdItemByUrls(@RequestBody List<String> urls) {
        return itemService.importJdItemByUrls(urls);
    }
}

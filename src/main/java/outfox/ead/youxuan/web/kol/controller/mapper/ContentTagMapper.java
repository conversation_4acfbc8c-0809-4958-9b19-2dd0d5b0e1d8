package outfox.ead.youxuan.web.kol.controller.mapper;

import org.mapstruct.Mapper;
import outfox.ead.youxuan.entity.ContentTag;
import outfox.ead.youxuan.web.kol.controller.vo.ContentTagListVO;
import outfox.ead.youxuan.web.kol.controller.vo.ContentTagSaveOrUpdateVO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface ContentTagMapper {
    ContentTagListVO doToContentTagListVo(ContentTag contentTag);

    List<ContentTagListVO> listDoToContentTagListVo(List<ContentTag> contentTags);

    ContentTag contentTagSaveOrUpdateVOToDo(ContentTagSaveOrUpdateVO contentTagSaveOrUpdateVO);
}

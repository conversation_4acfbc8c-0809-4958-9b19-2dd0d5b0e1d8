package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShowcaseComponentModifyVO {

    private Long id;

    @ApiModelProperty(value = " 组件名称")
    @NotNull
    private String name;

    @ApiModelProperty(value = " deeplink链接")
    private String deepLink;

    @ApiModelProperty(value = " 落地页链接")
    private String landingPageUrl;

    @ApiModelProperty(value = " 备用落地页链接")
    private String backupLandingPageUrl;

    @ApiModelProperty(value = " 是否自动为跳转链接添加来源参数配置项")
    private Boolean appendOutVendor;

    @ApiModelProperty(value = " 微信小程序原始ID")
    private String microProgramId;

    @ApiModelProperty(value = " 微信小程序目标页面路径")
    private String microProgramPath;

    @ApiModelProperty(value = " 应用平台；0-不限，1-Android，2-iOS")
    private Integer appPlatform;

    @ApiModelProperty(value = " 安卓应用包名")
    private String androidPackageName;

    @ApiModelProperty(value = " iOS应用ID")
    private String iosAppId;

    @ApiModelProperty(value = " 引导文案")
    private String leadText;

    @ApiModelProperty(value = " 商品名称")
    private String itemName;

    @ApiModelProperty(value = " 商品价格")
    @Range(min = 0, max = 99_999_999_00L, message = "商品价格不合法")
    private Long itemPrice;

    @ApiModelProperty(value = " 划线价")
    @Range(min = 0, max = 99_999_999_00L, message = "商品价格不合法")
    private Long strikeThroughPrice;

    @ApiModelProperty(value = " 按钮文案")
    private String buttonText;

    @ApiModelProperty(value = " 推广图片的URL")
    private String promoteImageUrl;

    @ApiModelProperty(value = " 推广标题")
    private String promoteTitle;

    @ApiModelProperty(value = " 推广文案")
    private String promoteText;

    @ApiModelProperty(value = " 应用名称")
    private String appName;

    @ApiModelProperty(value = " 是否标记为广告")
    private Boolean markAsAd;

    @ApiModelProperty(value = " 在存在关联关系的条件下更新？")
    private Boolean confirmUpdate;
}

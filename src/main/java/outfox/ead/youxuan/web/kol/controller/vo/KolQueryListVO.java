package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KolQueryListVO {

    private Long appAccountId;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("地点")
    private Integer area;

    @ApiModelProperty("粉丝数")
    private Long fansNum;

    @ApiModelProperty("内容标签们")
    private List<ContentTagListVO> tagIds;

    @ApiModelProperty("平台logo们")
    private String platformIcon;

    @ApiModelProperty("平台ID")
    private Long platformId;

    @ApiModelProperty("报价列表")
    private List<FlatPlatformTaskVO> contentPriceList;

    @ApiModelProperty("所属mcn")
    private String mcn;
}

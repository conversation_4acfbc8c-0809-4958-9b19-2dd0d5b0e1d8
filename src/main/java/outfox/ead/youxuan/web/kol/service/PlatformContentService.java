package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.PlatformContent;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.web.ad.service.YouxuanService;
import outfox.ead.youxuan.web.kol.controller.vo.FlatPlatformTaskVO;
import outfox.ead.youxuan.web.kol.controller.vo.PlatformContentWithPriceVO;
import outfox.ead.youxuan.web.kol.controller.vo.PlatformTaskVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【PlatformContent】的数据库操作Service
 * @createDate 2022-02-17 16:06:08
 */
public interface PlatformContentService extends YouxuanService<PlatformContent> {

    List<PlatformTaskVO> getPlatformContentByPlatformId(Long userId, Long platformId);

    List<PlatformTaskVO> getPlatformContentByAppAccountIdAndPlatformId(Long appAccountId, Long platformId);

    Long saveOrUpdatePrice(Long userId, Long platformContentId, Long price, Role currentRole);

    List<PlatformContentWithPriceVO> getPlatformContentWithPriceVOByPlatformTaskIds(Collection<Long> platformTaskIds);

    List<PlatformContent> getPlatformContentByIds(List<Long> ids);

    List<PlatformContent> getByPlatformTaskId(Long platformTaskId);


    /**
     * @return Map<AppAccountId, List < FlatPlatformTaskVO>>
     */
    Map<Long, List<FlatPlatformTaskVO>> getPlatformTaskVOByAppAccountIds(List<Long> appAccountIds, List<Long> platformIds,
                                                                         Integer priceOrder, Long priceMin, Long priceMax);

    PlatformContent getByPlatformTaskIdAndContentName(Long platformTaskId, String platformContentName);
}

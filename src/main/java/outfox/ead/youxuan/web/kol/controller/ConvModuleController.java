package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.hibernate.validator.constraints.Range;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleSaveOrUpdateVO;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleSwitchUpdateVO;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleVO;
import outfox.ead.youxuan.web.kol.service.ConvModuleService;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@BaseResponse
@Validated
@AllArgsConstructor
@RequestMapping("/conv_module")
@Api(tags = "转化组件设置")
@AccessControl(roles = RoleEnum.BRAND_KOL)
public class ConvModuleController {

    private final ConvModuleService convModuleService;

    @ApiOperation(value = "保存或更新主页转化工具")
    @PostMapping
    public Long saveOrUpdateConvModule(@RequestBody @Valid ConvModuleSaveOrUpdateVO convModuleSaveOrUpdateVo) {
        return convModuleService.saveOrUpdate(convModuleSaveOrUpdateVo, SecurityUtil.getUserId());
    }

    @ApiOperation(value = "获取主页转化工具的详情，用于展示或修改")
    @GetMapping
    public ConvModuleVO getConvModuleDetail(@RequestParam
                                            @Range(min = 0, max = 3, message = "type错误")
                                            @ApiParam(value = "类型：0-官方网站；1-应用下载；2-联系电话；3-推广活动")
                                                    Integer type) {
        return convModuleService.getByUserIdAndType(type, SecurityUtil.getUserId());
    }

    @ApiOperation(value = "改变开关状态")
    @PostMapping("switch")
    public void updateSwitch(@RequestBody ConvModuleSwitchUpdateVO convModuleSwitchUpdateVO) {
        convModuleService.updateSwitch(convModuleSwitchUpdateVO.getId(),
                convModuleSwitchUpdateVO.getType(),
                SecurityUtil.getUserId(),
                convModuleSwitchUpdateVO.getIsOpen());
    }

}

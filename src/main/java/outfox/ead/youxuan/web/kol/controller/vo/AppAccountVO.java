package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年02月09日 3:51 下午
 */
@Data
public class AppAccountVO {
    private Long id;

    @ApiModelProperty("媒体平台id")
    private Long platformId;

    @ApiModelProperty("媒体平台logo")
    private String icon;

    @ApiModelProperty("媒体平台名称")
    private String platformName;

    @ApiModelProperty("媒体账户昵称")
    private String name;

    @ApiModelProperty("媒体账户id")
    private String appUserId;

    @ApiModelProperty("媒体账户头像")
    private String avatar;

    @ApiModelProperty("性别，0-男 1-女")
    private Integer gender;

    @ApiModelProperty("粉丝数")
    private Long fansNum;

    @ApiModelProperty("状态 0-正常，1-暂停，2-删除")
    private Integer status;

    @ApiModelProperty("是否在接单")
    private Boolean inService;

    @ApiModelProperty("地区")
    private Integer area;
}

package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class ConvModuleVO {

    private Long id;

    @ApiModelProperty("0-官方网站；1-应用下载；2-联系电话；3-推广活动")
    private Integer type;

    @ApiModelProperty("启用？")
    private boolean enabled;

    private Long appAccountId;

    @ApiModelProperty("显示名称")
    private String displayName;

    @ApiModelProperty("官方网站和推广活动的链接地址")
    private String url;

    @ApiModelProperty("安卓应用包名")
    private String androidPackageName;

    @ApiModelProperty("iOS下载地址")
    private String iosDownloadUrl;

    @ApiModelProperty("电话（支持带区号或400）")
    private String phone;

    @ApiModelProperty("开启跳转微信小程序")
    private Boolean enabledWechatMicroProgramSwitch;

    @ApiModelProperty("微信小程序原始ID")
    private String wechatMicroProgramRawId;

    @ApiModelProperty("微信小程序路径")
    private String wechatMicroProgramPath;
}

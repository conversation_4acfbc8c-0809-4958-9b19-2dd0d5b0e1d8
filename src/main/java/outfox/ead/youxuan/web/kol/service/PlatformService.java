package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.Platform;
import outfox.ead.youxuan.web.ad.service.YouxuanService;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【Platform(平台信息)】的数据库操作Service
 * @date 2022-02-11 14:13:04
 */
public interface PlatformService extends YouxuanService<Platform> {

    Platform getByPlatformContentId(Long id);

    List<Platform> getById(Collection<Long> platformId);

    Platform getByName(String name);
}

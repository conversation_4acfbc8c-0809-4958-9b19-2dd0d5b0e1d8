package outfox.ead.youxuan.web.kol.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.UrlValidator;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.youxuan.constants.*;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.mapper.youxuan.ShowcaseComponentMapper;
import outfox.ead.youxuan.util.JacksonUtil;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserDetailService;
import outfox.ead.youxuan.web.ad.service.UserRelationService;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.controller.bo.Good;
import outfox.ead.youxuan.web.kol.controller.bo.ShowcaseComponentContentBO;
import outfox.ead.youxuan.web.kol.controller.bo.ShowcaseComponentDetail2DictBO;
import outfox.ead.youxuan.web.kol.controller.bo.ShowcaseComponentSwitchLinkBO;
import outfox.ead.youxuan.web.kol.controller.vo.*;
import outfox.ead.youxuan.web.kol.service.*;

import java.io.Serializable;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.ContentMarketingConstants.*;

/**
 * <AUTHOR> Li
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ShowcaseComponentServiceImpl extends YouxuanServiceImpl<ShowcaseComponentMapper, ShowcaseComponent>
        implements ShowcaseComponentService {

    private final AppAccountService appAccountService;

    private final ShowcaseComponentWhiteListService showcaseComponentWhiteListService;

    private final TaskOrderService taskOrderService;

    private final RoleService roleService;

    private final UserRelationService userRelationService;

    private final outfox.ead.youxuan.web.kol.controller.mapper.ShowcaseComponentMapper showcaseComponentMapper;

    private final static UrlValidator HTTP_URL_VALIDATOR = new UrlValidator(new String[]{"http", "https"});

    private final UserDetailService userDetailService;

    @Value("${dict.mp.ios}")
    private String iosMpId;
    @Value("${dict.mp.android}")
    private String androidMpId;

    private final DictService dictService;

    private final CacheManager cacheManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = CacheNameConstant.SHOWCASE_ID_CACHE_NAME, key = "#id")
    public void audit(String id, Integer auditStatus, Long userId) {
        audit(auditStatus, ((ShowcaseComponentService) AopContext.currentProxy()).getById(id), userId);
    }

    private void audit(Integer auditStatus, ShowcaseComponent showcaseComponent, Long userId) {
        if (showcaseComponent.getAuditStatus().equals(auditStatus) || showcaseComponent.getDeleted()) {
            return;
        }
        if (!showcaseComponent.getAuditStatus().equals(SHOWCASE_COMPONENT_STATUS_AUDIT_REJECT)
                && !showcaseComponent.getAuditStatus().equals(SHOWCASE_COMPONENT_STATUS_AUDITING)) {
            return;
        }

        showcaseComponent.setAuditStatus(auditStatus);
        showcaseComponent.setAuditor(userId);
        showcaseComponent.setAuditDatetime(LocalDateTime.now());
        if (auditStatus.equals(SHOWCASE_COMPONENT_STATUS_NORMAL)) {
            showcaseComponent.setAuditedContent(JacksonUtil.deepCopy(showcaseComponent, ShowcaseComponent.class));
            showcaseComponent.setAuditedPromoteTitle();
            syncComponentIncremental2BrandKolIfNeed(showcaseComponent);
        }
        baseMapper.updateById(showcaseComponent);
    }


    /**
     * 当广告主的橱窗组件过审以后，向绑定了其账号且打开了同步开关的账号推送审核完毕的组件
     *
     * @param showcaseComponent
     */
    private void syncComponentIncremental2BrandKolIfNeed(ShowcaseComponent showcaseComponent) {
        if (showcaseComponent.getRoleId().equals(roleService.getByRoleKey(RoleEnum.SPONSOR.getRoleKey()).getId())) {
            List<Long> validKolUserId = userRelationService.listValidBySponsorId(showcaseComponent.getCreator())
                    .stream()
                    .map(UserRelation::getKolUserId)
                    .collect(Collectors.toList());
            validKolUserId = userDetailService.listByUserIdAndSyncStatus(validKolUserId, true)
                    .stream()
                    .map(UserDetail::getUserId)
                    .collect(Collectors.toList());
            Map<Long, AppAccount> userId2BoundAppAccount = appAccountService.getBindByPlatformNameAndUserId(
                    PlatformEnum.YOUDAO_DICT.getName(), validKolUserId
            ).stream().collect(Collectors.toMap(AppAccount::getUserId, Function.identity()));
            syncSaveOrUpdate(Collections.singletonList(showcaseComponent), validKolUserId, userId2BoundAppAccount);
        }
    }


    /**
     * 将source的组件全部同步到target上
     * 当品牌号的是否同步开关状态变为true时会触发这里
     *
     * @param sourceUserId 目前是广告主账号
     * @param targetUserid 目前是目标账号 由上层方法保证source和target存在绑定关系且同步开关是打开的
     */
    @Override
    public void syncComponentFully2BrandKolIfNeed(Long sourceUserId, Long targetUserid) {
        AppAccount targetAppAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(),
                targetUserid);
        if (Objects.isNull(targetAppAccount)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "当前用户未绑定词典");
        }
        List<ShowcaseComponent> showcaseComponentsFromSource = baseMapper.list(false,
                sourceUserId,
                roleService.getByRoleKey(RoleEnum.SPONSOR.getRoleKey()),
                true);
        this.syncSaveOrUpdate(showcaseComponentsFromSource,
                Collections.singletonList(targetUserid),
                new HashMap<Long, AppAccount>() {{
                    put(targetAppAccount.getUserId(), targetAppAccount);
                }}
        );

    }

    /**
     * 将source的组件同步到target上
     *
     * @param showcaseComponentList  需要同步的橱窗组件列表
     * @param validBoundUserId       需要同步到的目标用户id
     * @param userId2BoundAppAccount 需要同步到的用户现在正绑定的词典账号
     */
    private void syncSaveOrUpdate(List<ShowcaseComponent> showcaseComponentList,
                                  List<Long> validBoundUserId,
                                  Map<Long, AppAccount> userId2BoundAppAccount) {
        List<Long> sourceIds = showcaseComponentList
                .stream()
                .map(ShowcaseComponent::getId)
                .collect(Collectors.toList());
        Map<Long, List<ShowcaseComponent>> sourceId2Objects =
                // 只同步处于绑定状态的AppAccount.
                baseMapper.listBySourceId(sourceIds).stream().filter(a ->
                        userId2BoundAppAccount.get(a.getCreator()) != null
                                &&
                                userId2BoundAppAccount.get(a.getCreator()).getId().equals(a.getAppAccountId())

                ).collect(Collectors.groupingBy(ShowcaseComponent::getSourceId));
        List<ShowcaseComponent> saveOrUpdateList = new ArrayList<>(showcaseComponentList.size() * validBoundUserId.size());
        for (ShowcaseComponent showcaseComponent : showcaseComponentList) {
            Map<Long, ShowcaseComponent> creator2ShowcaseComponent =
                    sourceId2Objects.getOrDefault(showcaseComponent.getId(), Collections.emptyList()).stream().collect(Collectors.toMap(
                            ShowcaseComponent::getCreator, Function.identity()
                    ));
            for (Long userId : validBoundUserId) {
                if (Objects.isNull(userId2BoundAppAccount.get(userId))) {
                    log.warn("user {} want to sync showcase component, but no YOUDAO_DICT account in binding!", userId);
                    continue;
                }
                ShowcaseComponent oldRecord = creator2ShowcaseComponent.get(userId);
                saveOrUpdateList.add(
                        buildSyncShowcaseComponent(
                                showcaseComponent.getAuditedContent(),
                                userId2BoundAppAccount.get(userId).getId(),
                                userId,
                                Objects.isNull(oldRecord) ? null : oldRecord.getStatus(),
                                Objects.isNull(oldRecord) ? null : oldRecord.getId(),
                                Objects.isNull(oldRecord) ? null : oldRecord.getComponentId()
                        )
                );

            }
        }
        saveOrUpdateBatch(saveOrUpdateList);
    }


    /**
     * 将审核内容反序列化出来用于数据同步
     *
     * @param id          当更新的时候传入ID，否则不传
     * @param componentId
     */
    private ShowcaseComponent buildSyncShowcaseComponent(ShowcaseComponent auditedContent,
                                                         Long appAccountId,
                                                         Long userId,
                                                         Integer status,
                                                         Long id, String componentId) {
        ShowcaseComponent showcaseComponent = JacksonUtil.deepCopy(auditedContent, ShowcaseComponent.class);
        showcaseComponent.setAuditedPromoteTitle();
        showcaseComponent.setComponentId(Objects.isNull(componentId) ? RandomStringUtils.random(10, true, false) : componentId);
        showcaseComponent.setAppAccountId(appAccountId);
        showcaseComponent.setStatus(Objects.isNull(status) ? SHOWCASE_COMPONENT_STATUS_NORMAL : status);
        showcaseComponent.setAuditStatus(SHOWCASE_COMPONENT_STATUS_NORMAL);
        showcaseComponent.setDeleted(false);
        showcaseComponent.setCreator(userId);
        showcaseComponent.setCreateTime(null);
        showcaseComponent.setLastModTime(null);
        showcaseComponent.setRoleId(roleService.getByRoleKey(RoleEnum.BRAND_KOL.getRoleKey()).getId());
        showcaseComponent.setSourceId(showcaseComponent.getId());
        showcaseComponent.setId(id);
        showcaseComponent.setAuditedContent(JacksonUtil.deepCopy(showcaseComponent, ShowcaseComponent.class));
        return showcaseComponent;
    }


    @Override
    @CacheEvict(cacheNames = CacheNameConstant.SHOWCASE_ID_CACHE_NAME, key = "#id")
    public Boolean updateStatus(Long id, Integer status, Boolean confirmUpdate, Long userId) {
        if (SHOWCASE_COMPONENT_STATUS_AUDITING == status || SHOWCASE_COMPONENT_STATUS_AUDIT_REJECT == status) {
            throw new CustomException(ResponseType.ACCESS_DENIED);
        }
        ShowcaseComponent showcaseComponent = baseMapper.selectById(id);
        if (Objects.isNull(showcaseComponent)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        if (SHOWCASE_COMPONENT_STATUS_TAKE_DOWN == status) {
            if (!confirmUpdate && this.isRelatedTaskOrPost(id, showcaseComponent.getComponentId())) {
                throw new CustomException(ResponseType.SHOWCASE_COMPONENT_IN_USING);
            }
            if (checkLiveStatusWhenModify(showcaseComponent.getComponentId(), showcaseComponent.getId())) {
                throw new CustomException(ResponseType.SHOWCASE_OR_TASK_LIVING);
            }
        }
        return baseMapper.updateStatus(id, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(ShowcaseComponentCreateVO showcaseComponentCreateVO, Long userId, Role currentRole) throws URISyntaxException {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(), userId);
        Long appAccountId = null;
        if (Objects.nonNull(appAccount)) {
            appAccountId = appAccount.getId();
        }
        this.insertPreCheck(showcaseComponentCreateVO, appAccountId, userId, currentRole);
        this.setAdMarkIfNeed(showcaseComponentCreateVO, userId);
        ShowcaseComponent showcaseComponent = showcaseComponentMapper.createVo2Do(showcaseComponentCreateVO);
        showcaseComponent.setComponentId(RandomStringUtils.random(10, true, false));
        if (!SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR)) {
            showcaseComponent.setAppAccountId(appAccountId);
        }
        showcaseComponent.setCreator(userId);
        showcaseComponent.setRoleId(currentRole.getId());
        showcaseComponent.setStatus(SHOWCASE_COMPONENT_STATUS_NORMAL);
        showcaseComponent.setAuditStatus(SHOWCASE_COMPONENT_STATUS_AUDITING);
        baseMapper.insert(showcaseComponent);
        return showcaseComponent.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = CacheNameConstant.SHOWCASE_ID_CACHE_NAME, key = "#showcaseComponentModifyVO.id")
    public Integer modify(ShowcaseComponentModifyVO showcaseComponentModifyVO, Long userId, Role currentRole) {
        ShowcaseComponent oldShowcaseComponent = baseMapper.selectById(showcaseComponentModifyVO.getId());
        if (Objects.isNull(oldShowcaseComponent)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        this.userCanAccess(oldShowcaseComponent, userId);
        if (oldShowcaseComponent.getAuditStatus() == SHOWCASE_COMPONENT_STATUS_AUDITING) {
            throw new CustomException(ResponseType.SHOWCASE_COMPONENT_IN_AUDITING);
        }
        if (!showcaseComponentModifyVO.getConfirmUpdate()
                && this.isRelatedTaskOrPost(showcaseComponentModifyVO.getId(), oldShowcaseComponent.getComponentId())) {
            throw new CustomException(ResponseType.SHOWCASE_COMPONENT_IN_USING);
        }
        if (checkLiveStatusWhenModify(oldShowcaseComponent.getComponentId(), oldShowcaseComponent.getId())) {
            throw new CustomException(ResponseType.SHOWCASE_OR_TASK_LIVING);
        }
        if (this.isContentModified(oldShowcaseComponent, showcaseComponentModifyVO)) {
            this.isNameOk(showcaseComponentModifyVO.getName(), showcaseComponentModifyVO.getId(), userId, currentRole);
            ShowcaseComponentSwitchLinkBO showcaseComponentSwitchLinkBO = showcaseComponentMapper.modifyVo2LinkBo(showcaseComponentModifyVO);
            showcaseComponentSwitchLinkBO.setPromotionType(oldShowcaseComponent.getPromotionType());
            showcaseComponentSwitchLinkBO.setSwitchType(oldShowcaseComponent.getSwitchType());
            this.isSwitchLinksOk(showcaseComponentSwitchLinkBO);
            ShowcaseComponentContentBO showcaseComponentContentBO = showcaseComponentMapper.modifyVo2ContentBo(showcaseComponentModifyVO);
            showcaseComponentContentBO.setPromotionType(oldShowcaseComponent.getPromotionType());
            showcaseComponentContentBO.setSchemaId(oldShowcaseComponent.getSchemaId());
            this.isContentsOk(showcaseComponentContentBO);
            this.checkPromotionMatchSwitchType(showcaseComponentSwitchLinkBO, showcaseComponentContentBO);
            ShowcaseComponent showcaseComponent = showcaseComponentMapper.modifyVo2Do(showcaseComponentModifyVO);
            showcaseComponent.setAuditStatus(SHOWCASE_COMPONENT_STATUS_AUDITING);
            return BooleanUtils.toInteger(
                    new LambdaUpdateChainWrapper<>(baseMapper)
                            .eq(ShowcaseComponent::getId, showcaseComponentModifyVO.getId())
                            .set(ShowcaseComponent::getStrikeThroughPrice, showcaseComponent.getStrikeThroughPrice())
                            .set(ShowcaseComponent::getAndroidPackageName, showcaseComponent.getAndroidPackageName())
                            .set(ShowcaseComponent::getIosAppId, showcaseComponent.getIosAppId())
                            .update(showcaseComponent)
            );
        }
        return BooleanUtils.toInteger(false);
    }

    private Boolean checkLiveStatusWhenModify(String componentId, Long showcaseComponentId) {
        // 个人创作者和品牌号只需要检查个人橱窗即可，广告主则需要检查其关联的任务
        List<Good> goodList = new ArrayList<>();
        if (!SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR)) {
            goodList.add(
                    Good.builder().goodId(componentId).source(DICT_API_SOURCE_PERSONAL_GOOD).build()
            );
            return dictService.isShowcaseLiving(goodList);
        }
        return taskOrderService.isShowcaseRelatedTaskLiving(showcaseComponentId);
    }

    @Override
    @CacheEvict(cacheNames = CacheNameConstant.SHOWCASE_ID_CACHE_NAME, key = "#id")
    public Boolean delete(Long id, Long userId) {
        ShowcaseComponent showcaseComponent = baseMapper.selectById(id);
        if (Objects.isNull(showcaseComponent)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        this.userCanAccess(showcaseComponent, userId);
        if (checkLiveStatusWhenModify(showcaseComponent.getComponentId(), showcaseComponent.getId())) {
            throw new CustomException(ResponseType.SHOWCASE_OR_TASK_LIVING);
        }
        if (this.isRelatedTaskOrPost(id, showcaseComponent.getComponentId())) {
            throw new CustomException(ResponseType.CAN_NOT_DELETE);
        }
        return baseMapper.logicDelete(id);
    }

    @Override
    public ShowcaseComponentDetailVO detail(Long id) {
        ShowcaseComponent showcaseComponent = ((ShowcaseComponentService) AopContext.currentProxy()).getById(id);
        if (Objects.isNull(showcaseComponent)) {
            throw new CustomException(ResponseType.ACCESS_DENIED, "橱窗组件不存在");
        }
        if (!showcaseComponent.getCreator().equals(SecurityUtil.getUserId()) ||
                !showcaseComponent.getRoleId().equals(SecurityUtil.getCurrentRole().getId())) {
            if (!SecurityUtil.getCurrentRole().getRoleKey().equals(RoleEnum.AUDIT_OPERATOR.getRoleKey())) {
                throw new CustomException(ResponseType.ACCESS_DENIED);
            }
        }
        return showcaseComponentMapper.do2DetailVo(showcaseComponent);
    }

    @Override
    public ShowcaseComponentDetailVO latestPassedDetail(Long id) {
        ShowcaseComponent showcaseComponent = ((ShowcaseComponentService) AopContext.currentProxy()).getById(id);
        if (Objects.isNull(showcaseComponent) || Objects.isNull(showcaseComponent.getAuditedContent())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "橱窗组件不存在或没有已过审的记录");
        }
        ShowcaseComponent auditedShowcaseComponent = showcaseComponent.getAuditedContent();
        auditedShowcaseComponent.setId(id);
        auditedShowcaseComponent.setComponentId(showcaseComponent.getComponentId());
        return showcaseComponentMapper.do2DetailVo(auditedShowcaseComponent);
    }

    /**
     * 当橱窗下架或者任务下架的时候将不再进行下发
     *
     * @param goodId source==0时为橱窗ID source==1/2时为任务子ID
     * @param source 0-个人商品；1-指派任务；2-投稿任务
     */
    @Override
    public ShowcaseComponentDetail2DictVO latestPassedDetail4Dict(String goodId, Integer source) {
        TaskOrder subTaskOrder = null;
        TaskOrder parentTaskOrder = null;
        ShowcaseComponent showcaseComponent;
        switch (source) {
            case DICT_API_SOURCE_PERSONAL_GOOD:
                showcaseComponent = baseMapper.getByComponentId(goodId);
                break;
            case DICT_API_SOURCE_POST_TASK:
                subTaskOrder = taskOrderService.getByOrderId(goodId);
                if (Objects.isNull(subTaskOrder)) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, String.format("subTaskOrder NOT exist goodId:%s source:%d.", goodId, source));
                }
                parentTaskOrder = taskOrderService.getById(subTaskOrder.getParentOrderId());
                showcaseComponent = ((ShowcaseComponentService) AopContext.currentProxy()).getById(parentTaskOrder.getShowcaseComponentId());
                break;
            case DICT_API_SOURCE_APPOINTMENT_TASK:
                subTaskOrder = taskOrderService.getByOrderId(goodId);
                if (Objects.isNull(subTaskOrder)) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, String.format("subTaskOrder NOT exist goodId:%s source:%d.", goodId, source));
                }
                showcaseComponent = ((ShowcaseComponentService) AopContext.currentProxy()).getById(subTaskOrder.getShowcaseComponentId());
                break;
            default:
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "Wrong source.");
        }

        return getShowcaseComponentDetail2DictVO(source, subTaskOrder, parentTaskOrder, showcaseComponent);
    }

    @Override
    public ShowcaseComponentDetail2DictVO latestPassedDetail4Dict(String goodId,
                                                                  Integer source,
                                                                  Map<Long, TaskOrder> id2SubTaskOrder,
                                                                  Map<String, TaskOrder> orderId2SubTaskOrder,
                                                                  Map<Long, TaskOrder> id2ParentTaskOrder,
                                                                  Map<Long, ShowcaseComponent> id2ShowcaseComponent,
                                                                  Map<String, ShowcaseComponent> componentId2ShowcaseComponent) {
        TaskOrder subTaskOrder = null;
        TaskOrder parentTaskOrder = null;
        ShowcaseComponent showcaseComponent;
        switch (source) {
            case DICT_API_SOURCE_PERSONAL_GOOD:
                showcaseComponent = componentId2ShowcaseComponent.get(goodId);
                break;
            case DICT_API_SOURCE_POST_TASK:
                subTaskOrder = orderId2SubTaskOrder.get(goodId);
                if (Objects.isNull(subTaskOrder)) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "subTaskOrder NOT exist.");
                }
                parentTaskOrder = id2ParentTaskOrder.get(subTaskOrder.getParentOrderId());
                showcaseComponent = id2ShowcaseComponent.get(parentTaskOrder.getShowcaseComponentId());
                break;
            case DICT_API_SOURCE_APPOINTMENT_TASK:
                subTaskOrder = orderId2SubTaskOrder.get(goodId);
                if (Objects.isNull(subTaskOrder)) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "subTaskOrder NOT exist.");
                }
                showcaseComponent = id2ShowcaseComponent.get(subTaskOrder.getShowcaseComponentId());
                break;
            default:
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "Wrong source.");
        }

        return getShowcaseComponentDetail2DictVO(source, subTaskOrder, parentTaskOrder, showcaseComponent);
    }

    @Override
    public ShowcaseComponentDetail2DictVO getShowcaseComponentDetail2DictVO(Integer source, TaskOrder subTaskOrder, TaskOrder parentTaskOrder, ShowcaseComponent showcaseComponent) {
        return Optional
                .ofNullable(showcaseComponentMapper.toShowcaseComponentDetail2DictVO(getShowcaseComponentDetail2DictBo(source, subTaskOrder, parentTaskOrder, showcaseComponent)))
                .orElse(new ShowcaseComponentDetail2DictVO());
    }

    @Override
    public ShowcaseComponentDetail2DictBO getShowcaseComponentDetail2DictBo(Integer source, TaskOrder subTaskOrder, TaskOrder parentTaskOrder, ShowcaseComponent showcaseComponent) {
        if (canNotRelease(parentTaskOrder, subTaskOrder, showcaseComponent)) {
            return null;
        }
        try {
            ShowcaseComponent auditedShowcaseComponent = showcaseComponent.getAuditedContent();
            ShowcaseComponentDetail2DictBO bo = showcaseComponentMapper.do2DetailDictBo(auditedShowcaseComponent);

            bo.setJdGoods(Objects.nonNull(showcaseComponent.getSkuId()));
            bo.setAuditedPromoteTitle(showcaseComponent.getAuditedPromoteTitle());
            bo.setTime(showcaseComponent.getCreateTime());
            bo.setId(showcaseComponent.getId());
            bo.setComponentId(showcaseComponent.getComponentId());
            bo.setGoodId(showcaseComponent.getComponentId());
            if (Objects.nonNull(parentTaskOrder)) {
                bo.setBillingType(
                        TaskOrderBillingTypeEnum.getEnum(parentTaskOrder.getBillingType()).getDescription()
                );
            }
            if (Objects.nonNull(subTaskOrder)) {
                bo.setSubTaskId(subTaskOrder.getOrderId());
                bo.setTime(subTaskOrder.getCreateTime());
                bo.setGoodId(subTaskOrder.getOrderId());
            } else if (Objects.nonNull(parentTaskOrder)) {
                bo.setTime(parentTaskOrder.getCreateTime());
            }
            if (SHOWCASE_COMPONENT_SWITCH_WECHAT_MP == showcaseComponent.getSwitchType()) {
                bo.setDictMpAndroidId(androidMpId);
                bo.setDictMpIosId(iosMpId);
            }
            bo.setLandingPageUrl(
                    appendOutVendorParamIfNeed(
                            auditedShowcaseComponent.getSwitchType(),
                            auditedShowcaseComponent.getAppendOutVendor(),
                            auditedShowcaseComponent.getLandingPageUrl()
                    )
            );
            bo.setSource(source);
            return bo;
        } catch (Exception e) {
            log.error("send showcase component 2 dict error.", e);
            throw new CustomException(ResponseType.SERVICE_ERROR);
        }
    }

    /**
     * 以下情况橱窗组件将不会下发
     * <ol>
     *     <li>组件不存在 或 组件存在但审核文案为空/被下架/被删除</li>
     *     <li>子订单存在 且 不在进行中/待付款</li>
     *     <li>父订单存在 且 不在进行中</li>
     * </ol>
     *
     * @param parentTaskOrder   父订单
     * @param subTaskOrder      子订单
     * @param showcaseComponent 橱窗组件
     * @return 橱窗组件不该被下发
     */
    private Boolean canNotRelease(TaskOrder parentTaskOrder, TaskOrder subTaskOrder, ShowcaseComponent showcaseComponent) {
        if (Objects.isNull(showcaseComponent)
                || Objects.isNull(showcaseComponent.getAuditedContent())
                || SHOWCASE_COMPONENT_STATUS_TAKE_DOWN == showcaseComponent.getStatus()
                || showcaseComponent.getDeleted()) {
            return true;
        }

        if (Objects.nonNull(subTaskOrder)) {
            if (TASK_ORDER_IN_PROGRESS != subTaskOrder.getStatus()
                    && TASK_ORDER_WAITING_FOR_PAYMENT != subTaskOrder.getStatus()) {
                return true;
            }
        }

        if (Objects.nonNull(parentTaskOrder)) {
            return parentTaskOrder.getType().equals(TASK_ORDER_TYPE_POST) && TASK_ORDER_IN_PROGRESS != parentTaskOrder.getStatus();
        }
        return false;
    }

    /**
     * 橱窗组件是否被任务或者动态关联？
     *
     * @param id
     */
    private Boolean isRelatedTaskOrPost(Long id, String componentId) {
        if (taskOrderService.countShowcaseComponentByTypeAndExcludeStatus(id, TASK_ORDER_TYPE_APPOINTMENT, TASK_ORDER_CANCELED) > 0 ||
                taskOrderService.countShowcaseComponentByTypeAndExcludeStatus(id, TASK_ORDER_TYPE_POST, TASK_ORDER_END) > 0) {
            return true;
        }
        if (isAnyCountGtZero(dictService.countPost(
                Collections.singletonList(componentId), DICT_API_SOURCE_PERSONAL_GOOD
        ))) {
            return true;
        }
        List<TaskOrder> taskOrders = taskOrderService.listByShowcaseComponentId(id);
        if (isAnyCountGtZero(dictService.countPost(
                taskOrders.stream()
                        .filter(taskOrder ->
                                taskOrder.getType() == TASK_ORDER_TYPE_APPOINTMENT && Objects.nonNull(taskOrder.getParentOrderId()))
                        .map(TaskOrder::getOrderId)
                        .collect(Collectors.toList()), DICT_API_SOURCE_APPOINTMENT_TASK))) {
            return true;
        }
        return isAnyCountGtZero(dictService.countPost(
                taskOrders.stream().filter(taskOrder -> taskOrder.getType() == TASK_ORDER_TYPE_SUB_POST)
                        .map(TaskOrder::getOrderId)
                        .collect(Collectors.toList()), DICT_API_SOURCE_POST_TASK
        ));
    }

    private Boolean isAnyCountGtZero(Set<DictPostCountVO> dictPostCountVOS) {
        return dictPostCountVOS.stream().anyMatch(a -> a.getCount() > 0);
    }

    private void userCanAccess(ShowcaseComponent showcaseComponent, Long userId) {
        if (!showcaseComponent.getCreator().equals(userId)) {
            throw new CustomException(ResponseType.ACCESS_DENIED);
        }
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(), userId);
        Long appAccountId = null;
        if (Objects.nonNull(appAccount)) {
            appAccountId = appAccount.getId();
        }
        if (!SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) && !showcaseComponent.getAppAccountId().equals(appAccountId)) {
            throw new CustomException(ResponseType.ACCESS_DENIED);
        }
    }

    private Boolean isContentModified(ShowcaseComponent old, ShowcaseComponentModifyVO modified) {
        return isItemModified(old.getName(), modified.getName()) ||
                isItemModified(old.getDeepLink(), modified.getDeepLink()) ||
                isItemModified(old.getLandingPageUrl(), modified.getLandingPageUrl()) ||
                isItemModified(old.getBackupLandingPageUrl(), modified.getBackupLandingPageUrl()) ||
                isItemModified(old.getMicroProgramId(), modified.getMicroProgramId()) ||
                isItemModified(old.getMicroProgramPath(), modified.getMicroProgramPath()) ||
                isItemModified(old.getAndroidPackageName(), modified.getAndroidPackageName()) ||
                isItemModified(old.getIosAppId(), modified.getIosAppId()) ||
                isItemModified(old.getLeadText(), modified.getLeadText()) ||
                isItemModified(old.getItemName(), modified.getItemName()) ||
                isItemModified(old.getButtonText(), modified.getButtonText()) ||
                isItemModified(old.getPromoteImageUrl(), modified.getPromoteImageUrl()) ||
                isItemModified(old.getPromoteTitle(), modified.getPromoteTitle()) ||
                isItemModified(old.getPromoteText(), modified.getPromoteText()) ||
                isItemModified(old.getAppName(), modified.getAppName()) ||
                ObjectUtils.notEqual(old.getAppPlatform(), modified.getAppPlatform()) ||
                ObjectUtils.notEqual(old.getItemPrice(), modified.getItemPrice()) ||
                ObjectUtils.notEqual(old.getStrikeThroughPrice(), modified.getStrikeThroughPrice()) ||
                (Objects.nonNull(modified.getMarkAsAd()) && ObjectUtils.notEqual(old.getMarkAsAd(), modified.getMarkAsAd())) ||
                (Objects.nonNull(modified.getAppendOutVendor()) && ObjectUtils.notEqual(old.getAppendOutVendor(), modified.getAppendOutVendor()));
    }

    /**
     * 当source不为blank的时候对比其与simple是否一致
     *
     * @param source
     * @param sample
     * @return
     */
    private Boolean isItemModified(String source, String sample) {
        return !Objects.equals(source, sample);
    }

    @Override
    public PageVO<ShowcaseComponentListVO> pageByCriteria(ShowcaseComponentCriteriaQueryVO showcaseComponentCriteriaQueryVO, Long userId, Role currentRole) {
        Long appAccountId = null;
        Collection<Role> userRoles = roleService.listByUserId(userId);
        if (!roleService.checkRole(userRoles, RoleEnum.SPONSOR)) {
            AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(), userId);
            if (Objects.isNull(appAccount)) {
                throw new CustomException(ResponseType.ACCESS_DENIED);
            } else {
                appAccountId = appAccount.getId();
            }
        }
        Page<ShowcaseComponent> showcaseComponentPage = baseMapper.pageByCriteria(showcaseComponentCriteriaQueryVO, appAccountId, userId, currentRole);
        List<ShowcaseComponent> showcaseComponentList = showcaseComponentPage.getRecords();
        List<ShowcaseComponentListVO> showcaseComponentListVOList = showcaseComponentMapper.do2ListVo(showcaseComponentList);
        return new PageVO<>(
                showcaseComponentPage.getCurrent(),
                showcaseComponentPage.getSize(),
                showcaseComponentListVOList,
                showcaseComponentPage.getTotal());
    }

    @Override
    public Boolean isNameRepeat(String name, Long userId, Role currentRole) {
        return this.isNameRepeat(name, null, userId, currentRole);
    }

    @Override
    public Boolean isNotifySchemaUpgrade(Long userId) {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(), userId);
        if (Objects.isNull(appAccount)) {
            throw new CustomException(ResponseType.KOL_STATUS_EXCEPTION);
        }
        return appAccount.getNotifySchemaUpgrade();
    }

    @Override
    public Boolean disableNotifySchemaUpgrade(Long userId) {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(), userId);
        if (Objects.isNull(appAccount)) {
            throw new CustomException(ResponseType.KOL_STATUS_EXCEPTION);
        }
        return appAccountService.disableNotifySchemaUpgrade(appAccount.getId());
    }

    @Override
    public List<Long> listIdByPromoteTitleLikely(String promoteTitle) {
        List<ShowcaseComponent> showcaseComponents = baseMapper.listByPromoteTitleLikely(promoteTitle);
        return showcaseComponents.stream().map(ShowcaseComponent::getId).collect(Collectors.toList());
    }


    /**
     * 检查推广类型、跳转类型是否匹配
     */
    private void checkPromotionMatchSwitchType(ShowcaseComponentSwitchLinkBO showcaseComponentSwitchLinkBO, ShowcaseComponentContentBO showcaseComponentContentBO) {
        if ((SHOWCASE_COMPONENT_PROMOTION_TYPE_APP == showcaseComponentContentBO.getPromotionType()
                && SHOWCASE_COMPONENT_SWITCH_APP_STORE != showcaseComponentSwitchLinkBO.getSwitchType())
                ||
                (SHOWCASE_COMPONENT_SWITCH_APP_STORE == showcaseComponentSwitchLinkBO.getSwitchType()
                        && SHOWCASE_COMPONENT_PROMOTION_TYPE_APP != showcaseComponentContentBO.getPromotionType())
        ) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
    }


    /**
     * 检查组件名称是否合法
     *
     * @param name        名字
     * @param id          传值代表忽略此ID对应的记录
     * @param currentRole
     * @return
     */
    private void isNameOk(String name, Long id, Long userId, Role currentRole) {
        String nameWithoutWhiteSpace = StringUtils.deleteWhitespace(name);

        if (StringUtils.isBlank(nameWithoutWhiteSpace) || nameWithoutWhiteSpace.length() > 100) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "组件名称未填写或长度过长");
        }
        if (isNameRepeat(name, id, userId, currentRole)) {
            throw new CustomException(ResponseType.NAME_REPEATED_EXCEPTION);
        }

    }

    /**
     * 检查名字是否重复
     *
     * @param id          传ID则忽略这个ID对应的name
     * @param currentRole
     */
    private Boolean isNameRepeat(String name, Long id, Long userId, Role currentRole) {
        List<ShowcaseComponent> showcaseComponents = baseMapper.listByName(name, userId, currentRole);
        for (ShowcaseComponent showcaseComponent : showcaseComponents) {
            if (Objects.isNull(id) ||
                    !(id.equals(showcaseComponent.getId()) || id.equals(showcaseComponent.getSourceId()))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 插入前的检查
     */
    private void insertPreCheck(ShowcaseComponentCreateVO componentCreateVO, Long appAccountId, Long userId, Role currentRole) {
        // 组件名称不可重复，插入时如果与旧的重复了则不做此项检查
        if (!roleService.checkRole(roleService.listByUserId(userId), RoleEnum.SPONSOR)) {
            AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(), userId);
            if (ObjectUtils.notEqual(appAccountId, appAccount.getId())) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS);
            }
        }
        isNameOk(componentCreateVO.getName(), null, userId, currentRole);
        if (componentCreateVO.getPromotionType() == ContentMarketingConstants.SHOWCASE_COMPONENT_PROMOTION_TYPE_BRANDING
                && !(showcaseComponentWhiteListService.isUserInWhiteList(userId)
                || roleService.checkRole(roleService.listByUserId(userId), RoleEnum.BRAND_KOL))
        ) {
            throw new CustomException(ResponseType.ACCESS_DENIED);
        }

        ShowcaseComponentSwitchLinkBO showcaseComponentMapperVo2LinkBo = showcaseComponentMapper.createVo2LinkBo(componentCreateVO);
        this.isSwitchLinksOk(showcaseComponentMapperVo2LinkBo);
        ShowcaseComponentContentBO showcaseComponentContentBO = showcaseComponentMapper.createVo2ContentBo(componentCreateVO);
        this.isContentsOk(showcaseComponentContentBO);
        this.checkPromotionMatchSwitchType(showcaseComponentMapperVo2LinkBo, showcaseComponentContentBO);
    }

    /**
     * 下发给词典的时候向deeplink后追加out vendor参数
     *
     * @param switchType        跳转类型
     * @param isAppendOutVendor 是否需要追加参数
     * @throws URISyntaxException
     */
    private String appendOutVendorParamIfNeed(Integer switchType, Boolean isAppendOutVendor, String landingPageUrl) throws URISyntaxException {
        if (SHOWCASE_COMPONENT_SWITCH_LANDING_PAGE == switchType
                && BooleanUtils.isTrue(isAppendOutVendor)) {
            // if URL already have out vendor param, do NOT append it again.
            URIBuilder uriBuilder = new URIBuilder(landingPageUrl);
            List<NameValuePair> queryParams = uriBuilder.getQueryParams();
            for (NameValuePair queryParam : queryParams) {
                if (SHOWCASE_COMPONENT_OUT_VENDOR_KEY.equals(queryParam.getName()) &&
                        SHOWCASE_COMPONENT_OUT_VENDOR_VALUE.equals(queryParam.getValue())) {
                    return landingPageUrl;
                }
            }
            return uriBuilder.addParameter(SHOWCASE_COMPONENT_OUT_VENDOR_KEY, SHOWCASE_COMPONENT_OUT_VENDOR_VALUE)
                    .build()
                    .toString();
        }
        return landingPageUrl;
    }

    private void setAdMarkIfNeed(ShowcaseComponentCreateVO componentCreateVO, Long userId) {
        if (!showcaseComponentWhiteListService.isUserInWhiteList(userId)) {
            componentCreateVO.setMarkAsAd(true);
        }
    }

    /**
     * 检查跳转链接是否合法
     */
    private void isSwitchLinksOk(ShowcaseComponentSwitchLinkBO showcaseComponentSwitchLinkBO) {
        if (ContentMarketingConstants.SHOWCASE_COMPONENT_PROMOTION_TYPE_APP == showcaseComponentSwitchLinkBO.getPromotionType()) {
            // switch type == app store
            if (ContentMarketingConstants.SHOWCASE_COMPONENT_SWITCH_APP_STORE == showcaseComponentSwitchLinkBO.getSwitchType()) {
                if (ContentMarketingConstants.SHOWCASE_COMPONENT_APP_PLATFORM_ANDROID == showcaseComponentSwitchLinkBO.getAppPlatform()
                        || ContentMarketingConstants.SHOWCASE_COMPONENT_APP_PLATFORM_BOTH == showcaseComponentSwitchLinkBO.getAppPlatform()) {
                    if (StringUtils.isBlank(showcaseComponentSwitchLinkBO.getAndroidPackageName())
                            || showcaseComponentSwitchLinkBO.getAndroidPackageName().length() > 3000) {
                        throw new CustomException(ResponseType.INVALID_PARAMETERS, "安卓应用包名未填写或长度过长");
                    }
                }
                if (ContentMarketingConstants.SHOWCASE_COMPONENT_APP_PLATFORM_IOS == showcaseComponentSwitchLinkBO.getAppPlatform()
                        || ContentMarketingConstants.SHOWCASE_COMPONENT_APP_PLATFORM_BOTH == showcaseComponentSwitchLinkBO.getAppPlatform()) {
                    if (StringUtils.isBlank(showcaseComponentSwitchLinkBO.getIosAppId())
                            || showcaseComponentSwitchLinkBO.getIosAppId().length() > 3000) {
                        throw new CustomException(ResponseType.INVALID_PARAMETERS, "iOS应用ID未填写或长度过长");
                    }
                }
            } else {
                throw new CustomException(ResponseType.INVALID_PARAMETERS);
            }
        } else {
            switch (showcaseComponentSwitchLinkBO.getSwitchType()) {
                case SHOWCASE_COMPONENT_SWITCH_LANDING_PAGE:
                    if (!HTTP_URL_VALIDATOR.isValid(showcaseComponentSwitchLinkBO.getLandingPageUrl()) || showcaseComponentSwitchLinkBO.getLandingPageUrl().length() > 3000) {
                        throw new CustomException(ResponseType.INVALID_PARAMETERS, "落地页链接未填写或长度过长");
                    }
                    break;
                case SHOWCASE_COMPONENT_SWITCH_DEEPLINK:
                    if (StringUtils.isBlank(showcaseComponentSwitchLinkBO.getDeepLink()) && showcaseComponentSwitchLinkBO.getDeepLink().length() > 3000) {
                        throw new CustomException(ResponseType.INVALID_PARAMETERS, "DeepLink链接未填写或长度过长");
                    }
                    if (!HTTP_URL_VALIDATOR.isValid(showcaseComponentSwitchLinkBO.getBackupLandingPageUrl()) || showcaseComponentSwitchLinkBO.getBackupLandingPageUrl().length() > 3000) {
                        throw new CustomException(ResponseType.INVALID_PARAMETERS, "备用落地页链接未填写或长度过长");
                    }
                    break;
                case SHOWCASE_COMPONENT_SWITCH_WECHAT_MP:
                    if (StringUtils.isBlank(showcaseComponentSwitchLinkBO.getMicroProgramId()) || showcaseComponentSwitchLinkBO.getMicroProgramId().length() > 50) {
                        throw new CustomException(ResponseType.INVALID_PARAMETERS, "微信小程序ID未填写或长度过长");
                    }
                    if (StringUtils.isBlank(showcaseComponentSwitchLinkBO.getMicroProgramPath()) || showcaseComponentSwitchLinkBO.getMicroProgramPath().length() > 3000) {
                        throw new CustomException(ResponseType.INVALID_PARAMETERS, "微信小程序目标页面路径未填写或长度过长");
                    }
                    if (!HTTP_URL_VALIDATOR.isValid(showcaseComponentSwitchLinkBO.getBackupLandingPageUrl()) || showcaseComponentSwitchLinkBO.getBackupLandingPageUrl().length() > 3000) {
                        throw new CustomException(ResponseType.INVALID_PARAMETERS, "落地页链接错误或长度过长");
                    }
                    break;
                default:
                    log.warn("UNKNOWN switch type when create showcase component, detail: {}", showcaseComponentSwitchLinkBO);
                    break;
            }
        }
    }

    /**
     * 检查展示卡片内容是否合法
     */
    private void isContentsOk(ShowcaseComponentContentBO showcaseComponentContentBO) {
        switch (showcaseComponentContentBO.getPromotionType()) {
            case SHOWCASE_COMPONENT_PROMOTION_TYPE_BRANDING:
                // nothing special need check.
                break;
            case SHOWCASE_COMPONENT_PROMOTION_TYPE_SALES:
                if (SHOWCASE_COMPONENT_SALES_SCHEMA_1_ID == showcaseComponentContentBO.getSchemaId()) {
                    if (Objects.isNull(showcaseComponentContentBO.getItemPrice())) {
                        throw new CustomException(ResponseType.INVALID_PARAMETERS, "价格未填写");
                    }
                } else {
                    if (StringUtils.isBlank(showcaseComponentContentBO.getPromoteText()) || showcaseComponentContentBO.getPromoteText().length() > 100) {
                        throw new CustomException(ResponseType.INVALID_PARAMETERS, "推广文案未填写或长度过长");
                    }
                }
                if (StringUtils.isBlank(showcaseComponentContentBO.getItemName()) || showcaseComponentContentBO.getItemName().length() > 100) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "商品名未填写或长度过长");
                }
                break;
            case SHOWCASE_COMPONENT_PROMOTION_TYPE_LEADS:
                if (StringUtils.isBlank(showcaseComponentContentBO.getPromoteTitle()) || showcaseComponentContentBO.getPromoteTitle().length() > 100) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "推广标题未填写或长度过长");
                }
                if (StringUtils.isBlank(showcaseComponentContentBO.getPromoteText()) || showcaseComponentContentBO.getPromoteText().length() > 100) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "推广文案未填写或长度过长");
                }
                break;
            case SHOWCASE_COMPONENT_PROMOTION_TYPE_APP:
                if (StringUtils.isBlank(showcaseComponentContentBO.getAppName()) || showcaseComponentContentBO.getAppName().length() > 100) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "应用名为未填写或长度过长");
                }
                if (StringUtils.isBlank(showcaseComponentContentBO.getPromoteText()) || showcaseComponentContentBO.getPromoteText().length() > 100) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "推广文案未填写或长度过长");
                }
                break;
            default:
                break;
        }
        // common check
        if (StringUtils.isBlank(showcaseComponentContentBO.getLeadText()) || showcaseComponentContentBO.getLeadText().length() > 100) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "引导文案未填写或长度过长");
        }
        if (!HTTP_URL_VALIDATOR.isValid(showcaseComponentContentBO.getPromoteImageUrl())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "推广图片URL不合法");
        }
        if (SHOWCASE_COMPONENT_PROMOTION_TYPE_BRANDING != showcaseComponentContentBO.getPromotionType()) {
            if (StringUtils.isBlank(showcaseComponentContentBO.getButtonText()) || showcaseComponentContentBO.getButtonText().length() > 2) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "按钮文案未填写或长度过长");
            }
        }
    }

    @Override
    public Page<ShowcaseComponentAuditListVO> pageAudit(AuditPageQuery auditPageQuery) {
        return baseMapper.pageAudit(new Page<>(auditPageQuery.getCurrent(), auditPageQuery.getSize()), auditPageQuery);
    }

    @Override
    public Set<Long> listIdByPromotionType(Integer promotionType) {
        return baseMapper.listByPromotionType(promotionType)
                .stream()
                .map(ShowcaseComponent::getId)
                .collect(Collectors.toSet());
    }

    @Override
    public Integer getSyncStatus(Long kolUserId, Role currentRole) {
        return userRelationService.existValidByKolUserId(kolUserId) ? BooleanUtils.toInteger(userDetailService.getSyncStatus(kolUserId, currentRole)) : -1;
    }

    @Override
    public Boolean updateSyncStatus(Long userId, Boolean isSync) {
        return userDetailService.updateSyncStatus(userId, isSync);
    }

    @Override
    public Long count(Long appAccountId) {
        return this.lambdaQuery().eq(ShowcaseComponent::getAppAccountId, appAccountId).count();
    }

    @Override
    public PageVO<DictPostVO> getRelatedDictPost(Long id, Long current, Long size) {
        ShowcaseComponent showcaseComponent = ((ShowcaseComponentService) AopContext.currentProxy()).getById(id);
        if (Objects.isNull(showcaseComponent)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "橱窗组件不存在");
        }
        return dictService.getPostDetail(showcaseComponent.getComponentId(), DICT_API_SOURCE_PERSONAL_GOOD, current, size);
    }

    @Override
    public PageVO<ShowcaseComponentListVO> pagePassed(ShowcaseComponentCriteriaQueryVO queryVO, Long userId, Role currentRole) throws Exception {
        List<ShowcaseComponentListVO> showcaseComponentListVOList = new ArrayList<>();
        Page<ShowcaseComponent> showcaseComponents = baseMapper.pagePassed(
                queryVO.getId(),
                queryVO.getComponentId(),
                queryVO.getName(),
                queryVO.getPromotionTitle(),
                queryVO.getSwitchType(),
                false,
                userId,
                currentRole,
                queryVO.getStatus(), queryVO.getCurrent(), queryVO.getSize());
        for (ShowcaseComponent showcaseComponent : showcaseComponents.getRecords()) {
            ShowcaseComponentListVO showcaseComponentListVO = showcaseComponentMapper.do2ListVo(showcaseComponent.getAuditedContent());
            showcaseComponentListVO.setId(showcaseComponent.getId());
            showcaseComponentListVO.setComponentId(showcaseComponent.getComponentId());
            showcaseComponentListVO.setStatus(showcaseComponent.getStatus());
            showcaseComponentListVO.setAuditedPromoteTitle(showcaseComponent.getAuditedPromoteTitle());
            showcaseComponentListVO.setAuditStatus(showcaseComponent.getAuditStatus());
            showcaseComponentListVOList.add(showcaseComponentListVO);
        }
        return new PageVO<>(showcaseComponents.getCurrent(),
                showcaseComponents.getSize(),
                showcaseComponentListVOList,
                showcaseComponents.getTotal());
    }

    @Override
    public ShowcaseComponent getByComponentId(String componentId) {
        return baseMapper.getByComponentId(componentId);
    }

    @Override
    public Collection<ShowcaseComponent> listByComponentIds(Set<String> showcaseComponentIds) {
        if (showcaseComponentIds.isEmpty()) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(baseMapper)
                .in(ShowcaseComponent::getComponentId, showcaseComponentIds)
                .list();
    }

    @Override
    public boolean exists(Long skuId) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(ShowcaseComponent::getSkuId, skuId)
                .exists();
    }

    @Override
    public List<ShowcaseComponent> listJdItem() {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .isNotNull(ShowcaseComponent::getSkuId)
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAudit(List<String> ids, int status, Long userId) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "请选择需要审核的橱窗组件");
        }
        this.listByIds(ids).forEach(showcaseComponent -> {
            audit(status, showcaseComponent, userId);
            Optional.ofNullable(cacheManager.getCache(CacheNameConstant.SHOWCASE_ID_CACHE_NAME)).ifPresent(cache -> cache.evict(showcaseComponent.getId()));
        });
    }

    @Override
    @Cacheable(cacheNames = CacheNameConstant.SHOWCASE_ID_CACHE_NAME, key = "#id", condition = "#id!=null", unless = "#result == null")
    public ShowcaseComponent getById(Serializable id) {
        return super.getById(id);
    }

    @Override
    public List<ShowcaseComponent> listValid(Long userId) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(ShowcaseComponent::getStatus, SHOWCASE_COMPONENT_STATUS_NORMAL)
                .eq(ShowcaseComponent::getAuditStatus, SHOWCASE_COMPONENT_STATUS_NORMAL)
                .eq(ShowcaseComponent::getCreator, userId)
                .list();
    }

    @Override
    public List<ShowcaseComponent> listInProcessByRoleId(Long roleId) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(ShowcaseComponent::getRoleId, roleId)
                .eq(ShowcaseComponent::getStatus, SHOWCASE_COMPONENT_STATUS_NORMAL)
                .eq(ShowcaseComponent::getAuditStatus, SHOWCASE_COMPONENT_STATUS_NORMAL)
                .eq(ShowcaseComponent::getPromotionType, SHOWCASE_COMPONENT_PROMOTION_TYPE_SALES)
                .list();
    }
}

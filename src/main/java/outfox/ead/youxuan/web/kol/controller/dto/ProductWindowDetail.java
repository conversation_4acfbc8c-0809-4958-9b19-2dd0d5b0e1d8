package outfox.ead.youxuan.web.kol.controller.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProductWindowDetail {
    @ApiModelProperty("粉丝达到开通需求")
    Boolean fansNumAndLevelRequire;
    @ApiModelProperty("发布内容达到开通需求")
    Boolean publishContentRequire;
    @ApiModelProperty("最近发布文章达到开通需求")
    Boolean recentPublishRequire;
    @ApiModelProperty("是否有资格开通橱窗")
    Boolean qualify;
}

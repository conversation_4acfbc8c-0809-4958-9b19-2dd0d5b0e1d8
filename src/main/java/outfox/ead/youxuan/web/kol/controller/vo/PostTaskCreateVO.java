package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.FutureOrPresent;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PostTaskCreateVO {
    @NotNull
    @Length(min = 1, max = 100)
    @ApiModelProperty("任务名称")
    private String name;
    @NotNull
    @ApiModelProperty("橱窗组件ID")
    private List<Long> showcaseComponentId;
    @NotNull
    @FutureOrPresent
    @ApiModelProperty("推广有效期-开始日期")
    private LocalDateTime beginDate;
    @NotNull
    @FutureOrPresent
    @ApiModelProperty("推广有效期-结束日期")
    private LocalDateTime endDate;
    @NotNull
    @Range(max = 99999999)
    @ApiModelProperty("参与人数上限，没上限传0")
    private Long postLimit;
    @NotNull
    @Range(max = 4)
    @ApiModelProperty("计费类型 计费类型；0-其他，1-CPM，2-CPC，3-CPA，4-CPS")
    private Integer billingType;
    @NotNull
    @Range(max = 9_999_999_900L)
    @ApiModelProperty("佣金单价，以人民币分为单位")
    private Long commissionPrice;
    @NotNull
    @Range(max = 3)
    @ApiModelProperty("结算周期;0-其他，1-月结，2-周结，3-日结")
    private Integer settlementInterval;
    @Length(max = 500)
    @ApiModelProperty("考核说明")
    private String assessmentDescription;
}

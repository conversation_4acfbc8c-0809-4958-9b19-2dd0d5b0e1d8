package outfox.ead.youxuan.web.kol.handler;

import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import outfox.ead.youxuan.web.kol.controller.bo.AutoShowcaseInfo;
import outfox.ead.youxuan.web.kol.controller.bo.AutoShowcasePostBO;
import outfox.ead.youxuan.web.kol.service.TaskOrderService;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 根据权重随机挂窗
 *
 * <AUTHOR>
 * @date 2022年12月27日 12:22
 */
@RequiredArgsConstructor
@Component
@Order(4)
public class RandomAutoShowcaseHandler implements AutoShowcaseHandler {
    private final TaskOrderService taskOrderService;

    @Override
    public void handler(List<AutoShowcasePostBO> posts) {
        List<AutoShowcaseInfo> autoShowcaseInfos = taskOrderService.listAutoShowcaseTaskOrder();
        List<AutoShowcaseInfo> originWeightPool = getOriginWeightPool(autoShowcaseInfos);
        if (originWeightPool.isEmpty()) {
            return;
        }
        ArrayList<AutoShowcaseInfo> weightPool = new ArrayList<>(originWeightPool);
        for (AutoShowcasePostBO post : posts) {
            if (!post.isFilled()) {
                if (weightPool.isEmpty()) {
                    weightPool = new ArrayList<>(originWeightPool);
                }
                post.setAutoShowcaseInfo(weightPool.remove(ThreadLocalRandom.current().nextInt(weightPool.size())));
            }
        }
    }

    private List<AutoShowcaseInfo> getOriginWeightPool(Collection<AutoShowcaseInfo> autoShowcaseInfos) {
        List<AutoShowcaseInfo> originWeightPools = new ArrayList<>();
        autoShowcaseInfos.forEach(info -> {
            int i = info.getTaskOrder().getWeight();
            while (i-- > 0) {
                originWeightPools.add(info);
            }
        });
        return originWeightPools;
    }
}

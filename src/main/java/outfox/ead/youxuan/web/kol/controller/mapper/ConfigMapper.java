package outfox.ead.youxuan.web.kol.controller.mapper;

import org.mapstruct.Mapper;
import outfox.ead.youxuan.entity.Config;
import outfox.ead.youxuan.web.ad.controller.vo.QualificationInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface ConfigMapper {
    QualificationInfoVO do2Vo(Config config);

    List<QualificationInfoVO> do2Vo(List<Config> configList);
}

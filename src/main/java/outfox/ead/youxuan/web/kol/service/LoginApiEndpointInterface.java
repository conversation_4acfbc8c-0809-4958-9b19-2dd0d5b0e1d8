package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.web.kol.controller.dto.DictLoginResponse;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 */
public interface LoginApiEndpointInterface {
    @GET("login/inner/yid")
    Call<DictLoginResponse> loginByYid(@Query("param") String param);

    @GET("login/inner/yid")
    Call<DictLoginResponse> loginByYidTest(@Query("from") String from, @Query("yid") String yid);
}
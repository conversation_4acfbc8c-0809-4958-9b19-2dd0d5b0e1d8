package outfox.ead.youxuan.web.kol.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.DistributedLock;
import outfox.ead.youxuan.core.annotation.DistributedLockKey;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.web.ad.service.*;
import outfox.ead.youxuan.web.kol.controller.bo.Good;
import outfox.ead.youxuan.web.kol.controller.dto.DictProfile;
import outfox.ead.youxuan.web.kol.controller.query.UserGoodInfoQuery;
import outfox.ead.youxuan.web.kol.controller.vo.*;
import outfox.ead.youxuan.web.kol.service.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static outfox.ead.youxuan.constants.ContentMarketingConstants.*;
import static outfox.ead.youxuan.constants.PlatformEnum.YOUDAO_DICT;
import static outfox.ead.youxuan.constants.StageEnum.COMPLETE_USER_DETAIL;

/**
 * <AUTHOR>
 * @date 2022年07月20日 15:21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class LiveServiceImpl implements LiveService {
    private final AppAccountService appAccountService;
    private final UserService userService;
    private final UserDetailService userDetailService;
    private final RoleService roleService;
    private final DictService dictService;
    private final ShowcaseComponentService showcaseComponentService;
    private final TaskOrderService taskOrderService;
    private final LoginService loginService;
    private final RegisterService registerService;
    private static final List<Integer> BRAND_KOL_CAN_NOT_ACCESS_LIST = Arrays.asList(DICT_API_SOURCE_APPOINTMENT_TASK, DICT_API_SOURCE_POST_TASK);


    /**
     * 开通橱窗/任务权限
     * 加锁再查，保证在当前事务执行过程中，不会有别的事务操作该媒体账户
     * KOL 三个权限都开， BRAND_KOL只开橱窗权限
     *
     * <ol>
     *     <li>生成user</li>
     *     <li>如果是kol完善用户信息</li>
     *     <li>绑定媒体账户</li>
     *     <li>开通权限</li>
     * </ol>
     * Refer to <a href="https://confluence.inner.youdao.com/pages/viewpage.action?pageId=*********#id-%E4%BC%98%E9%80%892.2%E6%B3%A8%E5%86%8C%E7%99%BB%E9%99%86-%E5%BC%80%E9%80%9A%E7%9B%B8%E5%85%B3%E6%9D%83%E9%99%90">词典权限开通</a>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(namespace = "appAccount")
    public void openAllPermission(@DistributedLockKey String uid, String name, String phone, String email) {
        User user = userService.getUserByDictUid(uid);
        // 用户不为空且已有BRAND_KOL角色，那么就以BRAND_KOL做后续操作，其他情况都用KOL做操作
        RoleEnum currentRoleEnum = Objects.nonNull(user) && roleService.checkRole(user.getId(), RoleEnum.BRAND_KOL) ? RoleEnum.BRAND_KOL : RoleEnum.KOL;
        Role currentRole = roleService.getByRoleKey(currentRoleEnum.getRoleKey());
        // 用户为空，或者用户未注册 kol/brandKol 角色,或者未绑定媒体账户
        if (Objects.isNull(user) || !(roleService.checkRole(user.getId(), RoleEnum.KOL) || roleService.checkRole(user.getId(), RoleEnum.BRAND_KOL))) {
            user = registerService.registerByDict(uid, currentRoleEnum.getRoleKey(), true);
        }
        loginService.login(user.getId(), currentRole.getRoleKey());
        DictProfile profiles = dictService.getProfiles(uid);
        insertOrUpdateUserDetail(name, phone, email, user, currentRoleEnum, currentRole, profiles);
        bindAppAccountIfAbsent(uid, user, currentRole, profiles);
        openPermission(uid, user, currentRoleEnum, currentRole);
    }

    private void insertOrUpdateUserDetail(String name, String phone, String email, User user, RoleEnum currentRoleEnum, Role currentRole, DictProfile profiles) {
        // KOL新增或插入userDetail
        if (currentRoleEnum.equals(RoleEnum.KOL)) {
            userDetailService.insertOrUpdate(
                    UserDetail.builder()
                            .userId(user.getId())
                            .name(name)
                            .phone(phone)
                            .stage(COMPLETE_USER_DETAIL.getStage())
                            .nickname(profiles.getNickname())
                            .avatar(profiles.getAvatar())
                            .email(email)
                            .build(),
                    currentRole
            );
        }
    }

    private void openPermission(String uid, User user, RoleEnum currentRoleEnum, Role currentRole) {
        if (currentRoleEnum.getRoleKey().equals(RoleEnum.KOL.getRoleKey())) {
            userDetailService.openTaskPermission(user.getId(), currentRole);
            userDetailService.openPostTaskPermission(user.getId(), currentRole);
        }
        dictService.operateProductWindow(uid, true);
    }

    private void bindAppAccountIfAbsent(String uid, User user, Role currentRole, DictProfile profiles) {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndAppUserId(YOUDAO_DICT.getName(), uid);
        if (Objects.isNull(appAccount)) {
            appAccount = AppAccount.builder()
                    .appUserId(uid)
                    .platformId(1L)
                    .name(profiles.getNickname())
                    .avatar(profiles.getAvatar())
                    .gender(profiles.getGender())
                    .fansNum(profiles.getFansNum())
                    .userId(user.getId())
                    .build();
            appAccountService.bind(appAccount, user.getId(), false, currentRole);
        }
    }

    @Override
    public List<ShowcaseComponentDetail2DictVO> getGoodInfoDetails(List<Good> goods) {
        List<ShowcaseComponentDetail2DictVO> res = new ArrayList<>();
        Set<String> taskOrderIds = goods.stream().filter(good -> good.getSource() != 0).map(Good::getGoodId).collect(Collectors.toSet());
        List<TaskOrder> subTaskOrders = taskOrderService.listByOrderIds(taskOrderIds);
        List<TaskOrder> parentOrders = taskOrderService.listByIds(subTaskOrders.stream().map(TaskOrder::getParentOrderId).collect(Collectors.toSet()));
        List<ShowcaseComponent> showcaseComponents = new ArrayList<>();
        showcaseComponents.addAll(
                showcaseComponentService
                        .listByIds(Stream.of(
                                subTaskOrders.stream()
                                        .map(TaskOrder::getShowcaseComponentId)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toSet()),
                                parentOrders.stream()
                                        .map(TaskOrder::getShowcaseComponentId)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toSet())
                                )
                                        .flatMap(Collection::stream)
                                        .collect(Collectors.toSet())
                        )
        );

        showcaseComponents.addAll(
                showcaseComponentService
                        .listByComponentIds(goods.stream()
                                .filter(good -> good.getSource() == 0)
                                .map(Good::getGoodId)
                                .collect(Collectors.toSet())
                        )
        );

        Map<Long, TaskOrder> id2SubTaskOrder = subTaskOrders.stream().collect(Collectors.toMap(TaskOrder::getId, Function.identity(), (oldValue, newValue) -> oldValue));
        Map<String, TaskOrder> orderId2SubTaskOrder = subTaskOrders.stream().collect(Collectors.toMap(TaskOrder::getOrderId, Function.identity(), (oldValue, newValue) -> oldValue));
        Map<Long, TaskOrder> id2ParentTaskOrder = parentOrders.stream().collect(Collectors.toMap(TaskOrder::getId, Function.identity(), (oldValue, newValue) -> oldValue));
        Map<Long, ShowcaseComponent> id2ShowcaseComponent = showcaseComponents.stream().collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity(), (oldValue, newValue) -> oldValue));
        Map<String, ShowcaseComponent> componentId2ShowcaseComponent = showcaseComponents.stream().collect(Collectors.toMap(ShowcaseComponent::getComponentId, Function.identity(), (oldValue, newValue) -> oldValue));

        goods.forEach(good -> {
            try {
                ShowcaseComponentDetail2DictVO vo = showcaseComponentService.latestPassedDetail4Dict(
                        good.getGoodId(),
                        good.getSource(),
                        id2SubTaskOrder,
                        orderId2SubTaskOrder,
                        id2ParentTaskOrder,
                        id2ShowcaseComponent,
                        componentId2ShowcaseComponent);
                if (vo.getId() == null) {
                    return;
                }
                res.add(vo);
            } catch (CustomException e) {
                log.warn("getGoodInfoDetail error goodId:{},source{}", good.getGoodId(), good.getSource(), e);
            }
        });
        return res;
    }

    @Override
    public List<GoodInfoVO> listUserGoodInfo(UserGoodInfoQuery userGoodInfoQuery) {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndAppUserId(YOUDAO_DICT.getName(), userGoodInfoQuery.getUid());
        if (Objects.isNull(appAccount)) {
            throw new CustomException(ResponseType.ACCESS_DENIED);
        }
        // 该接口只有创作者会调用，所以角色从 KOL 和 BRAND_KOL 二选一
        Role role = roleService
                .listByUserId(appAccount.getUserId())
                .stream()
                .filter(r ->
                        r.getRoleKey().equals(RoleEnum.KOL.getRoleKey())
                                || r.getRoleKey().equals(RoleEnum.BRAND_KOL.getRoleKey()))
                .findAny()
                .<CustomException>orElseThrow(() -> {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "该用户没有创作者角色");
                });
        if (roleService.checkRole(appAccount.getUserId(), RoleEnum.BRAND_KOL)
                && BRAND_KOL_CAN_NOT_ACCESS_LIST.contains(userGoodInfoQuery.getSource())) {
            return Collections.emptyList();
        }
        List<GoodInfoVO> goodInfoVOList = new ArrayList<>();
        if (Objects.isNull(userGoodInfoQuery.getSource())) {
            goodInfoVOList.addAll(listPersonalGoodByUserId(userGoodInfoQuery, appAccount, role));
            goodInfoVOList.addAll(listAllAppointmentByConditions(userGoodInfoQuery, appAccount));
            goodInfoVOList.addAll(listAllPostTaskByConditions(userGoodInfoQuery, appAccount));
        } else {
            switch (userGoodInfoQuery.getSource()) {
                case DICT_API_SOURCE_PERSONAL_GOOD:
                    goodInfoVOList.addAll(listPersonalGoodByUserId(userGoodInfoQuery, appAccount, role));
                    break;
                case DICT_API_SOURCE_APPOINTMENT_TASK:
                    goodInfoVOList.addAll(listAllAppointmentByConditions(userGoodInfoQuery, appAccount));
                    break;
                case DICT_API_SOURCE_POST_TASK:
                    goodInfoVOList.addAll(listAllPostTaskByConditions(userGoodInfoQuery, appAccount));
                    break;
                default:
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "can't handle this source.");
            }
        }
        return goodInfoVOList;
    }

    private List<GoodInfoVO> listAllPostTaskByConditions(UserGoodInfoQuery userGoodInfoQuery, AppAccount appAccount) {
        List<GoodInfoVO> goodInfoVOList = new ArrayList<>();
        taskOrderService.getOrderPage(TaskOrderQuery.builder()
                .promoteTitle(userGoodInfoQuery.getPromotionTitle())
                .taskOrderStatus(TASK_ORDER_IN_PROGRESS)
                .type(TASK_ORDER_TYPE_POST)
                .current(1L).size(Long.MAX_VALUE)
                .build(), RoleEnum.KOL, appAccount.getUserId()).getRecords()
                .forEach(item -> {
                    if (item.getStatus() == TASK_ORDER_IN_PROGRESS &&
                            CollectionUtils.isNotEmpty(item.getOrders()) &&
                            item.getOrders().size() == 1) {
                        item.getOrders().forEach(subTaskOrderVO -> {
                            goodInfoVOList.add(
                                    GoodInfoVO.builder()
                                            .goodId(subTaskOrderVO.getOrderId())
                                            .source(DICT_API_SOURCE_POST_TASK)
                                            .name(item.getShowcaseComponent().getAuditedPromoteTitle())
                                            .image(item.getShowcaseComponent().getPromoteImageUrl())
                                            .status(subTaskOrderVO.getStatus())
                                            .build());
                        });

                    }
                });
        return goodInfoVOList;
    }

    private List<GoodInfoVO> listAllAppointmentByConditions(UserGoodInfoQuery userGoodInfoQuery, AppAccount appAccount) {
        List<TaskOrderVO> taskOrderVOList = new ArrayList<>();
        taskOrderVOList.addAll(
                taskOrderService.getOrderPage(TaskOrderQuery.builder()
                        .promoteTitle(userGoodInfoQuery.getPromotionTitle())
                        .type(TASK_ORDER_TYPE_APPOINTMENT)
                        .taskOrderStatus(TASK_ORDER_IN_PROGRESS)
                        .current(0L).size(Long.MAX_VALUE)
                        .build(), RoleEnum.KOL, appAccount.getUserId()).getRecords()
        );
        taskOrderVOList.addAll(
                taskOrderService.getOrderPage(TaskOrderQuery.builder()
                        .promoteTitle(userGoodInfoQuery.getPromotionTitle())
                        .type(TASK_ORDER_TYPE_APPOINTMENT)
                        .taskOrderStatus(TASK_ORDER_WAITING_FOR_PAYMENT)
                        .current(1L).size(Long.MAX_VALUE)
                        .build(), RoleEnum.KOL, appAccount.getUserId()).getRecords()
        );
        Set<Long> showcaseComponentIds = new HashSet<>(taskOrderVOList.size());
        taskOrderVOList.forEach(item -> {
            item.getOrders().forEach(a -> {
                showcaseComponentIds.add(a.getShowcaseComponentId());
            });
        });
        Map<Long, ShowcaseComponent> id2ShowcaseComponent = showcaseComponentService.listByIds(
                showcaseComponentIds
        ).stream().collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));
        List<GoodInfoVO> goodInfoVOList = new ArrayList<>(taskOrderVOList.size());
        taskOrderVOList.forEach(item -> {
            if (CollectionUtils.isNotEmpty(item.getOrders()) && item.getOrders().size() == 1) {
                TaskOrderVO subTaskOrderVO = item.getOrders().get(0);
                if (Objects.nonNull(subTaskOrderVO.getShowcaseComponent())
                        &&
                        Objects.nonNull(id2ShowcaseComponent.get(subTaskOrderVO.getShowcaseComponentId()))) {
                    try {
                        ShowcaseComponent auditedContent = id2ShowcaseComponent.get(subTaskOrderVO.getShowcaseComponentId()).getAuditedContent();
                        goodInfoVOList.add(
                                GoodInfoVO.builder()
                                        .goodId(subTaskOrderVO.getOrderId())
                                        .source(DICT_API_SOURCE_APPOINTMENT_TASK)
                                        .name(id2ShowcaseComponent.get(subTaskOrderVO.getShowcaseComponentId()).getAuditedPromoteTitle())
                                        .image(auditedContent.getPromoteImageUrl())
                                        .status(subTaskOrderVO.getStatus())
                                        .build()
                        );
                    } catch (Exception e) {
                        log.error("reading json error", e);
                        throw new RuntimeException(e);
                    }
                }
            }
        });
        return goodInfoVOList;
    }

    private List<GoodInfoVO> listPersonalGoodByUserId(UserGoodInfoQuery userGoodInfoQuery, AppAccount appAccount, Role role) {
        try {
            List<GoodInfoVO> goodInfoVOList = new ArrayList<>();
            showcaseComponentService.pagePassed(
                    ShowcaseComponentCriteriaQueryVO.builder()
                            .status(SHOWCASE_COMPONENT_STATUS_NORMAL)
                            .promotionTitle(userGoodInfoQuery.getPromotionTitle())
                            .deleted(false)
                            .current(1L)
                            .size((long) Integer.MAX_VALUE)
                            .build(),
                    appAccount.getUserId(),
                    role
            ).getRecords().forEach(item -> {
                // 有审核通过的物料，且是上架状态，且审核状态不是不通过和审核中
                if (StringUtils.isNotBlank(item.getAuditedPromoteTitle())) {
                    goodInfoVOList.add(
                            GoodInfoVO.builder().goodId(item.getComponentId())
                                    .source(DICT_API_SOURCE_PERSONAL_GOOD)
                                    .name(item.getAuditedPromoteTitle())
                                    .image(item.getPromoteImageUrl())
                                    .status(item.getStatus())
                                    .auditStatus(item.getAuditStatus()).build());
                }
            });
            return goodInfoVOList;
        } catch (Exception e) {
            log.error("Error when list personal showcase", e);
            throw new CustomException(ResponseType.SERVICE_ERROR);
        }
    }

    @Override
    public List<GoodInfoVO> listGoodInfo(Set<Good> queryGoodsList) {
        Map<Integer, List<Good>> source2GoodList = queryGoodsList
                .stream()
                .filter(good -> Objects.nonNull(good) && Objects.nonNull(good.getSource()))
                .collect(Collectors.groupingBy(Good::getSource));
        List<GoodInfoVO> goodInfoVOList = new ArrayList<>(queryGoodsList.size());
        for (Map.Entry<Integer, List<Good>> entry : source2GoodList.entrySet()) {
            switch (entry.getKey()) {
                case DICT_API_SOURCE_PERSONAL_GOOD:
                    Collection<ShowcaseComponent> showcaseComponents =
                            showcaseComponentService.listByComponentIds(
                                    entry.getValue()
                                            .stream()
                                            .map(Good::getGoodId)
                                            .collect(Collectors.toSet())
                            );
                    showcaseComponents.forEach(item -> {
                        if (Objects.nonNull(item.getAuditedContent())) {
                            try {
                                ShowcaseComponent auditedShowcaseComponent = item.getAuditedContent();
                                goodInfoVOList.add(GoodInfoVO.builder()
                                        .goodId(item.getComponentId())
                                        .source(DICT_API_SOURCE_PERSONAL_GOOD)
                                        .name(item.getAuditedPromoteTitle())
                                        .image(auditedShowcaseComponent.getPromoteImageUrl())
                                        .status(item.getStatus())
                                        .auditStatus(item.getAuditStatus())
                                        .build()
                                );
                            } catch (Exception e) {
                                log.error("reading json ERROR!", e);
                                throw new RuntimeException(e);
                            }
                        } else {
                            goodInfoVOList.add(GoodInfoVO.builder()
                                    .goodId(item.getComponentId())
                                    .source(DICT_API_SOURCE_PERSONAL_GOOD)
                                    .status(item.getStatus())
                                    .auditStatus(item.getAuditStatus())
                                    .build()
                            );
                        }
                    });
                    break;
                case DICT_API_SOURCE_APPOINTMENT_TASK:
                    queryAppointmentTask(goodInfoVOList, entry);
                    break;
                case DICT_API_SOURCE_POST_TASK:
                    queryPostTask(goodInfoVOList, entry);
                    break;
                default:
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "can't handle this source: " + entry.getKey());

            }
        }
        return goodInfoVOList;
    }

    private void queryPostTask(List<GoodInfoVO> goodInfoVOList, Map.Entry<Integer, List<Good>> entry) {
        List<TaskOrder> subPostTaskOrders =
                taskOrderService.listByOrderIds(
                        entry.getValue().stream()
                                .map(Good::getGoodId)
                                .collect(Collectors.toSet())
                );
        List<TaskOrder> parentPostTaskOrders =
                taskOrderService.listByIds(subPostTaskOrders.stream()
                        .map(TaskOrder::getParentOrderId)
                        .collect(Collectors.toSet()));
        Map<Long, ShowcaseComponent> id2ShowcaseComponent =
                showcaseComponentService.listByIds(parentPostTaskOrders.stream()
                        .map(TaskOrder::getShowcaseComponentId)
                        .collect(Collectors.toSet()))
                        .stream()
                        .collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));
        Map<Long, TaskOrder> id2ParentPostTaskOrder =
                parentPostTaskOrders.stream()
                        .collect(Collectors.toMap(TaskOrder::getId, Function.identity()));
        subPostTaskOrders.forEach(item -> {
            ShowcaseComponent showcaseComponent = id2ShowcaseComponent.get(id2ParentPostTaskOrder.get(item.getParentOrderId()).getShowcaseComponentId());
            try {
                goodInfoVOList.add(
                        GoodInfoVO.builder().goodId(item.getOrderId())
                                .source(DICT_API_SOURCE_POST_TASK)
                                .name(showcaseComponent.getAuditedPromoteTitle())
                                .image(showcaseComponent.getAuditedContent().getPromoteImageUrl())
                                .status(item.getStatus())
                                .auditStatus(showcaseComponent.getAuditStatus())
                                .build()
                );
            } catch (Exception e) {
                throw new CustomException(ResponseType.SERVICE_ERROR);
            }
        });
    }

    private void queryAppointmentTask(List<GoodInfoVO> goodInfoVOList, Map.Entry<Integer, List<Good>> entry) {
        List<TaskOrder> subAppointmentTaskOrders =
                taskOrderService.listByOrderIds(
                        entry.getValue().stream().map(Good::getGoodId).collect(Collectors.toSet())
                );
        Map<Long, ShowcaseComponent> id2ShowcaseComponent =
                showcaseComponentService.listByIds(
                        subAppointmentTaskOrders.stream()
                                .map(TaskOrder::getShowcaseComponentId)
                                .collect(Collectors.toList()))
                        .stream()
                        .collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));
        subAppointmentTaskOrders.forEach(item -> {
            try {
                ShowcaseComponent showcaseComponent = id2ShowcaseComponent.get(item.getShowcaseComponentId());
                if (Objects.nonNull(showcaseComponent) && Objects.nonNull(showcaseComponent.getAuditedContent())) {
                    ShowcaseComponent auditedShowcaseComponent = showcaseComponent.getAuditedContent();
                    goodInfoVOList.add(
                            GoodInfoVO.builder()
                                    .goodId(item.getOrderId())
                                    .source(DICT_API_SOURCE_APPOINTMENT_TASK)
                                    .name(showcaseComponent.getAuditedPromoteTitle())
                                    .image(auditedShowcaseComponent.getPromoteImageUrl())
                                    .status(item.getStatus())
                                    .auditStatus(showcaseComponent.getAuditStatus())
                                    .build()
                    );
                } else {
                    goodInfoVOList.add(
                            GoodInfoVO.builder()
                                    .goodId(item.getOrderId())
                                    .source(DICT_API_SOURCE_APPOINTMENT_TASK)
                                    .status(item.getStatus())
                                    .build()
                    );
                }
            } catch (Exception e) {
                log.error("reading json error.", e);
                throw new RuntimeException(e);
            }
        });
    }
}

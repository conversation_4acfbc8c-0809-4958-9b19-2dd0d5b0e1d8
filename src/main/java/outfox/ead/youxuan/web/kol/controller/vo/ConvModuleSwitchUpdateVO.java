package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 */
@Data
public class ConvModuleSwitchUpdateVO {

    @ApiParam(value = "转化组件ID")
    private Long id;

    @Range(min = 0, max = 3, message = "type错误")
    @ApiParam(value = "类型：0-官方网站；1-应用下载；2-联系电话；3-推广活动")
    private Integer type;

    @ApiParam(value = "开关状态")
    private Boolean isOpen;
}

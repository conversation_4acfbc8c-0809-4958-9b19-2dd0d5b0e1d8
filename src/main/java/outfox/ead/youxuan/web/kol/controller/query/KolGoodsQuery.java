package outfox.ead.youxuan.web.kol.controller.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022年10月18日 16:43
 */
@Data
public class KolGoodsQuery {
    @ApiModelProperty("页数")
    @NotNull
    @Min(1)
    private int page;
    @ApiModelProperty("请求数据条数")
    @NotNull
    @Min(1)
    private Integer size;
    @ApiModelProperty("品类")
    private Integer category;
    @ApiModelProperty("商品Id")
    @NotNull
    private String componentId;
    @ApiModelProperty("词典创作者用户Id")
    @NotNull
    private String uid;
    private String name;
}

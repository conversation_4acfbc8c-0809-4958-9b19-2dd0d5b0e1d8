package outfox.ead.youxuan.web.kol.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.jd.open.api.sdk.domain.kplunion.GoodsService.response.query.*;
import com.jd.open.api.sdk.domain.kplunion.promotioncommon.PromotionService.response.get.GetResult;
import com.jd.open.api.sdk.response.kplunion.UnionOpenGoodsJingfenQueryResponse;
import com.jd.open.api.sdk.response.kplunion.UnionOpenGoodsQueryResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.ShowcaseComponent;
import outfox.ead.youxuan.util.JdItemUtil;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.BatchSaveDetailVO;
import outfox.ead.youxuan.web.ad.controller.vo.FailSaveInfoVO;
import outfox.ead.youxuan.web.kol.service.ItemService;
import outfox.ead.youxuan.web.kol.service.ShowcaseComponentService;

import java.util.*;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.ContentMarketingConstants.*;

/**
 * <AUTHOR>
 * @date 2022年08月30日 16:44
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ItemServiceImpl implements ItemService {
    private final ObjectMapper objectMapper;
    private final ShowcaseComponentService showcaseComponentService;
    private final DelegatingSecurityContextAsyncTaskExecutor executor;

    private final String JD_RESPONSE_SUCCESS_CODE = "0";
    private final int JD_QUERY_RESULT_SUCCESS_CODE = 200;

    @SneakyThrows
    @Override
    public BatchSaveDetailVO importJdItemByGroupIds(List<Long> groupIds) {
        BatchSaveDetailVO batchSaveDetailVO = new BatchSaveDetailVO();
        batchSaveDetailVO.setGroupIdCount((long) groupIds.size());
        List<Future<BatchSaveDetailVO>> futures = new ArrayList<>(groupIds.size());
        for (Long groupId : groupIds) {
            futures.add(executor.submit(() -> createShowcaseComponent(groupId)));
        }
        futures.forEach(future -> {
            try {
                batchSaveDetailVO.add(future.get());
            } catch (Exception e) {
                log.error("", e);
            }
        });
        return batchSaveDetailVO;
    }

    private final Pattern SKU_ID_PATTERN = Pattern.compile("/[0-9]+\\.html");

    @Override
    public BatchSaveDetailVO importJdItemByUrls(List<String> urls) {
        BatchSaveDetailVO batchSaveDetailVO = new BatchSaveDetailVO();
        batchSaveDetailVO.total((long) urls.size());
        List<List<String>> urlss = Lists.partition(urls, 20);
        for (List<String> url : urlss) {
            batchSaveDetailVO.add(extracted(url));
        }
        return batchSaveDetailVO;
    }

    private BatchSaveDetailVO extracted(List<String> urls) {
        BatchSaveDetailVO batchSaveDetailVO = new BatchSaveDetailVO();
        Set<Long> skuIds = getSkuIds(urls);
        UnionOpenGoodsQueryResponse response = JdItemUtil.getUnionOpenGoodsQueryResponse(skuIds.toArray(new Long[0]));
        GoodsQueryResult queryResult = response.getQueryResult();
        if (Objects.equals(response.getCode(), JD_RESPONSE_SUCCESS_CODE) && queryResult.getCode() == JD_QUERY_RESULT_SUCCESS_CODE) {
            GoodsResp[] dataArray = Optional.ofNullable(queryResult.getData()).orElse(new GoodsResp[0]);
            if (!Objects.equals(skuIds.size(), dataArray.length)) {
                skuIds.removeAll(Arrays.stream(dataArray).map(GoodsResp::getSkuId).collect(Collectors.toSet()));
                FailSaveInfoVO failSaveInfoVO = new FailSaveInfoVO();
                batchSaveDetailVO.fail((long) skuIds.size());
                failSaveInfoVO.setMsg(String.format("通过skuIds查询缺少部分数据，缺少skuIds: %s", skuIds));
                batchSaveDetailVO.failInfo(failSaveInfoVO);
            }
            List<GoodsResp> datas = Arrays.asList(dataArray);
            Map<String, Future<String>> clickUrlFutureMap = getClickUrlFutureMap(datas.stream().map(GoodsResp::getMaterialUrl).collect(Collectors.toSet()));
            List<ShowcaseComponent> showcaseComponents = new ArrayList<>(dataArray.length);
            for (GoodsResp data : datas) {
                if (showcaseComponentService.exists(data.getSkuId())) {
                    batchSaveDetailVO.skip();
                    continue;
                }
                try {
                    showcaseComponents.add(
                            getShowcaseComponent(clickUrlFutureMap,
                                    data.getSkuName(),
                                    data.getCategoryInfo(),
                                    data.getMaterialUrl(),
                                    data.getPriceInfo(),
                                    data.getCommissionInfo(),
                                    data.getSkuId(),
                                    data.getImageInfo())
                    );
                } catch (Exception e) {
                    log.error("获取clickURL异常 response:{}", response, e);
                    FailSaveInfoVO failSaveInfoVO = new FailSaveInfoVO();
                    failSaveInfoVO.setMsg(String.format("获取clickURL异常 skuId : %o", data.getSkuId()));
                    batchSaveDetailVO.failInfo(failSaveInfoVO);
                    batchSaveDetailVO.fail(1L);
                }
            }
            showcaseComponentService.saveBatch(showcaseComponents);
            log.info("成功导入{}条数据", showcaseComponents.size());
            batchSaveDetailVO.success((long) showcaseComponents.size());
        } else {
            batchSaveDetailVO.fail((long) urls.size());
            batchSaveDetailVO.failInfo(FailSaveInfoVO.builder().msg(String.format("getUnionOpenGoodsQueryResponse error , unionOpenGoodsQueryResponse code: %s", response.getCode())).build());
        }
        return batchSaveDetailVO;
    }

    @Override
    public void refreshItem() {
        List<Long> failUpdateComponentIds = new ArrayList<>();

        List<ShowcaseComponent> showcaseComponents = showcaseComponentService.listJdItem();

        for (List<ShowcaseComponent> components : Lists.partition(showcaseComponents, 20)) {
            try {
                doBatchRefreshItems(components, failUpdateComponentIds);
            } catch (Exception e) {
                log.error("update small batch showcase component failed.", e);
                failUpdateComponentIds.addAll(components.stream().map(ShowcaseComponent::getSkuId).collect(Collectors.toSet()));
            }
        }

        if (CollectionUtils.isNotEmpty(failUpdateComponentIds)) {
            log.warn("total:{}, error:{}, 更新失败的sku_id:{}", showcaseComponents.size(),
                    failUpdateComponentIds.size(),
                    failUpdateComponentIds);
        }
    }

    private void doBatchRefreshItems(List<ShowcaseComponent> components, List<Long> failUpdateComponentIds) {
        Map<Long, ShowcaseComponent> skuId2ShowcaseComponentMap = components.stream()
                .collect(Collectors.toMap(ShowcaseComponent::getSkuId, showcaseComponent -> showcaseComponent));

        List<GoodsResp> goodsResponses = getGoodsResponseFromJd(skuId2ShowcaseComponentMap.keySet());

        List<ShowcaseComponent> showcaseComponentsForUpdate = new ArrayList<>(goodsResponses.size());
        for (GoodsResp gr : goodsResponses) {
            ShowcaseComponent sc = skuId2ShowcaseComponentMap.get(gr.getSkuId());
            sc.setItemName(gr.getSkuName());
            sc.setName(gr.getSkuName());
            sc.setItemPrice(getItemPrice(gr.getPriceInfo()));
            sc.setOriginalCommissionPrice(gr.getCommissionInfo());
            sc.setPromoteImageUrl(gr.getImageInfo().getImageList()[0].getUrl());
            sc.setAuditedPromoteTitle();
            if (sc.getAuditedContent() != null) {
                ShowcaseComponent auditedContent = sc.getAuditedContent();
                auditedContent.setItemName(gr.getSkuName());
                auditedContent.setName(gr.getSkuName());
                auditedContent.setItemPrice(getItemPrice(gr.getPriceInfo()));
                auditedContent.setPromoteImageUrl(gr.getImageInfo().getImageList()[0].getUrl());
                auditedContent.setAuditedPromoteTitle();
            }
            showcaseComponentsForUpdate.add(sc);
        }
        skuId2ShowcaseComponentMap.keySet().removeAll(showcaseComponentsForUpdate.stream().map(ShowcaseComponent::getSkuId).collect(Collectors.toSet()));
        failUpdateComponentIds.addAll(skuId2ShowcaseComponentMap.keySet());
        showcaseComponentService.updateBatchById(showcaseComponentsForUpdate);
    }

    private List<GoodsResp> getGoodsResponseFromJd(Set<Long> skuIds) {
        List<GoodsResp> goodsResponses = Collections.emptyList();
        try {
            goodsResponses = Optional
                    .ofNullable(JdItemUtil.getUnionOpenGoodsQueryResponse(skuIds.toArray(new Long[0])))
                    .map(UnionOpenGoodsQueryResponse::getQueryResult)
                    .map(GoodsQueryResult::getData)
                    .map(Arrays::asList)
                    .orElse(Collections.emptyList());
        } catch (Exception e) {
            log.error("get goods from JD failed with sku ids {}.", skuIds, e);
        }
        return goodsResponses;
    }

    private Set<Long> getSkuIds(List<String> urls) {
        Set<Long> skuIds = new HashSet<>();
        for (String url : urls) {
            Matcher matcher = SKU_ID_PATTERN.matcher(url);
            if (matcher.find()) {
                String group = matcher.group(0);
                skuIds.add(Long.valueOf(group.substring(1, group.length() - 5)));
            }
        }
        return skuIds;
    }


    private BatchSaveDetailVO createShowcaseComponent(Long groupId) {
        UnionOpenGoodsJingfenQueryResponse response = JdItemUtil.getUnionOpenGoodsJingfenQueryResponse(groupId);
        JingfenQueryResult queryResult = response.getQueryResult();
        BatchSaveDetailVO batchSaveDetailVO = new BatchSaveDetailVO();
        if (JD_RESPONSE_SUCCESS_CODE.equals(response.getCode()) && queryResult.getCode() == JD_QUERY_RESULT_SUCCESS_CODE) {
            batchSaveDetailVO.setTotal((long) queryResult.getData().length);
            List<ShowcaseComponent> showcaseComponents = new ArrayList<>(queryResult.getData().length);
            List<JFGoodsResp> datas = Arrays.asList(queryResult.getData());
            Map<String, Future<String>> materialUrl2ClickUrl = getClickUrlFutureMap(datas.stream().map(JFGoodsResp::getMaterialUrl).collect(Collectors.toSet()));
            for (JFGoodsResp data : datas) {
                if (showcaseComponentService.exists(data.getSkuId())) {
                    batchSaveDetailVO.skip();
                    continue;
                }
                try {
                    showcaseComponents.add(
                            getShowcaseComponent(materialUrl2ClickUrl,
                                    data.getSkuName(),
                                    data.getCategoryInfo(),
                                    data.getMaterialUrl(),
                                    data.getPriceInfo(),
                                    data.getCommissionInfo(),
                                    data.getSkuId(),
                                    data.getImageInfo())
                    );
                } catch (Exception e) {
                    log.error("获取clickURL异常,groupId:{}", groupId, e);
                    FailSaveInfoVO failSaveInfoVO = new FailSaveInfoVO();
                    failSaveInfoVO.setJdGroupId(groupId);
                    failSaveInfoVO.setMsg(String.format("获取clickURL异常 groupId : %o skuId : %o", groupId, data.getSkuId()));
                    batchSaveDetailVO.failInfo(failSaveInfoVO);
                    batchSaveDetailVO.fail(1L);
                }
            }
            showcaseComponentService.saveBatch(showcaseComponents);
            log.info("groupId:{}, 成功导入{}条数据", groupId, showcaseComponents.size());
            batchSaveDetailVO.success((long) showcaseComponents.size());
        } else {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "getUnionOpenGoodsJingfenQueryResponse error groupId:" + groupId);
        }
        return batchSaveDetailVO;
    }

    private ShowcaseComponent getShowcaseComponent(Map<String, Future<String>> materialUrl2ClickUrl,
                                                   String skuName,
                                                   CategoryInfo categoryInfo,
                                                   String materialUrl,
                                                   PriceInfo priceInfo,
                                                   CommissionInfo commissionInfo,
                                                   Long skuId,
                                                   ImageInfo imageInfo) throws InterruptedException, java.util.concurrent.ExecutionException, JsonProcessingException {
        return ShowcaseComponent
                .builder()
                .name(skuName)
                .promotionType(SHOWCASE_COMPONENT_PROMOTION_TYPE_SALES)
                .originalCategoryInfo(categoryInfo)
                .category(10087)
                .switchType(SHOWCASE_COMPONENT_SWITCH_LANDING_PAGE)
                .landingPageUrl(materialUrl2ClickUrl.get(materialUrl).get())
                .appendOutVendor(false)
                .schemaId(SHOWCASE_COMPONENT_SALES_SCHEMA_1_ID)
                .leadText(skuName)
                .itemName(skuName)
                .itemPrice(getItemPrice(priceInfo))
                .buttonText("购买")
                .markAsAd(true)
                .originalCommissionPrice(commissionInfo)
                .skuId(skuId)
                .componentId(RandomStringUtils.random(10, true, false))
                .creator(SecurityUtil.getUserId())
                .modifier(SecurityUtil.getUserId())
                .promoteImageUrl(imageInfo.getImageList()[0].getUrl())
                .status(SHOWCASE_COMPONENT_STATUS_NORMAL)
                .auditStatus(SHOWCASE_COMPONENT_STATUS_AUDITING)
                .roleId(SecurityUtil.getCurrentRole().getId())
                .build();
    }

    private long getItemPrice(PriceInfo priceInfo) {
        Double price = ObjectUtils.firstNonNull(priceInfo.getLowestCouponPrice(), priceInfo.getLowestCouponPrice(), priceInfo.getLowestPrice(), priceInfo.getPrice());
        if (Objects.isNull(price)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "priceInfo error");
        }
        return (long) (price * 100);
    }

    private Map<String, Future<String>> getClickUrlFutureMap(Set<String> materialUrls) {
        Map<String, Future<String>> materialUrlMap = new HashMap<>(materialUrls.size());
        for (String materialUrl : materialUrls) {
            materialUrlMap.put(materialUrl, executor.submit(() -> {
                GetResult getResult = JdItemUtil.getUnionOpenPromotionCommonGetResponse(materialUrl).getGetResult();
                if (getResult.getCode() == JD_QUERY_RESULT_SUCCESS_CODE) {
                    return getResult.getData().getClickURL();
                }
                return null;
            }));
        }
        return materialUrlMap;
    }
}

package outfox.ead.youxuan.web.kol.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.youxuan.constants.CacheNameConstant;
import outfox.ead.youxuan.constants.ContentMarketingConstants;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.mapper.youxuan.TaskOrderMapper;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.LoginService;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserDetailService;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.controller.bo.AutoShowcaseInfo;
import outfox.ead.youxuan.web.kol.controller.bo.AutoShowcasePostBO;
import outfox.ead.youxuan.web.kol.controller.bo.Good;
import outfox.ead.youxuan.web.kol.controller.dto.PreTaskOrderDTO;
import outfox.ead.youxuan.web.kol.controller.mapper.PreTaskOrderMapper;
import outfox.ead.youxuan.web.kol.controller.mapper.ShowcaseComponentMapper;
import outfox.ead.youxuan.web.kol.controller.vo.*;
import outfox.ead.youxuan.web.kol.handler.AutoShowcaseHandler;
import outfox.ead.youxuan.web.kol.service.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.ConfigKeyConstants.DICT_FETCH_TASK_SPONSOR_LIST;
import static outfox.ead.youxuan.constants.ContentMarketingConstants.*;
import static outfox.ead.youxuan.constants.PlatformEnum.YOUDAO_DICT;

/**
 * <AUTHOR>
 * @description 针对表【TaskOrder(任务订单表)】的数据库操作Service实现
 * @createDate 2022-02-21 10:57:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskOrderServiceImpl extends YouxuanServiceImpl<TaskOrderMapper, TaskOrder>
        implements TaskOrderService {

    private final KolPlatformContentRelationService kolPlatformContentRelationService;

    private final PreTaskOrderService preTaskOrderService;

    @Lazy
    private final AppAccountService appAccountService;

    private final PlatformTaskService platformTaskService;

    private final PlatformContentService platformContentService;

    private final PlatformService platformService;

    @Lazy
    private final ShowcaseComponentService showcaseComponentService;

    private final PreTaskOrderMapper preTaskOrderMapper;

    private final ShowcaseComponentMapper showcaseComponentMapper;

    private final outfox.ead.youxuan.web.kol.controller.mapper.TaskOrderMapper taskOrderMapper;

    private final ObjectMapper objectMapper;

    private final LoginService loginService;

    private final DictService dictService;

    private final RoleService roleService;

    private final ConfigService configService;

    private final TypeReference<List<Long>> longListTypeReference;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int makeOrder(TaskOrderCreateVO taskOrderCreateVO, Long userId) {
        List<Long> preOrderIds = taskOrderCreateVO.getPreOrderId();
        if (CollectionUtils.isEmpty(preOrderIds)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        List<PreTaskOrder> preTaskOrders = preTaskOrderService.getValidById(preOrderIds);
        if (preOrderIds.size() != preTaskOrders.size()) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        makeOrderPreTaskOrderCheck(preTaskOrders);
        // cal total price
        Long total = preTaskOrders.stream().mapToLong(PreTaskOrder::getPrice).sum();
        // create main order
        Long parentOrderId = insertMainOrder(total);
        // create sub order
        insertSubOrder(parentOrderId, preTaskOrders);
        // delete pre task order
        preTaskOrderService.deleteByIds(preTaskOrders.stream().map(PreTaskOrder::getId).collect(Collectors.toList()));
        return preTaskOrders.size();
    }

    @Override
    public Long addKol2PreTaskOrder(CreatePreTaskOrderVO createPreTaskOrderVO, Long userId) {
        KolPlatformContentRelation kolPlatformContentRelation =
                kolPlatformContentRelationService.getByAppAccountIdAndPlatformContentId(
                        createPreTaskOrderVO.getAppAccountId(),
                        createPreTaskOrderVO.getPlatformContentId()
                );
        if (Objects.isNull(kolPlatformContentRelation) ||
                ObjectUtils.notEqual(kolPlatformContentRelation.getPrice(), createPreTaskOrderVO.getPrice())) {
            throw new CustomException(ResponseType.PRICE_WRONG);
        }
        AppAccount appAccount = appAccountService.getBindById(createPreTaskOrderVO.getAppAccountId());
        if (Objects.isNull(appAccount) || !appAccount.getInService()) {
            throw new CustomException(ResponseType.KOL_STATUS_EXCEPTION);
        }
        PreTaskOrder oldPreTaskOrder =
                preTaskOrderService.getOne(
                        createPreTaskOrderVO.getAppAccountId(),
                        createPreTaskOrderVO.getPlatformContentId(),
                        userId
                );
        if (Objects.nonNull(oldPreTaskOrder)) {
            //重复提交，更新备注
            preTaskOrderService.updateCommentAndShowcaseComponentById(oldPreTaskOrder.getId(), createPreTaskOrderVO.getComment(), createPreTaskOrderVO.getShowcaseComponentId());
            return ContentMarketingConstants.PRE_TASK_ORDER_REPETITION;
        }

        PreTaskOrder preTaskOrder = PreTaskOrder.builder()
                .appAccountId(appAccount.getId())
                .platformContentId(createPreTaskOrderVO.getPlatformContentId())
                .kolName(appAccount.getName())
                .kolAvatar(appAccount.getAvatar())
                .comment(createPreTaskOrderVO.getComment())
                .price(kolPlatformContentRelation.getPrice())
                .showcaseComponentId(createPreTaskOrderVO.getShowcaseComponentId())
                .build();

        preTaskOrderService.save(preTaskOrder);
        return preTaskOrder.getId();
    }

    @Override
    public List<PlatformTaskVO> getSelectedTaskTypeList(Long userId) {
        List<PreTaskOrder> preTaskOrders = preTaskOrderService.getByCreator(userId);
        if (CollectionUtils.isEmpty(preTaskOrders)) {
            return Collections.emptyList();
        }
        Set<Long> platformContentIds = preTaskOrders.stream().map(PreTaskOrder::getPlatformContentId).collect(Collectors.toSet());
        return platformTaskService.getListByIds(
                platformContentService.getPlatformContentByIds(new ArrayList<>(platformContentIds))
                        .stream()
                        .map(PlatformContent::getPlatformTaskId)
                        .collect(Collectors.toList()));
    }

    @Override
    public boolean deleteCandidateKol(Long id, Long userId) {
        return preTaskOrderService.deleteByIdAndCreatorId(id, userId);
    }

    @Override
    public Long getCandidateKolCount(Long userId) {
        return preTaskOrderService.getCountByCreator(userId);
    }

    @Override
    public PreTaskOrderListVO getPreOrderList(Long platformTaskId, Long userId) {
        //if has platform task id then query db get platform content belong to task.
        List<PlatformContent> platformContents = Collections.emptyList();
        List<Long> platformContentIds = Collections.emptyList();
        List<PlatformTaskVO> platformTaskVOList = Collections.emptyList();

        if (Objects.nonNull(platformTaskId)) {
            //get platform content by task.
            platformTaskVOList = platformTaskService.getListByIds(Collections.singletonList(platformTaskId));
            if (CollectionUtils.isEmpty(platformTaskVOList)) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS);
            }
            platformContents = platformContentService.getByPlatformTaskId(platformTaskId);
            platformContentIds = platformContents.stream()
                    .map(PlatformContent::getId)
                    .distinct()
                    .collect(Collectors.toList());
        }
        List<PreTaskOrder> preTaskOrders =
                preTaskOrderService.getListByPlatformContentIdsAndUserId(platformContentIds, userId);
        if (CollectionUtils.isEmpty(preTaskOrders)) {
            return PreTaskOrderListVO.builder().build();
        }
        if (CollectionUtils.isEmpty(platformTaskVOList)) {
            //get task by content.
            platformContentIds = preTaskOrders.stream().map(PreTaskOrder::getPlatformContentId).distinct().collect(Collectors.toList());
            platformContents = platformContentService.getPlatformContentByIds(platformContentIds);
            platformTaskVOList = platformTaskService.getListByIds(platformContents.stream().map(PlatformContent::getPlatformTaskId).distinct().collect(Collectors.toList()));
        }

        List<TaskOrderInitQueryVO> taskOrderInitQueryVOList = new ArrayList<>(preTaskOrders.size());

        for (PreTaskOrder preTaskOrder : preTaskOrders) {
            taskOrderInitQueryVOList.add(
                    TaskOrderInitQueryVO.builder()
                            .appAccountId(preTaskOrder.getAppAccountId())
                            .platformContentId(preTaskOrder.getPlatformContentId())
                            .build()
            );
        }

        Map<Long, Map<Long, KolPlatformContentRelation>> kolPlatformContentRelationQueryMirror =
                getKolPlatformContentRelationQueryMirror(taskOrderInitQueryVOList);
        Map<Long, AppAccount> appAccountId2Obj = appAccountService.getBindAndInServiceByIds(
                        preTaskOrders.stream()
                                .map(PreTaskOrder::getAppAccountId)
                                .distinct()
                                .collect(Collectors.toList())
                ).stream()
                .collect(Collectors.toMap(AppAccount::getId, Function.identity()));
        Map<Long, PlatformTaskVO> platformTaskId2Obj =
                platformTaskVOList.stream().collect(Collectors.toMap(PlatformTaskVO::getId, Function.identity()));
        Map<Long, PlatformContent> platformContentId2Obj =
                platformContents.stream().collect(Collectors.toMap(PlatformContent::getId, Function.identity()));

        List<PreTaskOrderVO> preTaskOrderVOList = new ArrayList<>();
        List<PreTaskOrder> preTaskOrderUpdateList = new ArrayList<>();
        boolean isHaveInvalid = false;
        boolean isHaveModified = false;
        Map<Long, ShowcaseComponent> id2ShowcaseComponent = getValidShowcaseComponent(preTaskOrders);
        // 需要更新或需要删除的pre task order id
        List<Long> needUpdateIdList =
                listIndexOfDetailModifiedOrInvalid(preTaskOrders, appAccountId2Obj, kolPlatformContentRelationQueryMirror, id2ShowcaseComponent);
        for (PreTaskOrder preTaskOrder : preTaskOrders) {
            PreTaskOrderVO.PreTaskOrderVOBuilder builder = PreTaskOrderVO.builder();
            PlatformContent platformContent = platformContentId2Obj.get(preTaskOrder.getPlatformContentId());
            //check状态
            if (needUpdateIdList.contains(preTaskOrder.getId())) {
                if (Objects.isNull(appAccountId2Obj.get(preTaskOrder.getAppAccountId()))
                        || showcaseInvalid(id2ShowcaseComponent, preTaskOrder)) {
                    preTaskOrder.setDeleted(true);
                    preTaskOrderUpdateList.add(preTaskOrder);
                    isHaveInvalid = true;
                    needUpdateIdList.remove(preTaskOrder.getId());
                    continue;
                } else {
                    PreTaskOrderDTO preTaskOrderDTO = updatePreTaskOrder(preTaskOrder,
                            appAccountId2Obj.get(preTaskOrder.getAppAccountId()),
                            kolPlatformContentRelationQueryMirror.get(preTaskOrder.getAppAccountId())
                                    .get(preTaskOrder.getPlatformContentId())
                                    .getPrice());
                    preTaskOrderUpdateList.add(preTaskOrderMapper.dto2preTaskOrderDo(preTaskOrderDTO));
                    builder.isModified(true);
                    isHaveModified = true;
                }
            }
            builder.preTaskOrderId(preTaskOrder.getId())
                    .kolAvatarUrl(preTaskOrder.getKolAvatar())
                    .kolName(preTaskOrder.getKolName())
                    .platformContentId(preTaskOrder.getPlatformContentId())
                    .platformContentName(platformContent.getName())
                    .platformTaskId(platformTaskId2Obj.get(platformContent.getPlatformTaskId()).getId())
                    .platformTaskName(platformTaskId2Obj.get(platformContent.getPlatformTaskId()).getName())
                    .price(preTaskOrder.getPrice())
                    .showcaseComponentId(preTaskOrder.getShowcaseComponentId())
                    .switchType(id2ShowcaseComponent.getOrDefault(preTaskOrder.getShowcaseComponentId(), new ShowcaseComponent()).getSwitchType())
                    .comment(preTaskOrder.getComment());
            preTaskOrderVOList.add(builder.build());
        }
        preTaskOrderService.updateBatchById(preTaskOrderUpdateList);
        return PreTaskOrderListVO.builder().isHaveInvalid(isHaveInvalid)
                .isHaveModified(isHaveModified)
                .preTaskOrderList(preTaskOrderVOList).build();
    }

    /**
     * 获取目前有效的橱窗组件列表
     */
    private Map<Long, ShowcaseComponent> getValidShowcaseComponent(List<PreTaskOrder> preTaskOrders) {
        Set<Long> showcaseComponentIds = preTaskOrders.stream().map(PreTaskOrder::getShowcaseComponentId).collect(Collectors.toSet());
        List<ShowcaseComponent> showcaseComponents = showcaseComponentService.listByIds(showcaseComponentIds);
        return showcaseComponents
                .stream()
                .filter(c -> c.getStatus().equals(SHOWCASE_COMPONENT_STATUS_NORMAL)
                        && Objects.nonNull(c.getAuditedContent())
                        && !c.getDeleted())
                .collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));
    }

    @Override
    public Long countUnfinishedAppointment(Long appAccountId, int type) {
        return baseMapper.count(appAccountId, new ArrayList<>(Arrays.asList(
                TASK_ORDER_WAITING_FOR_ACCEPT,
                TASK_ORDER_WAITING_FOR_PAYMENT,
                TASK_ORDER_IN_PROGRESS
        )), type);
    }

    @Override
    public Long count(Long appAccountId, int type) {
        return baseMapper.count(appAccountId, Collections.emptyList(), type);
    }

    @Override
    public List<PlatformTaskVO> getPlatformContentByAppAccountId(Long appAccountId) {
        AppAccount appAccount = appAccountService.getBindById(appAccountId);
        if (Objects.isNull(appAccount) || !appAccount.getInService()) {
            throw new CustomException(ResponseType.KOL_STATUS_EXCEPTION);
        }
        List<PlatformTaskVO> platformTaskVOList = platformContentService.getPlatformContentByAppAccountIdAndPlatformId(
                appAccount.getId(),
                appAccount.getPlatformId()
        );
        List<PlatformTaskVO> result = new ArrayList<>(platformTaskVOList.size());
        for (PlatformTaskVO platformTaskVO : platformTaskVOList) {
            platformTaskVO.setContentTypeList(
                    platformTaskVO.getContentTypeList().stream()
                            .filter(item -> Objects.nonNull(item.getPrice()))
                            .collect(Collectors.toList())
            );
            if (CollectionUtils.isNotEmpty(platformTaskVO.getContentTypeList())) {
                result.add(platformTaskVO);
            }
        }

        return result;
    }

    private final UserDetailService userDetailService;

    @Override
    public PageVO<TaskOrderVO> getOrderPage(TaskOrderQuery queryVO, RoleEnum roleEnum, Long userId) {
        if (Objects.isNull(queryVO.getType()) || TASK_ORDER_TYPE_APPOINTMENT == queryVO.getType()) {
            // will return data about post tasks.
            return this.pageAppointmentOrder(queryVO, roleEnum, userId);
        } else {
            return this.pagePostOrder(queryVO, userId, roleEnum);
        }
    }

    /**
     * 如果是广告主，那么查父任务；
     * 如果是创作者，那么查子任务；
     *
     * @param queryVO
     * @param userId
     * @param roleEnum
     * @return
     */
    private PageVO<TaskOrderVO> pagePostOrder(TaskOrderQuery queryVO, Long userId, RoleEnum roleEnum) {
        // only sponsor and kol can call this method.
        if (roleEnum.equals(RoleEnum.SPONSOR)) {
            return this.pageTaskOrder(queryVO, userId);
        } else if (roleEnum.equals(RoleEnum.KOL)) {
            return this.pageKolTaskOrder(queryVO, userId);
        } else if (roleEnum.equals(RoleEnum.KOL_OPERATOR)) {
            return this.pageTaskOrder(queryVO, null);
        } else {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
    }

    /**
     * 获取创作者的投稿任务管理
     */
    private PageVO<TaskOrderVO> pageKolTaskOrder(TaskOrderQuery queryVO, Long userId) {
        List<Long> showcaseComponentList = null;
        if (Objects.nonNull(queryVO.getPromoteTitle()) && StringUtils.isNotBlank(queryVO.getPromoteTitle())) {
            showcaseComponentList = showcaseComponentService.listIdByPromoteTitleLikely(queryVO.getPromoteTitle());
        }
        if (Objects.nonNull(queryVO.getComponentId())) {
            ShowcaseComponent showcaseComponent = showcaseComponentService.getByComponentId(queryVO.getComponentId());
            if (Objects.isNull(showcaseComponent)) {
                return PageVO.emptyPage(queryVO.getCurrent(), queryVO.getSize());
            }
            showcaseComponentList = Collections.singletonList(showcaseComponent.getId());
        }
        if (Objects.nonNull(queryVO.getShowcaseComponentId())) {
            showcaseComponentList = Collections.singletonList(queryVO.getShowcaseComponentId());
        }
        if (Objects.nonNull(showcaseComponentList) && showcaseComponentList.isEmpty()) {
            return PageVO.emptyPage(queryVO.getCurrent(), queryVO.getSize());
        }
        Page<TaskOrder> taskOrderPage = baseMapper.pagePostTask(
                showcaseComponentList,
                null,
                null,
                queryVO.getName(),
                queryVO.getTaskOrderStatus(),
                queryVO.getBillingType(),
                userId,
                TASK_ORDER_TYPE_SUB_POST,
                -3,
                new Page<>(queryVO.getCurrent(), queryVO.getSize())
        );

        List<TaskOrder> subTaskOrderList = taskOrderPage.getRecords();
        // if empty just return it.
        if (CollectionUtils.isEmpty(subTaskOrderList)) {
            return new PageVO<>(taskOrderPage.getCurrent(), taskOrderPage.getSize(), Collections.emptyList(), taskOrderPage.getTotal());
        }
        List<TaskOrder> parentTaskOrders =
                baseMapper.selectBatchIds(subTaskOrderList.stream().map(TaskOrder::getParentOrderId).collect(Collectors.toList()));
        // this map contains parent orders.
        Map<Long, TaskOrder> id2TaskOrder =
                parentTaskOrders.stream().collect(Collectors.toMap(TaskOrder::getId, Function.identity()));
        Map<Long, ShowcaseComponent> id2ShowcaseComponent =
                showcaseComponentService.listByIds(
                                parentTaskOrders.stream().map(TaskOrder::getShowcaseComponentId).collect(Collectors.toList())
                        )
                        .stream().collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));
        // build data
        List<TaskOrderVO> postTaskVOList = new ArrayList<>(subTaskOrderList.size());
        for (TaskOrder taskOrder : subTaskOrderList) {
            TaskOrder parentTaskOrder = id2TaskOrder.get(taskOrder.getParentOrderId());
            ShowcaseComponent showcaseComponent = id2ShowcaseComponent.get(parentTaskOrder.getShowcaseComponentId());
            ShowcaseComponent auditedContent = showcaseComponent.getAuditedContent();
            auditedContent.setId(showcaseComponent.getId());
            auditedContent.setComponentId(showcaseComponent.getComponentId());
            auditedContent.setAuditedPromoteTitle(showcaseComponent.getAuditedPromoteTitle());
            auditedContent.setStatus(showcaseComponent.getStatus());
            TaskOrderVO taskOrderVO = TaskOrderVO.builder()
                    .orders(taskOrderMapper.do2VoList(Collections.singletonList(taskOrder)))
                    .showcaseComponentId(parentTaskOrder.getShowcaseComponentId())
                    .showcaseComponent(showcaseComponentMapper.do2ListVo(auditedContent))
                    .name(parentTaskOrder.getName())
                    .beginDate(parentTaskOrder.getBeginDate())
                    .endDate(parentTaskOrder.getEndDate())
                    .billingType(parentTaskOrder.getBillingType())
                    .commissionPrice(parentTaskOrder.getCommissionPrice())
                    .settlementInterval(parentTaskOrder.getSettlementInterval())
                    .status(parentTaskOrder.getStatus()).build();
            postTaskVOList.add(taskOrderVO);
        }
        return new PageVO<>(queryVO.getCurrent(), queryVO.getSize(), postTaskVOList, taskOrderPage.getTotal());
    }

    /**
     * 获取 广告主/创作者运营 投稿任务列表
     */
    private PageVO<TaskOrderVO> pageTaskOrder(TaskOrderQuery queryVO, Long userId) {
        List<Long> showcaseComponentList = null;
        if (Objects.nonNull(queryVO.getPromoteTitle()) && StringUtils.isNotBlank(queryVO.getPromoteTitle())) {
            showcaseComponentList = showcaseComponentService.listIdByPromoteTitleLikely(queryVO.getPromoteTitle());
        }
        if (Objects.nonNull(queryVO.getComponentId())) {
            ShowcaseComponent showcaseComponent = showcaseComponentService.getByComponentId(queryVO.getComponentId());
            if (Objects.isNull(showcaseComponent)) {
                return PageVO.emptyPage(queryVO.getCurrent(), queryVO.getSize());
            }
            showcaseComponentList = Collections.singletonList(showcaseComponent.getId());
        }
        if (Objects.nonNull(queryVO.getShowcaseComponentId())) {
            showcaseComponentList = Collections.singletonList(queryVO.getShowcaseComponentId());
        }
        if (Objects.nonNull(showcaseComponentList) && showcaseComponentList.isEmpty()) {
            return PageVO.emptyPage(queryVO.getCurrent(), queryVO.getSize());
        }

        Page<TaskOrder> taskOrderPage = baseMapper.pagePostTask(
                showcaseComponentList,
                Objects.isNull(queryVO.getTaskId()) ? null : Collections.singletonList(queryVO.getTaskId()),
                Objects.isNull(queryVO.getOrderId()) ? null : Collections.singletonList(queryVO.getOrderId()),
                queryVO.getName(),
                queryVO.getTaskOrderStatus(),
                queryVO.getBillingType(),
                userId,
                TASK_ORDER_TYPE_POST,
                -3,
                new Page<>(queryVO.getCurrent(),
                        queryVO.getSize()));
        List<TaskOrderVO> taskOrderVOS = taskOrderMapper.do2VoList(taskOrderPage.getRecords());
        List<ShowcaseComponent> latestAuditedShowcaseComponents = new ArrayList<>(taskOrderVOS.size());
        for (ShowcaseComponent showcaseComponent :
                showcaseComponentService.listByIds(
                        taskOrderVOS.stream().map(TaskOrderVO::getShowcaseComponentId).distinct().collect(Collectors.toList())
                )) {
            if (Objects.nonNull(showcaseComponent.getAuditedContent())) {
                latestAuditedShowcaseComponents.add(showcaseComponent.getAuditedContent());
            }
        }
        Map<Long, ShowcaseComponent> id2showcaseComponent =
                latestAuditedShowcaseComponents.stream().collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));

        List<TaskOrder> subTaskOrders = baseMapper.listByParentIds(taskOrderVOS.stream().map(TaskOrderVO::getId).collect(Collectors.toList()));
        Map<Long, List<TaskOrder>> parentId2TaskOrders =
                subTaskOrders.stream().collect(Collectors.groupingBy(TaskOrder::getParentOrderId));
        Map<Long, Long> parentId2TotalCount = parentTaskOrderId2TotalCount(subTaskOrders, parentId2TaskOrders);
        for (TaskOrderVO taskOrderVO : taskOrderVOS) {
            if (Objects.nonNull(id2showcaseComponent.get(taskOrderVO.getShowcaseComponentId()))) {
                taskOrderVO.setShowcaseComponent(
                        showcaseComponentMapper.do2ListVo(
                                id2showcaseComponent.get(taskOrderVO.getShowcaseComponentId())
                        )
                );
            }
            taskOrderVO.setPickedUpUserCount(Objects.isNull(parentId2TaskOrders.get(taskOrderVO.getId())) ? 0L : parentId2TaskOrders.get(taskOrderVO.getId()).size());
            taskOrderVO.setPublishedPostCount(parentId2TotalCount.getOrDefault(taskOrderVO.getId(), 0L));
        }
        return new PageVO<>(
                taskOrderPage.getCurrent(),
                taskOrderPage.getSize(),
                taskOrderVOS,
                taskOrderPage.getTotal());
    }

    private Map<Long, Long> parentTaskOrderId2TotalCount(List<TaskOrder> subTaskOrders, Map<Long, List<TaskOrder>> parentId2TaskOrders) {
        if (CollectionUtils.isEmpty(subTaskOrders)) {
            return Collections.emptyMap();
        }
        List<String> subTaskOrderIdList = subTaskOrders.stream().map(TaskOrder::getOrderId).distinct().collect(Collectors.toList());
        Map<String, DictPostCountVO> orderId2Count = dictService.countPost(subTaskOrderIdList, DICT_API_SOURCE_POST_TASK)
                .stream().collect(Collectors.toMap(DictPostCountVO::getId, Function.identity()));
        Map<Long, Long> parentId2TotalCount = new HashMap<>(parentId2TaskOrders.size());
        for (Map.Entry<Long, List<TaskOrder>> entry : parentId2TaskOrders.entrySet()) {
            Long total = 0L;
            for (TaskOrder taskOrder : entry.getValue()) {
                total += orderId2Count.get(taskOrder.getOrderId()).getCount();
            }
            parentId2TotalCount.put(entry.getKey(), total);
        }
        return parentId2TotalCount;
    }


    private PageVO<TaskOrderVO> pageAppointmentOrder(TaskOrderQuery queryVO, RoleEnum roleEnum, Long userId) {
        List<String> kolUserIds = getKolUserIds(queryVO);

        if (Objects.nonNull(kolUserIds) && kolUserIds.isEmpty()) {
            return PageVO.emptyPage(queryVO.getCurrent(), queryVO.getSize());
        }

        List<Long> appAccountIds = getAppAccountIds(queryVO, kolUserIds);
        List<Long> platformContentIds = getPlatformContentIds(queryVO.getPlatformTaskId());
        List<Long> appAccountIdsByUserId = getAppAccountIdsByUserId(roleEnum, userId);
        List<String> sponsorUserIds = getSponsorUserIds(queryVO.getUserId(), queryVO.getSponsorName());
        List<Long> showcaseComponentIds = showcaseComponentService.listIdByPromoteTitleLikely(queryVO.getPromoteTitle());

        if ((Objects.nonNull(sponsorUserIds) && sponsorUserIds.isEmpty())
                || (Objects.nonNull(appAccountIds) && appAccountIds.isEmpty())
                || (Objects.nonNull(platformContentIds) && platformContentIds.isEmpty())
                || (Objects.nonNull(appAccountIdsByUserId) && appAccountIdsByUserId.isEmpty())
                || (Objects.nonNull(queryVO.getPromoteTitle()) && showcaseComponentIds.isEmpty())) {
            return PageVO.emptyPage(queryVO.getCurrent(), queryVO.getSize());
        }

        // 获得所有满足条件的父订单id
        Page<TaskOrder> page = pageAppointmentTaskOrder(queryVO,
                roleEnum,
                userId,
                sponsorUserIds,
                appAccountIds,
                platformContentIds,
                appAccountIdsByUserId,
                showcaseComponentIds);
        List<Long> parentIds = page.getRecords().stream().map(TaskOrder::getParentOrderId).collect(Collectors.toList());
        Map<Long, TaskOrder> id2ParentOrder = this.listByIds(parentIds)
                .stream().collect(Collectors.toMap(TaskOrder::getId, Function.identity()));
        if (parentIds.isEmpty()) {
            return PageVO.emptyPage(queryVO.getCurrent(), queryVO.getSize());
        }
        // 查出子订单
        List<TaskOrder> taskOrders = this.lambdaQuery()
                .in(TaskOrder::getParentOrderId, parentIds)
                .in(Objects.nonNull(sponsorUserIds), TaskOrder::getCreator, sponsorUserIds)
                .in(Objects.nonNull(appAccountIds), TaskOrder::getAppAccountId, appAccountIds)
                .in(Objects.nonNull(platformContentIds), TaskOrder::getPlatformContentId, platformContentIds)
                .in(CollectionUtils.isNotEmpty(showcaseComponentIds), TaskOrder::getShowcaseComponentId, showcaseComponentIds)
                .eq(Objects.nonNull(queryVO.getTaskOrderStatus()), TaskOrder::getStatus, queryVO.getTaskOrderStatus())
                .in(Objects.nonNull(queryVO.getPickedUpOnly()) && queryVO.getPickedUpOnly(), TaskOrder::getStatus, Arrays.asList(TASK_ORDER_IN_PROGRESS, TASK_ORDER_WAITING_FOR_PAYMENT))
                .eq(Objects.nonNull(queryVO.getTaskId()), TaskOrder::getId, queryVO.getTaskId())
                .eq(Objects.nonNull(queryVO.getOrderId()), TaskOrder::getOrderId, queryVO.getOrderId())
                .in(RoleEnum.KOL.equals(roleEnum), TaskOrder::getAppAccountId, appAccountIdsByUserId)
                .eq(TaskOrder::getType, TASK_ORDER_TYPE_APPOINTMENT)
                .orderByDesc(TaskOrder::getCreateTime)
                .list();
        // query components
        Map<Long, ShowcaseComponent> id2ShowcaseComponent =
                showcaseComponentService.listByIds(
                                taskOrders.stream()
                                        .map(TaskOrder::getShowcaseComponentId)
                                        .collect(Collectors.toList()))
                        .stream()
                        .collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));
        return new PageVO<>(page.getCurrent(),
                page.getSize(),
                combineData(queryVO, id2ParentOrder, taskOrders, id2ShowcaseComponent).values(),
                page.getTotal());
    }


    @Override
    public Long countShowcaseComponentByTypeAndExcludeStatus(Long showcaseComponentId, Integer type, Integer status) {
        return baseMapper.countShowcaseComponentByTypeAndExcludeStatus(showcaseComponentId, type, status);
    }

    /**
     * 我可投稿分页列表
     */
    @Override
    public PageVO<PostTaskVO> pagePostTask(PostTaskQuery postTaskQuery, Long userId) {
        AppAccount appAccount = appAccountService
                .getBindByPlatformNameAndUserId(YOUDAO_DICT.getName(), userId);
        if (Objects.isNull(appAccount)) {
            return PageVO.emptyPage(postTaskQuery.getCurrent(), postTaskQuery.getSize());
        }
        Long appAccountId = appAccount.getId();

        Set<Long> participatedPostTaskIds = baseMapper.getParticipatedPostOrder(appAccountId, userId);
        Collection<Long> showcaseComponentIds = null;
        if (Objects.nonNull(postTaskQuery.getPromotionType())) {
            showcaseComponentIds = showcaseComponentService.listIdByPromotionType(postTaskQuery.getPromotionType());
        }
        if (Objects.nonNull(postTaskQuery.getPromoteTitle()) && !StringUtils.EMPTY.equals(postTaskQuery.getPromoteTitle())) {
            showcaseComponentIds = showcaseComponentService
                    .listIdByPromoteTitleLikely(postTaskQuery.getPromoteTitle());
        }
        if (Objects.nonNull(showcaseComponentIds) && showcaseComponentIds.isEmpty()) {
            return PageVO.emptyPage(postTaskQuery.getCurrent(), postTaskQuery.getSize());
        } else if (postTaskQuery.isOnlyParticipated() && Objects.nonNull(participatedPostTaskIds) && participatedPostTaskIds.isEmpty()) {
            return PageVO.emptyPage(postTaskQuery.getCurrent(), postTaskQuery.getSize());
        }

        Page<TaskOrder> taskOrderPage = baseMapper.pagePostTaskCanPost(
                postTaskQuery.getId(),
                postTaskQuery.getOrderId(),
                showcaseComponentIds,
                participatedPostTaskIds,
                postTaskQuery.getBillingType(),
                postTaskQuery.getOrder(),
                postTaskQuery.isOnlyParticipated(),
                new Page<>(postTaskQuery.getCurrent(), postTaskQuery.getSize()));

        Map<Long, ShowcaseComponentListVO> id2ShowcaseComponent =
                showcaseComponentMapper
                        .do2ListVo(
                                showcaseComponentService
                                        .listByIds(taskOrderPage.getRecords().stream().map(TaskOrder::getShowcaseComponentId).collect(Collectors.toSet()))
                                        .stream()
                                        .map(ShowcaseComponent::getAuditedContent)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(ShowcaseComponentListVO::getId, Function.identity()));
        List<PostTaskVO> postTaskVOs = taskOrderMapper.do2PostTaskVO(taskOrderPage.getRecords());
        Set<Long> taskOrderIdSet = baseMapper.listPickedUp(appAccountId).stream().map(TaskOrder::getParentOrderId).collect(Collectors.toSet());
        postTaskVOs.forEach(a -> {
            a.setShowcaseComponentListVO(id2ShowcaseComponent.get(a.getShowcaseComponentId()));
            a.setPickedUp(taskOrderIdSet.contains(a.getId()));
        });
        return new PageVO<>(taskOrderPage.getCurrent(), taskOrderPage.getSize(), postTaskVOs, taskOrderPage.getTotal());
    }

    private Map<Long, TaskOrderVO> combineData(TaskOrderQuery queryVO,
                                               Map<Long, TaskOrder> id2ParentOrder,
                                               List<TaskOrder> taskOrders,
                                               Map<Long, ShowcaseComponent> id2ShowcaseComponent) {
        Map<Long, PlatformTask> id2PlatformTask = platformTaskService
                .lambdaQuery()
                .eq(Objects.nonNull(queryVO.getPlatformTaskId()), PlatformTask::getId, queryVO.getPlatformTaskId())
                .list()
                .stream().collect(Collectors.toMap(PlatformTask::getId, Function.identity()));
        Map<Long, PlatformContent> id2PlatformContent = platformContentService
                .list()
                .stream()
                .collect(Collectors.toMap(PlatformContent::getId, Function.identity()));
        Map<Long, AppAccount> id2AppAccount = appAccountService.listByIds(taskOrders.stream().map(TaskOrder::getAppAccountId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(AppAccount::getId, Function.identity()));
        Map<Long, UserDetail> userId2UserDetail = userDetailService.listByUserIdsAndRole(taskOrders.stream().map(TaskOrder::getCreator).collect(Collectors.toList()), roleService.getByRoleKey(RoleEnum.SPONSOR.getRoleKey()))
                .stream().collect(Collectors.toMap(UserDetail::getUserId, Function.identity()));
        Map<Long, Platform> id2Platform = platformService.list().stream().collect(Collectors.toMap(Platform::getId, Function.identity()));

        Map<Long, TaskOrderVO> res = new LinkedHashMap<>();
        for (TaskOrder taskOrder : taskOrders) {
            TaskOrderVO taskOrderVO = getTaskOrderVO(
                    id2PlatformTask,
                    id2PlatformContent,
                    id2AppAccount,
                    id2Platform,
                    taskOrder,
                    id2ShowcaseComponent);
            res.computeIfAbsent(taskOrder.getParentOrderId(),
                            k -> getParentTaskOrderVO(id2ParentOrder, userId2UserDetail, taskOrder, k))
                    .getOrders().add(taskOrderVO);
        }
        setTotalPrice(queryVO, res);
        return res;
    }

    /**
     * 分页获取指派任务
     */
    private Page<TaskOrder> pageAppointmentTaskOrder(TaskOrderQuery queryVO,
                                                     RoleEnum roleEnum,
                                                     Long userId,
                                                     List<String> sponsorUserIds,
                                                     List<Long> appAccountIds,
                                                     List<Long> platformContentIds,
                                                     List<Long> appAccountIdsByUserId,
                                                     List<Long> showcaseComponentIds) {
        return this.lambdaQuery()
                .select(TaskOrder::getParentOrderId)
                .in(Objects.nonNull(sponsorUserIds), TaskOrder::getCreator, sponsorUserIds)
                .in(CollectionUtils.isNotEmpty(appAccountIds), TaskOrder::getAppAccountId, appAccountIds)
                .in(CollectionUtils.isNotEmpty(platformContentIds), TaskOrder::getPlatformContentId, platformContentIds)
                .eq(Objects.nonNull(queryVO.getTaskOrderStatus()), TaskOrder::getStatus, queryVO.getTaskOrderStatus())
                .in(Objects.nonNull(queryVO.getPickedUpOnly()) && queryVO.getPickedUpOnly(), TaskOrder::getStatus, Arrays.asList(TASK_ORDER_IN_PROGRESS, TASK_ORDER_WAITING_FOR_PAYMENT))
                .eq(Objects.nonNull(queryVO.getTaskId()), TaskOrder::getId, queryVO.getTaskId())
                .eq(Objects.nonNull(queryVO.getOrderId()), TaskOrder::getOrderId, queryVO.getTaskId())
                .eq(RoleEnum.SPONSOR.equals(roleEnum), TaskOrder::getCreator, userId)
                .in(Objects.nonNull(appAccountIdsByUserId), TaskOrder::getAppAccountId, appAccountIdsByUserId)
                .in(CollectionUtils.isNotEmpty(showcaseComponentIds), TaskOrder::getShowcaseComponentId, showcaseComponentIds)
                .eq(TaskOrder::getType, TASK_ORDER_TYPE_APPOINTMENT)
                .isNotNull(TaskOrder::getParentOrderId)
                .orderByDesc(TaskOrder::getCreateTime)
                .groupBy(TaskOrder::getParentOrderId)
                .page(new Page<>(queryVO.getCurrent(), queryVO.getSize()));
    }

    private List<String> getSponsorUserIds(String userId, String sponsorName) {
        List<String> sponsorUserIds = null;
        if (Objects.nonNull(userId)) {
            sponsorUserIds = new ArrayList<>();
            sponsorUserIds.add(userId);
        }
        if (Objects.nonNull(sponsorName)) {
            sponsorUserIds = userDetailService.listUserIdByNickName(sponsorName).stream().map(Object::toString).collect(Collectors.toList());
        }
        return sponsorUserIds;
    }

    private List<Long> getAppAccountIdsByUserId(RoleEnum roleEnum, Long userId) {
        List<Long> appAccountIdsByUserId = null;
        if (RoleEnum.KOL.equals(roleEnum)) {
            appAccountIdsByUserId = appAccountService.getIdByUserId(userId);
        } else if (RoleEnum.KOL_OPERATOR.equals(roleEnum)) {
            appAccountIdsByUserId = appAccountService.getIdByKolOperator(userId);
        }
        return appAccountIdsByUserId;
    }

    private List<Long> getPlatformContentIds(Long platformTaskId) {
        if (Objects.nonNull(platformTaskId)) {
            return platformContentService.getByPlatformTaskId(platformTaskId)
                    .stream().map(PlatformContent::getId).collect(Collectors.toList());
        }
        return null;
    }

    private List<Long> getAppAccountIds(TaskOrderQuery queryVO, List<String> kolUserIds) {
        List<Long> appAccountIds = null;
        if (Objects.nonNull(queryVO.getKolName()) || Objects.nonNull(queryVO.getAppAccountId())) {
            appAccountIds = appAccountService.lambdaQuery()
                    .like(Objects.nonNull(queryVO.getKolName()), AppAccount::getName, queryVO.getKolName())
                    .eq(Objects.nonNull(queryVO.getAppAccountId()), AppAccount::getId, queryVO.getAppAccountId())
                    .in(Objects.nonNull(kolUserIds), AppAccount::getUserId, kolUserIds)
                    .list()
                    .stream().map(AppAccount::getId).collect(Collectors.toList());
        }
        return appAccountIds;
    }

    private List<String> getKolUserIds(TaskOrderQuery queryVO) {
        List<String> kolUserIds = null;
        if (Objects.nonNull(queryVO.getKolNickName())) {
            kolUserIds = userDetailService.listUserIdByNickName(queryVO.getKolNickName()).stream().map(Object::toString).collect(Collectors.toList());
        }
        if (Objects.nonNull(queryVO.getKolUserId())) {
            kolUserIds = new ArrayList<>();
            kolUserIds.add(queryVO.getKolUserId());
        }
        return kolUserIds;
    }

    private void setTotalPrice(TaskOrderQuery queryVO, Map<Long, TaskOrderVO> res) {
        if (Objects.nonNull(queryVO.getTaskOrderStatus()) && Objects.equals(queryVO.getTaskOrderStatus(), TASK_ORDER_CANCELED)) {
            res.values()
                    .forEach(parentOrder -> parentOrder
                            .setPrice(parentOrder
                                    .getOrders()
                                    .stream().mapToLong(TaskOrderVO::getPrice).sum()));
        } else {
            res.values()
                    .forEach(parentOrder -> parentOrder
                            .setPrice(parentOrder
                                    .getOrders()
                                    .stream()
                                    .filter(a -> a.getStatus() != TASK_ORDER_CANCELED)
                                    .mapToLong(TaskOrderVO::getPrice).sum()));
        }
    }

    private TaskOrderVO getParentTaskOrderVO(Map<Long, TaskOrder> id2ParentOrder, Map<Long, UserDetail> userId2UserDetail, TaskOrder taskOrder, Long k) {
        TaskOrderVO parentTaskOrderVO = new TaskOrderVO();
        UserDetail userDetail = userId2UserDetail.get(taskOrder.getCreator());
        parentTaskOrderVO.setTaskOrderUserDetailVO(
                new TaskOrderUserDetailVO(userDetail.getUserId(),
                        userDetail.getAvatar(),
                        userDetail.getNickname()));
        TaskOrder parentTaskOrder = id2ParentOrder.get(k);
        parentTaskOrderVO.setId(parentTaskOrder.getId());
        parentTaskOrderVO.setOrders(new ArrayList<>());
        return parentTaskOrderVO;
    }

    private TaskOrderVO getTaskOrderVO(Map<Long, PlatformTask> id2PlatformTask,
                                       Map<Long, PlatformContent> id2PlatformContent,
                                       Map<Long, AppAccount> id2AppAccount,
                                       Map<Long, Platform> id2Platform,
                                       TaskOrder taskOrder,
                                       Map<Long, ShowcaseComponent> id2ShowcaseComponent) {
        TaskOrderVO taskOrderVO = new TaskOrderVO();
        taskOrderVO.setParentOrderId(taskOrder.getParentOrderId());
        taskOrderVO.setId(taskOrder.getId());
        taskOrderVO.setOrderId(taskOrder.getOrderId());
        taskOrderVO.setComment(taskOrder.getComment());
        taskOrderVO.setPrice(taskOrder.getPrice());
        taskOrderVO.setStatus(taskOrder.getStatus());
        taskOrderVO.setCreateTime(taskOrder.getCreateTime());
        taskOrderVO.setLastModTime(taskOrder.getLastModTime());

        PlatformContent platformContent = id2PlatformContent.get(taskOrder.getPlatformContentId());
        taskOrderVO.setPlatformContent(platformContent.getName());
        PlatformTask platformTask = id2PlatformTask.get(platformContent.getPlatformTaskId());
        taskOrderVO.setTaskType(platformTask.getName());

        AppAccount appAccount = id2AppAccount.get(taskOrder.getAppAccountId());
        taskOrderVO.setName(appAccount.getName());
        taskOrderVO.setAvatar(appAccount.getAvatar());

        Platform platform = id2Platform.get(platformTask.getPlatformId());
        taskOrderVO.setPlatformId(platform.getId());
        taskOrderVO.setPlatformName(platform.getName());
        taskOrderVO.setIcon(platform.getIcon());

        ShowcaseComponent showcaseComponent = id2ShowcaseComponent.get(taskOrder.getShowcaseComponentId());
        if (Objects.nonNull(showcaseComponent)) {
            taskOrderVO.setShowcaseComponentId(showcaseComponent.getId());
            taskOrderVO.setShowcaseComponent(showcaseComponentMapper.do2ListVo(showcaseComponent));
        }

        return taskOrderVO;
    }

    private void makeOrderPreTaskOrderCheck(List<PreTaskOrder> preTaskOrders) {
        List<Long> appAccountIds = preTaskOrders.stream()
                .map(PreTaskOrder::getAppAccountId)
                .distinct()
                .collect(Collectors.toList());
        List<AppAccount> appAccounts =
                appAccountService.getBindAndInServiceByIds(appAccountIds);
        if (appAccountIds.size() != appAccounts.size()) {
            throw new CustomException(ResponseType.KOL_STATUS_EXCEPTION);
        }
        Set<TaskOrderInitQueryVO> taskOrderInitQueryVoS = new HashSet<>(preTaskOrders.size());
        for (PreTaskOrder preTaskOrder : preTaskOrders) {
            taskOrderInitQueryVoS.add(
                    TaskOrderInitQueryVO.builder()
                            .appAccountId(preTaskOrder.getAppAccountId())
                            .platformContentId(preTaskOrder.getPlatformContentId())
                            .build()
            );
        }
        Map<Long, Map<Long, KolPlatformContentRelation>> kolPlatformContentRelationQueryMirror =
                getKolPlatformContentRelationQueryMirror(taskOrderInitQueryVoS);

        if (preTaskOrders.size() != taskOrderInitQueryVoS.size()) {
            throw new CustomException(ResponseType.PRICE_WRONG);
        }
        Map<Long, ShowcaseComponent> id2ShowcaseComponent = getValidShowcaseComponent(preTaskOrders);
        if (preTaskOrders
                .stream()
                .map(PreTaskOrder::getShowcaseComponentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet())
                .size() != id2ShowcaseComponent.size()) {
            throw new CustomException(ResponseType.SHOWCASE_COMPONENT_INVALID);
        }
        List<Long> indexOfDetailModifiedOrInvalid = listIndexOfDetailModifiedOrInvalid(
                preTaskOrders,
                appAccounts.stream().collect(Collectors.toMap(AppAccount::getId, Function.identity())),
                kolPlatformContentRelationQueryMirror,
                id2ShowcaseComponent);

        if (CollectionUtils.isNotEmpty(indexOfDetailModifiedOrInvalid)) {
            throw new CustomException(ResponseType.PRICE_WRONG);
        }
    }

    /**
     * 购物车的达人昵称、头像、价格是否被修改
     */
    private boolean isDetailModified(PreTaskOrder preTaskOrder, AppAccount appAccount, Long price) {
        return ObjectUtils.notEqual(preTaskOrder.getKolName(), appAccount.getName()) ||
                ObjectUtils.notEqual(preTaskOrder.getKolAvatar(), appAccount.getAvatar()) ||
                ObjectUtils.notEqual(preTaskOrder.getPrice(), price);
    }

    private PreTaskOrderDTO updatePreTaskOrder(PreTaskOrder preTaskOrder, AppAccount appAccount, Long price) {
        PreTaskOrderDTO preTaskOrderDTO = new PreTaskOrderDTO();
        if (ObjectUtils.notEqual(preTaskOrder.getKolName(), appAccount.getName())) {
            preTaskOrder.setKolName(appAccount.getName());
            preTaskOrderDTO.setKolName(appAccount.getName());
        }
        if (ObjectUtils.notEqual(preTaskOrder.getKolAvatar(), appAccount.getAvatar())) {
            preTaskOrder.setKolAvatar(appAccount.getAvatar());
            preTaskOrderDTO.setKolAvatar(appAccount.getAvatar());
        }
        if (ObjectUtils.notEqual(preTaskOrder.getPrice(), price)) {
            preTaskOrder.setPrice(price);
            preTaskOrderDTO.setPrice(price);
        }
        if (ObjectUtils.anyNotNull(
                preTaskOrderDTO.getPrice(),
                preTaskOrderDTO.getKolAvatar(),
                preTaskOrderDTO.getKolName())) {
            preTaskOrderDTO.setId(preTaskOrder.getId());
        }
        return preTaskOrderDTO;
    }


    /**
     * @return AppAccountId -> PlatformContentId -> KolPlatformContentRelation
     */
    private Map<Long, Map<Long, KolPlatformContentRelation>> getKolPlatformContentRelationQueryMirror(Collection<TaskOrderInitQueryVO> taskOrderInitQueryVO) {
        List<KolPlatformContentRelation> kolPlatformContentRelationList =
                kolPlatformContentRelationService.getByTaskOrderInitQueryVO(taskOrderInitQueryVO);
        Map<Long, List<KolPlatformContentRelation>> appAccountId2KolPlatformContentRelation =
                kolPlatformContentRelationList.stream()
                        .collect(Collectors.groupingBy(KolPlatformContentRelation::getAppAccountId));
        Map<Long, Map<Long, KolPlatformContentRelation>> appAccountId2PlatformContentId2KolPlatformContentRelation =
                new HashMap<>(kolPlatformContentRelationList.size());
        for (Long appAccountId : appAccountId2KolPlatformContentRelation.keySet()) {
            appAccountId2PlatformContentId2KolPlatformContentRelation.put(
                    appAccountId,
                    appAccountId2KolPlatformContentRelation.get(appAccountId)
                            .stream().
                            collect(Collectors.toMap(
                                    KolPlatformContentRelation::getPlatformContentId,
                                    Function.identity())
                            ));
        }
        return appAccountId2PlatformContentId2KolPlatformContentRelation;
    }

    private Long insertMainOrder(Long totalPrice) {
        TaskOrder mainOrder = TaskOrder.builder().price(totalPrice).build();
        save(mainOrder);
        return mainOrder.getId();
    }

    private void insertSubOrder(Long parentOrderId, List<PreTaskOrder> preTaskOrders) {
        List<TaskOrder> subOrderList = new ArrayList<>(preTaskOrders.size());
        for (PreTaskOrder preTaskOrder : preTaskOrders) {
            TaskOrder taskOrder = taskOrderMapper.preTaskOrder2DO(preTaskOrder, parentOrderId, TASK_ORDER_WAITING_FOR_ACCEPT);
            taskOrder.setOrderId(RandomStringUtils.random(10, true, false));
            subOrderList.add(
                    taskOrder
            );
        }
        saveBatch(subOrderList);
    }


    /**
     * @return 被修改或无效的 preTaskOrder Id
     */
    private List<Long> listIndexOfDetailModifiedOrInvalid(List<PreTaskOrder> preTaskOrders,
                                                          Map<Long, AppAccount> appAccountId2Obj,
                                                          Map<Long, Map<Long, KolPlatformContentRelation>> kolPlatformContentRelationQueryMirror,
                                                          Map<Long, ShowcaseComponent> id2ShowcaseComponent) {
        List<Long> idNeedUpdate = new ArrayList<>(preTaskOrders.size());
        for (PreTaskOrder preTaskOrder : preTaskOrders) {
            AppAccount appAccount = appAccountId2Obj.get(preTaskOrder.getAppAccountId());
            //can't find app account that in binding and in service.
            if (Objects.isNull(appAccount)
                    || isDetailModified(preTaskOrder,
                    appAccountId2Obj.get(preTaskOrder.getAppAccountId()),
                    kolPlatformContentRelationQueryMirror.get(preTaskOrder.getAppAccountId())
                            .get(preTaskOrder.getPlatformContentId())
                            .getPrice())
                    || showcaseInvalid(id2ShowcaseComponent, preTaskOrder)) {
                idNeedUpdate.add(preTaskOrder.getId());
            }
        }
        return idNeedUpdate;
    }

    /**
     * 橱窗组件是否无效
     *
     * @return true-无效
     */
    private boolean showcaseInvalid(Map<Long, ShowcaseComponent> id2ShowcaseComponent, PreTaskOrder preTaskOrder) {
        return Objects.nonNull(preTaskOrder.getShowcaseComponentId()) && Objects.isNull(id2ShowcaseComponent.get(preTaskOrder.getShowcaseComponentId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String contribute(Long id, Long userId) {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(YOUDAO_DICT.getName(), userId);
        return contribute(id, appAccount);
    }

    private String contribute(Long id, AppAccount appAccount) {
        TaskOrder subOrder = baseMapper.getSubOrder(id, appAccount.getId());
        if (Objects.nonNull(subOrder)) {
            return subOrder.getOrderId();
        }
        baseMapper.lock(id);
        TaskOrder taskOrder = baseMapper.selectById(id);
        if (taskOrder.getPostLimit() != 0) {
            if (taskOrder.getPostRemain() == 0) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "任务投稿剩余数为0，不可再投");
            } else {
                taskOrder.setPostRemain(taskOrder.getPostRemain() - 1);
            }
        }
        subOrder = TaskOrder.builder()
                .parentOrderId(id)
                .status(TASK_ORDER_IN_PROGRESS)
                .appAccountId(appAccount.getId())
                .type(TASK_ORDER_TYPE_SUB_POST)
                .orderId(RandomStringUtils.random(10, true, false))
                .build();
        this.save(subOrder);
        this.updateById(taskOrder);
        return subOrder.getOrderId();
    }

    @Override
    public String ldapContribute(Long id, String uid) {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndAppUserId(YOUDAO_DICT.getName(), uid);
        if (Objects.isNull(appAccount)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "该账户未在优选注册，无法投稿");
        }
        Role kolRole = roleService.getByRoleKey(RoleEnum.KOL.getRoleKey());
        loginService.login(appAccount.getUserId(), RoleEnum.KOL.getRoleKey());
        UserDetail userDetail = userDetailService.getByUserId(appAccount.getUserId(), kolRole);
        // 运营给用户挂帖子，如果没有投稿权限，直接开通
        if (Objects.isNull(userDetail) || !userDetail.getPostTaskPermission()) {
            userDetailService.openPostTaskPermission(appAccount.getUserId(), kolRole);
        }
        return contribute(id, appAccount);
    }

    @Override
    public TaskOrder getByOrderId(String orderId) {
        return baseMapper.getByOrderId(orderId);
    }

    @Override
    public List<Long> createPostTaskOrder(PostTaskCreateVO postTaskCreateVO) {
        this.createPostTaskOrderPreCheck(postTaskCreateVO);
        AtomicInteger atomicInteger = new AtomicInteger(0);
        List<TaskOrder> taskOrders = new ArrayList<>(postTaskCreateVO.getShowcaseComponentId().size());
        for (Long showcaseComponentId : postTaskCreateVO.getShowcaseComponentId()) {
            TaskOrder taskOrder = taskOrderMapper.postTaskCreateVo2Do(postTaskCreateVO);
            taskOrder.setShowcaseComponentId(showcaseComponentId);
            taskOrder.setOrderId(RandomStringUtils.random(10, true, false));
            if (postTaskCreateVO.getShowcaseComponentId().size() > 1) {
                taskOrder.setName(taskOrder.getName() + "_" + atomicInteger.incrementAndGet());
            }
            if (0 != taskOrder.getPostLimit()) {
                taskOrder.setPostRemain(taskOrder.getPostLimit());
            }
            if (taskOrder.getBeginDate().toLocalDate().equals(LocalDate.now())) {
                taskOrder.setStatus(TASK_ORDER_IN_PROGRESS);
            } else {
                taskOrder.setStatus(TASK_ORDER_READY);
            }
            taskOrder.setType(TASK_ORDER_TYPE_POST);
            taskOrders.add(taskOrder);
        }
        this.saveBatch(taskOrders);
        return taskOrders.stream().map(TaskOrder::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePostTaskOrder(Long id, PostTaskUpdateVO postTaskUpdateVO, Long userId) {
        baseMapper.lock(id);
        TaskOrder oldTaskOrder = ((TaskOrderService) AopContext.currentProxy()).getById(id);
        if (isTaskLiving(Collections.singletonList(oldTaskOrder.getId()))) {
            throw new CustomException(ResponseType.SHOWCASE_OR_TASK_LIVING);
        }
        updatePostTaskOrderPreCheck(oldTaskOrder, postTaskUpdateVO, userId);
        TaskOrder taskOrder4Update = taskOrderMapper.postTaskUpdateVo2Do(postTaskUpdateVO);
        taskOrder4Update.setPostRemain(
                // post limit == 0 means unlimited
                taskOrder4Update.getPostLimit() == 0 ? null : taskOrder4Update.getPostLimit() - baseMapper.countByParentIdAndType(id, TASK_ORDER_TYPE_SUB_POST)
        );
        if (Objects.isNull(taskOrder4Update.getPostRemain())) {
            baseMapper.updatePostRemainAllowNull(id, taskOrder4Update.getPostRemain());
        }
        if (postTaskUpdateVO.getBeginDate().toLocalDate().equals(LocalDate.now())) {
            taskOrder4Update.setStatus(TASK_ORDER_IN_PROGRESS);
        }
        taskOrder4Update.setId(id);
        return updateById(taskOrder4Update);
    }

    /**
     * 检查任务是否在直播中
     */
    private Boolean isTaskLiving(List<Long> taskOrderIds) {
        // 给词典检查的应该都是子任务
        List<Good> goodList = new ArrayList<>();
        List<Long> parentIds = new ArrayList<>();
        listByIds(taskOrderIds).forEach(item -> {
            if (Objects.nonNull(item.getParentOrderId())) {
                goodList.add(
                        Good.builder()
                                .goodId(item.getOrderId())
                                .source(item.getType() == TASK_ORDER_TYPE_APPOINTMENT ? DICT_API_SOURCE_APPOINTMENT_TASK : DICT_API_SOURCE_POST_TASK)
                                .build()
                );
            } else {
                parentIds.add(item.getId());
            }
        });
        listByParentIds(parentIds).forEach(item -> {
            goodList.add(
                    Good.builder()
                            .goodId(item.getOrderId())
                            .source(item.getType() == TASK_ORDER_TYPE_APPOINTMENT ? DICT_API_SOURCE_APPOINTMENT_TASK : DICT_API_SOURCE_POST_TASK)
                            .build()
            );
        });
        return dictService.isShowcaseLiving(goodList);
    }

    @Override
    public PostTaskOrderDetailVO getDetailById(Long id, Long userId) {
        TaskOrder taskOrder = ((TaskOrderService) AopContext.currentProxy()).getById(id);
        if (Objects.isNull(taskOrder) || !taskOrder.getCreator().equals(userId)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        PostTaskOrderDetailVO postTaskOrderDetailVO = taskOrderMapper.do2PostTaskDetailVo(taskOrder);
        Long count = baseMapper.countByParentIdAndType(id, TASK_ORDER_TYPE_SUB_POST);
        postTaskOrderDetailVO.setPickedUpUserCount(Objects.isNull(count) ? 0 : count);
        return postTaskOrderDetailVO;
    }

    @Override
    public Boolean updatePostTaskStatus(Long id, Integer status, Boolean confirmUpdate, Long userId) {
        TaskOrder taskOrder = ((TaskOrderService) AopContext.currentProxy()).getById(id);
        updateStatusPreCheck(status, confirmUpdate, taskOrder, userId);
        if (TASK_ORDER_TAKE_DOWN == status && isTaskLiving(Collections.singletonList(taskOrder.getId()))) {
            throw new CustomException(ResponseType.SHOWCASE_OR_TASK_LIVING);
        }
        if (TASK_ORDER_TYPE_POST == taskOrder.getType() && status == TASK_ORDER_IN_PROGRESS) {
            if (taskOrder.getBeginDate().isBefore(LocalDateTime.now())) {
                return baseMapper.updateStatusById(id, TASK_ORDER_IN_PROGRESS);
            } else {
                return baseMapper.updateStatusById(id, TASK_ORDER_READY);
            }
        }
        return baseMapper.updateStatusById(id, status);
    }


    @Override
    public List<TaskOrder> listValidPostTaskOrders() {
        return baseMapper.listByTypeAndStatus(TASK_ORDER_TYPE_POST, Arrays.asList(
                TASK_ORDER_READY,
                TASK_ORDER_IN_PROGRESS,
                TASK_ORDER_TAKE_DOWN
        ));
    }

    @Override
    public List<TaskOrder> listByParentIds(List<Long> parentIds) {
        return baseMapper.listByParentIds(parentIds);
    }

    @Override
    public PageVO<DictPostVO> getRelatedDictPost(Long id, Long current, Long size) {
        TaskOrder taskOrder = ((TaskOrderService) AopContext.currentProxy()).getById(id);
        if (Objects.isNull(taskOrder)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        return dictService.getPostDetail(
                taskOrder.getOrderId(),
                TASK_ORDER_TYPE_SUB_POST == taskOrder.getType() ? DICT_API_SOURCE_POST_TASK : DICT_API_SOURCE_APPOINTMENT_TASK,
                current,
                size);
    }

    private void updateStatusPreCheck(Integer status, Boolean confirmUpdate, TaskOrder taskOrder, Long userId) {
        if (Objects.isNull(taskOrder) || !taskOrder.getCreator().equals(userId)) {
            throw new CustomException(ResponseType.ACCESS_DENIED);
        }
        if (TASK_ORDER_END == taskOrder.getStatus()) {
            throw new CustomException(ResponseType.CAN_NOT_UPDATE_POST_TASK_ORDER);
        }
        if (TASK_ORDER_TAKE_DOWN == status && !confirmUpdate) {
            if (TASK_ORDER_TYPE_POST == taskOrder.getType()
                    && baseMapper.countByParentIdAndType(taskOrder.getId(), TASK_ORDER_TYPE_SUB_POST) > 0) {
                throw new CustomException(ResponseType.POST_TASK_ORDER_HAVE_PUBLISHED_POST);
            }
            if (TASK_ORDER_TYPE_SUB_POST == taskOrder.getType()
                    && dictService.countPost(
                            Collections.singletonList(taskOrder.getOrderId()),
                            DICT_API_SOURCE_POST_TASK)
                    .stream()
                    .collect(Collectors.toMap(DictPostCountVO::getId, DictPostCountVO::getCount)).get(taskOrder.getOrderId()) > 0) {
                throw new CustomException(ResponseType.POST_TASK_ORDER_HAVE_PUBLISHED_POST);
            }
        }
    }

    private void updatePostTaskOrderPreCheck(TaskOrder taskOrder, PostTaskUpdateVO postTaskUpdateVO, Long userId) {
        if (Objects.isNull(taskOrder)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "任务不存在");
        }
        if (this.isPostTaskNameExist(postTaskUpdateVO.getName(), taskOrder.getId(), userId)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "名字重复");
        }
        if (!userId.equals(taskOrder.getCreator())) {
            throw new CustomException(ResponseType.ACCESS_DENIED);
        }
        if (TASK_ORDER_END == taskOrder.getStatus() &&
                !(taskOrder.getBeginDate().isEqual(postTaskUpdateVO.getBeginDate())
                        && taskOrder.getEndDate().isEqual(postTaskUpdateVO.getEndDate()))) {
            throw new CustomException(ResponseType.CAN_NOT_UPDATE_POST_TASK_ORDER);
        }
        if (taskOrder.getBeginDate().isBefore(LocalDateTime.now())
                && !postTaskUpdateVO.getBeginDate().toLocalDate().equals(taskOrder.getBeginDate().toLocalDate())) {
            throw new CustomException(ResponseType.CAN_NOT_UPDATE_POST_TASK_ORDER, "任务已经开启，不支持修改开始时间...");
        }
        if (postTaskUpdateVO.getPostLimit() != 0) {
            if (baseMapper.countByParentIdAndType(taskOrder.getId(), TASK_ORDER_TYPE_SUB_POST)
                    >
                    postTaskUpdateVO.getPostLimit()) {
                throw new CustomException(ResponseType.LIMIT_HAVE_TO_BIGGER_THAN_PICKED_UP_COUNT);
            }
        }
        if (TASK_ORDER_END == taskOrder.getStatus() && Objects.nonNull(postTaskUpdateVO.getAssessmentDescription())) {
            throw new CustomException(ResponseType.CAN_NOT_UPDATE_POST_TASK_ORDER);
        }
    }

    private void createPostTaskOrderPreCheck(PostTaskCreateVO postTaskCreateVO) {
        if (CollectionUtils.isEmpty(postTaskCreateVO.getShowcaseComponentId())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "橱窗必填");
        }
        String nameTemplate = postTaskCreateVO.getName();
        // when list's size bigger than 1, it means this request will generate more than one task order
        // we need to check the name that finally insert into db
        if (postTaskCreateVO.getShowcaseComponentId().size() > 1) {
            nameTemplate += "_1";
        }
        if (this.isPostTaskNameExist(nameTemplate, null, SecurityUtil.getUserId())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "名字重复");
        }
        if (postTaskCreateVO.getShowcaseComponentId().size() > 20) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "选择的组件数量>20");
        }
        if (postTaskCreateVO.getBeginDate().toLocalDate().isBefore(LocalDate.now()) || postTaskCreateVO.getEndDate().isBefore(postTaskCreateVO.getBeginDate())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
    }

    /**
     * 检查名字是否重复
     *
     * @param id 传值代表忽略这条记录
     */
    private Boolean isPostTaskNameExist(String name, Long id, Long userId) {
        return baseMapper.countTaskOrderExcludeId(name, id, userId) > 0;
    }

    @Override
    public List<LdapPostTaskVO> listPostTask(String name) throws Exception {
        List<LdapPostTaskVO> postTaskVOS = taskOrderMapper.do2LdapPostTaskVO(baseMapper.listPostTask(name, objectMapper.readValue(configService.queryConfig(DICT_FETCH_TASK_SPONSOR_LIST), longListTypeReference)));
        Set<Long> componentIds = postTaskVOS.stream().map(LdapPostTaskVO::getShowcaseComponentId).collect(Collectors.toSet());
        Map<Long, ShowcaseComponent> id2ShowcaseComponent = showcaseComponentService.listByIds(componentIds).stream().collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));
        postTaskVOS.forEach(postTaskVO ->
        {
            postTaskVO.setRecommendText(id2ShowcaseComponent.get(postTaskVO.getShowcaseComponentId()).getLeadText());
            postTaskVO.setShowcaseComponentName(id2ShowcaseComponent.get(postTaskVO.getShowcaseComponentId()).getName());
        });
        return postTaskVOS;
    }

    @Override
    public Boolean isNameRepeat(String name, Long userId) {
        return this.isPostTaskNameExist(name, null, userId);
    }

    @Override
    public List<TaskOrder> listByShowcaseComponentId(Long showcaseComponentId) {
        return baseMapper.listByShowcaseComponentId(showcaseComponentId);
    }

    @Override
    public List<TaskOrder> listByOrderIds(Set<String> taskOrderIds) {
        if (CollectionUtils.isEmpty(taskOrderIds)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(baseMapper)
                .in(TaskOrder::getOrderId, taskOrderIds)
                .list();
    }

    @Override
    public Boolean isShowcaseRelatedTaskLiving(Long showcaseComponentId) {
        //如果是指派任务查出来的已经是子订单了，如果是投稿任务还需要查出来其对应的子订单
        List<Good> goodList = new ArrayList<>();
        List<TaskOrder> taskOrders = this.listByShowcaseComponentId(showcaseComponentId);
        List<Long> parentPostOrderIdList = new ArrayList<>(taskOrders.size());
        taskOrders.forEach(item -> {
            if (TASK_ORDER_TYPE_APPOINTMENT == item.getType()) {
                goodList.add(
                        Good.builder().goodId(item.getOrderId()).source(DICT_API_SOURCE_APPOINTMENT_TASK).build()
                );
            } else if (TASK_ORDER_TYPE_POST == item.getType()) {
                parentPostOrderIdList.add(item.getId());
            }
        });
        this.listByParentIds(parentPostOrderIdList).forEach(item -> {
            goodList.add(
                    Good.builder().goodId(item.getOrderId()).source(DICT_API_SOURCE_POST_TASK).build()
            );
        });
        return dictService.isShowcaseLiving(goodList);
    }

    @Override
    public Boolean updateOrderStatus(Long id, Integer status) {
        TaskOrder taskOrder = ((TaskOrderService) AopContext.currentProxy()).getById(id);
        if (Objects.isNull(taskOrder)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        if (isTaskLiving(Collections.singletonList(taskOrder.getId()))) {
            throw new CustomException(ResponseType.SHOWCASE_OR_TASK_LIVING);
        }
        return lambdaUpdate()
                .set(TaskOrder::getStatus, status)
                .eq(TaskOrder::getId, id)
                .update();
    }

    @Override
    @Cacheable(value = CacheNameConstant.TASK_ORDER_ID_CACHE_NAME, key = "#id", condition = "#id != null")
    public TaskOrder getById(Serializable id) {
        return super.getById(id);
    }

    @Lazy
    private final List<AutoShowcaseHandler> autoShowcaseHandlers;

    @Override
    public List<ShowcaseComponentDetail2DictVO> fillPostTaskByUid(List<AutoShowcasePostVO> postVOS) {
        List<AutoShowcasePostBO> autoShowcasePostBOS = postVOS.stream().map(AutoShowcasePostBO::new).collect(Collectors.toList());
        autoShowcaseHandlers.forEach(autoShowcaseHandler -> autoShowcaseHandler.handler(autoShowcasePostBOS));

        return autoShowcasePostBOS
                .stream()
                .filter(AutoShowcasePostBO::isFilled)
                .map(autoShowcasePostBO -> {
                    ShowcaseComponentDetail2DictVO vo = showcaseComponentService
                            .getShowcaseComponentDetail2DictVO(
                                    DICT_API_SOURCE_AUTO_SHOWCASE_POST_TASK,
                                    null,
                                    autoShowcasePostBO.getAutoShowcaseInfo().getTaskOrder(),
                                    autoShowcasePostBO.getAutoShowcaseInfo().getShowcaseComponent()
                            );
                    vo.setPostId(autoShowcasePostBO.getPostVO().getPostId());
                    return vo;
                })
                .collect(Collectors.toList());
    }


    @Override
    public void updateAutoShowcase(Long id, Integer weight, Boolean autoShowcase) {
        new LambdaUpdateChainWrapper<>(baseMapper)
                .set(TaskOrder::getWeight, weight)
                .set(TaskOrder::getAutoShowcase, autoShowcase)
                .eq(TaskOrder::getId, id)
                .update();
    }

    @Override
    @Cacheable(value = CacheNameConstant.AUTO_SHOWCASE_TASK_ORDER_ID_CACHE_NAME)
    public List<AutoShowcaseInfo> listAutoShowcaseTaskOrder() {
        List<TaskOrder> taskOrders = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(TaskOrder::getAutoShowcase, true)
                .eq(TaskOrder::getStatus, TASK_ORDER_IN_PROGRESS)
                .eq(TaskOrder::getType, TASK_ORDER_TYPE_POST)
                .list();
        Set<Long> showcaseComponentIds = taskOrders
                .stream()
                .map(TaskOrder::getShowcaseComponentId)
                .collect(Collectors.toSet());
        Map<Long, ShowcaseComponent> id2ShowcaseComponent = showcaseComponentService
                .listByIds(showcaseComponentIds)
                .stream()
                .filter(showcaseComponent -> !showcaseComponent.getStatus().equals(SHOWCASE_COMPONENT_STATUS_TAKE_DOWN))
                .collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));
        ArrayList<AutoShowcaseInfo> res = new ArrayList<>();
        taskOrders.forEach(taskOrder -> {
            ShowcaseComponent showcaseComponent = id2ShowcaseComponent.get(taskOrder.getShowcaseComponentId());
            if (Objects.nonNull(showcaseComponent)) {
                res.add(new AutoShowcaseInfo(taskOrder, showcaseComponent));
            }
        });
        return res;
    }

    @Override
    public List<TaskOrder> listByAppAccountId(Long id) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(TaskOrder::getAppAccountId, id)
                .in(TaskOrder::getStatus, TASK_ORDER_IN_PROGRESS, TASK_ORDER_WAITING_FOR_PAYMENT)
                .list();
    }

    @Override
    public List<TaskOrder> listInProcessPostTask() {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(TaskOrder::getStatus, TASK_ORDER_IN_PROGRESS)
                .eq(TaskOrder::getType, TASK_ORDER_TYPE_POST)
                .list();
    }

    @Override
    @CachePut(value = CacheNameConstant.AUTO_SHOWCASE_TASK_ORDER_ID_CACHE_NAME)
    public List<AutoShowcaseInfo> updateAutoShowcaseTaskOrder() {
        return listAutoShowcaseTaskOrder();
    }
}

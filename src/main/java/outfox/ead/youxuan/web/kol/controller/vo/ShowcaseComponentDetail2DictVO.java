package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Li
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShowcaseComponentDetail2DictVO {
    @ApiModelProperty("帖子id")
    private String postId;

    @ApiModelProperty("商品id")
    private String goodId;

    @ApiModelProperty("0个人商品/橱窗组件 1指派任务 2投稿任务")
    private Integer source;

    @ApiModelProperty("组件ID")
    private Long id;

    @ApiModelProperty("组件ID（10位字母）")
    private String componentId;

    @ApiModelProperty("子任务ID（10位字母）")
    private String subTaskId;

    @ApiModelProperty("推广样式ID")
    private Long schemaId;

    @ApiModelProperty("组件名称")
    private String name;

    @ApiModelProperty("推广类型；1-品牌宣传，2-商品售卖，3-线索留资，4-应用推广")
    private Integer promotionType;

    @ApiModelProperty("推广品类")
    private Integer category;

    @ApiModelProperty("跳转类型；1-落地页，2-应用直达，3-微信小程序，4-应用商店")
    private Integer switchType;

    @ApiModelProperty("deeplink链接")
    private String deepLink;

    @ApiModelProperty("落地页链接")
    private String landingPageUrl;

    @ApiModelProperty("备用落地页链接")
    private String backupLandingPageUrl;

    @ApiModelProperty("微信小程序原始ID")
    private String microProgramId;

    @ApiModelProperty("微信小程序目标页面路径")
    private String microProgramPath;

    @ApiModelProperty("应用平台；0-不限，1-Android，2-iOS")
    private Integer appPlatform;

    @ApiModelProperty("安卓应用包名")
    private String androidPackageName;

    @ApiModelProperty("iOS应用ID")
    private String iosAppId;

    @ApiModelProperty("引导文案")
    private String leadText;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("商品价格")
    private Long itemPrice;

    @ApiModelProperty("划线价")
    private Long strikeThroughPrice;

    @ApiModelProperty("按钮文案")
    private String buttonText;

    @ApiModelProperty("推广图片的URL")
    private String promoteImageUrl;

    @ApiModelProperty("推广标题")
    private String promoteTitle;

    @ApiModelProperty("推广文案")
    private String promoteText;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty("是否标记为广告")
    private Boolean markAsAd;

    @ApiModelProperty("词典iOS小程序ID")
    private String dictMpIosId;

    @ApiModelProperty("词典安卓小程序ID")
    private String dictMpAndroidId;

    @ApiModelProperty("计费类型；见TaskOrderBillingTypeEnum")
    private String billingType;
}

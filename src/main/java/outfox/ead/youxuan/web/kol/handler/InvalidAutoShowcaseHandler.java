package outfox.ead.youxuan.web.kol.handler;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import outfox.ead.annotation.CentralDogmaJsonValue;
import outfox.ead.youxuan.constants.PostTypeEnum;
import outfox.ead.youxuan.web.kol.controller.bo.AutoShowcasePostBO;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 过滤不需要挂窗的帖子
 *
 * <AUTHOR>
 * @date 2022年12月27日 11:39
 */
@Component
@Order(0)
public class InvalidAutoShowcaseHandler implements AutoShowcaseHandler {
    @CentralDogmaJsonValue(project = "${centralDogma.project}", repo = "repo", name = "/authorizedUids.json")
    private Set<String> authorizedUids;
    Set<Integer> invalidPostType = Stream.of(PostTypeEnum.POST.getCode(), PostTypeEnum.VIDEO.getCode()).collect(Collectors.toSet());

    @Override
    public void handler(List<AutoShowcasePostBO> posts) {
        invalidPostType(posts);
        filterUids(posts);
        filterHasGoods(posts);
        filterMarkAsAd(posts);
    }

    private void filterUids(List<AutoShowcasePostBO> posts) {
        posts.removeIf(post -> !authorizedUids.contains(post.getPostVO().getUid()));
    }

    private void invalidPostType(List<AutoShowcasePostBO> posts) {
        posts.removeIf(post -> !invalidPostType.contains(post.getPostVO().getPostType()));
    }

    private void filterHasGoods(List<AutoShowcasePostBO> posts) {
        posts.removeIf(post -> post.getPostVO().hasGoods);
    }

    private void filterMarkAsAd(List<AutoShowcasePostBO> posts) {
        posts.removeIf(post -> post.getPostVO().markAsAd);
    }
}

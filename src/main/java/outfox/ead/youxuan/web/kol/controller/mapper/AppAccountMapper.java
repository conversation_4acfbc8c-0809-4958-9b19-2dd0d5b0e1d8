package outfox.ead.youxuan.web.kol.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.AppAccount;
import outfox.ead.youxuan.entity.Platform;
import outfox.ead.youxuan.web.ad.controller.bo.BrandKolTemplate;
import outfox.ead.youxuan.web.ad.controller.bo.KolTemplate;
import outfox.ead.youxuan.web.ad.controller.bo.SponsorTemplate;
import outfox.ead.youxuan.web.kol.controller.vo.AppAccountVO;
import outfox.ead.youxuan.web.kol.controller.vo.BindAppAccountVO;
import outfox.ead.youxuan.web.kol.controller.vo.ContentTagListVO;
import outfox.ead.youxuan.web.kol.controller.vo.KolDetailVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年02月09日 4:56 下午
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface AppAccountMapper {
    AppAccountVO account2AccountVO(AppAccount appAccount);

    AppAccount accountVO2Account(AppAccountVO appAccountVO);

    List<AppAccountVO> listAppAccount2VO(List<AppAccount> appAccounts);

    AppAccount sponsorTemplate2Do(SponsorTemplate sponsorTemplate);

    @Mapping(target = "kolOperatorUserId",expression = "java(username2Id.get(brandKolTemplate.getKolOperatorUsername()))")
    AppAccount brandKolTemplate2Do(BrandKolTemplate brandKolTemplate, Map<String, Long> username2Id);

    @Mapping(target = "name", source = "kolTemplate.appAccountName")
    @Mapping(target = "kolOperatorUserId",expression = "java(username2Id.get(kolTemplate.getKolOperatorUsername()))")
    AppAccount kolTemplate2Do(KolTemplate kolTemplate, Map<String, Long> username2Id);

    AppAccount bindAccountVO2Account(BindAppAccountVO appAccountVO);

    @Mapping(source = "appAccount.id", target = "appAccountId")
    @Mapping(source = "area", target = "area")
    @Mapping(target = "nickname", expression = "java(userId2Nickname.get(appAccount.getUserId()))")
    @Mapping(target = "platform", expression = "java(id2Platform.get(appAccount.getPlatformId()).getName())")
    @Mapping(target = "role", expression = "java(id2RoleName.get(userId2RoleId.get(appAccount.getUserId())))")
    @Mapping(target = "contentTags", expression = "java(appAccountId2ContentTagList.get(appAccount.getId()))")
    KolDetailVO do2KolDetailVO(AppAccount appAccount,
                               Map<Long, String> userId2Nickname,
                               Map<Long, Platform> id2Platform,
                               Map<Long, String> id2RoleName,
                               Map<Long, List<ContentTagListVO>> appAccountId2ContentTagList,
                               Map<Long, Long> userId2RoleId,
                               String area);
}

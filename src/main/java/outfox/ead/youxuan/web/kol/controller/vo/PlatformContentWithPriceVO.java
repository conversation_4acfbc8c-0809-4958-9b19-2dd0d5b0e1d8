package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlatformContentWithPriceVO {

    private Long id;

    private Long platformTaskId;

    @ApiModelProperty("内容类型")
    private String name;

    @ApiModelProperty("服务报价；单位为分")
    private Long price;
}

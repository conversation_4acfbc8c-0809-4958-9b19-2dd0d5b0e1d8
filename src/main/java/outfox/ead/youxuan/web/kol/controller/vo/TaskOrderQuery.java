package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022年03月01日 6:15 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskOrderQuery {

    @ApiModelProperty("广告主优选昵称")
    private String sponsorName;

    @ApiModelProperty("广告主优选id")
    private String userId;

    @ApiModelProperty("模糊搜索投稿任务标题关键词")
    private String promoteTitle;

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("达人昵称")
    private String kolName;

    @ApiModelProperty("达人优选昵称")
    private String kolNickName;

    @ApiModelProperty("达人优选id")
    private String kolUserId;

    @ApiModelProperty("达人id")
    private String appAccountId;

    @ApiModelProperty("子任务ID")
    private Long taskId;

    @ApiModelProperty("子任务字符串ID")
    private String orderId;

    @ApiModelProperty("平台任务类型")
    private Long platformTaskId;

    @ApiModelProperty("任务状态")
    private Integer taskOrderStatus;

    @ApiModelProperty("任务类型;1-指派任务，2-投稿任务")
    private Integer type;

    @ApiModelProperty("仅查看已参与过的任务")
    private Boolean pickedUpOnly;

    @ApiModelProperty("关联组件ID")
    private Long showcaseComponentId;

    @ApiModelProperty("字符串组件ID")
    private String componentId;

    @ApiModelProperty("结算类型")
    private Integer billingType;

    @NotNull
    private Long current;

    @NotNull
    private Long size;
}

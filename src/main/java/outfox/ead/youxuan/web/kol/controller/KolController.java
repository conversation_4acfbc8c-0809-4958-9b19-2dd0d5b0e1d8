package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.kol.controller.vo.KolCriteriaQueryVO;
import outfox.ead.youxuan.web.kol.controller.vo.KolDetailQueryVO;
import outfox.ead.youxuan.web.kol.controller.vo.KolDetailVO;
import outfox.ead.youxuan.web.kol.controller.vo.KolQueryListVO;
import outfox.ead.youxuan.web.kol.service.AppAccountService;

import javax.validation.Valid;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2022年02月22日 7:34 下午
 */
@RestController
@BaseResponse
@Valid
@RequiredArgsConstructor
@RequestMapping("/kol")
@Api(tags = "达人接口")
public class KolController {
    private final AppAccountService appAccountService;

    @GetMapping
    public PageVO<KolDetailVO> kolDetail(@Valid KolDetailQueryVO kolDetailQueryVO) {
        return appAccountService.pageKolDetail(kolDetailQueryVO);
    }

    @GetMapping("criteria")
    @ApiOperation("获取达人广场达人列表")
    public PageVO<KolQueryListVO> kolCriteriaPageQuery(KolCriteriaQueryVO kolCriteriaQueryVO) {
        return appAccountService.kolCriteriaPageQuery(
                kolCriteriaQueryVO,
                kolCriteriaQueryVO.getSize(),
                kolCriteriaQueryVO.getCurrent()
        );
    }

    @PutMapping
    @AccessControl(roles = RoleEnum.KOL_OPERATOR)
    public void saveOrUpdateMcn(@Size(max = 20,message = "mcn不可超过20个字") String mcn, Long id) {
        if (StringUtils.isBlank(mcn)) {
            return;
        }
        appAccountService.saveOrUpdateMcn(mcn, id);
    }
}

package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022年06月09日 11:57
 */
@Data
public class PostTaskQuery {
    @ApiModelProperty(value = "数据库主键")
    private Long id;
    @ApiModelProperty(value = "十位字符串id")
    private String orderId;
    @ApiModelProperty(value = " 推广类型；1-品牌宣传，2-商品售卖，3-线索留资，4-应用推广")
    private Integer promotionType;
    @ApiModelProperty(value = " 计费类型；0-其他，1-CPM，2-CPC，3-CPA，4-CPS")
    private Integer billingType;
    @ApiModelProperty("仅查看自己参与过的任务")
    private boolean onlyParticipated;
    @ApiModelProperty("推广标题")
    private String promoteTitle;
    @NotNull
    private Long current;
    @NotNull
    private Long size;
    @ApiModelProperty("排序 正数正排负数倒排 1-佣金单价 2-投稿截止时间 3-发布时间 4-投稿剩余量")
    private Integer order;
}

package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.annotation.LDAP;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.AppAccount;
import outfox.ead.youxuan.util.AESUtil;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.kol.controller.mapper.AppAccountMapper;
import outfox.ead.youxuan.web.kol.controller.vo.AppAccountCriteriaQueryVO;
import outfox.ead.youxuan.web.kol.controller.vo.AppAccountInServiceUpdateVO;
import outfox.ead.youxuan.web.kol.controller.vo.AppAccountVO;
import outfox.ead.youxuan.web.kol.controller.vo.BindAppAccountVO;
import outfox.ead.youxuan.web.kol.service.AppAccountService;
import outfox.ead.youxuan.web.kol.service.DictService;

import javax.validation.Valid;
import java.util.Objects;
import java.util.Optional;

import static outfox.ead.youxuan.constants.Constants.BIND;
import static outfox.ead.youxuan.constants.PlatformEnum.YOUDAO_DICT;

/**
 * <AUTHOR>
 * @date 2022年02月09日 4:45 下午
 */
@RestController
@RequiredArgsConstructor
@Validated
@BaseResponse
@Slf4j
@Api(tags = "媒体账户接口")
@RequestMapping("/appAccount")
@AccessControl(roles = {RoleEnum.KOL, RoleEnum.BRAND_KOL})
public class AppAccountController {
    private final AppAccountService appAccountService;
    private final DictService dictService;
    private final AppAccountMapper appAccountMapper;
    @Value("${dict.login.key}")
    private String key;

    /**
     * 用于绑定媒体或修改媒体信息
     */
    @ApiOperation("绑定媒体账户")
    @PostMapping("bind")
    public void bind(@Valid BindAppAccountVO appAccountVO, @RequestHeader(value = HttpHeaders.COOKIE) String cookie) {
        Long userId = SecurityUtil.getUserId();
        AppAccount appAccount;
        if (appAccountVO.getId() != null) {
            appAccount = Optional.ofNullable(appAccountService.getByAppUserIdAndPlatformIdAndUserId(appAccountVO.getAppUserId(),
                    appAccountVO.getPlatformId(), userId)).orElse(new AppAccount());
        } else {
            appAccount = appAccountMapper.bindAccountVO2Account(appAccountVO);
        }
        appAccount.setStatus(BIND);
        if (appAccount.getPlatformId().equals(1L)) {
            if (appAccountVO.getId() != null) {
                AppAccount oldAppAccount = appAccountService.getById(appAccountVO.getId());
                if (Objects.isNull(oldAppAccount) || !oldAppAccount.getAppUserId().equals(appAccount.getAppUserId())) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "账号信息错误");
                }
            }
            if (!Objects.equals(SecurityUtil.getLoginUser().getDictUid(), appAccount.getAppUserId())) {
                if (!Objects.equals(appAccount.getAppUserId(), dictService.validAndGetUid(cookie))) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS,"词典账号Cookie不属于当前用户");
                }
            }
        }
        appAccountService.bind(appAccount, userId, false, SecurityUtil.getCurrentRole());
    }

    @ApiOperation("通过词典登陆账户快速绑定词典媒体账户")
    @PostMapping("bindCurrentDictUid")
    public void bindCurrentDictUid() {
        String dictUid = SecurityUtil.getLoginUser().getDictUid();
        AppAccount appAccount = new AppAccount();
        appAccount.setAppUserId(dictUid);
        appAccount.setPlatformId(1L);
        appAccountService.bind(appAccount, SecurityUtil.getUserId(), false, SecurityUtil.getCurrentRole());
    }

    @ApiOperation("解除绑定媒体账户")
    @PostMapping("unbind")
    public void unbind(@RequestParam Long id) {
        appAccountService.unbind(id, SecurityUtil.getCurrentRole());
    }


    @ApiOperation("分页获取绑定的媒体账户列表")
    @GetMapping("page")
    public PageVO<AppAccountVO> getBoundAppAccount(AppAccountCriteriaQueryVO appAccountCriteriaQueryVO) {
        return appAccountService.pageByStatusAndUserId(
                appAccountCriteriaQueryVO.getStatus(),
                SecurityUtil.getUserId(),
                appAccountCriteriaQueryVO.getCurrent(),
                appAccountCriteriaQueryVO.getSize(),
                appAccountCriteriaQueryVO.getSortByPlatform(),
                SecurityUtil.getCurrentRole());
    }

    @AccessControl(roles = RoleEnum.KOL)
    @ApiOperation("个人创作者-服务管理-改变接单状态")
    @PostMapping("/switch")
    public int updateInServiceStatus(AppAccountInServiceUpdateVO appAccountInServiceUpdateVO) {
        return appAccountService.updateInServiceStatus(
                SecurityUtil.getUserId(),
                appAccountInServiceUpdateVO.getAppAccountId(),
                appAccountInServiceUpdateVO.isInService()
        );
    }

    @GetMapping("isBindDict")
    @ApiOperation("用户是否绑定了词典")
    public Boolean isBindDict() {
        return appAccountService.isBindDict(SecurityUtil.getUserId());
    }

    /**
     * 用于词典运营后台校验用户是否注册了优选
     *
     * @return true - 已注册
     */
    @ApiOperation("词典账号是否绑定了优选")
    @GetMapping("/ldap/isBindYouxuan")
    @LDAP
    public Boolean ldapIsBindYouxuan(String uid) {
        return Objects.nonNull(appAccountService.getBindByPlatformNameAndAppUserId(YOUDAO_DICT.getName(), uid));
    }

    /**
     * 词典后端校验用户是否注册了优选
     *
     * @param param AES加密的uid
     * @return true - 已注册
     */
    @ApiOperation("词典账号是否绑定了优选")
    @GetMapping("/isBindYouxuan")
    @AccessControl(exclude = true)
    public Boolean isBindYouxuan(String param) {
        try {
            return Objects.nonNull(appAccountService.getBindByPlatformNameAndAppUserId(YOUDAO_DICT.getName(), AESUtil.getDecryptResultNoResultStr(key, param)));
        } catch (Exception e) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "参数错误");
        }
    }
}

package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.FutureOrPresent;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class PostTaskUpdateVO {
    @NotNull
    @Length(min = 1, max = 100)
    @ApiModelProperty("任务名称")
    private String name;
    @NotNull
    @ApiModelProperty("橱窗组件ID")
    private Long showcaseComponentId;
    @NotNull
    @FutureOrPresent
    @ApiModelProperty("推广有效期-开始日期")
    private LocalDateTime beginDate;
    @NotNull
    @FutureOrPresent
    @ApiModelProperty("推广有效期-结束日期")
    private LocalDateTime endDate;
    @NotNull
    @Range(max = 99999999)
    @ApiModelProperty("参与人数上限，没上限传0")
    private Long postLimit;
    @ApiModelProperty("考核说明")
    private String assessmentDescription;
}

package outfox.ead.youxuan.web.kol.controller.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022年07月20日 19:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Good {
    @NotBlank
    @ApiModelProperty("商品id")
    private String goodId;
    @NotNull
    @ApiModelProperty("0个人商品/橱窗组件 1指派任务 2投稿任务")
    private Integer source;
}

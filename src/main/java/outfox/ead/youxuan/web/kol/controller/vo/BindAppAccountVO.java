package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022年02月26日 11:40 上午
 */
@Data
public class BindAppAccountVO {
    private Long id;

    @ApiModelProperty("媒体平台id")
    private Long platformId;

    @ApiModelProperty("媒体账户昵称")
    private String name;

    @ApiModelProperty("媒体账户id")
    @NotNull
    private String appUserId;

    @ApiModelProperty("媒体账户头像")
    private String avatar;

    @ApiModelProperty("性别，0-男 1-女")
    private Integer gender;

    @ApiModelProperty("粉丝数")
    private Long fansNum;

    @ApiModelProperty("状态 0-正常，1-暂停，2-删除")
    private Integer status;

    @ApiModelProperty("地区")
    private Integer area;
}

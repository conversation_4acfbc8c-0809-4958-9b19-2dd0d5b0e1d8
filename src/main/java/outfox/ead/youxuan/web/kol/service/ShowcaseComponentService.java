package outfox.ead.youxuan.web.kol.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.ShowcaseComponent;
import outfox.ead.youxuan.entity.TaskOrder;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.YouxuanService;
import outfox.ead.youxuan.web.kol.controller.bo.ShowcaseComponentDetail2DictBO;
import outfox.ead.youxuan.web.kol.controller.vo.*;

import java.net.URISyntaxException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【ShowcaseComponent(橱窗组件)】的数据库操作Service
* @createDate 2022-05-25 19:47:02
*/
public interface ShowcaseComponentService extends YouxuanService<ShowcaseComponent> {

    /**
     * 审核组件
     * <ol>
     *     <li>只能审核未通过/正在审核中的数据</li>
     * </ol>
     *
     * @param id          -
     * @param auditStatus 审核状态
     * @param userId
     */
    void audit(String id, Integer auditStatus, Long userId);

    void syncComponentFully2BrandKolIfNeed(Long sourceUserId, Long targetUserid);

    /**
     * 修改橱窗组件的状态
     * 这里特指的是上架与下架的操作。
     *
     * @param id
     * @param status
     * @param userId
     * @return
     */
    Boolean updateStatus(Long id, Integer status, Boolean confirmUpdate, Long userId);

    /**
     * 新建一个橱窗组件
     *
     * @param showcaseComponentCreateVO
     * @param currentRole
     * @return
     */
    Long create(ShowcaseComponentCreateVO showcaseComponentCreateVO, Long userId, Role currentRole) throws URISyntaxException;

    /**
     * 组件审核分页列表
     */
    Page<ShowcaseComponentAuditListVO> pageAudit(AuditPageQuery auditPageQuery);

    /**
     * 修改橱窗组件的内容
     *
     * @param showcaseComponentModifyVO
     * @param userId
     * @param currentRole
     * @return
     * @throws URISyntaxException
     */
    Integer modify(ShowcaseComponentModifyVO showcaseComponentModifyVO, Long userId, Role currentRole);

    /**
     * 逻辑删除橱窗组件
     * @param id
     * @param userId
     * @return
     */
    Boolean delete(Long id, Long userId);

    /**
     *
     * @param id
     * @return
     */
    ShowcaseComponentDetailVO detail(Long id);

    ShowcaseComponentDetailVO latestPassedDetail(Long id);

    /**
     * 当橱窗下架或者任务下架的时候将不再进行下发
     *
     * @param goodId source==0时为橱窗ID source==1/2时为任务子ID
     * @param source 0-个人商品；1-指派任务；2-投稿任务
     */
    ShowcaseComponentDetail2DictVO latestPassedDetail4Dict(String goodId, Integer source);

    PageVO<ShowcaseComponentListVO> pageByCriteria(ShowcaseComponentCriteriaQueryVO showcaseComponentCriteriaQueryVO, Long userId, Role currentRole);

    ShowcaseComponentDetail2DictVO getShowcaseComponentDetail2DictVO(Integer source, TaskOrder subTaskOrder, TaskOrder parentTaskOrder, ShowcaseComponent showcaseComponent);

    ShowcaseComponentDetail2DictBO getShowcaseComponentDetail2DictBo(Integer source, TaskOrder subTaskOrder, TaskOrder parentTaskOrder, ShowcaseComponent showcaseComponent);

    Boolean isNameRepeat(String name, Long userId, Role currentRole);

    Boolean isNotifySchemaUpgrade(Long userId);

    Boolean disableNotifySchemaUpgrade(Long userId);

    /**
     * 通过推广标题模糊的查询橱窗组件ID
     */
    List<Long> listIdByPromoteTitleLikely(String promoteTitle);

    Set<Long> listIdByPromotionType(Integer promotionType);

    Integer getSyncStatus(Long kolUserId, Role currentRole);

    Boolean updateSyncStatus(Long userId, Boolean isSync);

    Long count(Long appAccountId);

    PageVO<DictPostVO> getRelatedDictPost(Long id, Long current, Long size);

    PageVO<ShowcaseComponentListVO> pagePassed(ShowcaseComponentCriteriaQueryVO showcaseComponentCriteriaQueryVO, Long userId, Role currentRole) throws Exception;

    ShowcaseComponent getByComponentId(String componentId);

    Collection<ShowcaseComponent> listByComponentIds(Set<String> collect);

    ShowcaseComponentDetail2DictVO latestPassedDetail4Dict(String goodId,
                                                           Integer source,
                                                           Map<Long, TaskOrder> id2SubTaskOrder,
                                                           Map<String, TaskOrder> orderId2SubTaskOrder,
                                                           Map<Long, TaskOrder> id2ParentTaskOrder,
                                                           Map<Long, ShowcaseComponent> id2ShowcaseComponent,
                                                           Map<String, ShowcaseComponent> componentId2ShowcaseComponent);

    boolean exists(Long skuId);

    List<ShowcaseComponent> listJdItem();

    void batchAudit(List<String> ids, int status, Long userId);

    /**
     * 获取用户所有的状态为2且审核状态为2的橱窗组件
     *
     * @param userId -
     * @return -
     */
    List<ShowcaseComponent> listValid(Long userId);

    /**
     * 根据roleId获取用户所有的有效橱窗组件
     *
     * @param roleId -
     * @return -
     */
    List<ShowcaseComponent> listInProcessByRoleId(Long roleId);
}

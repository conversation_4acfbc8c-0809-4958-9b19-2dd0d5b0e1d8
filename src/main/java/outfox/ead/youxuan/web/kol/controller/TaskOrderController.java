package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.CacheNameConstant;
import outfox.ead.youxuan.constants.MetricsEnum;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.*;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.kol.controller.mapper.TaskOrderMapper;
import outfox.ead.youxuan.web.kol.controller.vo.*;
import outfox.ead.youxuan.web.kol.service.TaskOrderService;

import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@BaseResponse
@AllArgsConstructor
@RequestMapping("/task_order")
@Api(tags = "服务管理；推广管理；达人广场")
@Validated
public class TaskOrderController {

    private final TaskOrderService taskOrderService;

    private final TaskOrderMapper taskOrderMapper;

    @PostMapping("add_candidate")
    @ApiOperation("将达人加入已选达人")
    @AccessControl(roles = {RoleEnum.SPONSOR})
    public Long addCandidateKol(@RequestBody CreatePreTaskOrderVO createPreTaskOrderVO) {
        return taskOrderService.addKol2PreTaskOrder(createPreTaskOrderVO, SecurityUtil.getUserId());
    }

    @GetMapping("candidate")
    @ApiOperation("选择服务，获取当前KOL能提供的所有推广任务&内容")
    @AccessControl(roles = {RoleEnum.SPONSOR})
    public List<PlatformTaskVO> initCandidate(Long appAccountId) {
        return taskOrderService.getPlatformContentByAppAccountId(appAccountId);
    }

    @GetMapping("task_type")
    @ApiOperation("获取已选择的任务类型")
    @AccessControl(roles = {RoleEnum.SPONSOR})
    public List<PlatformTaskVO> getSelectedTaskTypeList() {
        return taskOrderService.getSelectedTaskTypeList(SecurityUtil.getUserId());
    }

    @GetMapping("list_candidate")
    @ApiOperation("获取当前广告主已选达人列表")
    @AccessControl(roles = {RoleEnum.SPONSOR})
    public PreTaskOrderListVO getCandidateKolPage(Long platformTaskId) {
        return taskOrderService.getPreOrderList(platformTaskId, SecurityUtil.getUserId());
    }

    @GetMapping("count")
    @ApiOperation("获取当前广告主已选达人数量")
    @AccessControl(roles = {RoleEnum.SPONSOR})
    public Long getCandidateKolCount() {
        return taskOrderService.getCandidateKolCount(SecurityUtil.getUserId());
    }

    @PostMapping("del_kol_candidate")
    @ApiOperation("删除候选达人")
    @AccessControl(roles = {RoleEnum.SPONSOR})
    public boolean deleteKolCandidate(@RequestBody DelCandidateKolVO delCandidateKolVO) {
        return taskOrderService.deleteCandidateKol(delCandidateKolVO.getPreTaskOrderId(), SecurityUtil.getUserId());
    }

    @PostMapping
    @ApiOperation("下单")
    @AccessControl(roles = {RoleEnum.SPONSOR})
    public int makeOrder(@RequestBody TaskOrderCreateVO taskOrderCreateVO) {
        return taskOrderService.makeOrder(taskOrderCreateVO, SecurityUtil.getUserId());
    }

    @GetMapping("/task/sponsor")
    @ApiOperation("获取广告主的任务管理")
    @AccessControl(roles = {RoleEnum.SPONSOR})
    public PageVO<TaskOrderVO> getSponsorTaskOrderPage(TaskOrderQuery taskOrderQueryVO) {
        return taskOrderService.getOrderPage(taskOrderMapper.sponsorQuery(taskOrderQueryVO), RoleEnum.SPONSOR, SecurityUtil.getUserId());
    }

    @PostMapping("/post")
    @ApiOperation("广告主新建投稿任务")
    @AccessControl(roles = RoleEnum.SPONSOR)
    public List<Long> sponsorCreatePostTask(@RequestBody PostTaskCreateVO postTaskCreateVO) {
        return taskOrderService.createPostTaskOrder(postTaskCreateVO);
    }

    @PutMapping("/post/{id}")
    @ApiOperation("广告主修改投稿任务")
    @AccessControl(roles = RoleEnum.SPONSOR)
    @CacheEvict(cacheNames = CacheNameConstant.TASK_ORDER_ID_CACHE_NAME, key = "#id")
    public Boolean sponsorUpdatePostTask(@PathVariable Long id, @RequestBody PostTaskUpdateVO postTaskUpdateVO) {
        return taskOrderService.updatePostTaskOrder(id, postTaskUpdateVO, SecurityUtil.getUserId());
    }

    @GetMapping("/repeat")
    @ApiOperation("检查是否重名")
    @AccessControl(roles = RoleEnum.SPONSOR)
    public Boolean isNameExist(String name) {
        return taskOrderService.isNameRepeat(name, SecurityUtil.getUserId());
    }

    @GetMapping("/post/{id}")
    @ApiOperation("通过ID获取其数据")
    public PostTaskOrderDetailVO getDetailById(@PathVariable Long id) {
        return taskOrderService.getDetailById(id, SecurityUtil.getUserId());
    }

    @PutMapping("/post/status")
    @ApiOperation("改变任务状态;特指上架下架")
    @CacheEvict(cacheNames = CacheNameConstant.TASK_ORDER_ID_CACHE_NAME, key = "#id")
    public Boolean updatePostTaskOrderStatus(Long id, Integer status, Boolean confirmUpdate) {
        return taskOrderService.updatePostTaskStatus(id, status, confirmUpdate, SecurityUtil.getUserId());
    }

    @GetMapping("/task/kol")
    @ApiOperation("获取达人的我的任务")
    @AccessControl(roles = {RoleEnum.KOL})
    public PageVO<TaskOrderVO> getKolTaskOrderPage(TaskOrderQuery taskOrderQueryVO) {
        return taskOrderService.getOrderPage(taskOrderMapper.kolQuery(taskOrderQueryVO), RoleEnum.KOL, SecurityUtil.getUserId());
    }

    @GetMapping("/kol/post_task")
    @ApiOperation("个人创作者-我可投稿分页列表")
    @AccessControl(roles = {RoleEnum.KOL})
    public PageVO<PostTaskVO> pageKolPostTask(@Valid PostTaskQuery postTaskQuery) {
        return taskOrderService.pagePostTask(postTaskQuery, SecurityUtil.getUserId());
    }

    @ApiOperation("词典投稿任务列表-ldap")
    @GetMapping("/ldap/kol/post_task")
    @LDAP
    public List<LdapPostTaskVO> ldapListKolPostTask(String name) throws Exception {
        return taskOrderService.listPostTask(name);
    }

    @PostMapping("/kol/contribute")
    @ApiOperation("个人创作者-立即投稿")
    @AccessControl(roles = {RoleEnum.KOL})
    public String contribute(@ApiParam("任务id") @RequestParam Long id) {
        return taskOrderService.contribute(id, SecurityUtil.getUserId());
    }

    /**
     * call by KOL
     */
    @LDAP
    @ApiOperation("立即投稿-ldap")
    @PostMapping("/ldap/kol/contribute")
    public String ldapContribute(@ApiParam("任务id") @RequestParam(name = "id") Long id, @ApiParam("uid") @RequestParam(name = "uid") String uid) {
        return taskOrderService.ldapContribute(id, uid);
    }


    @GetMapping("/task/op")
    @ApiOperation("获取创作者运营的推广管理")
    @AccessControl(roles = RoleEnum.KOL_OPERATOR)
    public PageVO<TaskOrderVO> getOpTaskOrderPage(TaskOrderQuery taskOrderQueryVO) {
        return taskOrderService.getOrderPage(taskOrderQueryVO, RoleEnum.KOL_OPERATOR, SecurityUtil.getUserId());
    }

    @PostMapping("/task/update")
    @ApiOperation("更新子任务订单的状态")
    @AccessControl(roles = RoleEnum.KOL_OPERATOR)
    @CacheEvict(cacheNames = CacheNameConstant.TASK_ORDER_ID_CACHE_NAME, key = "#id")
    public void updateTaskOrderStatus(@ApiParam(value = "主键", type = "Long", name = "id")
                                      @RequestParam Long id,
                                      @ApiParam(value = "状态", type = "Integer", name = "status")
                                      @RequestParam Integer status) {
        taskOrderService.updateOrderStatus(id, status);
    }

    @GetMapping("/post/related")
    @ApiOperation("获取关联的动态")
    public PageVO<DictPostVO> getRelatedDictPost(Long id,
                                                 @ApiParam("来源：0-个人商品；1-投稿任务；2-指派任务") Integer source,
                                                 @ApiParam("页码；注意：从0开始") Long current,
                                                 Long size) {
        return taskOrderService.getRelatedDictPost(id, current, size);
    }

    @PostMapping("/post/intranet/showcaseComponents")
    @YoudaoIntranet
    @Report(metrics = MetricsEnum.timer)
    @ApiOperation("根据帖子uid填充投稿任务橱窗组件")
    public List<ShowcaseComponentDetail2DictVO> fillPostTaskByUid(@RequestBody List<AutoShowcasePostVO> postVOS) {
        return taskOrderService.fillPostTaskByUid(postVOS);
    }

    @PutMapping("/post/weight")
    @ApiOperation("修改投稿任务权重")
    @CacheEvict(cacheNames = CacheNameConstant.TASK_ORDER_ID_CACHE_NAME, key = "#id")
    public void autoShowcase(
            @ApiParam("任务id") @RequestParam Long id,
            @ApiParam("权重") @RequestParam(required = false) Integer weight,
            @ApiParam("自动挂窗") @RequestParam Boolean autoShowcase) {
        taskOrderService.updateAutoShowcase(id, weight, autoShowcase);
    }
}

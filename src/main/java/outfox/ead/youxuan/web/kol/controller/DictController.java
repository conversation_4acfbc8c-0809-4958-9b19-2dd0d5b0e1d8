package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.kol.controller.dto.DictProfile;
import outfox.ead.youxuan.web.kol.controller.dto.ProductWindowDetail;
import outfox.ead.youxuan.web.kol.service.DictService;

/**
 * <AUTHOR>
 * @date 2022年02月18日 7:46 下午
 */
@RestController
@BaseResponse
@Validated
@RequiredArgsConstructor
@RequestMapping("/dict")
@Api(tags = "词典接口")
public class DictController {
    private final DictService dictService;

    @ApiOperation("当前用户是否已经开通橱窗")
    @GetMapping("isOpenProductWindow")
    public boolean isOpenProductWindow() {
        return dictService.isOpenProductWindow(SecurityUtil.getUserId());
    }

    @ApiOperation("橱窗权限校验")
    @GetMapping("productWindowQualify")
    public ProductWindowDetail productWindowQualify() {
        return dictService.productWindowQualify(SecurityUtil.getUserId());
    }

    @ApiOperation("粉丝数")
    @GetMapping("fans")
    public Long getFans(String uid) {
        return dictService.getFans(uid);
    }

    @ApiOperation("词典头像昵称性别等信息")
    @GetMapping("profile")
    public DictProfile getProfile(String uid) {
        return dictService.getProfiles(uid);
    }
}

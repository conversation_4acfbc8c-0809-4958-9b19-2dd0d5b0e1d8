package outfox.ead.youxuan.web.kol.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.ShowcaseComponent;
import outfox.ead.youxuan.web.kol.controller.bo.InProcessGoodsCacheBO;
import outfox.ead.youxuan.web.kol.controller.bo.ShowcaseComponentContentBO;
import outfox.ead.youxuan.web.kol.controller.bo.ShowcaseComponentDetail2DictBO;
import outfox.ead.youxuan.web.kol.controller.bo.ShowcaseComponentSwitchLinkBO;
import outfox.ead.youxuan.web.kol.controller.vo.*;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface ShowcaseComponentMapper {

    ShowcaseComponent createVo2Do(ShowcaseComponentCreateVO showcaseComponentCreateVO);

    ShowcaseComponentContentBO createVo2ContentBo(ShowcaseComponentCreateVO showcaseComponentCreateVO);

    ShowcaseComponentContentBO modifyVo2ContentBo(ShowcaseComponentModifyVO showcaseComponentModifyVO);

    ShowcaseComponentSwitchLinkBO createVo2LinkBo(ShowcaseComponentCreateVO showcaseComponentCreateVO);

    ShowcaseComponentSwitchLinkBO modifyVo2LinkBo(ShowcaseComponentModifyVO showcaseComponentModifyVO);

    ShowcaseComponent modifyVo2Do(ShowcaseComponentModifyVO showcaseComponentModifyVO);

    List<ShowcaseComponentListVO> do2ListVo(List<ShowcaseComponent> showcaseComponentList);

    @Mapping(target = "isSyncFromSponsor", expression = "java(showcaseComponent.getSourceId() != null)")
    ShowcaseComponentListVO do2ListVo(ShowcaseComponent showcaseComponent);

    ShowcaseComponentDetailVO do2DetailVo(ShowcaseComponent showcaseComponent);

    ShowcaseComponentDetail2DictVO do2DetailDictVO(ShowcaseComponent showcaseComponent);

    Collection<ShowcaseComponentDetail2DictVO> dos2DetailDictVOs(Collection<ShowcaseComponent> showcaseComponents);

    List<InProcessGoodsCacheBO> showcaseVo2InProcessGoodsCacheBo(List<ShowcaseComponentDetail2DictBO> res);

    ShowcaseComponentDetail2DictVO toShowcaseComponentDetail2DictVO(ShowcaseComponentDetail2DictBO showcaseComponentDetail2DictBo);

    ShowcaseComponentDetail2DictBO do2DetailDictBo(ShowcaseComponent showcaseComponent);

    List<ShowcaseComponentDetail2DictVO> toShowcaseComponentDetail2DictVOS(Collection<ShowcaseComponentDetail2DictBO> bos);
}

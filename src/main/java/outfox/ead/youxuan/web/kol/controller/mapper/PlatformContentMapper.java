package outfox.ead.youxuan.web.kol.controller.mapper;

import org.mapstruct.Mapper;
import outfox.ead.youxuan.entity.PlatformContent;
import outfox.ead.youxuan.web.kol.controller.vo.PlatformContentWithPriceVO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface PlatformContentMapper {

    List<PlatformContentWithPriceVO> do2PlatformContentWithPriceVO(List<PlatformContent> platformContents);

    PlatformContentWithPriceVO do2PlatformContentWithPriceVO(PlatformContent platformContent);
}

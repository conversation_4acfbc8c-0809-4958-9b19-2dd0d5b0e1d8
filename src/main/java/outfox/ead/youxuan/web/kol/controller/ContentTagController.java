package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.kol.controller.vo.ContentTagCriteriaQueryVO;
import outfox.ead.youxuan.web.kol.controller.vo.ContentTagListVO;
import outfox.ead.youxuan.web.kol.controller.vo.ContentTagSaveOrUpdateVO;
import outfox.ead.youxuan.web.kol.controller.vo.ContentTagUserUpdateVO;
import outfox.ead.youxuan.web.kol.service.ContentTagService;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@BaseResponse
@AllArgsConstructor
@Validated
@RequestMapping("/content_tag")
@Api(tags = "内容分类标签管理")
@AccessControl(roles = {RoleEnum.KOL_OPERATOR, RoleEnum.SPONSOR})
public class ContentTagController {

    private final ContentTagService contentTagService;

    @ApiOperation("分页列表查询")
    @AccessControl(roles = RoleEnum.KOL_OPERATOR)
    @GetMapping
    public PageVO<ContentTagListVO> getContentTagPageList(ContentTagCriteriaQueryVO contentTagCriteriaQueryVO) {
        return contentTagService.getPageList(contentTagCriteriaQueryVO);
    }


    @ApiOperation("获取所有可以被选择的标签ID和名字")
    @GetMapping("/valid")
    public List<ContentTagListVO> getAllValidContentTagList() {
        return contentTagService.getAllValidContentTagList();
    }

    @ApiOperation("新建或编辑标签")
    @AccessControl(roles = RoleEnum.KOL_OPERATOR)
    @PostMapping
    public Long saveOrUpdateContentTag(@RequestBody ContentTagSaveOrUpdateVO contentTagSaveOrUpdateVO) {
        return contentTagService.saveOrUpdate(contentTagSaveOrUpdateVO, SecurityUtil.getUserId());
    }

    @PostMapping("/{appAccountId}")
    @AccessControl(roles = RoleEnum.KOL_OPERATOR)
    @ApiOperation("更新某达人的标签")
    public Long saveOrUpdateAppAccountContentTag(@PathVariable Long appAccountId,
                                                 @RequestBody List<ContentTagUserUpdateVO> contentTagUserUpdateVOList) {
        return contentTagService.saveOrUpdateUserContentTag(appAccountId, contentTagUserUpdateVOList, SecurityUtil.getUserId());
    }

}

package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConvModuleSaveOrUpdateVO {

    @ApiModelProperty("ID")
    private Long id;

    @NotNull(message = "类型不能为空")
    @ApiModelProperty("0-官方网站；1-应用下载；2-联系电话；3-推广活动")
    private Integer type;

    @NotNull(message = "enabled不能为空")
    @ApiModelProperty("启用？")
    private boolean enabled;

    @ApiModelProperty("显示名称")
    private String displayName;

    @ApiModelProperty("官方网站和推广活动的链接地址")
    @Pattern(regexp = "https?://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]", message = "URL格式错")
    private String url;

    @ApiModelProperty("安卓应用包名")
    @Pattern(regexp = "([a-zA-Z_][a-zA-Z0-9_]*)+([.][a-zA-Z_][a-zA-Z0-9_]*)+", message = "androidPackageName格式错")
    private String androidPackageName;

    @ApiModelProperty("iOS下载地址")
    private String iosDownloadUrl;

    @ApiModelProperty("电话（支持带区号或400）")
    @Pattern(regexp = "1\\d{10}|0\\d{2,3}-\\d{7,8}|400-[016789]\\d{3}-\\d{3}", message = "电话格式错误，支持国内11位手机号码/400号码/座机号码")
    private String phone;

    @ApiModelProperty("开启跳转微信小程序")
    private Boolean enabledWechatMicroProgramSwitch;

    @ApiModelProperty("微信小程序原始ID")
    private String wechatMicroProgramRawId;

    @ApiModelProperty("微信小程序路径")
    private String wechatMicroProgramPath;
}

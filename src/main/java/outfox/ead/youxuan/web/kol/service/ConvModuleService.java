package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.ConvModule;
import outfox.ead.youxuan.web.ad.service.YouxuanService;
import outfox.ead.youxuan.web.kol.controller.dto.ConvModule2DictDTO;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleSaveOrUpdateVO;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ConvModuleService extends YouxuanService<ConvModule> {

    Long saveOrUpdate(ConvModuleSaveOrUpdateVO convModuleSaveOrUpdateVo, Long userId);

    void updateSwitch(Long id, Integer type, Long userId, Boolean isOpen);

    ConvModuleVO getByUserIdAndType(Integer type, Long userId);

    List<ConvModule2DictDTO> getAllTypeByAppAccountId(Long appAccountId, Long userId);
}

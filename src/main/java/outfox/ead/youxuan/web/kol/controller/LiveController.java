package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.MetricsEnum;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.annotation.Report;
import outfox.ead.youxuan.core.annotation.YoudaoIntranet;
import outfox.ead.youxuan.core.validator.Phone;
import outfox.ead.youxuan.web.kol.controller.bo.Good;
import outfox.ead.youxuan.web.kol.controller.query.UserGoodInfoQuery;
import outfox.ead.youxuan.web.kol.controller.vo.GoodInfoVO;
import outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentDetail2DictVO;
import outfox.ead.youxuan.web.kol.service.LiveService;

import javax.validation.constraints.Email;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022年07月12日 20:17
 */
@RestController
@BaseResponse
@Validated
@RequiredArgsConstructor
@RequestMapping("/live/intranet")
@Api(tags = "对接直播接口")
@YoudaoIntranet
public class LiveController {
    private final LiveService liveService;

    @ApiOperation("用户商品搜索")
    @GetMapping("userGoodInfos")
    public List<GoodInfoVO> getUserGoodInfo(UserGoodInfoQuery userGoodInfoQuery) {
        return liveService.listUserGoodInfo(userGoodInfoQuery);
    }

    @ApiOperation("商品查询")
    @PostMapping("goodInfos")
    public List<GoodInfoVO> getGoodInfo(@RequestBody Set<Good> queryGoodsList) {
        return liveService.listGoodInfo(queryGoodsList);
    }

    @ApiOperation("权限开通")
    @PostMapping("openPermissions")
    public void openPermission(
            @ApiParam("词典uid")
            @RequestParam
                    String uid,
            @ApiParam("联系人姓名")
            @RequestParam
            @Length(max = 20, message = "联系人姓名不得超过20个字符")
                    String name,
            @ApiParam("联系人电话")
            @RequestParam
            @Phone
                    String phone,
            @ApiParam("联系人邮箱") @RequestParam
            @Email(regexp = "^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$"
                    , message = "联系邮箱格式不正确，请重新输入")
                    String email) {
        liveService.openAllPermission(uid, name, phone, email);
    }

    /**
     * 只要上架且审核通过的
     *
     * @return
     */
    @ApiOperation("查询直播间商品")
    @Report(metrics = MetricsEnum.timer)
    @PostMapping("goodInfos/details")
    public List<ShowcaseComponentDetail2DictVO> getGoodInfoDetails(@RequestBody
                                                                   @ApiParam("商品id列表") List<Good> goods) {
        return liveService.getGoodInfoDetails(goods);
    }
}

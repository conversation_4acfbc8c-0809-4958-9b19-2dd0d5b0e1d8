package outfox.ead.youxuan.web.kol.controller.bo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ShowcaseComponentContentBO {

    /**
     * 推广类型
     */
    private Integer promotionType;

    /**
     * 样式ID
     */
    private Long schemaId;

    /**
     * 引导文案
     */
    private String leadText;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品价格
     */
    private Long itemPrice;

    /**
     * 划线价
     */
    private Long strikeThroughPrice;

    /**
     * 按钮文案
     */
    private String buttonText;

    /**
     * 推广图片的URL
     */
    private String promoteImageUrl;

    /**
     * 推广标题
     */
    private String promoteTitle;

    /**
     * 推广文案
     */
    private String promoteText;

    /**
     * 应用名称
     */
    private String appName;
}

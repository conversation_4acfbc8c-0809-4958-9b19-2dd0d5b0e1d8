package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.PreTaskOrder;
import outfox.ead.youxuan.web.ad.service.YouxuanService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【PreTaskOrder(已选达人表)】的数据库操作Service
 * @createDate 2022-02-23 12:34:32
 */
public interface PreTaskOrderService extends YouxuanService<PreTaskOrder> {

    PreTaskOrder getOne(Long accountId, Long platformContentId, Long userId);

    List<PreTaskOrder> getByCreator(Long userId);

    Long getCountByCreator(Long userId);

    boolean deleteByIdAndCreatorId(Long id, Long userId);

    List<PreTaskOrder> getListByPlatformContentIdsAndUserId(List<Long> platformContentIds, Long userId);

    List<PreTaskOrder> getValidById(List<Long> ids);

    boolean deleteByIds(List<Long> ids);

    boolean updateCommentAndShowcaseComponentById(Long id, String comment, Long showcaseComponentId);
}

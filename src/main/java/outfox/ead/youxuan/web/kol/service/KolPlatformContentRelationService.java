package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.AppAccount;
import outfox.ead.youxuan.entity.KolPlatformContentRelation;
import outfox.ead.youxuan.web.ad.service.YouxuanService;
import outfox.ead.youxuan.web.kol.controller.vo.TaskOrderInitQueryVO;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【KolPlatformContentRelation(达人与平台内容类型绑定关系)】的数据库操作Service
 * @createDate 2022-02-17 18:31:07
 */
public interface KolPlatformContentRelationService extends YouxuanService<KolPlatformContentRelation> {

    List<KolPlatformContentRelation> getByAppAccountIdAndPlatformContentId(Long appAccountId, List<Long> platformContentIds);

    List<KolPlatformContentRelation> getByTaskOrderInitQueryVO(Collection<TaskOrderInitQueryVO> taskOrderInitQueryVOs);

    KolPlatformContentRelation getByAppAccountIdAndPlatformContentId(Long appAccountId, Long platformContentId);

    List<KolPlatformContentRelation> listByAppAccounts(List<AppAccount> appAccount);

    List<KolPlatformContentRelation> getByAppAccountIds(List<Long> appAccountIds, Integer priceOrder, Long priceMin, Long priceMax);
}

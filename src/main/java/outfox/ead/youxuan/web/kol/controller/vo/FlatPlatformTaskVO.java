package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlatPlatformTaskVO {
    @ApiModelProperty("内容ID")
    private Long id;

    @ApiModelProperty("任务ID")
    private Long platformTaskId;

    @ApiModelProperty("任务名字")
    private String platformTaskName;

    @ApiModelProperty("内容名字")
    private String name;

    @ApiModelProperty("服务报价；单位为分")
    private Long price;
}

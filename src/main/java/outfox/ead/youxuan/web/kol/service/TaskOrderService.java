package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.entity.TaskOrder;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.YouxuanService;
import outfox.ead.youxuan.web.kol.controller.bo.AutoShowcaseInfo;
import outfox.ead.youxuan.web.kol.controller.vo.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【TaskOrder(任务订单表)】的数据库操作Service
 * @createDate 2022-02-21 10:57:49
 */
public interface TaskOrderService extends YouxuanService<TaskOrder> {
    int makeOrder(TaskOrderCreateVO taskOrderCreateVO, Long userId);

    Long addKol2PreTaskOrder(CreatePreTaskOrderVO createPreTaskOrderVO, Long userId);

    List<PlatformTaskVO> getSelectedTaskTypeList(Long userId);

    PreTaskOrderListVO getPreOrderList(Long platformTaskId, Long userId);

    Long getCandidateKolCount(Long userId);

    boolean deleteCandidateKol(Long id, Long userId);

    Long countUnfinishedAppointment(Long appAccountId, int type);

    /**
     * 根据id和type查询数量
     * @param appAccountId
     * @param type
     * @return
     */
    Long count(Long appAccountId, int type);

    List<PlatformTaskVO> getPlatformContentByAppAccountId(Long appAccountId);

    /**
     * 分页条件查询任务信息
     *
     * @param taskOrderQueryVO -
     * @return -
     */
    PageVO<TaskOrderVO> getOrderPage(TaskOrderQuery taskOrderQueryVO, RoleEnum roleEnum, Long userId);


    Long countShowcaseComponentByTypeAndExcludeStatus(Long showcaseComponentId, Integer type, Integer status);

    PageVO<PostTaskVO> pagePostTask(PostTaskQuery postTaskQuery, Long userId);

    /**
     * 立即投稿
     * @param id 投稿任务id
     * @param userId
     */
    String contribute(Long id, Long userId);

    TaskOrder getByOrderId(String orderId);

    List<Long> createPostTaskOrder(PostTaskCreateVO postTaskCreateVO);

    Boolean updatePostTaskOrder(Long id, PostTaskUpdateVO postTaskUpdateVO, Long userId);

    PostTaskOrderDetailVO getDetailById(Long id, Long userId);

    Boolean updatePostTaskStatus(Long id, Integer status, Boolean confirmUpdate, Long userId);

    /**
     * 投稿任务列表
     *
     * @param name   投稿任务名称
     * @return list
     */
    List<LdapPostTaskVO> listPostTask(String name) throws Exception;

    String ldapContribute(Long id, String uid);

    List<TaskOrder> listValidPostTaskOrders();

    List<TaskOrder> listByParentIds(List<Long> parentIds);

    PageVO<DictPostVO> getRelatedDictPost(Long id, Long current, Long size);

    Boolean isNameRepeat(String name, Long userId);

    List<TaskOrder> listByShowcaseComponentId(Long showcaseComponentId);

    List<TaskOrder> listByOrderIds(Set<String> taskOrderIds);

    /**
     * 检查与橱窗组件关联的任务是否有正在直播中的
     */
    Boolean isShowcaseRelatedTaskLiving(Long showcaseComponentId);

    Boolean updateOrderStatus(Long id, Integer status);

    /**
     * 根据uid进行自动挂窗，最多返回三个广告数据，并且随即进行绑定<p></p>
     *
     * <ol>
     *     <li>打乱顺序</li>
     *     <li>过滤获得需要自动挂窗的帖子</li>
     *     <li>根据权重随机抽取投稿任务</li>
     * </ol>
     *
     * @param postVOS 词典帖子数据列表
     * @return 橱窗数据
     */
    List<ShowcaseComponentDetail2DictVO> fillPostTaskByUid(List<AutoShowcasePostVO> postVOS);

    /**
     * 设置任务自动挂窗相关属性
     *
     * @param id           -
     * @param weight       权重
     * @param autoShowcase 自动挂窗
     */
    void updateAutoShowcase(Long id, Integer weight, Boolean autoShowcase);

    /**
     * 查询支持自动挂窗的任务
     *
     * @return -
     */
    List<AutoShowcaseInfo> listAutoShowcaseTaskOrder();

    /**
     * 查询该创作者 领取/受指派 的所有任务
     *
     * @param id -
     * @return 子任务
     */
    List<TaskOrder> listByAppAccountId(Long id);

    /**
     * 查询广告主发的正在进行中的投稿任务
     *
     * @return -
     */
    List<TaskOrder> listInProcessPostTask();

    List<AutoShowcaseInfo> updateAutoShowcaseTaskOrder();
}

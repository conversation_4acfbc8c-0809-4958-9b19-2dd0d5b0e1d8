package outfox.ead.youxuan.web.kol.handler;

import com.jd.open.api.sdk.domain.kplunion.GoodsService.response.query.CategoryInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import outfox.ead.youxuan.entity.ShowcaseComponent;
import outfox.ead.youxuan.util.IndustryUtil;
import outfox.ead.youxuan.web.kol.controller.bo.AutoShowcaseInfo;
import outfox.ead.youxuan.web.kol.controller.bo.AutoShowcasePostBO;
import outfox.ead.youxuan.web.kol.controller.vo.AutoShowcasePostVO;
import outfox.ead.youxuan.web.kol.service.TaskOrderService;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 根据帖子属性进行挂窗
 *
 * <AUTHOR>
 * @date 2022年12月27日 12:05
 */
@RequiredArgsConstructor
@Component
@Order(2)
@Slf4j
public class SimilarityAutoShowcaseHandler implements AutoShowcaseHandler {

    private final RedisTemplate<String, String> redisTemplate;

    private final TaskOrderService taskOrderService;

    @Override
    public void handler(List<AutoShowcasePostBO> posts) {
        List<AutoShowcaseInfo> autoShowcaseInfos = taskOrderService.listAutoShowcaseTaskOrder();

        Map<AutoShowcasePostBO, CompletableFuture<String>> map = new HashMap<>();
        posts.forEach(post -> {
                    if (!post.isFilled()) {
                        map.put(post,
                                CompletableFuture
                                        .supplyAsync(() -> getTargetCategory(post.getPostVO()))
                        );
                    }
                }
        );
        posts.forEach(post -> {
            if (!post.isFilled()) {
                Optional.ofNullable(map.get(post))
                        .map(future -> {
                            try {
                                return future.get(100, TimeUnit.MILLISECONDS);
                            } catch (TimeoutException e) {
                                log.warn("post future get timeout");
                                return null;
                            } catch (Exception e) {
                                log.warn("post future get error", e);
                                return null;
                            }
                        })
                        .ifPresent(targetCategory -> post.setAutoShowcaseInfo(getAutoShowcaseInfo(autoShowcaseInfos, targetCategory)));
            }
        });
    }

    private AutoShowcaseInfo getAutoShowcaseInfo(List<AutoShowcaseInfo> autoShowcaseInfos, String targetCategory) {
        Set<AutoShowcaseInfo> infos;
        infos = autoShowcaseInfos.stream()
                .filter(info ->
                        youxuanCategoryFilter(targetCategory, info)
                                ||
                                jdCategoryFilter(targetCategory, info)
                )
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(infos)) {
            List<AutoShowcaseInfo> weightPool = getOriginWeightPools(infos);
            return weightPool.get(ThreadLocalRandom.current().nextInt(weightPool.size()));
        } else {
            return null;
        }
    }

    private static boolean jdCategoryFilter(String targetCategory, AutoShowcaseInfo info) {
        return info.getShowcaseComponent().getSkuId() != null
                &&
                StringUtils.equals(
                        Optional.ofNullable(info.getShowcaseComponent())
                                .map(ShowcaseComponent::getOriginalCategoryInfo)
                                .map(CategoryInfo::getCid3Name)
                                .orElse(null),
                        targetCategory);
    }

    private static boolean youxuanCategoryFilter(String targetCategory, AutoShowcaseInfo info) {
        return StringUtils.equals(IndustryUtil.getById(info.getShowcaseComponent().getCategory()).getName(), targetCategory);
    }

    private String getTargetCategory(AutoShowcasePostVO post) {
        return redisTemplate.opsForValue().get("youxuan::post::" + post.getPostId());
    }

    /**
     * 获取原始权重池
     */
    private List<AutoShowcaseInfo> getOriginWeightPools(Collection<AutoShowcaseInfo> autoShowcaseInfos) {
        List<AutoShowcaseInfo> originWeightPools = new ArrayList<>();
        autoShowcaseInfos.forEach(info -> {
            int i = info.getTaskOrder().getWeight();
            while (i-- > 0) {
                originWeightPools.add(info);
            }
        });
        return originWeightPools;
    }
}

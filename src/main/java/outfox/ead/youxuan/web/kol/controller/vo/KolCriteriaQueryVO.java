package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class KolCriteriaQueryVO {

    @ApiModelProperty("平台ID")
    private Long platformId;

    @ApiModelProperty("昵称")
    private String nickName;


    @ApiModelProperty("内容分类标签ID")
    private List<Long> contentTagIds;

    @ApiModelProperty("最小粉丝数")
    private Long fansNumMin;

    @ApiModelProperty("最大粉丝数")
    private Long fansNumMax;


    @ApiModelProperty("最小价格")
    private Long priceMin;

    @ApiModelProperty("最大价格")
    private Long priceMax;


    @ApiModelProperty("是否是名师")
    private Boolean famousTeacher;

    @ApiModelProperty("性别")
    private Integer gender;


    @ApiModelProperty("粉丝排序 0-降序 1-升序")
    private Integer fansOrder;


    @ApiModelProperty("价格排序 0-降序 1-升序")
    private Integer priceOrder;

    @NotNull
    private Long current;

    @NotNull
    private Long size;
}

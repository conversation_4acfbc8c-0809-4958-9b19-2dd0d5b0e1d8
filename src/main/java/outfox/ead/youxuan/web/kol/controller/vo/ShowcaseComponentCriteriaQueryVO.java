package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShowcaseComponentCriteriaQueryVO {

    @ApiParam(value = "组件名称")
    private String name;
    @ApiParam(value = "数据库主键")
    private Long id;
    @ApiParam(value = "组件ID")
    private String componentId;
    @ApiParam(value = "跳转类型")
    private Integer switchType;
    @ApiParam(value = "状态")
    private Integer status;
    @ApiParam(value = "已删除")
    private Boolean deleted;
    @ApiParam(value = "推广标题")
    private String promotionTitle;
    @ApiParam(value = "是否同步自广告主")
    private Boolean isSyncFromSponsor;
    @ApiParam(value = "审核状态")
    private Integer auditStatus;
    @NotNull
    private Long current;
    @NotNull
    private Long size;
}

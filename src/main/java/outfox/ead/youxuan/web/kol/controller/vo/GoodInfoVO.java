package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022年07月12日 20:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodInfoVO {
    @ApiModelProperty("商品id")
    private String goodId;
    @ApiModelProperty("0个人商品/橱窗组件 1指派任务 2投稿任务")
    private Integer source;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("图片")
    private String image;
    @ApiModelProperty("商品状态 " +
            "source=1/2  0-待接收,1-待付款,2-进行中,3-已完成,4-已取消,5-待开启,6-已下架,7-已结束" +
            "source=3  1-审核中（待审核），2-已上架（已通过），3-已下架，4-未通过")
    private Integer status;
    @ApiModelProperty("审核状态 source=3 1-审核中（待审核），2-已通过，4-未通过")
    private Integer auditStatus;
}

package outfox.ead.youxuan.web.kol.service.impl;

import com.google.common.collect.ImmutableSet;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.AppAccount;
import outfox.ead.youxuan.entity.ShowcaseComponent;
import outfox.ead.youxuan.entity.TaskOrder;
import outfox.ead.youxuan.entity.User;
import outfox.ead.youxuan.util.IndustryUtil;
import outfox.ead.youxuan.util.PageUtil;
import outfox.ead.youxuan.web.ad.controller.dto.Industry;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserService;
import outfox.ead.youxuan.web.kol.controller.bo.ShowcaseComponentDetail2DictBO;
import outfox.ead.youxuan.web.kol.controller.mapper.ShowcaseComponentMapper;
import outfox.ead.youxuan.web.kol.controller.query.GuessYouLikeQuery;
import outfox.ead.youxuan.web.kol.controller.query.KolGoodsQuery;
import outfox.ead.youxuan.web.kol.controller.vo.IsShoppingVO;
import outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentDetail2DictVO;
import outfox.ead.youxuan.web.kol.service.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static outfox.ead.youxuan.constants.CacheNameConstant.*;
import static outfox.ead.youxuan.constants.ContentMarketingConstants.*;
import static outfox.ead.youxuan.constants.PlatformEnum.YOUDAO_DICT;

/**
 * <AUTHOR>
 * @date 2022年10月18日 15:20
 */
@Service
@RequiredArgsConstructor
public class GoodsServiceImpl implements GoodsService {
    private final TaskOrderService taskOrderService;
    private final ShowcaseComponentService showcaseComponentService;
    private final UserService userService;
    private final AppAccountService appAccountService;
    private final RoleService roleService;
    private final RedisTemplate redisTemplate;
    private final DictService dictService;
    private final ShowcaseComponentMapper showcaseComponentMapper;

    /**
     * 正在购买的用户   数据过期时间
     * 在 isShoppingExpireTime 内的用户，都是正在购买的用户
     */
    private final int isShoppingExpireTime = 1000 * 60 * 2;

    /**
     * 使用redis zset存储，id为showcaseComponent的componentId，score为timestamp，value为用户名称
     */
    @Override
    public void addIsShopping(IsShoppingVO isShoppingVO, String cookie) {
        String nickname = getNickname(isShoppingVO.getYduuid(), cookie);
        long now = System.currentTimeMillis();
        redisTemplate.executePipelined((RedisCallback) connection -> {
            connection.openPipeline();
            connection.zAdd(
                    getIsShoppingKey(isShoppingVO.getComponentId()).getBytes(),
                    now,
                    redisTemplate.getValueSerializer().serialize(nickname)
            );
            connection.zRemRangeByScore(getIsShoppingKey(isShoppingVO.getComponentId()).getBytes(), 0, now - isShoppingExpireTime);
            return null;
        });
    }

    @Value("${cache.prefix}")
    private String cachePrefix;

    private String getIsShoppingKey(String componentId) {
        return cachePrefix + componentId;
    }

    /**
     * 通过词典cookie获取用户名，如果没有cookie，就直接取客户端传的yduuid
     *
     * @param yduuid -
     * @param cookie -
     * @return nickname
     */
    private String getNickname(String yduuid, String cookie) {
        try {
            return dictService.getProfiles(dictService.validAndGetUid(cookie)).getNickname();
        } catch (Exception e) {
            String invalidYduuid = "00000000-0000-0000-0000-000000000000";
            if (invalidYduuid.equals(yduuid)) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "yduuid无效");
            }
            return yduuid;
        }
    }


    @Override
    public Collection<String> isShopping(String componentId) {
        long max = System.currentTimeMillis();
        long min = max - isShoppingExpireTime;
        String key = getIsShoppingKey(componentId);
        return (
                (Set<String>) redisTemplate
                        .opsForZSet()
                        .rangeByScore(key, min, max)
        )
                .stream()
                .map(this::desensitize)
                .collect(Collectors.toList());
    }

    private String desensitize(String nickname) {
        if (StringUtils.isBlank(nickname) || nickname.length() == 1) {
            return nickname;
        } else if (nickname.length() == 2) {
            return nickname.charAt(0) + "*";
        } else if (nickname.length() == 3) {
            return nickname.charAt(0) + "**";
        } else {
            return nickname.substring(0, 2) + "**" + nickname.substring(nickname.length() - 1);
        }
    }

    @Override
    public List<ShowcaseComponentDetail2DictVO> kolRecommendGoods(KolGoodsQuery kolGoodsQuery) {
        List<ShowcaseComponentDetail2DictBO> sc = getAllGoodsByUid(kolGoodsQuery.getUid())
                .stream()
                .filter(vo -> !vo.getComponentId().equals(kolGoodsQuery.getComponentId()))
                .filter(vo -> StringUtils.isBlank(kolGoodsQuery.getName())
                        || vo.getAuditedPromoteTitle().contains(kolGoodsQuery.getName())
                )
                .collect(Collectors.toList());
        sortShowcaseComponent(sc, kolGoodsQuery.getCategory());
        return showcaseComponentMapper.toShowcaseComponentDetail2DictVOS(PageUtil.page(kolGoodsQuery.getPage(), kolGoodsQuery.getSize(), sc));
    }

    private final Set<Long> SCHEMA_IDS = ImmutableSet.of(3L, 5L, 7L, 9L);

    /**
     * 根据 uid 获取该创作者发布的所有商品(指派任务/投稿任务/个人橱窗)
     * 且商品的橱窗样式id为 3，5，7，9
     *
     * @param uid -
     * @return -
     */
    private List<ShowcaseComponentDetail2DictBO> getAllGoodsByUid(String uid) {
        User user = Optional.ofNullable(userService.getUserByDictUid(uid))
                .orElseThrow(() -> new CustomException(ResponseType.INVALID_PARAMETERS, "用户不存在"));

        // 用户个人商品
        List<ShowcaseComponent> showcaseComponents = showcaseComponentService.listValid(user.getId());

        AppAccount appAccount = Optional
                .ofNullable(appAccountService.getBindByPlatformNameAndAppUserId(YOUDAO_DICT.getName(), user.getDictUid()))
                .orElseThrow(() -> new CustomException(ResponseType.INVALID_PARAMETERS, "用户未绑定有道账号"));

        // 用户子任务
        List<TaskOrder> taskOrders = taskOrderService.listByAppAccountId(appAccount.getId());

        // 查询父任务
        List<TaskOrder> parentTaskOrders = taskOrderService.listByIds(taskOrders.stream().map(TaskOrder::getParentOrderId).collect(Collectors.toSet()));

        Set<Long> showcaseComponentIds = Stream.of(
                        taskOrders.stream().map(TaskOrder::getShowcaseComponentId),
                        parentTaskOrders.stream().map(TaskOrder::getShowcaseComponentId),
                        showcaseComponents.stream().map(ShowcaseComponent::getId)
                )
                .flatMap(x -> x)
                .collect(Collectors.toSet());
        Map<Long, ShowcaseComponent> id2ShowcaseComponent = showcaseComponentService.listByIds(showcaseComponentIds)
                .stream()
                .filter(showcaseComponent -> SCHEMA_IDS.contains(showcaseComponent.getSchemaId()))
                .collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));

        return getShowcaseComponentDetail2DictBOS(showcaseComponents, taskOrders, parentTaskOrders, id2ShowcaseComponent)
                .stream()
                .filter(this::imageFilter)
                .collect(Collectors.toList());
    }

    private boolean imageFilter(ShowcaseComponentDetail2DictBO bo) {
        return bo.isJdGoods() || isBigPic(bo.getPromoteImageUrl());
    }

    private boolean isBigPic(String url) {
        return BooleanUtils.isTrue((Boolean) redisTemplate.opsForValue().get(IS_BIG_PIC + JOINER + DigestUtils.md5Hex(url).toLowerCase()));
    }

    /**
     * 组装数据
     */
    private List<ShowcaseComponentDetail2DictBO> getShowcaseComponentDetail2DictBOS(List<ShowcaseComponent> showcaseComponents, List<TaskOrder> taskOrders, List<TaskOrder> parentTaskOrders, Map<Long, ShowcaseComponent> id2ShowcaseComponent) {
        List<ShowcaseComponentDetail2DictBO> sc = new ArrayList<>();
        showcaseComponents.forEach(showcaseComponent ->
                Optional.ofNullable(showcaseComponentService
                                .getShowcaseComponentDetail2DictBo(DICT_API_SOURCE_PERSONAL_GOOD, null, null, showcaseComponent))
                        .ifPresent(sc::add)
        );

        Map<Long, TaskOrder> id2ParentTaskOrder = parentTaskOrders.stream().collect(Collectors.toMap(TaskOrder::getId, Function.identity()));
        taskOrders.forEach(taskOrder -> {
                    TaskOrder parentTaskOrder = id2ParentTaskOrder.get(taskOrder.getParentOrderId());
                    if (taskOrder.getType().equals(TASK_ORDER_TYPE_SUB_POST) && id2ShowcaseComponent.containsKey(parentTaskOrder.getShowcaseComponentId())) {
                        Optional
                                .ofNullable(showcaseComponentService
                                        .getShowcaseComponentDetail2DictBo(DICT_API_SOURCE_POST_TASK,
                                                taskOrder,
                                                parentTaskOrder,
                                                id2ShowcaseComponent.get(parentTaskOrder.getShowcaseComponentId())
                                        )
                                )
                                .ifPresent(sc::add);
                    } else if (taskOrder.getType().equals(TASK_ORDER_TYPE_APPOINTMENT) && id2ShowcaseComponent.containsKey(taskOrder.getShowcaseComponentId())) {
                        Optional.ofNullable(showcaseComponentService
                                .getShowcaseComponentDetail2DictBo(DICT_API_SOURCE_APPOINTMENT_TASK,
                                        taskOrder,
                                        parentTaskOrder,
                                        id2ShowcaseComponent.get(taskOrder.getShowcaseComponentId())
                                )).ifPresent(sc::add);
                    }
                }
        );
        return sc;
    }

    /**
     * 根据商品品类进行倒序，如果品类相同，则按照发布时间排序
     *
     * @param showcaseComponents 橱窗组件
     * @param targetCategory     目标商品类别
     */
    public void sortShowcaseComponent(List<ShowcaseComponentDetail2DictBO> showcaseComponents, Integer targetCategory) {
        Industry targetIndustry = Optional
                .ofNullable(IndustryUtil.getById(targetCategory))
                .orElse(new Industry());
        showcaseComponents.sort((o1, o2) -> {
            int similarity = Industry.calSimilarity(targetIndustry, IndustryUtil.getById(o2.getCategory())) - Industry.calSimilarity(targetIndustry, IndustryUtil.getById(o1.getCategory()));
            return similarity != 0 ? similarity : o2.getTime().compareTo(o1.getTime());
        });
    }

    @Override
    public List<ShowcaseComponentDetail2DictVO> guessYouLike(GuessYouLikeQuery guessYouLikeQuery) {
        Collection<ShowcaseComponentDetail2DictBO> inProcessGoods = ((GoodsService) AopContext.currentProxy())
                .getInProcessGoods()
                .stream()
                .filter(this::imageFilter)
                .collect(Collectors.toList());
        List<ShowcaseComponentDetail2DictBO> guessYouLikeGoods = filter(guessYouLikeQuery, inProcessGoods);
        sortShowcaseComponent(guessYouLikeGoods, guessYouLikeQuery.getCategory());
        return showcaseComponentMapper.toShowcaseComponentDetail2DictVOS(PageUtil.page(guessYouLikeQuery.getPage(), guessYouLikeQuery.getSize(), guessYouLikeGoods));
    }

    /**
     * @return 所有上架商品 - 达人好物推荐 = 猜你喜欢商品
     */
    private List<ShowcaseComponentDetail2DictBO> filter(GuessYouLikeQuery guessYouLikeQuery, Collection<ShowcaseComponentDetail2DictBO> inProcessGoods) {
        ShowcaseComponent showcaseComponent = showcaseComponentService.getById(guessYouLikeQuery.getComponentId());
        Long creator = Optional.ofNullable(showcaseComponent)
                .map(ShowcaseComponent::getCreator)
                .orElse(null);

        Long appAccountId = Optional.ofNullable(showcaseComponent)
                .map(ShowcaseComponent::getAppAccountId)
                .orElse(null);

        return inProcessGoods
                .stream()
                .filter(goods -> StringUtils.isBlank(guessYouLikeQuery.getComponentId()) || !goods.getComponentId().equals(guessYouLikeQuery.getComponentId()))
                .filter(bo -> StringUtils.isBlank(guessYouLikeQuery.getName()) || bo.getAuditedPromoteTitle().contains(guessYouLikeQuery.getName()))
                .filter(bo -> !(Objects.equals(creator, bo.getCreator()) && Objects.equals(appAccountId, bo.getAppAccountId())))
                .collect(Collectors.toList());
    }

    @Cacheable(value = IN_PROCESS_GOODS)
    @Override
    public Collection<ShowcaseComponentDetail2DictBO> getInProcessGoods() {
        return Stream.of(
                        getInProcessGodsByTaskOrder(),
                        getInProcessGoodsByShowcaseComponent()
                )
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
    }

    /**
     * @return 状态为2且  关联的ShowcaseComponent的PromotionType=2  的PostTaskOrder 封装的 ShowcaseComponentDetail2DictBO
     */
    private Collection<ShowcaseComponentDetail2DictBO> getInProcessGodsByTaskOrder() {
        Set<ShowcaseComponentDetail2DictBO> res = new HashSet<>();
        List<TaskOrder> taskOrders = taskOrderService.listInProcessPostTask();
        Map<Long, ShowcaseComponent> id2ShowcaseComponent = showcaseComponentService
                .listByIds(taskOrders.stream().map(TaskOrder::getShowcaseComponentId).collect(Collectors.toSet()))
                .stream()
                .filter(showcaseComponent -> showcaseComponent.getAuditStatus().equals(SHOWCASE_COMPONENT_STATUS_NORMAL))
                .filter(showcaseComponent -> showcaseComponent.getPromotionType().equals(SHOWCASE_COMPONENT_PROMOTION_TYPE_SALES))
                .collect(Collectors.toMap(ShowcaseComponent::getId, Function.identity()));
        taskOrders.forEach(taskOrder ->
                Optional.ofNullable(id2ShowcaseComponent.get(taskOrder.getShowcaseComponentId()))
                        .ifPresent(showcaseComponent ->
                                res.add(
                                        showcaseComponentService
                                                .getShowcaseComponentDetail2DictBo(
                                                        null,
                                                        null,
                                                        taskOrder,
                                                        showcaseComponent
                                                )
                                )
                        )
        );
        return res;
    }

    /**
     * @return 返回品牌号创建的状态为2、PromotionType=2且已经审核过的ShowcaseComponent 封装的 ShowcaseComponentDetail2DictBO
     */
    private Collection<ShowcaseComponentDetail2DictBO> getInProcessGoodsByShowcaseComponent() {
        Set<ShowcaseComponentDetail2DictBO> res = new HashSet<>();
        List<ShowcaseComponent> brandKolShowcaseComponents = showcaseComponentService.listInProcessByRoleId(roleService.getByRoleKey(RoleEnum.BRAND_KOL.getRoleKey()).getId());
        brandKolShowcaseComponents.forEach(component -> res.add(showcaseComponentService.getShowcaseComponentDetail2DictBo(null, null, null, component)));
        return res;
    }

    @Override
    @CachePut(value = IN_PROCESS_GOODS)
    public Collection<ShowcaseComponentDetail2DictBO> updateInProcessGoodsCache() {
        return getInProcessGoods();
    }
}

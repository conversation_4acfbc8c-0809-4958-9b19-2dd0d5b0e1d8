package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022年06月01日 17:42
 */
@Data
public class AuditPageQuery {
    @ApiModelProperty("组件数据库id")
    private String id;
    @ApiModelProperty("组件id")
    private String componentId;
    @ApiModelProperty("组件名称")
    private String name;
    @ApiModelProperty("审核状态 1-审核中（待审核），2-已通过，4-未通过")
    private Integer auditStatus;
    @ApiModelProperty("true - 已失效 false - 未失效")
    private Boolean deleted;
    @ApiModelProperty("角色")
    private String roleKey;
    @ApiModelProperty("用户id")
    private String userId;
    @ApiModelProperty("用户优选名称")
    private String nickname;
    @ApiModelProperty("当前页数")
    @NotNull
    private Long current;
    @ApiModelProperty("每页个数")
    @NotNull
    private Long size;
}

package outfox.ead.youxuan.web.kol.service;

import com.github.rholder.retry.RetryException;
import outfox.ead.youxuan.entity.AppAccount;
import outfox.ead.youxuan.entity.ConvModule;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.web.ad.controller.bo.DictVideoPost;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.kol.controller.bo.Good;
import outfox.ead.youxuan.web.kol.controller.dto.DictProfile;
import outfox.ead.youxuan.web.kol.controller.dto.ProductWindowDetail;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleVO;
import outfox.ead.youxuan.web.kol.controller.vo.DictPostCountVO;
import outfox.ead.youxuan.web.kol.controller.vo.DictPostVO;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2022年02月21日 4:32 下午
 */
public interface DictService {
    /**
     * 操作橱窗权限
     *
     * @param uid -
     * @param isAdd true-开 false-关
     */
    void operateProductWindow(String uid, boolean isAdd);

    /**
     * 词典端是否已经开通商品橱窗
     *
     * @param userId 优选userId
     * @return true-开通 false-为开通
     */
    boolean isOpenProductWindow(Long userId);

    /**
     * 用户是否有开通橱窗的资格
     *
     * @param userId 163邮箱
     * @return true-有 false-没有
     */
    ProductWindowDetail productWindowQualify(Long userId);

    /**
     * 获取用户粉丝数
     *
     * @param uid uid/appAccountId
     * @return fans
     */
    Long getFans(String uid);

    /**
     * 通过uid获取词典用户数据
     *
     * @param uid 媒体账户id
     * @return profile
     */
    DictProfile getProfiles(String uid);

    /**
     * 批量获取profile
     *
     * @param uids 媒体账户id
     * @return map
     */
    Map<String, DictProfile> mapProfiles(List<String> uids);

    /**
     * 发送转化组件的信息给词典审核系统
     */
    void sendConvModuleData(ConvModule newConvModule, ConvModuleVO oldConvModule, String appUserId, Long userId);


    /**
     * 发送公司名称和开通品牌号权限
     */
    void sendCompanyDataByAppAccountIdAndUserId(AppAccount appAccount, Long userId, Role currentRole);


    /**
     * 仅发送公司名称，修改公司名称的时候用
     */
    void sendCompanyDataByUserId(Long userId, String companyName, Role currentRole);


    /**
     * 发送品牌号权限
     */
    void sendConvModulePermission(AppAccount appAccount, Long userId, Boolean hasPermission);

    void sendConvModulePermission(Long appAccountId, Long userId, Boolean hasPermission);

    void grantBrandKolPermission(AppAccount appAccount, Long userId);

    void cancelBrandKolPermission(Long appAccountId, Long userId);

    void resendAllInfoWhenRebind(Long appAccountId, Long userId, Role currentRole);

    /**
     * 校验cookie，从cookie中获取词典用户的yd_uid
     *
     * @param cookie -
     * @return uid
     */
    String validAndGetUid(String cookie);

    /**
     * 校验词典uid合法性
     * @param uid -
     * @return true-合法
     */
    boolean isUidValid(String uid);

    /**
     * 注册用户，返回词典uid
     *
     * @param icon     头像
     * @param nickname 昵称
     * @param dictUid  词典用户id
     * @return dictUid
     */
    String insertOrUpdateUser(String icon, String nickname, String dictUid);

    /**
     * 发布内容
     *
     * @param dictVideoPost 词典视屏流
     * @param urlCache
     * @return postId
     */
    String postContent(DictVideoPost dictVideoPost, Map<String, String> urlCache) throws ExecutionException, RetryException;

    /**
     * 通过任务ID或者商品ID获取关联的动态
     *
     * @param goodId  10位字符串组成的橱窗组件ID
     * @param source  10位字符串组成的任务ID
     * @param current 页码（从0开始）
     * @param size    分页大小
     * @return
     */
    PageVO<DictPostVO> getPostDetail(String goodId, Integer source, Long current, Long size);

    /**
     * 通过任务ID或者商品ID获取关联的动态数
     *
     * @param goodIds 10位字符串组成的橱窗组件ID
     * @param source  10位字符串组成的任务ID
     * @return
     */
    Set<DictPostCountVO> countPost(List<String> goodIds, Integer source);

    /**
     * 检查橱窗组件是否正在直播中使用
     *
     * @param goodList 橱窗组件ID
     */
    Boolean isShowcaseLiving(List<Good> goodList);
}

package outfox.ead.youxuan.web.kol.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.entity.PlatformTask;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.kol.controller.vo.PlatformContentPriceUpdateVO;
import outfox.ead.youxuan.web.kol.controller.vo.PlatformTaskVO;
import outfox.ead.youxuan.web.kol.service.PlatformContentService;
import outfox.ead.youxuan.web.kol.service.PlatformTaskService;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@BaseResponse
@AllArgsConstructor
@RequestMapping("/platform_content")
@Api(tags = "平台内容/任务")
@Validated
public class PlatformContentController {

    private final PlatformContentService platformContentService;
    private final PlatformTaskService platformTaskService;

    @ApiOperation("通过platformId获得当前用户的服务报价")
    @GetMapping("/{platformId}")
    public List<PlatformTaskVO> getPlatformContentByAppAccountId(@NotNull @PathVariable Long platformId) {
        return platformContentService.getPlatformContentByPlatformId(SecurityUtil.getUserId(), platformId);
    }

    @PostMapping("/price")
    @ApiOperation("服务管理改报价")
    public Long updatePriceByPlatformContentId(@RequestBody PlatformContentPriceUpdateVO platformContentPriceUpdateVO) {
        return platformContentService.saveOrUpdatePrice(
                SecurityUtil.getUserId(),
                platformContentPriceUpdateVO.getPlatformContentId(),
                platformContentPriceUpdateVO.getPrice(),
                SecurityUtil.getCurrentRole());
    }

    @GetMapping("task")
    @ApiOperation("任务类型")
    public List<PlatformTask> listPlatformTask() {
        return platformTaskService
                .lambdaQuery()
                .select(PlatformTask::getId, PlatformTask::getName)
                .list();
    }
}

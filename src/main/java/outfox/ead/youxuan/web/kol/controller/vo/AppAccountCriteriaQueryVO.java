package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class AppAccountCriteriaQueryVO {
    @ApiParam("状态：不传查全部，0-正常，1-暂停，2-删除，可以传入多个状态")
    Set<Integer> status;
    @NotNull
    @ApiParam("页数")
    Long current;
    @NotNull
    @ApiParam("条数")
    Long size;
    @ApiParam("按平台ID升序？")
    Boolean sortByPlatform;

    public Boolean getSortByPlatform() {
        // give sortByPlatform a default false value.
        return Objects.nonNull(sortByPlatform) && sortByPlatform;
    }
}

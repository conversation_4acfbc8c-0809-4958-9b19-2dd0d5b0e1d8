package outfox.ead.youxuan.web.kol.controller.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class ShowcaseComponentListVO {

    private Long id;

    @ApiModelProperty(value = "组件ID")
    private String componentId;

    @ApiModelProperty(value = "组件名称")
    private String name;

    @ApiModelProperty(value = "推广类型；1-品牌宣传，2-商品售卖，3-线索留资，4-应用推广")
    private Integer promotionType;

    @ApiModelProperty(value = "推广品类，复用行业信息类目")
    private Integer category;

    @ApiModelProperty(value = "跳转类型；1-落地页，2-应用直达，3-微信小程序，4-应用商店")
    private Integer switchType;

    @ApiModelProperty(value = "deeplink链接")
    private String deepLink;

    @ApiModelProperty(value = "落地页跳转链接")
    private String landingPageUrl;

    @ApiModelProperty(value = "备用落地页链接")
    private String backupLandingPageUrl;

    @ApiModelProperty(value = "是否自动为跳转链接添加来源参数配置项")
    private Boolean appendOutVendor;

    @ApiModelProperty(value = "微信小程序原始ID")
    private String microProgramId;

    @ApiModelProperty(value = "微信小程序目标页面路径")
    private String microProgramPath;

    @ApiModelProperty(value = "应用平台；0-不限，1-Android，2-iOS")
    private Integer appPlatform;

    @ApiModelProperty(value = "安卓应用包名")
    private String androidPackageName;

    @ApiModelProperty(value = "iOS应用ID")
    private String iosAppId;

    @ApiModelProperty(value = "样式ID")
    private Long schemaId;

    @ApiModelProperty(value = "引导文案")
    private String leadText;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品价格")
    private Long itemPrice;

    @ApiModelProperty(value = "划线价")
    private Long strikeThroughPrice;

    @ApiModelProperty(value = "按钮文案")
    private String buttonText;

    @ApiModelProperty(value = "推广图片的URL")
    private String promoteImageUrl;

    @ApiModelProperty(value = "推广标题")
    private String promoteTitle;

    @ApiModelProperty(value = "推广文案")
    private String promoteText;

    @ApiModelProperty("已经审核通过的推广标题")
    @JsonIgnore
    private String auditedPromoteTitle;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "是否标记为广告；0-否，1是")
    private Boolean markAsAd;

    @ApiModelProperty(value = "是否同步自广告主")
    private Boolean isSyncFromSponsor;

    @ApiModelProperty(value = "状态；1-审核中（待审核），2-已上架（已通过），3-已下架，4-未通过")
    private Integer status;

    @ApiModelProperty(value = "审核状态;组件审核状态 1-审核中 2-通过 4-未通过")
    private Integer auditStatus;

    @ApiModelProperty(value = "已删除？0-未删除，1-删掉了")
    private Boolean deleted;
}

package outfox.ead.youxuan.web.kol.controller.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发送给词典审核系统的转化组件数据结构
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConvModule2DictDTO {

    @ApiModelProperty("-1->是否显示全部转化模块；0->官方网站；1->应用下载；2->联系电话；3->推广活动；4->机构名称")
    private Integer type;

    @ApiModelProperty("启用？")
    private Boolean enabled;

    @ApiModelProperty("仅开关状态改变？")
    private Boolean switchChangedOnly;

    @ApiModelProperty("机构名称")
    private String companyName;

    @ApiModelProperty("显示名称")
    private String displayName;

    @ApiModelProperty("官方网站和推广活动的链接地址")
    private String url;

    @ApiModelProperty("安卓应用包名")
    private String androidPackageName;

    @ApiModelProperty("iOS下载地址")
    private String iosDownloadUrl;

    @ApiModelProperty("电话（支持带区号或400）")
    private String phone;

    @ApiModelProperty("开启跳转微信小程序")
    private Boolean enabledWechatMicroProgramSwitch;

    @ApiModelProperty("微信小程序原始ID")
    private String wechatMicroProgramRawId;

    @ApiModelProperty("微信小程序路径")
    private String wechatMicroProgramPath;

    @ApiModelProperty("词典iOS小程序ID")
    private String dictMpIosId;

    @ApiModelProperty("词典安卓小程序ID")
    private String dictMpAndroidId;
}

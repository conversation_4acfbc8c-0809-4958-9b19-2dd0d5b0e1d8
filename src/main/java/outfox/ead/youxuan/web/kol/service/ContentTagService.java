package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.ContentTag;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.YouxuanService;
import outfox.ead.youxuan.web.kol.controller.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ContentTag(达人内容标签)】的数据库操作Service
 * @createDate 2022-02-09 18:03:39
 */
public interface ContentTagService extends YouxuanService<ContentTag> {

    PageVO<ContentTagListVO> getPageList(ContentTagCriteriaQueryVO contentTagCriteriaQueryVO);

    List<ContentTagListVO> getAllValidContentTagList();

    Long saveOrUpdate(ContentTagSaveOrUpdateVO contentTagSaveOrUpdateVO, Long userId);

    Long saveOrUpdateUserContentTag(Long appAccountId, List<ContentTagUserUpdateVO> contentTagUserUpdateVOList, Long opUserId);

    List<ContentTagByUserAppAccountIdVO> getContentTagByAppAccountIds(List<Long> userIds);

    List<ContentTagListVO> getValidByType(Integer type);
}

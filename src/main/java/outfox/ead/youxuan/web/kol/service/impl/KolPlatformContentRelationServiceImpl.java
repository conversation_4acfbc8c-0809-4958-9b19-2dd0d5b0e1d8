package outfox.ead.youxuan.web.kol.service.impl;

import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.AppAccount;
import outfox.ead.youxuan.entity.KolPlatformContentRelation;
import outfox.ead.youxuan.mapper.youxuan.KolPlatformContentRelationMapper;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.controller.vo.TaskOrderInitQueryVO;
import outfox.ead.youxuan.web.kol.service.KolPlatformContentRelationService;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【KolPlatformContentRelation(达人与平台内容类型绑定关系)】的数据库操作Service实现
 * @createDate 2022-02-17 18:31:07
 */
@Service
public class KolPlatformContentRelationServiceImpl extends YouxuanServiceImpl<KolPlatformContentRelationMapper, KolPlatformContentRelation>
        implements KolPlatformContentRelationService {

    @Override
    public List<KolPlatformContentRelation> getByAppAccountIdAndPlatformContentId(Long appAccountId, List<Long> platformContentIds) {
        return baseMapper.getByUserIdAndContentPlatformId(appAccountId, platformContentIds);
    }

    @Override
    public List<KolPlatformContentRelation> getByTaskOrderInitQueryVO(Collection<TaskOrderInitQueryVO> taskOrderInitQueryVOs) {
        return baseMapper.getByTaskOrderInitQueryVOList(taskOrderInitQueryVOs);
    }

    @Override
    public KolPlatformContentRelation getByAppAccountIdAndPlatformContentId(Long appAccountId, Long platformContentId) {
        return baseMapper.getByUserIdAndContentPlatformId(appAccountId, platformContentId);
    }

    @Override
    public List<KolPlatformContentRelation> listByAppAccounts(List<AppAccount> appAccounts) {
        if (CollectionUtils.isEmpty(appAccounts)) {
            return Lists.emptyList();
        }
        return baseMapper.listByAppAccounts(appAccounts);
    }

    @Override
    public List<KolPlatformContentRelation> getByAppAccountIds(List<Long> appAccountIds, Integer priceOrder, Long priceMin, Long priceMax) {
        return baseMapper.getByAppAccountIdsAndConditions(appAccountIds, priceOrder, priceMin, priceMax);

    }
}

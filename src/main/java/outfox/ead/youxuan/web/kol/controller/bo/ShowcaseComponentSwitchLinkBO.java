package outfox.ead.youxuan.web.kol.controller.bo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ShowcaseComponentSwitchLinkBO {

    /**
     * 推广类型；1-品牌宣传，2-商品售卖，3-线索留资，4-应用推广
     */
    private Integer promotionType;

    /**
     * 跳转类型；1-落地页，2-应用直达，3-微信小程序，4-应用商店
     */
    private Integer switchType;

    /**
     * deeplink链接
     */
    private String deepLink;

    /**
     * 落地页跳转链接
     */
    private String landingPageUrl;

    /**
     * 备用落地页链接
     */
    private String backupLandingPageUrl;

    /**
     * 是否自动为跳转链接添加来源参数配置项
     */
    private Boolean appendOutVendor;

    /**
     * 微信小程序原始ID
     */
    private String microProgramId;

    /**
     * 微信小程序目标页面路径
     */
    private String microProgramPath;

    /**
     * 应用平台；0-不限，1-Android，2-iOS
     */
    private Integer appPlatform;

    /**
     * 安卓应用包名
     */
    private String androidPackageName;

    /**
     * iOS应用ID
     */
    private String iosAppId;
}

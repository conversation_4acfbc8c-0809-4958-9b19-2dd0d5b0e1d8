package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PostTaskOrderDetailVO {

    @ApiModelProperty("任务ID")
    private Long id;
    @ApiModelProperty("任务名称")
    private String name;
    @ApiModelProperty("橱窗组件ID")
    private Long showcaseComponentId;
    @ApiModelProperty("推广有效期-开始日期")
    private LocalDateTime beginDate;
    @ApiModelProperty("推广有效期-结束日期")
    private LocalDateTime endDate;
    @ApiModelProperty("参与人数上限，没上限传0")
    private Long postLimit;
    @ApiModelProperty("计费类型 计费类型；0-其他，1-CPM，2-CPC，3-CPA，4-CPS")
    private Integer billingType;
    @ApiModelProperty("佣金单价，以人民币分为单位")
    private Long commissionPrice;
    @ApiModelProperty("结算周期;0-其他，1-月结，2-周结，3-日结")
    private Integer settlementInterval;
    @ApiModelProperty("考核说明")
    private String assessmentDescription;
    @ApiModelProperty("状态；0待接收、1待付款、2进行中、3已完成、4已取消、5待开启、6已下架、7已结束")
    private Integer status;
    @ApiModelProperty("参与人数")
    private Long pickedUpUserCount;
}

package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CreatePreTaskOrderVO {
    private Long appAccountId;

    @ApiModelProperty("内容类型ID")
    private Long platformContentId;

    @ApiModelProperty("橱窗组件id")
    private Long showcaseComponentId;

    @ApiModelProperty("备注")
    private String comment;

    @ApiModelProperty("价格，仅做校验用，如果价格与数据库中记录的不符将返回添加失败")
    private Long price;
}

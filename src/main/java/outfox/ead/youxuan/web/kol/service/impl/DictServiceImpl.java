package outfox.ead.youxuan.web.kol.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.rholder.retry.*;
import com.googlecode.mp4parser.authoring.Movie;
import com.googlecode.mp4parser.authoring.Track;
import com.googlecode.mp4parser.authoring.container.mp4.MovieCreator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.TaskExecutor;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import outfox.ead.youxuan.constants.ContentMarketingConstants;
import outfox.ead.youxuan.constants.PlatformEnum;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.dto.ApiResponse;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.util.FileUtil;
import outfox.ead.youxuan.web.ad.controller.bo.DictVideoPost;
import outfox.ead.youxuan.web.ad.controller.response.NosResponse;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.LoginService;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserDetailService;
import outfox.ead.youxuan.web.kol.controller.bo.Good;
import outfox.ead.youxuan.web.kol.controller.dto.*;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleVO;
import outfox.ead.youxuan.web.kol.controller.vo.DictPostCountVO;
import outfox.ead.youxuan.web.kol.controller.vo.DictPostVO;
import outfox.ead.youxuan.web.kol.service.AppAccountService;
import outfox.ead.youxuan.web.kol.service.ConvModuleService;
import outfox.ead.youxuan.web.kol.service.DictService;

import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.ContentMarketingConstants.DICT_API_OK;
import static outfox.ead.youxuan.constants.ContentMarketingConstants.DICT_API_USER_STATUS_AUDITING;
import static outfox.ead.youxuan.constants.PlatformEnum.YOUDAO_DICT;
import static outfox.ead.youxuan.constants.ResponseType.*;

/**
 * <AUTHOR>
 * @date 2022年02月21日 4:32 下午
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DictServiceImpl implements DictService {

    public static final String LDAP_URL_STRING_FORMAT = "_ldap=%s";
    public static final String CONV_MODULE_URL_PATH = "userProperties/submit?userId=%s&" + LDAP_URL_STRING_FORMAT;
    public static final String OPEN_PRODUCT_WINDOW_URL_PATH = "good/user/qualify/modify?userId=%s&isAdd=%s&" + LDAP_URL_STRING_FORMAT;
    public static final String IS_PRODUCT_WINDOW_OPEN_URL_PATH = "ugc/good/openStatus?_ldap=%s&_userId=%s";
    public static final String PRODUCT_WINDOW_QUALIFY_URL_PATH = "ugc/good/qualify";
    public static final String FAN_DAY_URL_PATH = "user/fan/day?publisher=%s&" + LDAP_URL_STRING_FORMAT;
    public static final String GET_PROFILES_URL_PATH = "batch/get?userid=%s&content=%s&" + LDAP_URL_STRING_FORMAT;
    public static final String BATCH_USERS_GET_URL_PATH = "batch/usersget";
    public static final String SET_PROFILE_TEXT = "text/set";
    /**
     * ["option_gender","option_avatar","nickname"]
     */
    public static final String CONTENT_LIST_AFTER_URL_ENCODE = "[\"option_gender\",\"option_avatar\",\"nickname\"]";

    public static final String LDAP_ACCOUNT = "youxuan";

    public static final String BRAND_KOL_PROFILE_PERMISSION_KEY = "option_advertiser";

    public static final String BRAND_KOL_PROFILE_PERMISSION_VALUE = "advertiser";

    public static final String POSTS_COUNT_URL_PATH = "youxuan/posts/count";

    public static final String POSTS_SEARCH_URL_PATH = "youxuan/posts/search";

    public static final String GOODS_IN_LIVE_CHECK_PATH = "live/shop/goods/check";

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final LoginService loginService;
    @Lazy
    private final UserDetailService userDetailService;
    private final PlatformServiceImpl platformService;
    private final AppAccountService appAccountService;
    @Value("${dict.mp.ios}")
    private final String iosMpId;
    @Value("${dict.mp.android}")
    private final String androidMpId;
    @Value("${dict.community.analyzer.baseUrl}")
    private final String communityAnalyzerBaseUrl;
    @Value("${dict.community.admin.baseUrl}")
    private final String communityAdminBaseUrl;
    @Value("${dict.community.video.baseUrl}")
    private final String communityVideoBaseUrl;
    @Value("${dict.profile.baseUrl}")
    private final String profileBaseUrl;
    @Value("${dict.live.baseUrl}")
    private final String dictLiveBaseUrl;
    private final RoleService roleService;
    @Lazy
    private final ConvModuleService convModuleService;
    @Qualifier("threadPoolTaskExecutor")
    private final TaskExecutor threadPoolExecutor;

    @Override
    public void operateProductWindow(String uid, boolean isAdd) {
        try {
            String url = String.format(communityAdminBaseUrl + OPEN_PRODUCT_WINDOW_URL_PATH, uid, isAdd, LDAP_ACCOUNT);
            ResponseEntity<ApiResponse> response;
            response = restTemplate.postForEntity(url, null, ApiResponse.class);
            if (!(response.getStatusCode().is2xxSuccessful() && response.getBody().getCode().equals(200))) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "开通橱窗失败");
            }
        } catch (CustomException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "openProductWindow unknown error", e);
        }
    }

    @Override
    public boolean isOpenProductWindow(Long userId) {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(YOUDAO_DICT.getName(), userId);
        if (appAccount != null) {
            try {
                String url = String.format(communityVideoBaseUrl + IS_PRODUCT_WINDOW_OPEN_URL_PATH, LDAP_ACCOUNT, appAccount.getAppUserId());
                ResponseEntity<ApiResponse> exchange = restTemplate.getForEntity(url, ApiResponse.class);
                return (boolean) ((Map) Objects.requireNonNull(exchange.getBody()).getData()).get("open");
            } catch (Exception e) {
                log.error("isOpenProductWindow error", e);
                throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "isOpenProductWindow error", e);
            }
        } else {
            return false;
        }
    }

    @Override
    public ProductWindowDetail productWindowQualify(Long userId) {
        if (appAccountService.isBindDict(userId)) {
            Collection<Role> roles = roleService.listByUserId(userId);
            ProductWindowDetail data = new ProductWindowDetail();
            for (Role role : roles) {
                if (role.getRoleKey().equals(RoleEnum.BRAND_KOL.getRoleKey())) {
                    data.setQualify(true);
                    return data;
                }
            }
            String url = communityVideoBaseUrl + PRODUCT_WINDOW_QUALIFY_URL_PATH;
            HttpHeaders headers = getLoginHeaders(userId);
            try {
                HttpEntity<ProductWindowResponse> request = new HttpEntity<>(null, headers);
                ResponseEntity<ProductWindowResponse> exchange = restTemplate.exchange(url, HttpMethod.GET, request, ProductWindowResponse.class);
                data = exchange.getBody().getData();
                data.setQualify(data.getQualify() || isOpenProductWindow(userId));
                return data;
            } catch (Exception e) {
                throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "productWindowQualify error", e);
            }
        } else {
            return null;
        }
    }

    private HttpHeaders getLoginHeaders(Long userId) {
        List<String> cookies = loginService.loginDictByUserId(userId);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.put(HttpHeaders.COOKIE, cookies);
        return headers;
    }


    @Override
    public Long getFans(String uid) {
        try {
            String url = String.format(communityAnalyzerBaseUrl + FAN_DAY_URL_PATH, uid, LDAP_ACCOUNT);
            ResponseEntity<ApiResponse> response;
            response = restTemplate.getForEntity(url, ApiResponse.class);
            Map data = (Map) Objects.requireNonNull(response.getBody()).getData();
            return Long.parseLong(data.get("total").toString());
        } catch (Exception e) {
            throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "getFans error", e);
        }
    }

    @Override
    public DictProfile getProfiles(String uid) {
        if (isUidValid(uid)) {
            String url = String.format(profileBaseUrl + GET_PROFILES_URL_PATH, uid, CONTENT_LIST_AFTER_URL_ENCODE, LDAP_ACCOUNT);
            ResponseEntity<DictResponse> response = restTemplate.getForEntity(url, DictResponse.class);
            DictProfile dictProfile = Objects.requireNonNull(response.getBody()).getValues();
            dictProfile.setFansNum(this.getFans(uid));
            return dictProfile;
        } else {
            throw new CustomException(INVALID_PARAMETERS, "词典账户不存在");
        }
    }

    private static final String CONTENT_LIST = "[\"option_gender\", \"option_avatar\", \"nickname\"]";

    @Override
    public Map<String, DictProfile> mapProfiles(List<String> uids) {
        try {
            return getProfileMap(uids);
        } catch (Exception e) {
            log.error("mapProfiles error,uids is {}", Arrays.toString(uids.toArray()), e);
            throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "获取词典用户信息失败");
        }
    }

    /**
     * 批量接口一次只支持查询100个数据
     */
    private Map<String, DictProfile> getProfileMap(List<String> uids) throws JsonProcessingException, URISyntaxException, ExecutionException, InterruptedException {
        Map<String, DictProfile> res = new HashMap<>();
        uids = uids.stream().distinct().collect(Collectors.toList());
        List<List<String>> partitions = ListUtils.partition(uids, 100);
        for (List<String> partition : partitions) {
            res.putAll(getDictProfileMap(partition));
        }
        Set<String> uidSet = res.keySet();
        List<List<String>> uidsPartitions = ListUtils.partition(new ArrayList<>(uidSet), 20);
        for (List<String> uidsPartition : uidsPartitions) {
            List<Future<Map<String, Long>>> futures = new ArrayList<>();
            futures.add(((AsyncTaskExecutor) threadPoolExecutor).submit(() -> {
                Map<String, Long> uid2Fans = new HashMap<>();
                for (String uid : uidsPartition) {
                    uid2Fans.put(uid, getFans(uid));
                }
                return uid2Fans;
            }));
            for (Future<Map<String, Long>> future : futures) {
                Map<String, Long> uid2Fans = future.get();
                for (Map.Entry<String, Long> entry : uid2Fans.entrySet()) {
                    DictProfile dictProfile = res.get(entry.getKey());
                    dictProfile.setGender(mapGender(dictProfile.getGender()));
                    dictProfile.setFansNum(entry.getValue());
                }
            }
        }
        return res;
    }

    private Integer mapGender(Integer gender) {
        return gender == null ? null : gender == 1 || gender == 2 ? gender - 1 : null;
    }

    private Map<String, DictProfile> getDictProfileMap(List<String> uids) throws JsonProcessingException, URISyntaxException {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("content", CONTENT_LIST);
        map.add("userids", objectMapper.writeValueAsString(uids));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
        ResponseEntity<DictResponse> response = restTemplate
                .postForEntity(profileBaseUrl + BATCH_USERS_GET_URL_PATH, request, DictResponse.class);

        return Objects.requireNonNull(response.getBody()).getData();
    }

    /**
     * @param newConvModule 这个是需要更新的
     * @param oldConvModule 这个是数据库里的
     * @param appUserId
     */
    @Override
    public void sendConvModuleData(ConvModule newConvModule, ConvModuleVO oldConvModule, String appUserId, Long userId) {
        if (!isBrandKol(userId)) {
            return;
        }
        //newConvModule 不必再检查
        ConvModule2DictDTO.ConvModule2DictDTOBuilder builder = ConvModule2DictDTO.builder();
        if (Objects.nonNull(oldConvModule) && isConvModuleEquals(newConvModule, oldConvModule)) {
            if (newConvModule.isEnabled() != oldConvModule.isEnabled()) {
                builder.switchChangedOnly(true);
            } else {
                return;
            }
        } else {
            builder.switchChangedOnly(false);
        }

        builder.displayName(newConvModule.getDisplayName())
                .url(newConvModule.getUrl())
                .androidPackageName(newConvModule.getAndroidPackageName())
                .iosDownloadUrl(newConvModule.getIosDownloadUrl())
                .phone(newConvModule.getPhone())
                .enabledWechatMicroProgramSwitch(newConvModule.getEnabledWechatMicroProgramSwitch())
                .wechatMicroProgramRawId(newConvModule.getWechatMicroProgramRawId())
                .wechatMicroProgramPath(newConvModule.getWechatMicroProgramPath())
                .type(newConvModule.getType())
                .enabled(newConvModule.isEnabled());

        if ((newConvModule.getType().equals(ContentMarketingConstants.OFFICIAL_WEBSITE)
                || newConvModule.getType().equals(ContentMarketingConstants.PROMOTION_ACTIVITY))
                && Boolean.TRUE.equals(newConvModule.getEnabledWechatMicroProgramSwitch())) {
            builder.dictMpIosId(this.iosMpId)
                    .dictMpAndroidId(this.androidMpId);
        }
        this.sendConvModuleData(builder.build(), appUserId);
    }

    @Override
    public void sendCompanyDataByAppAccountIdAndUserId(AppAccount appAccount, Long userId, Role currentRole) {
        UserDetail userDetail = userDetailService.getByUserId(userId, currentRole);
        sendConvModulePermission(appAccount, userId, true);
        sendConvModuleData(
                ConvModule2DictDTO.builder()
                        .type(ContentMarketingConstants.COMPANY_NAME)
                        .companyName(userDetail.getCompanyName())
                        .enabled(true)
                        .build(),
                appAccount.getAppUserId()
        );
    }

    @Override
    public void sendCompanyDataByUserId(Long userId, String companyName, Role currentRole) {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(), userId);
        Platform platform = platformService.getByName(PlatformEnum.YOUDAO_DICT.getName());
        if (isBrandKol(userId) && Objects.nonNull(appAccount) && appAccount.getPlatformId().equals(platform.getId())) {
            UserDetail userDetail = userDetailService.getByUserId(userId, currentRole);
            if (Objects.isNull(userDetail) || !StringUtils.equals(userDetail.getCompanyName(), companyName)) {
                sendConvModuleData(
                        ConvModule2DictDTO.builder().enabled(true)
                                .type(ContentMarketingConstants.COMPANY_NAME)
                                .companyName(companyName).build(),
                        appAccount.getAppUserId()
                );
            }
        }
    }

    /**
     * call this method when bind/unbind/rebind dict app account.
     */
    @Override
    public void sendConvModulePermission(AppAccount appAccount, Long userId, Boolean hasPermission) {
        //without check here
        Platform platform = platformService.getByName(PlatformEnum.YOUDAO_DICT.getName());
        if (isBrandKol(userId)) {
            if (appAccount.getPlatformId().equals(platform.getId())) {
                ConvModule2DictDTO convModule2DictDTO = ConvModule2DictDTO.builder()
                        .type(ContentMarketingConstants.CONV_MODULE_PERMISSION)
                        .enabled(hasPermission).build();
                this.sendConvModuleData(convModule2DictDTO, appAccount.getAppUserId());
            }
        }
    }

    /**
     * call this method when unbind/rebind dict app account.
     *
     * @param appAccountId
     * @param hasPermission
     */
    @Override
    public void sendConvModulePermission(Long appAccountId, Long userId, Boolean hasPermission) {
        AppAccount appAccount = appAccountService.getBindById(appAccountId);
        if (isBrandKol(userId) && Objects.nonNull(appAccount)) {
            sendConvModulePermission(appAccount, userId, hasPermission);
        }
    }

    /**
     * 开通品牌号权限
     */
    @Override
    public void grantBrandKolPermission(AppAccount appAccount, Long userId) {
        sendProfileSponsorData(appAccount.getAppUserId(), BRAND_KOL_PROFILE_PERMISSION_VALUE);
    }

    /**
     * 关闭品牌号权限
     */
    @Override
    public void cancelBrandKolPermission(Long appAccountId, Long userId) {
        AppAccount appAccount = appAccountService.getBindById(appAccountId);
        if (isBrandKol(userId) && Objects.nonNull(appAccount)) {
            sendProfileSponsorData(appAccount.getAppUserId(), StringUtils.EMPTY);
        }
    }

    /**
     * 重新绑定的时候需要向词典发送所有的转化工具信息
     * 这里需要发送type == 0/1/2/3/4 的转化工具
     */
    @Override
    public void resendAllInfoWhenRebind(Long appAccountId, Long userId, Role currentRole) {
        //type 0/1/2/3
        List<ConvModule2DictDTO> convModule2DictDTOList = convModuleService.getAllTypeByAppAccountId(appAccountId, userId);
        AppAccount appAccount = appAccountService.getById(appAccountId);
        //type 4
        UserDetail userDetail = userDetailService.getByUserId(userId, currentRole);
        convModule2DictDTOList.add(
                ConvModule2DictDTO.builder().enabled(true)
                        .type(ContentMarketingConstants.COMPANY_NAME)
                        .companyName(userDetail.getCompanyName()).build()
        );
        for (ConvModule2DictDTO convModule2DictDTO : convModule2DictDTOList) {
            Integer type = convModule2DictDTO.getType();
            //set dict mp id
            if (type.equals(ContentMarketingConstants.OFFICIAL_WEBSITE) ||
                    type.equals(ContentMarketingConstants.PROMOTION_ACTIVITY)) {
                convModule2DictDTO.setDictMpIosId(this.iosMpId);
                convModule2DictDTO.setDictMpAndroidId(this.androidMpId);
            }
            convModule2DictDTO.setSwitchChangedOnly(false);
            sendConvModuleData(convModule2DictDTO, appAccount.getAppUserId());
        }
    }

    /**
     * 向词典推送是否拥有广告主权限
     */
    private void sendProfileSponsorData(String appUserId, String flag) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> requestMap = new LinkedMultiValueMap<>(3);
        requestMap.add("userid", appUserId);
        requestMap.add("key", BRAND_KOL_PROFILE_PERMISSION_KEY);
        requestMap.add("value", flag);
        log.info(profileBaseUrl + SET_PROFILE_TEXT);
        ResponseEntity<DictResponse> dictResponseEntity =
                restTemplate.postForEntity(profileBaseUrl + SET_PROFILE_TEXT,
                        new HttpEntity<>(
                                requestMap,
                                headers),
                        DictResponse.class);
        isDictSetProfileApiExecSuccess(appUserId, requestMap, dictResponseEntity);
    }


    private void sendConvModuleData(ConvModule2DictDTO convModule2DictDTO, String appUserId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        log.info(communityAdminBaseUrl + String.format(CONV_MODULE_URL_PATH, appUserId, LDAP_ACCOUNT));
        ResponseEntity<DictCommunityAdminResponse> dictResponseResponseEntity =
                restTemplate.postForEntity(communityAdminBaseUrl + String.format(CONV_MODULE_URL_PATH, appUserId, LDAP_ACCOUNT),
                        new HttpEntity<>(convModule2DictDTO, headers),
                        DictCommunityAdminResponse.class);
        isDictConvModuleApiExecSuccess(appUserId, convModule2DictDTO, dictResponseResponseEntity);

    }

    private boolean isConvModuleEquals(ConvModule convModule, ConvModuleVO convModuleVO) {
        return Objects.equals(convModule.getDisplayName(), convModuleVO.getDisplayName())
                && Objects.equals(convModule.getUrl(), convModuleVO.getUrl())
                && Objects.equals(convModule.getAndroidPackageName(), convModuleVO.getAndroidPackageName())
                && Objects.equals(convModule.getIosDownloadUrl(), convModuleVO.getIosDownloadUrl())
                && Objects.equals(convModule.getPhone(), convModuleVO.getPhone())
                && Objects.equals(convModule.getEnabledWechatMicroProgramSwitch(), convModuleVO.getEnabledWechatMicroProgramSwitch())
                && Objects.equals(convModule.getWechatMicroProgramRawId(), convModuleVO.getWechatMicroProgramRawId())
                && Objects.equals(convModule.getWechatMicroProgramPath(), convModuleVO.getWechatMicroProgramPath());
    }

    private void isDictConvModuleApiExecSuccess(String appUserId, ConvModule2DictDTO convModule2DictDTO, ResponseEntity<DictCommunityAdminResponse> dictCommunityAdminResponse) {
        log.info("send conv module data, appUserId: {} req: {}, resp: {}", appUserId, convModule2DictDTO, dictCommunityAdminResponse);
        if (dictCommunityAdminResponse.getStatusCode().is2xxSuccessful()) {
            DictCommunityAdminResponse respBody = dictCommunityAdminResponse.getBody();
            //have module in auditing
            if (DICT_API_USER_STATUS_AUDITING == Objects.requireNonNull(respBody).getCode()) {
                throw new CustomException(HAVE_MODULE_IN_AUDITING);
            }
        } else {
            throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "isDictConvModuleApiExecSuccess error");
        }
    }

    private void isDictSetProfileApiExecSuccess(String appUserId, MultiValueMap<String, String> req, ResponseEntity<DictResponse> response) {
        log.info("send profile data, appUserId: {}, req: {}, resp: {}.", appUserId, req, response);
        if (response.getStatusCode().is2xxSuccessful()) {
            Integer code = Objects.requireNonNull(response.getBody()).getError();
            if (!code.equals(ContentMarketingConstants.DICT_PROFILE_API_OK)) {
                throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "isDictSetProfileApiExecSuccess error");
            }
        } else {
            throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "isDictSetProfileApiExecSuccess response code erro");
        }
    }

    private Boolean isBrandKol(Long userId) {
        return roleService.checkRole(roleService.listByUserId(userId), RoleEnum.BRAND_KOL);
    }

    @Value("${dict.login.baseUrl}")
    private final String loginBaseUrl;
    private final String VALID_USER_ID = "server/valid/yduserid?yidlist=";
    private final String QUERY_ACCOUNT_INFO = "query/accountinfo";

    @Override
    public String validAndGetUid(String cookie) {
        String url = loginBaseUrl + QUERY_ACCOUNT_INFO;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(HttpHeaders.COOKIE, cookie);
        HttpEntity<ApiResponse> request = new HttpEntity<>(null, headers);
        ResponseEntity<ApiResponse> response = restTemplate.exchange(url, HttpMethod.GET, request, ApiResponse.class);
        if (response.getStatusCode() == HttpStatus.OK) {
            if (Objects.requireNonNull(response.getBody()).getCode().equals(0)) {
                Map<String, String> data = (Map<String, String>) (response.getBody().getData());
                return data.get("yduserid");
            } else {
                throw new CustomException(INVALID_PARAMETERS,
                        String.format("validAndGetUid error，status code:%d,code:%d,msg:%s",
                                response.getStatusCodeValue(),
                                response.getBody().getCode(),
                                response.getBody().getMessage()));
            }
        } else {
            throw new CustomException(DICT_SERVICE_ERROR, "validAndGetUid error，status code：" + response.getStatusCodeValue());
        }
    }

    @Override
    public boolean isUidValid(String uid) {
        try {
            String url = loginBaseUrl + VALID_USER_ID + objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(Collections.singletonList(uid));
            ResponseEntity<DictUidValidResponse> response = restTemplate.getForEntity(url, DictUidValidResponse.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody().getData().get(uid);
            } else {
                throw new CustomException(DICT_SERVICE_ERROR, "词典接口调用失败");
            }
        } catch (Exception e) {
            log.error("isUidValid error", e);
            throw new CustomException(INVALID_PARAMETERS, "Uid Valid ERROR", e);
        }
    }

    private static final String REGISTER_URL = "/batch/set";
    private static final String PROFILE_CONTENT = "{\"option_avatar\": \"%s\",\"nickname\": \"%s\"}";

    @Override
    @Retryable(value = Exception.class, backoff = @Backoff(value = 0), maxAttempts = 5)
    public String insertOrUpdateUser(String icon, String nickname, String dictUid) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, String> param = new LinkedMultiValueMap<>();
        dictUid = StringUtils.isNotBlank(dictUid) ? dictUid : generateDictUid();
        param.add("userid", dictUid);
        param.add("content", String.format(PROFILE_CONTENT, icon, addPrefix(nickname)));
        ResponseEntity<DictResponse> res = restTemplate
                .postForEntity(profileBaseUrl + REGISTER_URL,
                        new HttpEntity<>(param, headers),
                        DictResponse.class);
        if (res.getStatusCode().is2xxSuccessful() && res.getBody().getError().equals(0)) {
            return dictUid;
        } else {
            throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "生成虚拟用户失败");
        }
    }


    private static final String AD_FLAG = "EAD";

    /**
     * 词典昵称不能重复，加后缀
     * 词典昵称最长20位，目前appName最长为10
     * 前缀设置为10位随机数
     */
    private String addPrefix(String nickname) {
        return AD_FLAG + String.valueOf(System.currentTimeMillis()).substring(6) + nickname;
    }


    private String generateDictUid() {
        return UUID.randomUUID().toString();
    }


    private static final String POST_CONTENT_URL = "posts/createByApi?creator=%s&editor=%s&source=AD&status=PUBLISH&_ldap=youxuan";

    @Override
    @Retryable(value = Exception.class, backoff = @Backoff(value = 0), maxAttempts = 5)
    public String postContent(DictVideoPost dictVideoPost, Map<String, String> urlCache) throws ExecutionException, RetryException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        ResponseEntity<NosResponse> res = restTemplate
                .postForEntity(
                        String.format(communityAdminBaseUrl + POST_CONTENT_URL, dictVideoPost.getUid(), dictVideoPost.getUid()),
                        new HttpEntity<>(getPostContentParam(dictVideoPost, urlCache), headers),
                        NosResponse.class);
        if (res.getStatusCode().is2xxSuccessful() && res.getBody().getCode().equals(200)) {
            return (String) res.getBody().getData().get("postId");
        } else {
            throw new CustomException(ResponseType.DICT_SERVICE_ERROR, "发布内容失败");
        }
    }

    private MultiValueMap<String, String> getPostContentParam(DictVideoPost property, Map<String, String> urlCache) throws ExecutionException, RetryException {
        MultiValueMap<String, String> param = new LinkedMultiValueMap<>();
        param.add("postType", "VIDEO");
        if (StringUtils.isNotBlank(property.getDictPostId())) {
            param.add("postId", property.getDictPostId());
        }
        String coverImage = FileUtil.transferNosUrl(property.getCoverImage()) + String.format("?_w=%d&_h=%d", property.getCoverImageWidth(), property.getCoverImageHeight());
        param.add("title", property.getTitle());
        param.add("coverImage", coverImage);
        param.add("videoStaticCoverImage", coverImage);
        param.add("content", property.getText());
        param.add("videoUrl", spliceVideoSize(property.getVideo(), urlCache));
        param.add("weight", "5");
        param.add("youthVisible", property.getYouthVisible().toString());
        return param;
    }

    private String spliceVideoSize(String url, Map<String, String> videoUrlCache) throws ExecutionException, RetryException {
        if (StringUtils.isBlank(url)) {
            return url;
        } else if (videoUrlCache.containsKey(url)) {
            return videoUrlCache.get(url);
        } else {
            Retryer<String> retryer = RetryerBuilder.<String>newBuilder()
                    .retryIfException()
                    .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                    .withRetryListener(new RetryListener() {
                        @Override
                        public <V> void onRetry(Attempt<V> attempt) {
                            if (attempt.hasException()) {
                                log.error("retry to splice video size,video url is {},attempt number is {}", url, attempt.getAttemptNumber(), attempt.getExceptionCause());
                            }
                        }
                    }).build();
            return retryer.call(() -> {
                final Path tempDir = Files.createDirectories(Paths.get("./temp"));
                final Path tmpFile = Files.createTempFile(tempDir, "video", ".tmp");

                FileUtils.copyURLToFile(new URL(url), tmpFile.toFile());
                Movie movie = MovieCreator.build(tmpFile.toFile().getPath());
                int width = 0, height = 0;
                for (Track track : movie.getTracks()) {
                    if ("vide".equals(track.getHandler())) {
                        width = new Double(track.getTrackMetaData().getWidth()).intValue();
                        height = new Double(track.getTrackMetaData().getHeight()).intValue();
                        break;
                    }
                }
                Files.deleteIfExists(tmpFile);
                String res = UriComponentsBuilder.fromUriString(url)
                        .replaceQueryParam("_w", width)
                        .replaceQueryParam("_h", height)
                        .build().toString();
                videoUrlCache.put(url, res);
                return res;
            });
        }
    }


    @Override
    public PageVO<DictPostVO> getPostDetail(String goodId, Integer source, Long current, Long size) {
        // 因为词典API的分页是以0为第一页，所以这里得处理下
        Long dictCurrent = current - 1;
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(communityAdminBaseUrl + POSTS_SEARCH_URL_PATH)
                .queryParam("goodId", goodId)
                .queryParam("source", source)
                .queryParam("page", dictCurrent)
                .queryParam("size", size)
                .queryParam("_ldap", LDAP_ACCOUNT);
        ResponseEntity<DictPostsSearchResponse> response = restTemplate.getForEntity(uriComponentsBuilder.toUriString(), DictPostsSearchResponse.class);
        DictPostsSearchResponse dictPostsSearchResponse = response.getBody();
        if (Objects.nonNull(dictPostsSearchResponse) && DICT_API_OK == dictPostsSearchResponse.getCode()) {
            DictPostsSearchResponse.Data data = dictPostsSearchResponse.getData();
            List<DictPostVO> dictPostVOList = new ArrayList<>(data.getContent().size());
            for (DictPostsSearchResponse.Content content : data.getContent()) {
                dictPostVOList.add(this.dictPostListResponse2Vo(content));
            }
            return new PageVO<>(current, size, dictPostVOList, data.getTotalElements());
        }
        log.warn("dict search post API response bad result, request: {}, return data: {}.", uriComponentsBuilder.toUriString(), response);
        return new PageVO<>(current, size, Collections.emptyList(), 0L);
    }

    @Override
    public Set<DictPostCountVO> countPost(List<String> goodIds, Integer source) {
        if (CollectionUtils.isEmpty(goodIds)) {
            return Collections.emptySet();
        }
        // 250 items each call is safe for GET method.
        List<List<String>> partitionedGoodIds = ListUtils.partition(goodIds, 250);
        List<Future<List<DictPostCountVO>>> futures = new ArrayList<>();
        for (List<String> goodIdList : partitionedGoodIds) {
            futures.add(((AsyncTaskExecutor) threadPoolExecutor).submit(() -> {
                UriComponentsBuilder uriComponentsBuilder =
                        UriComponentsBuilder.fromUriString(communityAdminBaseUrl + POSTS_COUNT_URL_PATH)
                                .queryParam("goodIds", goodIdList)
                                .queryParam("_ldap", LDAP_ACCOUNT)
                                .queryParam("source", source);
                ResponseEntity<DictPostsCountResponse> response = restTemplate.getForEntity(uriComponentsBuilder.toUriString(), DictPostsCountResponse.class);
                DictPostsCountResponse dictPostsCountResponse = response.getBody();
                if (Objects.nonNull(dictPostsCountResponse) && DICT_API_OK == dictPostsCountResponse.getCode()) {
                    return dictPostsCountResponse.getData();
                } else {
                    log.warn("dict count post API response bad result, request: {}, return data: {}.", uriComponentsBuilder.toUriString(), response);
                    return Collections.emptyList();
                }
            }));
        }
        Set<DictPostCountVO> result = new HashSet<>(goodIds.size());
        for (Future<List<DictPostCountVO>> future : futures) {
            try {
                result.addAll(future.get());
            } catch (Exception e) {
                log.error("error when fetch dict post count.", e);
                throw new CustomException(DICT_SERVICE_ERROR);
            }
        }
        return result;
    }

    public static final Integer DICT_LIVE_API_OK = 0;
    public static final Integer DICT_GOODS_LIVE_STATUS_LIVING = 1;
    public static final Integer DICT_GOODS_LIVE_STATUS_FREE = 2;

    @Override
    public Boolean isShowcaseLiving(List<Good> goodList) {
        if (CollectionUtils.isEmpty(goodList)) {
            return false;
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        ResponseEntity<GoodWithLiveStatus> goodWithLiveStatusResponseEntity =
                restTemplate.postForEntity(dictLiveBaseUrl + GOODS_IN_LIVE_CHECK_PATH, new HttpEntity<>(goodList, httpHeaders), GoodWithLiveStatus.class);
        if (goodWithLiveStatusResponseEntity.getStatusCode() != HttpStatus.OK
                || Objects.isNull(goodWithLiveStatusResponseEntity.getBody())
                || !goodWithLiveStatusResponseEntity.getBody().getCode().equals(DICT_LIVE_API_OK)) {
            log.warn("exception response when checking showcase live status, url: {}, request: {}, response: {}",
                    dictLiveBaseUrl + GOODS_IN_LIVE_CHECK_PATH, goodList, goodWithLiveStatusResponseEntity);
            throw new CustomException(DICT_SERVICE_ERROR);
        }
        log.info("dict live status resp: {}", goodWithLiveStatusResponseEntity);
        return goodWithLiveStatusResponseEntity.getBody().getData()
                .stream()
                .anyMatch(item -> item.getGoodStatus().equals(DICT_GOODS_LIVE_STATUS_LIVING));
    }

    private DictPostVO dictPostListResponse2Vo(DictPostsSearchResponse.Content content) {
        return DictPostVO.builder()
                .name(content.getPostTitle())
                .channelName(content.getCommunity().get(0).getName())
                .publishTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(content.getPublishTime()), TimeZone.getDefault().toZoneId()))
                .expiredTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(content.getGoodEndTime()), TimeZone.getDefault().toZoneId()))
                .status(content.getStatus())
                .ugcStatus(content.getUgcStatus())
                .build();

    }
}

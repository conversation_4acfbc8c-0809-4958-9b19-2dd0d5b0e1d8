package outfox.ead.youxuan.web.kol.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.PreTaskOrder;
import outfox.ead.youxuan.entity.TaskOrder;
import outfox.ead.youxuan.web.kol.controller.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface TaskOrderMapper {

    /**
     * KOL的查询条件只能是 达人昵称 广告主昵称 任务ID 任务类型 任务状态
     */
    default TaskOrderQuery kolQuery(TaskOrderQuery taskOrderQuery) {
        taskOrderQuery.setUserId(null);
        taskOrderQuery.setAppAccountId(null);
        return taskOrderQuery;
    }

    /**
     * 广告主的查询条件只能是 达人昵称 任务ID 任务类型 任务状态
     */
    default TaskOrderQuery sponsorQuery(TaskOrderQuery taskOrderQuery) {
        taskOrderQuery.setUserId(null);
        taskOrderQuery.setSponsorName(null);
        taskOrderQuery.setAppAccountId(null);
        return taskOrderQuery;
    }

    /**
     * 运营的查询条件只能是 达人昵称 广告主昵称 达人ID 广告主ID 任务类型 任务状态
     *
     * @param taskOrderQuery
     * @return
     */
    default TaskOrderQuery opQuery(TaskOrderQuery taskOrderQuery) {
        taskOrderQuery.setTaskId(null);
        return taskOrderQuery;
    }

    List<TaskOrderVO> do2VoList(List<TaskOrder> taskOrderList);

    List<PostTaskVO> do2PostTaskVO(List<TaskOrder> records);

    @Mapping(target = "showcaseComponentId", ignore = true)
    TaskOrder postTaskCreateVo2Do(PostTaskCreateVO postTaskCreateVO);


    TaskOrder postTaskUpdateVo2Do(PostTaskUpdateVO postTaskUpdateVO);

    PostTaskOrderDetailVO do2PostTaskDetailVo(TaskOrder taskOrder);

    @Mapping(target = "parentOrderId", expression = "java(parentOrderId)")
    @Mapping(target = "status", expression = "java(status)")
    @Mapping(target = "id", ignore = true)
    TaskOrder preTaskOrder2DO(PreTaskOrder preTaskOrder, Long parentOrderId, int status);

    List<LdapPostTaskVO> do2LdapPostTaskVO(List<TaskOrder> listPostTask);

    PostTaskVO do2PostTaskVO(TaskOrder taskOrder);
}

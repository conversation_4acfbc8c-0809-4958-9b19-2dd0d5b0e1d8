package outfox.ead.youxuan.web.kol.service;

import outfox.ead.youxuan.entity.AppAccount;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.YouxuanService;
import outfox.ead.youxuan.web.kol.controller.vo.*;

import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @description 针对表【AppAccount(媒体账户)】的数据库操作Service
 * @date 2022-02-09 15:49:35
 */
public interface AppAccountService extends YouxuanService<AppAccount> {

    /**
     * 分页获取绑定的账户
     *
     * @param status
     * @param userId         用户id
     * @param current        -
     * @param size           -
     * @param sortByPlatform 按platformId排序
     * @param currentRole
     * @return 分页数据
     */
    PageVO<AppAccountVO> pageByStatusAndUserId(Set<Integer> status, Long userId, Long current, Long size, Boolean sortByPlatform, Role currentRole);

    /**
     * 创作者绑定媒体账户，更新stage
     *
     * @param appAccount  媒体账户
     * @param userId      -
     * @param noException 重复绑定不需要抛出异常
     * @param currentRole 当前登陆的角色
     */
    void bind(AppAccount appAccount, Long userId, boolean noException, Role currentRole);

    /**
     * 解绑或重新绑定账户，并且会计算userDetail的stage
     *
     * @param id          AppAccount id
     * @param status      状态
     * @param currentRole
     */
    void updateStatusById(Long id, int status, Role currentRole);

    /**
     * @param userId -
     * @return -
     */
    List<AppAccount> listByUserId(Long userId);

    List<AppAccount> listBindByUserId(Long userId);

    void unbind(Long id, Role currentRole);

    int updateInServiceStatus(Long userId, Long appAccountId, boolean isInService);

    /**
     * 根据appUserId、platformId和userId查询绑定的关系
     */
    AppAccount getByAppUserIdAndPlatformIdAndUserId(String appUserId, Long platformId, Long userId);

    /**
     * 根据Id查询AppAccount
     * @return 获取AppAccount
     */
    AppAccount getBindById(Long id);

    List<AppAccount> getBindAndInServiceByIds(List<Long> ids);

    AppAccount getByPlatformIdAndUserId(Long platformId, Long userId);

    PageVO<KolQueryListVO> kolCriteriaPageQuery(KolCriteriaQueryVO kolCriteriaQueryVO, Long size, Long current);

    List<AppAccount> listByPlatformId(Long id);

    /**
     * 根据 platformName 和 userId 获取绑定状态的 AppAccount
     *
     * @param platformName 平台名字
     * @param userId       用户id
     * @return AppAccount
     */
    AppAccount getBindByPlatformNameAndUserId(String platformName, Long userId);

    List<AppAccount> getBindByPlatformNameAndUserId(String platformName, List<Long> userIdList);

    /**
     * 根据平台名称和AppUserId查询账户
     *
     * @param platformName 平台名称
     * @param uid          AppUserId
     * @return 媒体账户
     */
    AppAccount getBindByPlatformNameAndAppUserId(String platformName, String uid);

    /**
     * 用户是否绑定了词典
     *
     * @param userId user表的id
     * @return true-绑定
     */
    Boolean isBindDict(Long userId);

    /**
     * 条件分页查询kol详情
     *
     * @param kolDetailQueryVO -
     * @return page
     */
    PageVO<KolDetailVO> pageKolDetail(KolDetailQueryVO kolDetailQueryVO);

    List<Long> getIdByUserId(Long userId);

    /**
     * 编辑达人mcn属性
     * @param mcn
     * @param id
     */
    void saveOrUpdateMcn(String mcn, Long id);


    Boolean disableNotifySchemaUpgrade(Long appAccountId);

    /**
     * 没有完善用户数据直接进行绑定
     *
     * @param appAccount
     * @param id
     * @param currentRole
     */
    void specialBind(AppAccount appAccount, Long id, Role currentRole);

    List<Long> getIdByKolOperator(Long userId);
}

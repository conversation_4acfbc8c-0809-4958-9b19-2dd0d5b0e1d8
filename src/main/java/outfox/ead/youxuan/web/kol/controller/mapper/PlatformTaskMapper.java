package outfox.ead.youxuan.web.kol.controller.mapper;

import org.mapstruct.Mapper;
import outfox.ead.youxuan.entity.PlatformTask;
import outfox.ead.youxuan.web.kol.controller.vo.PlatformTaskVO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface PlatformTaskMapper {

    List<PlatformTaskVO> do2PlatformTaskVO(List<PlatformTask> platformTasks);

    PlatformTaskVO do2PlatformTaskVO(PlatformTask platformTask);

}

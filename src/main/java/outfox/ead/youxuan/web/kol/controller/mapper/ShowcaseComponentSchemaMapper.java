package outfox.ead.youxuan.web.kol.controller.mapper;

import org.mapstruct.Mapper;
import outfox.ead.youxuan.entity.ShowcaseComponentSchema;
import outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentSchemaListVO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface ShowcaseComponentSchemaMapper {

    List<ShowcaseComponentSchemaListVO> do2Vo(List<ShowcaseComponentSchema> showcaseComponentSchemaList);
}

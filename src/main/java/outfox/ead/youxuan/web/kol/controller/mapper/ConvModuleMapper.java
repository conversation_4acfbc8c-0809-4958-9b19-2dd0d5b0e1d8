package outfox.ead.youxuan.web.kol.controller.mapper;

import org.mapstruct.Mapper;
import outfox.ead.youxuan.entity.ConvModule;
import outfox.ead.youxuan.web.kol.controller.dto.ConvModule2DictDTO;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleSaveOrUpdateVO;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleVO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface ConvModuleMapper {
    ConvModule saveOrUpdateVoToDo(ConvModuleSaveOrUpdateVO vo);

    ConvModuleVO doToConvModuleVo(ConvModule convModule);

    List<ConvModuleVO> doToConvModuleVo(List<ConvModule> convModules);

    List<ConvModule2DictDTO> doToConvModule2DictDTO(List<ConvModule> convModules);

    ConvModule vo2ConvModuleDo(ConvModuleVO convModuleVO);
}

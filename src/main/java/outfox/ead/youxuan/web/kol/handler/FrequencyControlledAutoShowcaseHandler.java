package outfox.ead.youxuan.web.kol.handler;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import outfox.ead.youxuan.web.kol.controller.bo.AutoShowcasePostBO;

import java.util.Collections;
import java.util.List;

/**
 * 自动挂窗频控
 * 随机获取需要挂橱窗的图文/视频帖子
 *
 * <AUTHOR>
 * @date 2022年12月27日 11:17
 */
@Component
@Order(1)
public class FrequencyControlledAutoShowcaseHandler implements AutoShowcaseHandler {

    @Override
    public void handler(List<AutoShowcasePostBO> posts) {
        Collections.shuffle(posts);
        posts.subList(Math.min(3, posts.size()), posts.size()).clear();
    }
}

package outfox.ead.youxuan.web.kol.service.impl;

import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import outfox.ead.youxuan.entity.ShowcaseComponentSchema;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentSchemaListVO;
import outfox.ead.youxuan.web.kol.service.ShowcaseComponentSchemaService;
import outfox.ead.youxuan.mapper.youxuan.ShowcaseComponentSchemaMapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ShowcaseComponentSchema(橱窗组件样式)】的数据库操作Service实现
* @createDate 2022-05-30 19:55:45
*/
@Service
@AllArgsConstructor
public class ShowcaseComponentSchemaServiceImpl extends YouxuanServiceImpl<ShowcaseComponentSchemaMapper, ShowcaseComponentSchema>
implements ShowcaseComponentSchemaService{

    outfox.ead.youxuan.web.kol.controller.mapper.ShowcaseComponentSchemaMapper showcaseComponentSchemaMapper;

    @Override
    public List<ShowcaseComponentSchemaListVO> listValidByPromotionType(Integer promotionType) {
        List<ShowcaseComponentSchema> showcaseComponentSchemas = baseMapper.listValidByPromotionType(promotionType);
        if (CollectionUtils.isNotEmpty(showcaseComponentSchemas)) {
            return showcaseComponentSchemaMapper.do2Vo(showcaseComponentSchemas);
        } else {
            return Collections.emptyList();
        }
    }
}
package outfox.ead.youxuan.web.kol.service.impl;

import io.jsonwebtoken.lang.Collections;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.KolContentTagRelation;
import outfox.ead.youxuan.mapper.youxuan.KolContentTagRelationMapper;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.controller.dto.ContentTagUsageCountDTO;
import outfox.ead.youxuan.web.kol.service.KolContentTagRelationService;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Li
 * @description 针对表【KolContentTagRelation(达人与内容标签绑定关系)】的数据库操作Service实现
 * @createDate 2022-02-10 17:45:52
 */
@Service
public class KolContentTagRelationServiceImpl extends YouxuanServiceImpl<KolContentTagRelationMapper, KolContentTagRelation>
        implements KolContentTagRelationService {

    @Override
    public List<ContentTagUsageCountDTO> getContentTagUsageCountByTagIds(List<Long> ids) {
        return baseMapper.getUsageCountByContentTagIds(ids);
    }

    @Override
    public ContentTagUsageCountDTO getContentTagUsageCountByContentTagId(Long id) {
        return ContentTagUsageCountDTO.builder()
                .contentTagId(id)
                .usageCount(baseMapper.getCountByCountTagId(id))
                .build();
    }

    @Override
    public Integer delByAppAccountId(Long appAccountId) {
        return baseMapper.delByAppAccountId(appAccountId);
    }

    @Override
    public List<KolContentTagRelation> getByAppAccountIds(List<Long> appAccountIds) {
        if (Collections.isEmpty(appAccountIds)) {
            return Lists.emptyList();
        }
        return baseMapper.getByAppAccountIds(appAccountIds);
    }

    @Override
    public void batchBuildAndSave(Long appAccountId, List<Long> tagIds) {
        List<KolContentTagRelation> kolContentTagRelationList = new ArrayList<>(tagIds.size());
        for (Long tagId : tagIds) {
            kolContentTagRelationList.add(
                    KolContentTagRelation
                            .builder()
                            .appAccountId(appAccountId)
                            .contentTagId(tagId)
                            .build());
        }
        saveBatch(kolContentTagRelationList);
    }
}
package outfox.ead.youxuan.web.kol.service.impl;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.constants.PlatformEnum;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.AppAccount;
import outfox.ead.youxuan.entity.ConvModule;
import outfox.ead.youxuan.mapper.youxuan.ConvModuleMapper;
import outfox.ead.youxuan.util.JacksonUtil;
import outfox.ead.youxuan.web.ad.service.impl.YouxuanServiceImpl;
import outfox.ead.youxuan.web.kol.controller.dto.ConvModule2DictDTO;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleSaveOrUpdateVO;
import outfox.ead.youxuan.web.kol.controller.vo.ConvModuleVO;
import outfox.ead.youxuan.web.kol.service.AppAccountService;
import outfox.ead.youxuan.web.kol.service.ConvModuleService;
import outfox.ead.youxuan.web.kol.service.DictService;

import java.util.List;
import java.util.Objects;

import static outfox.ead.youxuan.constants.ContentMarketingConstants.*;

/**
 * <AUTHOR> Li
 */
@Service
@RequiredArgsConstructor
public class ConvModuleServiceImpl extends YouxuanServiceImpl<ConvModuleMapper, ConvModule>
        implements ConvModuleService {

    private final outfox.ead.youxuan.web.kol.controller.mapper.ConvModuleMapper convModuleMapper;

    private final AppAccountService appAccountService;

    @Lazy
    private final DictService dictService;

    @Override
    public Long saveOrUpdate(ConvModuleSaveOrUpdateVO convModuleSaveOrUpdateVo, Long userId) {
        // FE does not set ID.
        // But this user has a same type record in DB.
        // Set ID that already in DB.
        ConvModuleVO convModuleVO = getByUserIdAndType(convModuleSaveOrUpdateVo.getType(), userId);
        if (Objects.nonNull(convModuleVO) && (Objects.isNull(convModuleSaveOrUpdateVo.getId()) ||
                !convModuleSaveOrUpdateVo.getId().equals(convModuleVO.getId()))) {
            convModuleSaveOrUpdateVo.setId(convModuleVO.getId());
        }
        ConvModule convModule = convModuleMapper.saveOrUpdateVoToDo(saveOrUpdatePreCheckAndBuild(convModuleSaveOrUpdateVo));
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(), userId);
        convModule.setAppAccountId(appAccount.getId());
        // 在这里发给词典
        dictService.sendConvModuleData(convModule, convModuleVO, appAccount.getAppUserId(), userId);
        saveOrUpdate(convModule);

        return convModule.getId();
    }

    @Override
    public void updateSwitch(Long id, Integer type, Long userId, Boolean isOpen) {
        ConvModuleVO convModuleVO = getByUserIdAndType(type, userId);
        if (Objects.isNull(convModuleVO) || !id.equals(convModuleVO.getId())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        if (convModuleVO.isEnabled() == isOpen) {
            //equals, do nothing.
            return;
        }
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(), userId);
        ConvModule newConvModule = convModuleMapper.vo2ConvModuleDo(JacksonUtil.deepCopy(convModuleVO, ConvModuleVO.class));
        newConvModule.setAppAccountId(appAccount.getId());
        newConvModule.setEnabled(isOpen);
        dictService.sendConvModuleData(newConvModule, convModuleVO, appAccount.getAppUserId(), userId);
        saveOrUpdate(newConvModule);
    }

    /**
     * 取且仅取 当前用户 正绑定的 词典AppAccount 对应的转化工具信息
     *
     * @param type
     * @param userId
     * @return
     */
    @Override
    public ConvModuleVO getByUserIdAndType(Integer type, Long userId) {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndUserId(PlatformEnum.YOUDAO_DICT.getName(), userId);
        if (Objects.isNull(appAccount)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "当前用户没有绑定有道词典");
        }
        return convModuleMapper.doToConvModuleVo(baseMapper.getByTypeAppAccountIdAndCreator(type, appAccount.getId(), userId));
    }

    @Override
    public List<ConvModule2DictDTO> getAllTypeByAppAccountId(Long appAccountId, Long userId) {
        return convModuleMapper.doToConvModule2DictDTO(baseMapper.getByAppAccountIdAndUserId(appAccountId, userId));
    }

    private ConvModuleSaveOrUpdateVO saveOrUpdatePreCheckAndBuild(ConvModuleSaveOrUpdateVO convModuleSaveOrUpdateVO) {
        if (convModuleSaveOrUpdateVO.getType().equals(OFFICIAL_WEBSITE) || convModuleSaveOrUpdateVO.getType().equals(PROMOTION_ACTIVITY)) {
            checkWechatMicroProgramParams(convModuleSaveOrUpdateVO.getEnabledWechatMicroProgramSwitch(),
                    convModuleSaveOrUpdateVO.getWechatMicroProgramRawId(),
                    convModuleSaveOrUpdateVO.getWechatMicroProgramPath());
        }
        ConvModuleSaveOrUpdateVO.ConvModuleSaveOrUpdateVOBuilder builder = ConvModuleSaveOrUpdateVO.builder();
        switch (convModuleSaveOrUpdateVO.getType()) {
            case OFFICIAL_WEBSITE:
                if (ObjectUtils.anyNull(convModuleSaveOrUpdateVO.getUrl(),
                        convModuleSaveOrUpdateVO.getEnabledWechatMicroProgramSwitch())) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS);
                }
                builder.displayName(OFFICIAL_WEBSITE_DISPLAY_NAME)
                        .url(convModuleSaveOrUpdateVO.getUrl())
                        .enabledWechatMicroProgramSwitch(convModuleSaveOrUpdateVO.getEnabledWechatMicroProgramSwitch())
                        .wechatMicroProgramRawId(convModuleSaveOrUpdateVO.getWechatMicroProgramRawId())
                        .wechatMicroProgramPath(convModuleSaveOrUpdateVO.getWechatMicroProgramPath());
                break;
            case APPS_DOWNLOAD:
                if (ObjectUtils.allNull(convModuleSaveOrUpdateVO.getAndroidPackageName(), convModuleSaveOrUpdateVO.getIosDownloadUrl())) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS, "安卓包名、iOS下载链接至少写一个");
                }
                builder.displayName(APPS_DOWNLOAD_DISPLAY_NAME)
                        .androidPackageName(convModuleSaveOrUpdateVO.getAndroidPackageName())
                        .iosDownloadUrl(convModuleSaveOrUpdateVO.getIosDownloadUrl());
                break;
            case CONTACT:
                if (ObjectUtils.anyNull(convModuleSaveOrUpdateVO.getPhone())
                        && ArrayUtils.contains(CONTACT_DISPLAY_NAMES, convModuleSaveOrUpdateVO.getDisplayName())) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS);
                }
                builder.displayName(convModuleSaveOrUpdateVO.getDisplayName())
                        .phone(convModuleSaveOrUpdateVO.getPhone());
                break;
            case PROMOTION_ACTIVITY:
                if (ObjectUtils.anyNull(convModuleSaveOrUpdateVO.getUrl(),
                        convModuleSaveOrUpdateVO.getEnabledWechatMicroProgramSwitch())) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS);
                }
                builder.displayName(PROMOTION_ACTIVITY_DISPLAY_NAME)
                        .url(convModuleSaveOrUpdateVO.getUrl())
                        .enabledWechatMicroProgramSwitch(convModuleSaveOrUpdateVO.getEnabledWechatMicroProgramSwitch())
                        .wechatMicroProgramRawId(convModuleSaveOrUpdateVO.getWechatMicroProgramRawId())
                        .wechatMicroProgramPath(convModuleSaveOrUpdateVO.getWechatMicroProgramPath());
                break;
            default:
                throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
        return builder.type(convModuleSaveOrUpdateVO.getType())
                .id(convModuleSaveOrUpdateVO.getId())
                .enabled(convModuleSaveOrUpdateVO.isEnabled())
                .build();
    }

    private void checkWechatMicroProgramParams(Boolean enabledWechatMicroProgramSwitch, String rawId, String path) {
        //enabledWechatMicroProgramSwitch == null || == false    rawId path should null/blank
        //enabledWechatMicroProgramSwitch == true                rawId path should have value
        if (Boolean.TRUE.equals(enabledWechatMicroProgramSwitch) && StringUtils.isAnyBlank(rawId, path) ||
                (Boolean.FALSE.equals(enabledWechatMicroProgramSwitch) || null == enabledWechatMicroProgramSwitch) &&
                        StringUtils.isNotBlank(rawId) && StringUtils.isNotBlank(path)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS);
        }
    }
}

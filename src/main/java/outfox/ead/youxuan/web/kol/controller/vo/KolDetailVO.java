package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年02月22日 7:38 下午
 */
@Data
public class KolDetailVO {
    @ApiModelProperty("达人优选id")
    private Long userId;

    @ApiModelProperty("达人优选昵称")
    private String nickname;

    @ApiModelProperty("账户类型")
    private String role;

    @ApiModelProperty("媒体平台")
    private String platform;

    @ApiModelProperty("媒体账户id")
    private Long appAccountId;

    @ApiModelProperty("媒体账户昵称")
    private String name;

    @ApiModelProperty("媒体平台名称")
    private String platformName;

    @ApiModelProperty("媒体账户头像")
    private String avatar;

    @ApiModelProperty("性别，0-男 1-女")
    private Integer gender;

    @ApiModelProperty("粉丝数")
    private Long fansNum;

    @ApiModelProperty("地区")
    private String area;

    @ApiModelProperty("所属mcn")
    private String mcn;

    @ApiModelProperty
    private List<ContentTagListVO> contentTags;
}

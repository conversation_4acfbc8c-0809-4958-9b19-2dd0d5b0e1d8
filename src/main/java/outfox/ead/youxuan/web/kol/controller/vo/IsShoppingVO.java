package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022年10月18日 17:25
 */
@Data
public class IsShoppingVO {
    @NotNull(message = "商品id不能为空")
    @ApiModelProperty("商品Id")
    private String componentId;
    @NotNull(message = "yduuid不能为空")
    @ApiModelProperty("有道uuid，用户未登陆使用该值作为唯一标识")
    private String yduuid;
}

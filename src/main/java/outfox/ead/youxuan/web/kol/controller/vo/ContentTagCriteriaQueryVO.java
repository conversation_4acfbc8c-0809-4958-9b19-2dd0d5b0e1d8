package outfox.ead.youxuan.web.kol.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class ContentTagCriteriaQueryVO {
    @Range(min = 0, max = 1, message = "状态错误")
    @ApiModelProperty("状态 0-有效 1-无效 全部不传这个参数")
    private Integer status;
    @NotNull
    @ApiModelProperty("页数")
    private Long current;
    @NotNull
    @ApiModelProperty("条数")
    private Long size;
}

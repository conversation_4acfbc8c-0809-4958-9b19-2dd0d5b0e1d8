package outfox.ead.youxuan.web.ad.controller.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import outfox.ead.youxuan.constants.ElementKey;

import java.util.Comparator;
import java.util.HashSet;
import java.util.Set;

/**
 * 创意内容
 *
 * <AUTHOR> <EMAIL>
 * @date 2021/8/26 20:31
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreativeContent implements Comparable<CreativeContent> {
    @ApiModelProperty("样式元素名称,用于开发者和发布系统前端展示")
    private String name;
    @ApiModelProperty("样式元素key，用于解析")
    private String elementKey;
    @ApiModelProperty("样式元素类型，1:文字;2:图片;3:视频")
    private Integer type;
    @EqualsAndHashCode.Exclude
    private Set<CreativeContentProperty> properties = new HashSet<>();

    public void copyOldData(CreativeContent creativeContent) {
        for (CreativeContentProperty property : properties) {
            if (creativeContent.getProperties().contains(property)) {
                for (CreativeContentProperty oldProperty : creativeContent.getProperties()) {
                    if (oldProperty.equals(property)) {
                        property.setOldData(oldProperty);
                        break;
                    }
                }
            }
        }
    }

    @Override
    public int compareTo(CreativeContent o) {
        ElementKey thisElem = ElementKey.getEnum(this.getElementKey());
        ElementKey other = ElementKey.getEnum(o.getElementKey());
        if (thisElem.equals(ElementKey.UNKNOWN) && other.equals(ElementKey.UNKNOWN)) {
            return this.getElementKey().compareTo(o.getElementKey());
        }
        return Comparator
                .comparing(ElementKey::getLevel)
                .thenComparing(ElementKey::getSubLevel)
                .thenComparing(ElementKey::getValue)
                .compare(thisElem, other);
    }

}

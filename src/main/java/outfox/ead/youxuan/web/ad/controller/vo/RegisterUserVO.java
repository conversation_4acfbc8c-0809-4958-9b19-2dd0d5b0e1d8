package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022年08月12日 15:40
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RegisterUserVO {
    private Long id;
    private String username;
    @ApiModelProperty("词典uid")
    private String dictUid;
    private String roleKey;
    private String nickname;
    private String avatar;
}

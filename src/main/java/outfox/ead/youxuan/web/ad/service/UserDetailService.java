package outfox.ead.youxuan.web.ad.service;

import lombok.NonNull;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.UserDetail;
import outfox.ead.youxuan.web.ad.controller.vo.QualificationInfoVO;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【UserDetail】的数据库操作Service
 * @date 2022-01-28 14:10:32
 */
public interface UserDetailService extends YouxuanService<UserDetail> {

    /**
     * 上传头像
     *
     * @param avatar      图片地址
     * @param currentRole
     */
    void uploadAvatar(String avatar, Role currentRole);

    /**
     * 通过userId获取数据
     *
     * @param userId      用户id
     * @param currentRole
     * @return UserDetail
     */
    UserDetail getByUserId(Long userId,@NonNull Role currentRole);

    /**
     * 新增或修改数据
     *
     * @param userDetail  -
     * @param currentRole
     */
    void insertOrUpdate(UserDetail userDetail, Role currentRole);

    /**
     * 开启指派任务
     *
     * @param userId      用户id
     * @param currentRole
     */
    void openTaskPermission(Long userId, Role currentRole);

    /**
     * 开启商品橱窗
     *
     * @param userId      用户id
     * @param currentRole
     */
    void openProductWindow(Long userId, Role currentRole);

    /**
     * 计算并且更新stage
     * 在rebind和unbind的时候情况比较复杂，直接计算stage
     *
     * @param userId      用户id
     * @param currentRole
     */
    void calStageAndUpdate(Long userId, Role currentRole);

    Collection<UserDetail> listByUserIdsAndRole(List<Long> userIds, Role currentRole);

    /**
     * 多用于查询几种互斥角色的集合，例如查询个人创作者和品牌号
     */
    Collection<UserDetail> listByUserIdsAndRoles(Collection<Long> userIds, Collection<Role> roles);

    List<Long> listUserIdByNickName(String name);

    /**
     * 开启投稿任务权限
     *
     * @param userId      用户id
     * @param currentRole
     */
    void openPostTaskPermission(Long userId, Role currentRole);

    List<UserDetail> listByUserIdAndSyncStatus(List<Long> kolUserIds, Boolean isSync);

    Boolean getSyncStatus(Long userId, Role currentRole);


    Boolean updateSyncStatus(Long userId, Boolean isSync);

    void closePostTaskPermission(Long userId, Role currentRole);

    QualificationInfoVO listQualificationContact(Long userId, Role currentRole);

}

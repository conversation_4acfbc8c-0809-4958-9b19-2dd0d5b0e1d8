package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.User;
import outfox.ead.youxuan.web.ad.controller.bo.*;
import outfox.ead.youxuan.web.ad.controller.dto.UserDetails;
import outfox.ead.youxuan.web.ad.controller.vo.LoginResponseVO;

import java.util.Collection;
import java.util.Set;

@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface UserMapper {
    User UserDetails2User(UserDetails userDetails);

    User sponsorTemplate2User(SponsorTemplate sponsorTemplate);

    User brandKolTemplate2User(BrandKolTemplate brandKolTemplate);

    User kolTemplate2User(KolTemplate kolTemplate);

    @Mapping(source = "user.id", target = "id")
    LoginUser do2LoginUser(User user, Collection<Role> roles, Role currentRole);

    @Mapping(source = "user.id", target = "userId")
    LoginResponseVO do2LoginResponseVO(User user, String token, Set<String> role, String currentRole);

    LoginUserVO loginUser2Vo(LoginUser loginUser);
}

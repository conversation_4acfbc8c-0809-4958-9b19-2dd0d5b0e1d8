package outfox.ead.youxuan.web.ad.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.vo.CustomCrowdPackPageVO;
import outfox.ead.youxuan.web.ad.controller.vo.CustomCrowdPackQueryVO;
import outfox.ead.youxuan.web.ad.controller.vo.UpdateStatusVO;
import outfox.ead.youxuan.web.ad.service.CustomCrowdPackService;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;

@RestController
@RequestMapping("/custom-crowd-pack")
@BaseResponse
@AllArgsConstructor
@Api(tags = "自定义人群包")
@Slf4j
public class CustomCrowdPackController {
    private final CustomCrowdPackService customCrowdPackService;

    @ApiOperation("分页列表查询")
    @GetMapping
    public IPage<CustomCrowdPackPageVO> getAdGroupList(@Valid CustomCrowdPackQueryVO vo) {
        return customCrowdPackService.pageByVo(vo);
    }

    @ApiOperation("保存人群包")
    @PostMapping
    public void saveCustomCrowdPack(@RequestParam(required = false) Long id,
                                    @RequestParam @Length(max = 25) @NotNull String name,
                                    @RequestParam @ApiParam("设备号类型 0:idfa 1:idfa-MD5 2:IMEI 3:IMEI-MD5 4:OAID 5:OAID-MD5 6:YDID") Integer deviceIdType,
                                    @RequestPart("file") MultipartFile file) throws IOException {
        customCrowdPackService.save(id, name, deviceIdType, file);
    }

    @ApiOperation("修改人群包状态")
    @PutMapping
    public void updateStatus(@RequestBody UpdateStatusVO updateStatusVO) {
        customCrowdPackService.updateStatus(updateStatusVO.getId(), updateStatusVO.getStatus());
    }

    @ApiOperation("名字重复查询")
    @GetMapping("nameExists")
    public Boolean nameExists(@RequestParam(required = false) Long id, String name) {
        return customCrowdPackService.nameExists(id, name);
    }
}

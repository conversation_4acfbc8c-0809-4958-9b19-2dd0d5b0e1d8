package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UnionFrequencyRuleCriteriaQueryVO {
    @ApiModelProperty(value = "当前页面", required = true)
    @NotNull(message = "当前页面不能为空")
    private Long current;

    @ApiModelProperty(value = "页面大小", required = true)
    @NotNull(message = "页面大小不能为空")
    private Long size;

    @ApiModelProperty("推广计划id")
    private Long adPlanId;

    @ApiModelProperty("规则名称")
    private String name;

    @ApiModelProperty("联合频控状态")
    private List<Integer> status;

    @ApiModelProperty("联合频控id")
    private Long unionFrequencyRuleId;
}
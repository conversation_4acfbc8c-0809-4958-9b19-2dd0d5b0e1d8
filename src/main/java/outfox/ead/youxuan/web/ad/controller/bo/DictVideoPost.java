package outfox.ead.youxuan.web.ad.controller.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022年03月11日 10:48 上午
 */
@Data
public class DictVideoPost {
    /**
     * 发布文章所需属性
     */
    private String title;
    private String text;
    private String coverImage;
    private Integer coverImageWidth;
    private Integer coverImageHeight;
    private String video;

    /**
     * 生成用户所需属性
     */
    @EqualsAndHashCode.Exclude
    private String appName;
    @EqualsAndHashCode.Exclude
    private String iconImage;

    private Long relationId;
    /**
     * 词典uid
     */
    private String uid;
    /**
     * 发布内容id
     */
    private String dictPostId;

    @EqualsAndHashCode.Exclude
    private Set<String> historyDictPostIds;
    /**
     * 青少年可见
     */
    @EqualsAndHashCode.Exclude
    private Boolean youthVisible;
}

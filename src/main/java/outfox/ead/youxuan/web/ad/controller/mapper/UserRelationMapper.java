package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import outfox.ead.youxuan.entity.UserRelation;
import outfox.ead.youxuan.web.ad.controller.vo.UserRelationVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年02月10日 11:51 上午
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface UserRelationMapper {
    List<UserRelationVO> userRelation2VO(List<UserRelation> userRelation);

    UserRelationVO userRelation2VO(UserRelation userRelation, Long boundUserId, Long userId);
}

package outfox.ead.youxuan.web.ad.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.web.ad.service.ImageService;

import java.util.Collections;

import static org.springframework.http.MediaType.ALL;

@Service
@Slf4j
@RequiredArgsConstructor
public class OimageServiceImpl implements ImageService {
    private final String URL = "http://oimagea1.youdao.com/upload?method=uploadImage&product=adpublish&dont_redirect=true";
    private final RestTemplate restTemplate;

    @SneakyThrows
    @Override
    public String upload(MultipartFile file) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setAccept(Collections.singletonList(ALL));
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", file.getResource());
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(URL, HttpMethod.POST, requestEntity, String.class);
            HttpStatus statusCode = responseEntity.getStatusCode();
            if (statusCode == HttpStatus.OK) {
                log.info("Oimage upload success, response: {}", responseEntity.getBody());
                return responseEntity.getBody();
            } else {
                throw new CustomException(ResponseType.SERVICE_ERROR, "Oimage服务上传失败");
            }
        } catch (Exception e) {
            log.error("Oimage服务上传失败", e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "Oimage服务上传失败");
        }
    }
}

package outfox.ead.youxuan.web.ad.controller.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 用于解析 批量创建广告计划和推广组的excel
 *
 * <AUTHOR>
 */
@Data
public class BatchAdPlanExcel {
    @ExcelProperty(value = "推广计划名称")
    @NotNull(message = "推广计划名称不能为空")
    private String adPlanName;
    @ExcelProperty(value = "投放日期")
    @NotNull(message = "投放日期不能为空")
    private String date;
    @ExcelProperty(value = "地域定向")
    @NotNull(message = "地域定向不能为空")
    private String area;
    @ExcelProperty(value = "推广组名称")
    @NotNull(message = "推广组名称不能为空")
    private String adGroupName;
    @ExcelProperty(value = "订单ID")
    @NotNull(message = "订单ID不能为空")
    private String dealId;
    @ExcelProperty(value = "日展示量上限")
    @NotNull(message = "日展示量上限不能为空")
    private Integer dailyDisplayLimit;
    @ExcelProperty(value = "总展示量")
    @NotNull(message = "总展示量不能为空")
    private Integer sumDisplayCount;
}

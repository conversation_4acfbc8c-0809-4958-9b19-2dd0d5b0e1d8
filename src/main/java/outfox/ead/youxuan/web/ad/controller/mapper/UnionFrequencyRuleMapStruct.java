package outfox.ead.youxuan.web.ad.controller.mapper;


import org.mapstruct.*;
import outfox.ead.youxuan.entity.UnionFrequencyRule;
import outfox.ead.youxuan.entity.User;
import outfox.ead.youxuan.web.ad.controller.vo.UnionFrequencyRuleByIdVO;
import outfox.ead.youxuan.web.ad.controller.vo.UnionFrequencyRulePageVO;
import outfox.ead.youxuan.web.ad.controller.vo.UnionFrequencyRuleSaveOrUpdateVO;

import java.util.Map;


@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface UnionFrequencyRuleMapStruct {
    @Mapping(expression = "java(id2User.get(rule.getCreator()).getUsername())", target = "creator")
    @Mapping(expression = "java(id2User.get(rule.getModifier()).getUsername())", target = "modifier")
    UnionFrequencyRulePageVO do2PageVo(UnionFrequencyRule rule, Map<Long, User> id2User);

    @Mapping(expression = "java(id2User.get(rule.getCreator()).getUsername())", target = "creator")
    @Mapping(expression = "java(id2User.get(rule.getModifier()).getUsername())", target = "modifier")
    UnionFrequencyRuleByIdVO do2ByIdVo(UnionFrequencyRule rule, Map<Long, User> id2User);

    UnionFrequencyRule saveOrUpdateVoToDo(UnionFrequencyRuleSaveOrUpdateVO saveOrUpdateVo);


}

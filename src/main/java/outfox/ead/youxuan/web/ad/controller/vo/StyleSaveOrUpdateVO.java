package outfox.ead.youxuan.web.ad.controller.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.core.handler.mybatis.StyleElementTypeHandler;
import outfox.ead.youxuan.web.ad.controller.dto.StyleElement;

import javax.validation.constraints.*;
import java.util.List;
import java.util.Set;

/**
 * @author: 李梦杰
 * @date: 2021/8/24/15:50
 * @description:
 */
@Data
public class StyleSaveOrUpdateVO {
    @ApiModelProperty("样式主键")
    private Long id;

    @ApiModelProperty("广告位ID")
    @NotNull(message = "广告位ID不能为空")
    private Long adPositionId;

    @ApiModelProperty("样式名称")
    @NotBlank
    @Size(max = 50, message = "样式名称不能超过50个字符")
    private String name;

    @ApiModelProperty("可兼容的最低版本号")
    @NotBlank
    private String minVersionSupported;


    @ApiModelProperty("自定义展示时间，单位s")
    @NotNull(message = "展示时间不能为空")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Integer> showTime;
    @ApiModelProperty("样式类型，0-无，1，原生开屏样式，2，摇一摇样式")
    private Integer styleType;

    @ApiModelProperty("样式元素")
    @TableField(value = "PIC_STYLE_CONTENT", typeHandler = StyleElementTypeHandler.class)
    private List<StyleElement> picStyleContent;
    @TableField(value = "TEXT_STYLE_CONTENT", typeHandler = StyleElementTypeHandler.class)
    private List<StyleElement> textStyleContent;
    @TableField(value = "VIDEO_STYLE_CONTENT", typeHandler = StyleElementTypeHandler.class)
    private List<StyleElement> videoStyleContent;
    @ApiModelProperty("状态,0-有效，1-暂停,2-已删除")
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态类型错误")
    @Max(value = 2, message = "状态类型错误")
    private Integer status;

    @ApiModelProperty("是否全屏，0-否，1-是")
    private Boolean fullScreen;

    @ApiModelProperty("开屏回收 0-不支持 1-支持")
    @Min(value = 0, message = "开屏回收类型错误")
    @Max(value = 1, message = "开屏回收类型错误")
    private Integer openScreenRecycling;

    @ApiModelProperty("开机首刷 0-不支持 1-支持")
    @Min(value = 0, message = "开机首刷类型错误")
    @Max(value = 1, message = "开机首刷类型错误")
    private Integer bootFirstRefresh;

    @ApiModelProperty("是否接受开屏广告 0-否 1-是")
    @Min(value = 0, message = "开屏广告类型错误")
    @Max(value = 1, message = "开屏广告类型错误")
    private Integer receiveOpenScreenRecycling;

    @ApiModelProperty("是否开启摇一摇")
    private Boolean shakable;
}

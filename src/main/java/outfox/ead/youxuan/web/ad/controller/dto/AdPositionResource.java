package outfox.ead.youxuan.web.ad.controller.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/24 10:01
 */
@Data
public class AdPositionResource {
    private Long id;
    private String name;
    @ApiModelProperty("总轮播数")
    private Integer carouselStock;
    @ApiModelProperty("剩余轮播数")
    private Integer carouseRemain;
    private List<StyleResource> styleList;
    @ApiModelProperty("广告位类型 0-信息流 1-开屏 2-插屏 3-焦点图 4-激励视频 5-横幅 6-自定义")
    private Integer type;
}

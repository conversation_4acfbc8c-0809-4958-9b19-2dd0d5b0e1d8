package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.web.ad.service.RoleService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年01月27日 2:47 下午
 */
@BaseResponse
@RestController
@RequestMapping("/role")
@AllArgsConstructor
@Validated
@Api(tags = "角色")
@AccessControl(roles = RoleEnum.ADMIN)
public class RoleController {
    private final RoleService roleService;

    @PostMapping
    public void saveOrUpdate(Role role) {
        roleService.saveOrUpdate(role);
    }

    @GetMapping
    public Role getRole(Long id) {
        return roleService.getById(id);
    }

    @GetMapping("/list")
    @AccessControl(roles = RoleEnum.AUDIT_OPERATOR)
    public List<Role> getRoleList() {
        return roleService.list();
    }
}

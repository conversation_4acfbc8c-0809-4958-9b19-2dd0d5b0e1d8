package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: 李梦杰
 * @date: 2021/8/23/18:25
 * @description:
 */
@Data
public class MediaCriteriaQueryVO {
    @ApiModelProperty(value = "当前页面", required = true)
    @NotNull(message = "当前页面不能为空")
    private Long current;
    @ApiModelProperty(value = "页面大小", required = true)
    @NotNull(message = "页面大小不能为空")
    private Long size;

    private String id;

    private String name;
    @ApiModelProperty(value = "状态")
    private Integer status;
}

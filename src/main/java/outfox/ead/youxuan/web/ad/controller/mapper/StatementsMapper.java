package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.core.thirdParty.druid.StatementTimeSeriesStatistic;
import outfox.ead.youxuan.web.ad.controller.vo.AccountStatementsResponseVO;

@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface StatementsMapper {
    @Mapping(target = "clickRate",expression = "java(String.format(\"%,.4f\",event.getClickRate()))")
    AccountStatementsResponseVO toAccountStatementsResponseVO(StatementTimeSeriesStatistic.Event event);
}

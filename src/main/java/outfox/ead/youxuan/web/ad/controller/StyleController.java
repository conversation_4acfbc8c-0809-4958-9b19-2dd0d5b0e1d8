package outfox.ead.youxuan.web.ad.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.StyleService;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * @author: 李梦杰
 * @date: 2021/8/19/12:25
 * @description:
 */
@BaseResponse
@RestController
@AllArgsConstructor
@Validated
@Api(tags = {"样式"})
@RequestMapping("/style")
@AccessControl(roles = RoleEnum.AD_OPERATOR)
public class StyleController {
    private final StyleService styleService;

    @GetMapping("/{id}")
    public StyleByIdVO getStyle(@PathVariable("id") @NotNull(message = "id不能为空") Long id) {
        return styleService.getVoById(id);
    }

    /**
     * 条件查询样式
     *
     * @param style 样式查询属性
     * @return 样式列表
     */
    @GetMapping
    public IPage<StyleListVO> getStyleList(StyleCriteriaQueryVO style) {
        return styleService.pageList(style);
    }

    /**
     * 插入/修改样式
     *
     * @param style 样式
     */
    @PostMapping
    public Long saveOrUpdateStyle(@Valid @RequestBody StyleSaveOrUpdateVO style) {
        return styleService.saveOrUpdate(style);
    }

    /**
     * 批量修改样式状态
     *
     * @param styles 样式列表
     */
    @PutMapping("/batch_update_status")
    public String batchUpdateStatus(@RequestBody @Valid BatchUpdateStatusVO styles) {
        return styleService.batchUpdateStatus(styles);

    }

    /**
     * 校验名字是否重复
     *
     * @param countByNameVO 样式名字
     * @return apiResponse
     */
    @GetMapping("/name_repeat")
    public void repeatMediaName(StyleCountByNameVO countByNameVO) {
        styleService.nameRepeat(countByNameVO);
    }
}

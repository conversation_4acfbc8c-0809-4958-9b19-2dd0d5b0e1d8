package outfox.ead.youxuan.web.ad.controller.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: 李梦杰
 * @date: 2021/8/23/18:29
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdPositionByIdVO {
    @ApiModelProperty("广告位主键")
    private Long id;

    @ApiModelProperty("媒体ID")
    private Long mediaId;

    @ApiModelProperty("广告位名称")
    private String name;

    @ApiModelProperty("广告位类型")
    private Integer type;

    @ApiModelProperty("支持轮播图片数量")
    private Integer displayTimes;

    @ApiModelProperty("推广标类型")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> promotionType;

    @ApiModelProperty("是否支持查词定向")
    private boolean supportAdvertisingByKeywords;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("是否有在投的推广组,0-无，1-有")
    private Integer adGroupDelivering;

    @ApiModelProperty("是否支持摇一摇")
    private Boolean hasShakable;
}

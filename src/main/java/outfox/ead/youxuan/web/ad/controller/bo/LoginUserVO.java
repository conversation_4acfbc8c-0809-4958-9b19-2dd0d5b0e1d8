package outfox.ead.youxuan.web.ad.controller.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import outfox.ead.youxuan.entity.Role;

import java.util.Collection;


/**
 * 登录用户
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginUserVO {
    /**
     * 用户id
     */
    private Long id;

    /**
     * 163邮箱
     */
    private String username;
    private String dictUid;

    /**
     * 角色
     */
    private Collection<Role> roles;
}

package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import outfox.ead.youxuan.entity.AdContent;
import outfox.ead.youxuan.web.ad.controller.vo.AdContentSaveOrUpdateVO;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/15 19:11
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface AdContentMapper {
    List<AdContentSaveOrUpdateVO> doListToAdContentSaveOrUpdateVO(List<AdContent> adContents);

    AdContent saveOrUpdateVoToDo(AdContentSaveOrUpdateVO adContentSaveOrUpdateVO);
}

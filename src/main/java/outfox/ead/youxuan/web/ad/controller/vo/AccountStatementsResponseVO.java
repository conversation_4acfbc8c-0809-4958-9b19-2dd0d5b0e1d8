package outfox.ead.youxuan.web.ad.controller.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2021/9/13/11:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class AccountStatementsResponseVO implements Cloneable {
    @EqualsAndHashCode.Include
    @ExcelProperty(value = "时间", order = 1)
    @ApiModelProperty("时间")
    private String dateTime;
    @EqualsAndHashCode.Include
    @ExcelProperty(value = "推广组Id", order = 2)
    @ApiModelProperty("推广组Id")
    private Long adGroupId;

    @ExcelProperty(value = "推广组", order = 3)
    @ApiModelProperty("推广组")
    private String adGroupName;
    @EqualsAndHashCode.Include
    @ExcelProperty(value = "推广计划Id", order = 4)
    @ApiModelProperty("推广计划Id")
    private Long adPlanId;

    @ExcelProperty(value = "推广计划", order = 5)
    @ApiModelProperty("推广计划")
    private String adPlanName;

    @ExcelProperty(value = "客户名称", order = 6)
    @ApiModelProperty("客户名称")
    private String customerName;

    @ExcelProperty(value = "推广目标", order = 7)
    @ApiModelProperty("推广目标 1-落地页推广 2-应用直达 3-小程序推广")
    private Integer promotionTarget;

    @ExcelProperty(value = "样式", order = 10)
    @ApiModelProperty("样式")
    private String styleName;

    @ExcelProperty(value = "广告位", order = 9)
    @ApiModelProperty("广告位")
    private String adPositionName;

    @ExcelProperty(value = "媒体", order = 8)
    @ApiModelProperty("媒体")
    private String mediaName;

    @ExcelProperty(value = "展示数", order = 11)
    private Long impr = 0L;

    @ApiModelProperty("点击数")
    @ExcelProperty(value = "点击数", order = 12)
    private Long click = 0L;

    @ApiModelProperty("点击率")
    @ExcelProperty(value = "点击率", order = 13)
    private String clickRate;

    @ApiModelProperty("展示独立设备数")
    @ExcelProperty(value = "展示独立设备数", order = 14)
    private Long ui = 0L;

    @ApiModelProperty("点击独立设备数")
    @ExcelProperty(value = "点击独立设备数", order = 15)
    private Long uc = 0L;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}

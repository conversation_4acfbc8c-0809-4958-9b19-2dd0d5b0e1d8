package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.web.ad.controller.dto.DateSlot;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/24 14:16
 */
@Data
public class ResourceSalesDetailsVO {
    @ApiModelProperty("计划名称")
    private String adPlanName;
    @ApiModelProperty("客户名称")
    private String customerName;
    @ApiModelProperty("组名称")
    private String adGroupName;
    @ApiModelProperty("投放位置")
    private String deliveryPosition;
    @ApiModelProperty("广告创意")
    private String previewImage;
    private Integer status;
    private List<DateSlot> adDeliverySlot;
    @ApiModelProperty("轮播数")
    private Integer displayWeight;
    private LocalDateTime time;
}

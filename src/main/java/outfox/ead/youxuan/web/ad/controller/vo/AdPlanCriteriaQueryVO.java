package outfox.ead.youxuan.web.ad.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.joda.time.LocalDate;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/23 16:04
 */
@Data
public class AdPlanCriteriaQueryVO {
    @ApiModelProperty("推广计划id")
    private String id;
    @ApiModelProperty("推广计划id列表")
    private List<String> ids;
    @ApiModelProperty("推广计划名称")
    private String name;
    @ApiModelProperty("客户名称")
    private String customerName;
    @ApiModelProperty("客户id")
    private Long customerId;
    @ApiModelProperty("计划状态")
    private List<Integer> status;
    @ApiModelProperty("推广目标 1-落地页推广 2-应用直达 3-小程序推广")
    private List<Integer> promotionTarget;
    @ApiModelProperty("计费方式 0-CPT 1-CPM")
    private List<Integer> billingType;
    @ApiModelProperty("投放方式")
    private List<Long> deliveryType;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("druid查询开始时间")
    private LocalDate adOpenDate;
    @ApiModelProperty("druid查询结束时间")
    private LocalDate adCloseDate;
    @NotNull
    private Long current;
    @NotNull
    private Long size;
    @ApiModelProperty("是否存在联合频控规则 true-存在 false-不存在")
    private Boolean existUnionFrequencyRule;
}

package outfox.ead.youxuan.web.ad.controller.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/10 11:41
 */
@Data
@EqualsAndHashCode
public class ResourceDetail {
    @ApiModelProperty("广告位Id")
    private Long adPositionId;
    @ApiModelProperty("时间")
    private LocalDateTime dateTime;
    @ApiModelProperty("已售 CPM")
    @EqualsAndHashCode.Exclude
    private Long sale;
    /**
     * 购买的轮播数
     */
    @EqualsAndHashCode.Exclude
    private Integer weight;
    @EqualsAndHashCode.Exclude
    private Integer status;
}

package outfox.ead.youxuan.web.ad.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.multipart.MultipartFile;
import outfox.ead.youxuan.entity.CustomCrowdPack;
import outfox.ead.youxuan.web.ad.controller.vo.CustomCrowdPackPageVO;
import outfox.ead.youxuan.web.ad.controller.vo.CustomCrowdPackQueryVO;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description 针对表【CustomCrowdPack(自定义人群包)】的数据库操作Service
 * @createDate 2023-08-30 11:34:25
 */
public interface CustomCrowdPackService extends YouxuanService<CustomCrowdPack> {

    IPage<CustomCrowdPackPageVO> pageByVo(CustomCrowdPackQueryVO vo);

    void save(Long id, String name, Integer deviceIdType, MultipartFile file) throws IOException;

    void updateStatus(Long id, Integer status);

    Boolean nameExists(Long id, String name);
}
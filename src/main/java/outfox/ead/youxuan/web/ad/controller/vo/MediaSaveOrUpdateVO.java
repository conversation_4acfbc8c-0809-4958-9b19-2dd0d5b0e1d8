package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @author: 李梦杰
 * @date: 2021/8/24/15:49
 * @description:
 */
@Data
public class MediaSaveOrUpdateVO {
    @ApiModelProperty("媒体主键")
    private Long id;

    @ApiModelProperty("媒体名称")
    @NotNull
    private String name;

    @ApiModelProperty("平台")
    @NotNull(message = "平台不能为空")
    @Min(value = 0, message = "平台类型错误")
    @Max(value = 2, message = "平台类型错误")
    private Integer osType;

    @ApiModelProperty("备注")
    private String note;

    @ApiModelProperty("状态,0-有效，1-暂停,2-已删除")
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态错误")
    @Max(value = 2, message = "状态错误")
    private Integer status;

    @ApiModelProperty("是否屏蔽青少年，0-否，1-是")
    private Boolean youthMode;
}

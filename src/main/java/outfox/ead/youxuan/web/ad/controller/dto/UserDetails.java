package outfox.ead.youxuan.web.ad.controller.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年01月28日 11:51 上午
 */
@Data
public class UserDetails {
    @ApiModelProperty("用户名")
    @Pattern(regexp = "(\\w){4,20}@163\\.com$", message = "请使用网易163邮箱注册")
    private String username;
    @ApiModelProperty(
            "广告主 - sponsor " +
                    "个人创作者 - kol " +
                    "机构创作者 - brandKol " +
                    "投放运营人员 - adOperator " +
                    "创作运营人员 - kolOperator " +
                    "审核运营 - auditOperator"+
                    "超管 - admin")
    @Size(max = 1, message = "目前用户角色只支持1个")
    private List<String> roles;
}

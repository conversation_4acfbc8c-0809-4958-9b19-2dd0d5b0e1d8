package outfox.ead.youxuan.web.ad.controller.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.entity.Role;

import java.util.Collection;
import java.util.Objects;


/**
 * 登录用户
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginUser {
    /**
     * 用户id
     */
    private Long id;

    /**
     * 163邮箱
     */
    private String username;
    private String dictUid;

    /**
     * 角色
     */
    private Collection<Role> roles;

    private Role currentRole;

    public boolean isAdmin() {
        return Objects.nonNull(currentRole) && Objects.equals(currentRole.getRoleKey(), RoleEnum.ADMIN.getRoleKey());
    }

    public boolean checkRole(String roleKey) {
        return roles.stream().anyMatch(a -> a.getRoleKey().equals(roleKey));
    }

    @Override
    public String toString() {
        return "LoginUser{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", dictUid='" + dictUid + '\'' +
                ", roles=" + roles +
                ", currentRole=" + currentRole +
                '}';
    }
}

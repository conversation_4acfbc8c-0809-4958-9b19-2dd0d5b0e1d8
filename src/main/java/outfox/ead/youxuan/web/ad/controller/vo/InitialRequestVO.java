package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.constants.InteractionTypeEnum;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/24 16:24
 */
@Data
public class InitialRequestVO {
    @ApiModelProperty(value = "样式id列表")
    List<Long> styleIds;
    @ApiModelProperty(value = "推广计划id")
    @NotNull(message = "推广计划id不能为空")
    Long adPlanId;
    @ApiModelProperty(value = "推广组id")
    Long adGroupId;
    @ApiModelProperty(value = "0-修改，1-复制")
    Integer mode;
    /**
     * {@link InteractionTypeEnum#getCode()}
     */
    @ApiModelProperty("交互类型, 0:无，1:摇一摇，2:滑动互动，3:双link，4:三link,5:扭一扭")
    Integer interactionType;
}

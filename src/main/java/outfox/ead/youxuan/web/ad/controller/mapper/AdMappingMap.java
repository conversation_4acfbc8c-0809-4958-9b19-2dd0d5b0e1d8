package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.AdGroup;
import outfox.ead.youxuan.entity.AdMapping;
import outfox.ead.youxuan.entity.User;
import outfox.ead.youxuan.web.ad.controller.response.AdMappingPageResponse;
import outfox.ead.youxuan.web.ad.controller.vo.AdMappingSaveVO;
import outfox.ead.youxuan.web.ad.controller.vo.UpdateStatusVO;

import java.util.Map;

@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface AdMappingMap {

    @Mapping(expression = "java(id2AdGroup.get(adMapping.getAdGroupId()).getName())", target = "adGroupName")
    @Mapping(expression = "java(id2AdGroup.get(adMapping.getAdGroupId()).getStatus())", target = "adGroupStatus")
    @Mapping(expression = "java(id2Customer.get(adPlanId2CustomerId.get(id2AdGroup.get(adMapping.getAdGroupId()).getAdPlanId())))", target = "customerName")
    @Mapping(expression = "java(String.valueOf(adPlanId2CustomerId.get(id2AdGroup.get(adMapping.getAdGroupId()).getAdPlanId())))", target = "customer")
    @Mapping(expression = "java(id2User.get(adMapping.getCreator()).getUsername())", target = "creatorName")
    @Mapping(expression = "java(id2User.get(adMapping.getModifier()).getUsername())", target = "modifier")
    @Mapping(expression = "java(styleString.get(adMapping.getSourceStyleId()))", target = "sourceStyle")
    @Mapping(expression = "java(styleString.get(adMapping.getMappingStyleId()))", target = "mappingStyle")
    AdMappingPageResponse vo2AdMappingPageResponse(AdMapping adMapping, Map<Long, AdGroup> id2AdGroup, Map<Long, User> id2User, Map<Long, Long> adPlanId2CustomerId, Map<Long, String> id2Customer, Map<Long, String> styleString);

    AdMapping saveVO2DO(AdMappingSaveVO adMappingSaveVO);

    AdMapping updateStatusVO2DO(UpdateStatusVO vo);
}

package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/7 15:00
 */
@Data
public class BatchUpdateStatusVO {
    @NotNull(message = "id不能为空")
    private List<Long> ids;
    @NotNull(message = "状态不能为空")
    @ApiModelProperty("状态 0-投放中、1-投放结束、2-即将开启、3-暂停、4-未暂停、5-已删除、6-未删除")
    private Integer status;
}

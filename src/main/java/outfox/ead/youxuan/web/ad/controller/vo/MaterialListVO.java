package outfox.ead.youxuan.web.ad.controller.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-05-19 17:34
 **/
@Data
public class MaterialListVO {

    @ExcelIgnore
    @ApiModelProperty(value = "ID")
    private Long id;

    @ExcelProperty("文件名称")
    @ApiModelProperty(value = "文件名称")
    private String name;

    @ExcelProperty("url")
    @ApiModelProperty(value = "url")
    private String url;

    @ExcelProperty("上传时间")
    @ApiModelProperty(value = "上传时间")
    private String createTime;

    @ExcelProperty("操作人")
    @ApiModelProperty("操作人")
    private String creatorName;
}
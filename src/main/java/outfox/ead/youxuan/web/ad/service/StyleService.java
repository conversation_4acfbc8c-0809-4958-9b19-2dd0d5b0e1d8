package outfox.ead.youxuan.web.ad.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import outfox.ead.youxuan.entity.Style;
import outfox.ead.youxuan.web.ad.controller.vo.*;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface StyleService extends YouxuanService<Style> {
    /**
     * 条件查询样式（模糊查询）、状态 样式id（精确查询）、样式名称（模糊查询）、媒体ID（精确查询) 媒体名称（模糊查询）、广告位ID（精确查询)，广告位名称（模糊查询）
     *
     * @param style 样式
     * @return List<StyleListVO>
     */
    IPage<StyleListVO> pageList(StyleCriteriaQueryVO style);


    /**
     * 插入/修改样式
     *
     * @param style 样式
     * @return 影响行数
     */
    Long saveOrUpdate(StyleSaveOrUpdateVO style);

    /**
     * 通过广告位id查询未删除样式
     *
     * @param adPositionIds 广告位id
     * @return 样式列表
     */
    List<Style> listNotDeletedByAdPositionIds(Collection<Long> adPositionIds);

    /**
     * 通过广告位id查询有效的样式
     *
     * @param adPositionIds 广告位id列表
     * @return 样式列表
     */
    List<Style> listValidByAdPositionIds(Collection<Long> adPositionIds);

    /**
     * 批量修改样式状态
     *
     * @param styles 样式列表
     * @return 影响行数
     */
    String batchUpdateStatus(BatchUpdateStatusVO styles);

    /**
     * 统计重复名字
     *
     * @param countByNameVO 样式名字
     */
    void nameRepeat(StyleCountByNameVO countByNameVO);

    /**
     * 通过id查询有效的样式
     *
     * @param styleIds 样式id
     * @return List<Style>
     */
    List<Style> listValidByIds(Collection<Long> styleIds);

    /**
     * 通过id查询
     *
     * @param id 样式id
     * @return StyleByIdVO
     */
    StyleByIdVO getVoById(Long id);

    /**
     * 通过名字模糊查询
     *
     * @param styleName 样式名
     * @return 样式列表
     */
    List<Style> listLikeName(String styleName);

    /**
     * 通过组id查询
     *
     * @param adGroupId 推广组id
     * @return 样式列表
     */
    List<Style> listByAdGroupId(Long adGroupId);

    /**
     * 查样式列表
     *
     * @param adGroupIds 推广组id列表
     * @return 样式列表
     */
    List<Style> listByAdGroupIds(Collection<Long> adGroupIds);

    /**
     * 通过广告位id查找未删除的样式
     *
     * @param adPositionId 广告位id
     * @return 样式列表
     */
    List<Style> listNotDeletedByAdPositionId(Long adPositionId);

    /**
     * 通过id查找未删除的样式
     *
     * @param styleIds 样式id
     * @return 样式列表
     */
    List<Style> listNotDeletedByIds(Collection<Long> styleIds);

    /**
     * 通过样式id列表以及广告位id列表查询未删除样式
     *
     * @param ids           样式id列表
     * @param adPositionIds 广告位id列表
     * @return 样式列表
     */
    List<Style> listNotDeletedByIdsAndAdPositionIds(Set<Long> ids, Collection<Long> adPositionIds);

    /**
     * 历史有无投放过广告
     *
     * @param id id
     * @return 0-没有，1-有
     */
    Integer historyAdDelivering(Long id);

    /**
     * 根据名字查询样式
     * 根据广告位id查询样式
     *
     * @param name          名字
     * @param adPositionIds 广告位id
     * @param status
     * @return 样式列表
     */
    List<Style> list(String name, Set<Long> adPositionIds, List<Integer> status);
}

package outfox.ead.youxuan.web.ad.controller.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import outfox.ead.youxuan.core.validator.Phone;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2022年07月28日 16:53
 */
@Data
public class BaseTemplate {
    @ExcelProperty(value = "163邮箱账户")
    private String username;
    @ExcelProperty(value = "有道词典账户UID")
    private String dictUid;
    @ExcelProperty(value = "优选账户昵称")
    @Size(max = 20, message = "昵称最多20个字符")
    @NotNull(message = "账户昵称不能为空")
    private String nickname;
    @ExcelProperty(value = "优选账户头像(输入图片url)")
    @NotNull(message = "优选账户头像不能为空")
    private String avatar;
    @ExcelProperty(value = "联系人")
    @Size(max = 20, message = "联系人姓名最多20个字符")
    @NotNull(message = "联系人姓名不能为空")
    private String name;
    @ExcelProperty(value = "联系人电话")
    @NotNull(message = "联系人电话不能为空")
    @Phone
    private String phone;
    @ExcelProperty(value = "联系邮箱")
    @Email(regexp = "^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$"
            , message = "联系邮箱格式不正确，请重新输入")
    @NotNull(message = "联系邮箱不能为空")
    @Size(max = 50, message = "联系人邮箱不得超过50个字符")
    private String email;
}

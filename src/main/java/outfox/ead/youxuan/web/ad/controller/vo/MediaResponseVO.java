package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import outfox.ead.youxuan.entity.Media;

/**
 * @author: 李梦杰
 * @date: 2021/8/19/11:36
 * @description:
 */
@Data
@NoArgsConstructor
public class MediaResponseVO {
    @ApiModelProperty("媒体主键")
    private Long id;

    @ApiModelProperty("媒体名称")
    private String name;

    @ApiModelProperty("平台")
    private Integer osType;

    @ApiModelProperty("备注")
    private String note;

    @ApiModelProperty("状态,0-有效，1-暂停,2-已删除")
    private Integer status;

     @ApiModelProperty("是否屏蔽青少年，0-否，1-是")
    private Boolean youthMode;

    public MediaResponseVO(Media media) {
        BeanUtils.copyProperties(media, this);
    }
}

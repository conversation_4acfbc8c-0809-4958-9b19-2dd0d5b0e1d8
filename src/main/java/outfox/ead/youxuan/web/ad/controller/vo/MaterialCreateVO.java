package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2025-05-19 17:25
 **/
@Data
public class MaterialCreateVO {
    @ApiModelProperty(value = "文件名称", required = true)
    @NotNull(message = "文件名称")
    private String name;
    @ApiModelProperty(value = "URL", required = true)
    @NotNull(message = "URL")
    private String url;
}

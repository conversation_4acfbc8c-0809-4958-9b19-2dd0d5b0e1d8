package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;
import java.util.Collection;

/**
 * 请使用{@link com.baomidou.mybatisplus.extension.plugins.pagination.Page}
 *
 * <AUTHOR> <EMAIL>
 * @date 2021/9/6 15:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Deprecated
public class PageVO<T> {
    @ApiModelProperty("当前页")
    public Long current;
    @ApiModelProperty("每页显示条数，默认 10")
    public Long size;
    @ApiModelProperty("查询数据列表")
    public Collection<T> records;
    @ApiModelProperty("总数")
    public Long total;

    public static <T> PageVO<T> emptyPage(@NotNull Long current, @NotNull Long size) {
        PageVO<T> pageVO = new PageVO<>();
        pageVO.setCurrent(current);
        pageVO.setSize(size);
        pageVO.setRecords(Lists.emptyList());
        pageVO.setTotal(0L);
        return pageVO;
    }
}

package outfox.ead.youxuan.web.ad.controller.dto;

import com.opencsv.bean.CsvBindByName;
import lombok.Data;
import outfox.ead.youxuan.util.IndustryUtil;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022年02月14日 11:19 上午
 */
@Data
public class Industry {
    @CsvBindByName(column = "id")
    private Integer id;
    @CsvBindByName(column = "name")
    private String name;
    @CsvBindByName(column = "parentId")
    private Integer parentId;

    private List<Industry> industryList;

    public static int calSimilarity(Industry source, Industry industry) {
        int similarity = 0;
        while (industry != null && source != null) {
            if (Objects.equals(industry, source)) {
                similarity += 1;
            }
            industry = IndustryUtil.getByIdOrDefault(industry.getParentId(), null);
            source = IndustryUtil.getByIdOrDefault(source.getParentId(), null);
        }
        return similarity;
    }

}

package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-06-06 18:03
 **/
@Data
public class AdSensitivityDetailVO {

    @ApiModelProperty(value = "灵敏度配置ID")
    private Long id;

    @ApiModelProperty("广告点击交互类型，1：摇一摇，2：滑动互动，5：扭一扭, 6:三合一")
    private Integer clickType;

    @ApiModelProperty("广告位ID")
    private Long adPositionId;

    @ApiModelProperty("广告位名称")
    private String adPositionName;

    @ApiModelProperty("广告组ID")
    private Long adGroupId;

    @ApiModelProperty("灵敏度配置-摇动加速度")
    private Integer shakeSpeed;

    @ApiModelProperty("灵敏度配置-滑动角度")
    private Integer slideAngle;

    @ApiModelProperty("灵敏度配置-摇动角度/扭转角度")
    private Integer rotationAngle;

    @ApiModelProperty("操作人")
    private String modifierName;

    @ApiModelProperty("编辑时间")
    private String modifierTime;
}

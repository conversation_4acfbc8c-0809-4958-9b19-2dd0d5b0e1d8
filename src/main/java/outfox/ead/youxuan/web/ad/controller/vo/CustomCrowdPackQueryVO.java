package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CustomCrowdPackQueryVO {
    @ApiModelProperty("人群包名称")
    private String name;
    @ApiModelProperty("人群包名称 or id 查询")
    private String queryKeyword;
    @ApiModelProperty("人群包id")
    private String id;
    @ApiModelProperty("状态 0-有效 1-校验中 2-校验失败 3-生成中 4-已失效 5-已删除")
    private List<Integer> status;
    @NotNull
    @ApiModelProperty("页数")
    private Long current;
    @NotNull
    @ApiModelProperty("条数")
    private Long size;
}

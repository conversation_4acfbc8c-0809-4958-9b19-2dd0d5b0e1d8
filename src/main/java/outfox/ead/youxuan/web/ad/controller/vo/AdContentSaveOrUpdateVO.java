package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.web.ad.controller.dto.CreativeContent;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/23 12:14
 */
@Data
public class AdContentSaveOrUpdateVO {
    @ApiModelProperty("推广内容主键")
    private Long id;
    /**
     * 创意内容类型
     * 0-信息流
     * 1-开屏
     * 2-插屏
     * 3-焦点图
     * 4-激励视频
     * 5-横幅
     * 6-自定义
     */
    private Integer type;
    @ApiModelProperty("创意内容文字类型")
    private Set<CreativeContent> textCreativeContents = new HashSet<>();
    @ApiModelProperty("创意内容图片类型")
    private Set<CreativeContent> imageCreativeContents = new HashSet<>();
    @ApiModelProperty("创意内容视频类型")
    private Set<CreativeContent> videoCreativeContents = new HashSet<>();
    @ApiModelProperty("展示时长")
    private Integer showTime;
}

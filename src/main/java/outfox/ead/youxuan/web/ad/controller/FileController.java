package outfox.ead.youxuan.web.ad.controller;

import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import outfox.ead.youxuan.constants.AccountTypeEnum;
import outfox.ead.youxuan.constants.Constants;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.util.FileUtil;
import outfox.ead.youxuan.web.ad.controller.vo.BatchSaveDetailVO;
import outfox.ead.youxuan.web.ad.service.RichMediaService;
import outfox.ead.youxuan.web.ad.service.TemplateService;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/17 12:24
 */
@RestController
@RequestMapping("/file")
@Api(tags = "文件上传")
@BaseResponse
@Validated
@RequiredArgsConstructor
public class FileController {
    private final RichMediaService richMediaService;
    @Value("${file.template}")
    private String prefix;
    private final TemplateService templateService;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("上传视频")
    public String upload(@RequestPart("file") MultipartFile file) throws Exception {
        return richMediaService.upload(file, Constants.VIDEO_PATH);
    }

    @PostMapping(value = "/audio", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("上传音频")
    public String uploadAudio(@RequestPart("file") MultipartFile file) throws Exception {
        return richMediaService.uploadAudio(file, Constants.AUDIO_PATH);
    }

    @LogRecord(
            success = "上传素材到msf_ead,结果:{{#_ret}}",
            type = "materialLibrary",
            bizNo = "uploadMaterial"
    )
    @PostMapping(value = "/material", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("上传素材")
    public String uploadMaterial(@RequestPart("file") MultipartFile file) throws Exception {
        return richMediaService.uploadMaterial(file, Constants.MATERIAL_PATH);
    }

    private static final Set<String> ROLE_KEY_SET = new HashSet<>(
            Arrays.asList(RoleEnum.SPONSOR.getRoleKey(), RoleEnum.KOL.getRoleKey(), RoleEnum.BRAND_KOL.getRoleKey()));

    @Operation(summary = "通过excel文件批量上传用户")
    @PostMapping(value = "batchSaveUser", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @AccessControl(roles = RoleEnum.KOL_OPERATOR)
    public BatchSaveDetailVO batchSave(@RequestPart("file") MultipartFile file,
                                       String roleKey,
                                       @RequestParam AccountTypeEnum registerType) {
        if (ROLE_KEY_SET.contains(roleKey)) {
            return templateService.batchSaveByFile(file, roleKey, registerType);
        } else {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "没有该模板");
        }
    }



    @GetMapping("downloadTemplate")
    @ApiOperation("下载模板")
    public void downloadTemplate(HttpServletResponse response,
                                 @ApiParam("0-163注册 1-词典账号注册") AccountTypeEnum type,
                                 @ApiParam("广告主 - sponsor " +
                                         "个人创作者 - kol " +
                                         "机构创作者 - brandKol ") String roleKey) throws Exception {
        String basePath;
        if (type.equals(AccountTypeEnum.DICT_UID)) {
            basePath = prefix + "/dict/";
        } else {
            basePath = prefix + "/163/";
        }
        if (RoleEnum.SPONSOR.getRoleKey().equals(roleKey)) {
            FileUtil.download(basePath + "sponsor模板.xlsx", response);
        } else if (RoleEnum.KOL.getRoleKey().equals(roleKey)) {
            FileUtil.download(basePath + "kol模板.xlsx", response);
        } else if (RoleEnum.BRAND_KOL.getRoleKey().equals(roleKey)) {
            FileUtil.download(basePath + "brandKol模板.xlsx", response);
        } else {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "没有该模板");
        }
    }

    @GetMapping("downloadAdPlanCreateTemplate")
    @ApiOperation("下载批量创建计划模板")
    public void downloadTemplate(HttpServletResponse response) throws Exception {
        FileUtil.download(prefix + "批量创建广告模板.xlsx", response);
    }
}

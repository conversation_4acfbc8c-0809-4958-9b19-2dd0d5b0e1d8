package outfox.ead.youxuan.web.ad.controller.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @TableName AdPlan
 */
@Data
public class AdPlanSaveOrUpdateVO implements Serializable {
    @ApiModelProperty("推广计划主键")
    private Long id;

    /**
     * 推广目标<p>
     * 1-落地页推广<p>
     * 2-应用直达<p>
     * 3-小程序推广
     */
    @NotNull(message = "promotionTarget不能为null")
    @Min(value = 1, message = "推广目标类型错误")
    @Max(value = 3, message = "推广目标类型错误")
    private Integer promotionTarget;

    @ApiModelProperty("推广计划名称")
    @NotBlank(message = "推广计划名称不能为空")
    private String name;

    @ApiModelProperty("客户名称")
    @NotBlank(message = "客户名称不能为空")
    private String customerName;

    @ApiModelProperty("计费模式 0-CPT 1-CPM")
    @NotNull(message = "billingType不能为null")
    @Min(value = 0, message = "计费模式类型错误")
    @Max(value = 1, message = "计费模式类型错误")
    private Integer billingType;

    @ApiModelProperty("投放方式")
    @NotNull(message = "deliveryType不能为null")
    @Min(value = 0, message = "投放方式错误")
    @Max(value = 2, message = "投放方式错误")
    private Integer deliveryType;

    /**
     * 日期是否连续<p>
     * 0-连续 1-不连续
     */
    @NotNull(message = "dateContinuous不能为null")
    @Min(value = 0, message = "日期连续类型错误")
    @Max(value = 1, message = "日期连续类型错误")
    private Integer dateContinuous;

    @ApiModelProperty("投放时间")
    private Set<String> adDeliveryDate;

    /**
     * 投放开始日期
     * 连续日期只需要存储开始和结束时间
     * 不连续日期要通过adDeliveryDate推出一下两个属性用于筛选
     */
    private LocalDate adOpenDate;

    @ApiModelProperty("投放结束时间")
    private LocalDate adCloseDate;

    @ApiModelProperty("投放时段 0-不限 1-指定时段")
    @NotNull(message = "timeOrientation不能为null")
    @Min(value = 0, message = "投放时段类型错误")
    @Max(value = 1, message = "投放时段类型错误")
    private Integer timeOrientation;

    @ApiModelProperty("投放时段")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<List<Integer>> timeDest;

    @ApiModelProperty("地域定向 0-不限 1-按区域")
    @NotNull(message = "regionalOrientation不能为null")
    @Min(value = 0, message = "地域定向类型错误")
    @Max(value = 1, message = "地域定向类型错误")
    private Integer regionalOrientation;

    @ApiModelProperty("地域定向流量范围 0-全部 1-内部流量")
    private Integer regionalOrientationScope;

    @ApiModelProperty("投放目标地址国际")
    private Set<Integer> internationalRegionalDest;

    @ApiModelProperty("投放目标地址国内")
    private Set<Integer> inlandRegionalDest;

    @ApiModelProperty("计划状态 0-投放中，1-投放结束，2-即将开始，3-暂停，4已删除")
    @NotNull(message = "status不能为null")
    @Min(value = 0, message = "状态类型错误")
    private Integer status;

    @ApiModelProperty("频控方式 0-不频控 1-按天频控 2-按周频控 3-按投放周期频控")
    @Min(value = 0, message = "频控方式类型错误")
    @Max(value = 3, message = "频控方式类型错误")
    @NotNull(message = "frequencyType不能为null")
    private Integer frequencyType;

    @ApiModelProperty("频控次数")
    @Min(value = 0, message = "频控次数错误")
    @Max(value = 20, message = "频控次数错误")
    private Integer frequencyLimit;
}

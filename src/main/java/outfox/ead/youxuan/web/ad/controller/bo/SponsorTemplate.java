package outfox.ead.youxuan.web.ad.controller.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2022年02月12日 10:51 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SponsorTemplate extends BaseTemplate{
    @ExcelProperty(value = "公司名称")
    @NotNull(message = "公司名称不能为空")
    @Size(max = 50, message = "公司名称最多50个字符")
    private String companyName;
    @ExcelProperty(value = "行业信息")
    private String industryName;
}

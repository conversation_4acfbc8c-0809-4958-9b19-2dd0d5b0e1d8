package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.AccountTypeEnum;
import outfox.ead.youxuan.constants.RegisterRoleEnum;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.annotation.DistributedLock;
import outfox.ead.youxuan.core.annotation.DistributedLockKey;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.User;
import outfox.ead.youxuan.util.CookieVerifyUtil;
import outfox.ead.youxuan.web.ad.controller.vo.RegisterUserVO;
import outfox.ead.youxuan.web.ad.service.RegisterService;
import outfox.ead.youxuan.web.kol.controller.dto.DictProfile;
import outfox.ead.youxuan.web.kol.service.DictService;

/**
 * <AUTHOR>
 * @date 2022年07月18日 12:01
 */
@BaseResponse
@RestController
@RequestMapping("/register")
@AllArgsConstructor
@Validated
@Api(tags = "注册")
public class RegisterController {
    private final RegisterService registerService;
    private final DictService dictService;

    @GetMapping("active")
    @ApiOperation("邮件激活注册")
    @DistributedLock
    public RegisterUserVO activeRegister(@DistributedLockKey String activeRegisterToken) {
        return registerService.activeRegister(activeRegisterToken);
    }

    @GetMapping
    @ApiOperation("注册")
    public RegisterUserVO register(@RequestHeader(HttpHeaders.COOKIE) String cookie,
                         @ApiParam("注册的角色") RegisterRoleEnum roleEnum,
                         AccountTypeEnum accountTypeEnum) {
        if (AccountTypeEnum.DICT_UID.equals(accountTypeEnum)) {
            User user = registerService.registerByDict(dictService.validAndGetUid(cookie), roleEnum.getRoleKey(), false);
            DictProfile profile = dictService.getProfiles(user.getDictUid());
            return  RegisterUserVO.builder()
                    .username(user.getUsername())
                    .id(user.getId())
                    .dictUid(user.getDictUid())
                    .roleKey(roleEnum.getRoleKey())
                    .avatar(profile.getAvatar())
                    .nickname(profile.getNickname())
                    .build();
        }else if (AccountTypeEnum.MAIL_163.equals(accountTypeEnum)){
            registerService.sendActivationRegisterEmail(CookieVerifyUtil.verifyCookieWithEmailAndGetEmail(cookie), roleEnum.getRoleKey());
        }else{
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "目前不支持该该类型注册");
        }
        return null;
    }
}

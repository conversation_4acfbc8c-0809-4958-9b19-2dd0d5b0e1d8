package outfox.ead.youxuan.web.ad.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import outfox.ead.youxuan.web.ad.controller.vo.MaterialCreateVO;
import outfox.ead.youxuan.web.ad.controller.vo.MaterialListVO;
import outfox.ead.youxuan.web.ad.controller.vo.MaterialQueryVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface MaterialLibraryService {
    void saveMaterial(List<MaterialCreateVO> materialCreateVOs);

    IPage<MaterialListVO> getMaterialList(MaterialQueryVO materialQueryVO);

    void download(HttpServletRequest request, HttpServletResponse response, List<MaterialListVO> materialListVOs);

    /**
     * 删除素材
     * @param id 素材ID
     */
    void deleteMaterial(Long id);
}
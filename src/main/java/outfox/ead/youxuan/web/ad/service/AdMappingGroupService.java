package outfox.ead.youxuan.web.ad.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import outfox.ead.youxuan.entity.AdMappingGroup;
import outfox.ead.youxuan.web.ad.controller.response.AdMappingPageResponse;
import outfox.ead.youxuan.web.ad.controller.vo.AdMappingCriteriaQueryVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdMappingGroupSaveVO;
import outfox.ead.youxuan.web.ad.controller.vo.UpdateStatusVO;

/**
 * <AUTHOR>
 * @description 针对表【AdMappingGroup(广告映射组)】的数据库操作Service
 * @createDate 2023-07-27 14:13:55
 */
public interface AdMappingGroupService extends YouxuanService<AdMappingGroup> {
    /**
     * 分页条件查询
     */
    IPage<AdMappingPageResponse> page(AdMappingCriteriaQueryVO adMappingCriteriaQueryVO);

    void saveOrUpdate(AdMappingGroupSaveVO adMappingSaveVO);

    void updateStatus(UpdateStatusVO vo);

    boolean nameExists(Long id, String name);

    AdMappingGroup getValidById(Long id);
}
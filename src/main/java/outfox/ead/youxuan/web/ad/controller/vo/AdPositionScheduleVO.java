package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 一个月的排期表
 *
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/20 15:35
 */
@Data
public class AdPositionScheduleVO {
    @ApiModelProperty("广告位名称")
    private String adPositionName;

    private Long adPositionId;
    @ApiModelProperty("详细信息")
    List<ScheduleDetail> detailList;
}
package outfox.ead.youxuan.web.ad.controller.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import outfox.ead.youxuan.constants.ElementKey;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/25 12:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ElementListVO implements Comparable<ElementListVO> {
    private String elementKey;
    private String name;
    private Integer type;
    List<ElementVO> elements;

    @Override
    public int compareTo(ElementListVO o) {
        ElementKey thisElem = ElementKey.getEnum(this.getElementKey());
        ElementKey other = ElementKey.getEnum(o.getElementKey());
        if (thisElem.equals(ElementKey.UNKNOWN) && other.equals(ElementKey.UNKNOWN)) {
            return this.getElementKey().compareTo(o.getElementKey());
        }
        return Comparator
                .comparing(ElementKey::getLevel)
                .thenComparing(ElementKey::getSubLevel)
                .thenComparing(ElementKey::getValue)
                .compare(thisElem, other);
    }
}

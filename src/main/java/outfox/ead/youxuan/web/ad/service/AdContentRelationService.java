package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.entity.AdContentRelation;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface AdContentRelationService extends YouxuanService<AdContentRelation> {

    /**
     * 根据推广组id查找对应AdContentRelation
     *
     * @param adGroupId 推广组id
     * @return List
     */
    List<AdContentRelation> listByAdGroupId(Long adGroupId);

    /**
     * 根据推广组id列表查找对应的AdContentRelation
     *
     * @param adGroupIds 推广组id列表
     * @return List
     */
    List<AdContentRelation> listByAdGroupIds(Collection<Long> adGroupIds);

    /**
     * 根据adGroupId删除关系
     *
     * @param adGroupId 推广组id
     */
    void removeByAdGroupId(Long adGroupId);

    /**
     * 根据样式id获取AdContentRelation
     *
     * @param styleIds 样式id列表
     * @return List<AdContentRelation>
     */
    List<AdContentRelation> listByStyleIds(Collection<Long> styleIds);

    /**
     * 根据样式id获取AdContentRelation，可以指定排除某个推广组的AdContentRelation
     *
     * @param styleIds  样式id列表
     * @param adGroupId 推广组id
     * @return List<AdContentRelation>
     */
    List<AdContentRelation> listByStyleIdsAndFilterByAdGroupId(Collection<Long> styleIds, Long adGroupId);

    /**
     * 根据推广组id列表获取有效的购买关联
     *
     * @param adGroupIds 推广组id列表
     * @return 有效的购买关联
     */
    List<AdContentRelation> listValidByAdGroupIds(List<Long> adGroupIds);

    /**
     * 根据样式id获取有效的AdContentRelation，可以指定排除某个推广组的AdContentRelation
     *
     * @param styleIds  样式id列表
     * @param adGroupId 推广组id
     * @return List<AdContentRelation>
     */
    List<AdContentRelation> listValidByStyleIdsAndFilterByAdGroupId(List<Long> styleIds, Long adGroupId);

    /**
     * 根据样式id获取有效的AdContentRelation
     *
     * @param styleId 样式id
     * @return List<AdContentRelation>
     */
    List<AdContentRelation> listByStyleId(Long styleId);

    List<AdContentRelation> listByStyleIdsAndGroupIds(Set<Long> styleIds, Set<Long> adGroupIds);
}

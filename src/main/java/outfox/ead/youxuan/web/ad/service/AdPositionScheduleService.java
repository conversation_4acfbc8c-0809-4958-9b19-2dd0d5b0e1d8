package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.web.ad.controller.vo.AdPositionScheduleCriteriaQueryVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdPositionScheduleVO;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.controller.vo.ResourceSalesDetailsVO;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdPositionScheduleService {
    /**
     * 条件查询获得相关广告位，最终返回广告位当月排期数据
     *
     * @param adPositionSchedule 条件查询
     * @return 广告位排期列表
     */
    PageVO<AdPositionScheduleVO> getAdPositionSchedule(AdPositionScheduleCriteriaQueryVO adPositionSchedule);

    /**
     * 获取资源售卖详情
     * <p>只需要求出命中这个时间的推广计划，然后进行数据组合即可</p>
     *
     * @param date         日期
     * @param adPositionId 广告位id
     * @param billingType  计费方式
     * @return 资源售卖详情列表
     */
    List<ResourceSalesDetailsVO> getAdResourceSalesDetails(LocalDate date, Long adPositionId, Integer billingType);
}

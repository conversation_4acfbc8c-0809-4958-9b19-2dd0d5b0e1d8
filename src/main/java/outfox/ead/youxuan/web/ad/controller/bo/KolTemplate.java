package outfox.ead.youxuan.web.ad.controller.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import outfox.ead.youxuan.core.validator.LegalNumber;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import static outfox.ead.youxuan.constants.Constants.UPPER_LIMIT;

/**
 * <AUTHOR>
 * @date 2022年02月12日 10:51 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KolTemplate extends BaseTemplate{

    @ExcelProperty(value = "媒体平台")
    @NotNull(message = "媒体平台不能为空")
    private String platformName;
    @ExcelProperty(value = "媒体账户id")
    @NotNull(message = "媒体账户id不能为空")
    @Size(max = 50, message = "媒体账户id不能超过50个字符")
    private String appUserId;

    @ExcelProperty(value = "媒体账户昵称")
    private String appAccountName;

    @ExcelProperty(value = "媒体账户头像")
    private String appAccountAvatar;

    @ExcelProperty(value = "性别(男-0 女-1)")
    @LegalNumber(message = "性别错误", max = 1, min = 0)
    private String gender;

    @ExcelProperty(value = "粉丝数")
    @LegalNumber(min = 0, max = UPPER_LIMIT, message = "粉丝数支持输入0-1亿之间的数值")
    private String fansNum;

    @ExcelProperty(value = "地点")
    private String areaName;

    @ExcelProperty(value = "内容标签")
    private String contentTagName;

    @ExcelProperty(value = "任务类型")
    @NotNull(message = "任务类型不能为空")
    private String platformTaskName;

    @ExcelProperty(value = "内容类型")
    @NotNull(message = "内容类型不能为空")
    private String platformContentName;

    @ExcelProperty(value = "报价")
    @NotNull(message = "报价不能为空")
    @Pattern(regexp = "(^[1-9](\\d+)?(\\.\\d{1,2})?$)|(^(0){1}$)|(^\\d\\.\\d{1,2}?$)", message = "报价只支持输入正数，最多支持两位小数")
    private String price;

    @ExcelProperty(value = "所属MCN")
    @Size(max = 20,message = "mcn不可超过20个字")
    private String mcn;

    @ExcelProperty(value = "责任运营优选账户")
    private String kolOperatorUsername;

    @ExcelProperty(value = "主页链接")
    private String homepageLink;
}

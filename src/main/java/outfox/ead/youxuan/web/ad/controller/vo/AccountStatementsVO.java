package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.joda.time.DateTime;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/13/11:25
 */
@Data
public class AccountStatementsVO {
    @NotNull(message = "粒度不能为空")
    @ApiModelProperty("粒度 <p>0-天 <p>1-小时 <p>2-all")
    private Integer granularity;
    @NotNull(message = "维度不能为空")
    @ApiModelProperty("0-all 1-客户名称 2-推广目标 3-媒体 4-广告位 5-样式")
    private Integer dimension;
    @NotNull(message = "报表类型不能为空")
    @ApiModelProperty("报表类型 0-all 6-推广计划 7-推广组")
    private Integer statement;
    /**
     * 推广目标
     */
    @ApiModelProperty("推广目标 1-落地页推广 2-应用直达 3-小程序推广")
    private List<Integer> promotionTarget;
    /**
     * 计费方式
     */
    @ApiModelProperty("计费方式 0-CPT 1-CPM")
    private List<Integer> billingType;
    /**
     * 投放方式
     */
    @ApiModelProperty("投放方式")
    private List<Integer> deliveryType;
    @ApiModelProperty("媒体id")
    private List<Long> mediaIds;
    @ApiModelProperty("广告位id")
    private List<Long> adPositionIds;
    @ApiModelProperty("样式id")
    private List<Long> styleIds;
    @ApiModelProperty("推广计划id")
    private List<Long> adPlanIds;
    @ApiModelProperty("推广组id")
    private List<Long> adGroupIds;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty("开始时间")
    private DateTime startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty("结束时间")
    private DateTime endTime;

    private Long current;
    private Long size;


    /**
     * 是否过滤0，0-不过滤，1-过滤
     */
    @ApiModelProperty("是否过滤0，0-不过滤，1-过滤")
    private Integer filter;

}

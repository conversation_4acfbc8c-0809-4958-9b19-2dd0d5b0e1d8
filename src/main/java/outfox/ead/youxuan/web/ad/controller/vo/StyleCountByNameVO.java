package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/9/13/14:23
 */
@Data
public class StyleCountByNameVO {
    @NotNull(message = "名字不能为空")
    private String name;
    @ApiModelProperty(value = "样式id")
    private Long id;
    @ApiModelProperty(value = "广告位id")
    private Long adPositionId;
}

package outfox.ead.youxuan.web.ad.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.vo.MaterialCreateVO;
import outfox.ead.youxuan.web.ad.controller.vo.MaterialListVO;
import outfox.ead.youxuan.web.ad.controller.vo.MaterialQueryVO;
import outfox.ead.youxuan.web.ad.service.MaterialLibraryService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-05-19 16:23
 **/
@RestController
@AllArgsConstructor
@Validated
@BaseResponse
@Slf4j
@Api(tags = "素材库")
@RequestMapping("/material")
public class MaterialLibraryController {

    private final MaterialLibraryService materialLibraryService;

    /**
     * 由于文件存储到msf_ead与存储到数据库，是两个接口。
     * 为了观察是否会出现：文件存储到msf_ead了，但是用户最终没有将这个文件落到数据库，因此加上日志
     *
     * 其实更好的方法是文件先存储到临时目录，当URL落库时，再将文件移动到真正的目录。但由于服务端是多台机器部署的，
     * 要实现这个功能，需要引入分布式存储，因此先不采用这种方案，可以采用加日志的方式，先在线上观察一段时间
     */
    @LogRecord(
            success = "保存素材到数据库,参数{{#materialCreateVOs}}",
            type = "materialLibrary",
            bizNo = "saveMaterial"
    )
    @PutMapping
    @ApiOperation("保存素材")
    public void saveMaterial(@RequestBody List<MaterialCreateVO> materialCreateVOs) {
        materialLibraryService.saveMaterial(materialCreateVOs);
    }

    @GetMapping
    @ApiOperation("获取素材列表")
    public IPage<MaterialListVO> getMaterialList(MaterialQueryVO materialQueryVO) {
        return materialLibraryService.getMaterialList(materialQueryVO);
    }

    @LogRecord(
            success = "删除素材,参数{{#id}}",
            type = "materialLibrary",
            bizNo = "deleteMaterial"
    )
    @DeleteMapping("/{id}")
    @ApiOperation("删除素材")
    public void deleteMaterial(@PathVariable Long id) {
        materialLibraryService.deleteMaterial(id);
    }

    @PostMapping("/download")
    @ApiOperation("下载Excel")
    public void getExcel(HttpServletRequest request, HttpServletResponse response,@RequestBody List<MaterialListVO> materialListVOs) {
        materialLibraryService.download(request, response, materialListVOs);
    }
}

package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-06-06 18:03
 **/
@Data
public class AdSensitivityQueryVO {

    @ApiModelProperty(value = "广告点击交互类型，1：摇一摇，2：滑动互动，5：扭一扭, 6:三合一")
    private Integer clickType;

    @ApiModelProperty(value = "广告位ID")
    private Long adPositionId;

    @ApiModelProperty(value = "推广组ID")
    private Long adGroupId;

    @ApiModelProperty(value = "当前页码", example = "1")
    private Long current = 1L;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Long size = 10L;
}
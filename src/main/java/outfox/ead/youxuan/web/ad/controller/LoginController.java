package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.User;
import outfox.ead.youxuan.util.CookieVerifyUtil;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.mapper.UserMapper;
import outfox.ead.youxuan.web.ad.controller.vo.LoginResponseVO;
import outfox.ead.youxuan.web.ad.service.LoginService;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserRelationService;
import outfox.ead.youxuan.web.ad.service.UserService;
import outfox.ead.youxuan.web.kol.service.DictService;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.net.URISyntaxException;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.Constants.USER_STATUS_NORMAL;
import static outfox.ead.youxuan.constants.ResponseType.*;

/**
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/20 9:39
 */
@RestController
@RequestMapping("/login")
@BaseResponse
@AllArgsConstructor
@Api(tags = {"登录接口"})
@Slf4j
public class LoginController {
    private final LoginService loginService;
    private final UserService userService;
    private final RoleService roleService;
    private final UserRelationService userRelationService;
    private final DictService dictService;
    private final UserMapper userMapper;

    @ApiOperation(value = "URS cookie登录", notes = "URS cookie登录")
    @GetMapping("/login_cookie")
    public LoginResponseVO loginByCookie(@RequestHeader(value = HttpHeaders.COOKIE) String cookie) {
        User user = userService.getUserByUsername(CookieVerifyUtil.verifyCookieWithEmailAndGetEmail(cookie));
        return getLoginResponseVO(user);
    }

    @ApiOperation(value = "词典uid登录")
    @GetMapping("/login_cookie_uid")
    public LoginResponseVO loginByDictUid(@RequestHeader(value = HttpHeaders.COOKIE) String cookie) {
        User user = userService.getUserByDictUid(dictService.validAndGetUid(cookie));
        return getLoginResponseVO(user);
    }

    private LoginResponseVO getLoginResponseVO(User user) {
        if (Objects.nonNull(user) && user.getStatus().equals(USER_STATUS_NORMAL)) {
            Collection<Role> roles = roleService.listByUserId(user.getId());
            return getLoginResponseVO(user.getId(), roles.size() == 1 ? roles.iterator().next().getRoleKey() : null);
        } else {
            throw new CustomException(AUTHENTICATION_FAILED, "用户未注册");
        }
    }

    @ApiOperation(value = "登陆绑定用户")
    @GetMapping("/login_relation")
    public LoginResponseVO loginByRelation(@ApiParam("要跳转过去的用户id") Long userId) {
        if (userRelationService.existsValidByUserIdAndBoundUserId(SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) ? SecurityUtil.getUserId() : userId,
                SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) ? userId : SecurityUtil.getUserId())) {
            return getLoginResponseVO(userId,
                    SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) ?
                            RoleEnum.BRAND_KOL.getRoleKey() : RoleEnum.SPONSOR.getRoleKey());
        } else {
            throw new CustomException(ACCESS_DENIED, "没有该用户跳转权限");
        }
    }

    @ApiOperation(value = "登陆词典")
    @GetMapping("/login_dict")
    public void loginDict(HttpServletResponse response) {
        List<String> cookies = loginService.loginDictByUserId(SecurityUtil.getUserId());
        for (String cookie : cookies) {
            try {
                response.addCookie(createCookie(cookie));
            } catch (URISyntaxException e) {
                throw new CustomException(SERVICE_ERROR, "词典cookie解析错误");
            }
        }
    }

    private Cookie createCookie(String cookieStr) throws URISyntaxException {
        String name = null;
        String value = null;
        boolean httpOnly = false;
        String path = null;
        for (String s : cookieStr.split(";")) {
            if (s.contains("DICT_SESS")) {
                name = "DICT_SESS";
                value = s.replace("DICT_SESS=", "");
            } else if (s.contains("Path")) {
                path = s.replace("Path=", "");
            } else if (s.contains("DICT_LOGIN")) {
                name = "DICT_LOGIN";
                value = s.replace("DICT_LOGIN=", "");
            } else if (s.contains("HttpOnly")) {
                httpOnly = true;
            }
        }
        Cookie cookie = new Cookie(name, value);
        cookie.setPath(path);
        cookie.setHttpOnly(httpOnly);
        return cookie;
    }

    /**
     * 词典无缝跳转优选
     * 获取优选的token
     *
     * @param param    AES加密的uid
     * @param response -
     */
    @GetMapping("/login_aes_param")
    public Boolean loginByUid(@ApiParam("AES加密的uid") String param, HttpServletResponse response) {
        return loginService.loginByAESUid(param, response);
    }

    private LoginResponseVO getLoginResponseVO(Long userId, String currentRole) {
        User user = userService.getById(userId);
        String token = loginService.loginByUser(user, Optional.ofNullable(roleService.getByRoleKey(currentRole)).orElseGet(Role::new));
        return userMapper.do2LoginResponseVO(user,
                token,
                roleService.listByUserId(user.getId()).stream().map(Role::getRoleKey).collect(Collectors.toSet()),
                currentRole);
    }
}

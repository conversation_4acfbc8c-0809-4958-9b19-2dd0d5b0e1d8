package outfox.ead.youxuan.web.ad.controller;

import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.service.ImageService;

@RestController
@RequestMapping("/image")
@Api(tags = "图片上传")
@BaseResponse
@Validated
@RequiredArgsConstructor
public class ImageController {
    private final ImageService imageService;

    @PostMapping
    @LogRecord(
            success = "上传图片结果:{{#_ret}}",
            type = "uploadImage",
            bizNo = "uploadImage"
    )
    public String upload(@RequestPart("file") MultipartFile file) {
        return imageService.upload(file);
    }
}

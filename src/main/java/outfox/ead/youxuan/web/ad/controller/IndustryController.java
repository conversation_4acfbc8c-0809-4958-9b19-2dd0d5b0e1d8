package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.util.IndustryUtil;
import outfox.ead.youxuan.web.ad.controller.dto.Industry;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022年02月14日 11:17 上午
 */
@RestController
@RequestMapping("/industry")
@BaseResponse
@AllArgsConstructor
@Api(tags = "行业接口")
@Slf4j
public class IndustryController {
    @GetMapping
    @Operation(method = "返回行业列表", description = "返回行业列表")
    public Collection<Industry> listIndustry() {
        return IndustryUtil.getIndustryList();
    }
}

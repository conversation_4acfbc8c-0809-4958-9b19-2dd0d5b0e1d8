package outfox.ead.youxuan.web.ad.controller.dto;

import com.opencsv.bean.CsvBindByName;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年02月14日 11:19 上午
 */
@Data
public class CrowdLabel {
    @CsvBindByName(column = "id")
    private Integer id;
    @CsvBindByName(column = "name")
    private String name;
    @CsvBindByName(column = "parentId")
    private Integer parentId;
    @CsvBindByName(column = "displayable")
    private Integer displayable;

    private List<CrowdLabel> crowdLabelList = new ArrayList<>(0);
}

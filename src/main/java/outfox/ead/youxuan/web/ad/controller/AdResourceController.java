package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.dto.MediaResource;
import outfox.ead.youxuan.web.ad.controller.dto.PromotionResource;
import outfox.ead.youxuan.web.ad.controller.vo.MediaAdPositionVO;
import outfox.ead.youxuan.web.ad.controller.vo.PageLikeNameVO;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.AdResourceService;

import java.util.Collection;

/**
 * 资源列表
 *
 * <AUTHOR> <EMAIL>
 * @date 2021/8/29 20:12
 */
@BaseResponse
@RestController
@AllArgsConstructor
@Validated
@Api(tags = "资源管理")
@RequestMapping("/ad_resource")
@AccessControl(roles = RoleEnum.AD_OPERATOR)
public class AdResourceController {
    private final AdResourceService adResourceService;

    /**
     * @param id        推广计划id
     * @param name      资源名称
     * @param adGroupId 推广组id
     * @return 资源列表
     */
    @GetMapping
    public Collection<MediaResource> getResource(@ApiParam("推广计划id") @RequestParam(required = false) Integer id,
                                                 @ApiParam("推广组id") @RequestParam(required = false) Long adGroupId,
                                                 @ApiParam("名称") String name) {
        return adResourceService.listResource(id, adGroupId, name);
    }


    /**
     * 用于资源排期  资源筛选列表
     */
    @GetMapping("/like_name")
    public PageVO<MediaResource> getAdPositionResourceList(MediaAdPositionVO mediaAdPositionVO, Integer level) {
        return adResourceService.listResourceLikeName(mediaAdPositionVO, level);
    }


    @GetMapping("/promotion_like_name")
    public PageVO<PromotionResource> getPromotionResourceList(PageLikeNameVO pageLikeNameVO) {
        return adResourceService.listPromotionResourceLikeName(pageLikeNameVO);
    }
}

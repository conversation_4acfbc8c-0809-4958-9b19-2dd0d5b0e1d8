package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.MaterialLibrary;
import outfox.ead.youxuan.web.ad.controller.vo.MaterialCreateVO;
import outfox.ead.youxuan.web.ad.controller.vo.MaterialListVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025-05-19 18:25
 **/
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface MaterialLibraryMapstruct {

    List<MaterialLibrary> materialCreateVO2EntityBatch(List<MaterialCreateVO> materialCreateVO);

    @Mapping(target = "creatorName", expression = "java(creatorNameMap.get(materialLibrary.getCreator()))")
    @Mapping(target = "createTime", expression = "java(materialLibrary.getCreateTime().format(java.time.format.DateTimeFormatter.ofPattern(outfox.ead.youxuan.constants.Constants.MIN_FORMAT)))")
    MaterialListVO entity2MaterialListVO(MaterialLibrary materialLibrary, Map<Long, String> creatorNameMap);
}
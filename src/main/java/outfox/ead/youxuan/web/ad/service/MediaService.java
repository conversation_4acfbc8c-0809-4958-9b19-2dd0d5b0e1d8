package outfox.ead.youxuan.web.ad.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import outfox.ead.youxuan.entity.Media;
import outfox.ead.youxuan.web.ad.controller.vo.*;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface MediaService extends YouxuanService<Media> {
    /**
     * 条件查询媒体（模糊查询）、状态 媒体ID（精确查询) 媒体名称（模糊查询）
     *
     * @param media 媒体
     * @return List<Media>
     */
    PageVO<MediaResponseVO> pageList(MediaCriteriaQueryVO media);

    /**
     * 插入/修改媒体
     *
     * @param media 媒体
     * @return 影响行数
     */
    Long saveOrUpdate(MediaSaveOrUpdateVO media);

    /**
     * 统计重复名字
     *
     * @param mediaCountByNameVO 媒体名字,id
     */
    void nameRepeat(MediaCountByNameVO mediaCountByNameVO);

    /**
     * 批量修改
     *
     * @param medias 媒体列表
     * @return 影响行数
     */
    String batchUpdateStatusById(BatchUpdateStatusVO medias);

    /**
     * 根据id查询未删除的媒体
     *
     * @param ids 媒体id
     * @return 媒体列表
     */
    List<Media> listNotDeletedByIds(Collection<Long> ids);

    /**
     * 查询有效的媒体
     *
     * @param ids 媒体id列表
     * @return 媒体列表
     */
    List<Media> listValidByIds(Collection<Long> ids);

    /**
     * 根据名字模糊查询媒体
     *
     * @param mediaName 媒体名称
     * @return 媒体列表
     */
    List<Media> listLikeName(String mediaName);

    /**
     * 通过名字查询未删除的媒体
     *
     * @param name 名字
     * @param status
     * @return 媒体列表
     */
    List<Media> listByName(String name, List<Integer> status);

    /**
     * 通过媒体id分页查找
     *
     * @param mediaIds 媒体id列表
     * @param current  页码
     * @param size     行数
     * @return 媒体分页
     */
    Page<Media> pageListInIds(Set<Long> mediaIds, Long current, Long size);

    List<Media> listByStyleIds(List<Long> styleIds);

    Integer getOsByStyleId(Long styleId);
}

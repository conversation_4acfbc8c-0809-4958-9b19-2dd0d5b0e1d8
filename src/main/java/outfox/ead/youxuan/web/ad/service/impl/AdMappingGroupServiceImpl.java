package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.mapper.youxuan.AdMappingGroupMapper;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.mapper.AdMappingGroupMap;
import outfox.ead.youxuan.web.ad.controller.response.AdMappingPageResponse;
import outfox.ead.youxuan.web.ad.controller.vo.AdMappingCriteriaQueryVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdMappingGroupSaveVO;
import outfox.ead.youxuan.web.ad.controller.vo.UpdateStatusVO;
import outfox.ead.youxuan.web.ad.service.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 针对表【AdMappingGroup(广告映射组)】的数据库操作Service实现
 * @createDate 2023-07-27 14:13:55
 */
@Service
@RequiredArgsConstructor
public class AdMappingGroupServiceImpl extends YouxuanServiceImpl<AdMappingGroupMapper, AdMappingGroup>
        implements AdMappingGroupService {
    private final AdMappingGroupMap adMappingGroupMap;

    private final AdGroupService adGroupService;
    private final AdPlanService adPlanService;
    private final UserService userService;
    private final CustomerService customerService;

    private final MediaService mediaService;
    private final AdPositionService adPositionService;
    private final StyleService styleService;

    private final AdContentRelationService adContentRelationService;
    private final AdMappingService adMappingService;

    @Override
    public IPage<AdMappingPageResponse> page(AdMappingCriteriaQueryVO vo) {
        LambdaQueryChainWrapper<AdMappingGroup> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .like(Objects.nonNull(vo.getName()), AdMappingGroup::getName, vo.getName())
                .eq(Objects.nonNull(vo.getAdGroupId()), AdMappingGroup::getAdGroupId, vo.getAdGroupId())
                .eq(Objects.nonNull(vo.getId()), AdMappingGroup::getId, vo.getId())
                .in(AdMappingGroup::getStatus, vo.getStatus())
                .eq(Objects.nonNull(vo.getCreator()), AdMappingGroup::getCreator, vo.getCreator())
                .eq(!SecurityUtil.isAdmin(), AdMappingGroup::getCreator, SecurityUtil.getUserId())
                .eq(!SecurityUtil.isAdmin(), AdMappingGroup::getRoleId, SecurityUtil.getCurrentRole().getId())
                .orderByDesc(AdMappingGroup::getCreateTime);
        if (Objects.nonNull(vo.getMappingStyleIds())) {
            Set<Long> adMappingGroupIds = adMappingService.listAdMappingGroupIdsByMappingStyleIds(vo.getMappingStyleIds());
            if (adMappingGroupIds.isEmpty()) {
                return new Page<>();
            }
            wrapper.in(AdMappingGroup::getId, adMappingGroupIds);
        }
        if (Objects.nonNull(vo.getSourceStyleIds())) {
            Set<Long> adMappingGroupIds = adMappingService.listAdMappingGroupIdsBySourceStyleIds(vo.getSourceStyleIds());
            if (adMappingGroupIds.isEmpty()) {
                return new Page<>();
            }
            wrapper.in(AdMappingGroup::getId, adMappingGroupIds);
        }
        if (Objects.nonNull(vo.getCustomer())) {
            List<AdPlan> adPlans = adPlanService.listByCustomerId(vo.getCustomer());
            List<Long> adGroupIds = adGroupService.listNoteDeleteByAdPlanIds(adPlans.stream().map(AdPlan::getId).collect(Collectors.toSet()))
                    .stream().map(AdGroup::getId).collect(Collectors.toList());
            if (adGroupIds.isEmpty()) {
                return new Page<>();
            }
            wrapper.in(AdMappingGroup::getAdGroupId, adGroupIds);

        } else if (Objects.nonNull(vo.getAdGroupName())) {
            List<Long> adGroupIds = adGroupService.listByName(vo.getAdGroupName()).stream().map(AdGroup::getId).collect(Collectors.toList());
            if (adGroupIds.isEmpty()) {
                return new Page<>();
            }
            wrapper.in(AdMappingGroup::getAdGroupId, adGroupIds);
        }
        Page<AdMappingGroup> page = wrapper
                .page(new Page<>(vo.getCurrent(), vo.getSize()));
        List<AdGroup> adGroups = adGroupService.listByIds(page.getRecords().stream().map(AdMappingGroup::getAdGroupId).collect(Collectors.toSet()));
        Map<Long, AdGroup> id2AdGroup = adGroups
                .stream()
                .collect(Collectors.toMap(AdGroup::getId, Function.identity()));

        List<AdPlan> adPlans = adPlanService.listByIds(adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toSet()));
        Map<Long, Long> adPlanId2CustomerId = adPlans.stream().collect(Collectors.toMap(AdPlan::getId, AdPlan::getCustomerId));
        Set<Long> customerIds = adPlans
                .stream().map(AdPlan::getCustomerId)
                .collect(Collectors.toSet());
        Map<Long, String> id2Customer = customerService.listByIds(customerIds).stream().collect(Collectors.toMap(Customer::getId, Customer::getName));

        Set<Long> userIds = Stream.concat(
                page.getRecords().stream().map(AdMappingGroup::getCreator),
                page.getRecords().stream().map(AdMappingGroup::getModifier)
        ).collect(Collectors.toSet());

        Map<Long, User> id2User = userService.listByIds(userIds).stream().collect(Collectors.toMap(User::getId, Function.identity()));

        return page.convert(adMappingGroup -> adMappingGroupMap
                .vo2AdMappingPageResponse(adMappingGroup, id2AdGroup, id2User, adPlanId2CustomerId, id2Customer, getId2SourceStyleString(page), getId2MappingStyleString(page)));
    }

    private Map<Long, String> getId2SourceStyleString(Page<AdMappingGroup> page) {
        Map<Long, String> styleId2String = getStyleId2String(page);
        Map<Long, String> res = new HashMap<>();
        for (AdMappingGroup record : page.getRecords()) {
            List<String> ss = new ArrayList<>();
            for (Long sourceStyleId : record.getSourceStyleIds()) {
                ss.add(styleId2String.get(sourceStyleId));
            }
            res.put(record.getId(), StringUtils.join(ss, ","));
        }
        return res;
    }

    private Map<Long, String> getId2MappingStyleString(Page<AdMappingGroup> page) {
        Map<Long, String> styleId2String = getStyleId2String(page);
        Map<Long, String> res = new HashMap<>();
        for (AdMappingGroup record : page.getRecords()) {
            List<String> ss = new ArrayList<>();
            for (Long mappingStyleId : record.getMappingStyleIds()) {
                ss.add(styleId2String.get(mappingStyleId));
            }
            res.put(record.getId(), StringUtils.join(ss, ","));
        }
        return res;
    }

    private Map<Long, String> getStyleId2String(Page<AdMappingGroup> page) {
        List<Style> styles = styleService.listByIds(
                Stream.concat(
                                page.getRecords().stream().map(AdMappingGroup::getSourceStyleIds),
                                page.getRecords().stream().map(AdMappingGroup::getMappingStyleIds)
                        )
                        .flatMap(Set::stream)
                        .collect(Collectors.toSet()));
        List<AdPosition> adPositions = adPositionService.listByIds(styles.stream().map(Style::getAdPositionId).collect(Collectors.toList()));
        Map<Long, AdPosition> id2AdPosition = adPositions
                .stream().collect(Collectors.toMap(AdPosition::getId, Function.identity()));
        Map<Long, Media> id2Media = mediaService.listByIds(adPositions.stream().map(AdPosition::getMediaId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(Media::getId, Function.identity()));
        return formatString(id2Media, id2AdPosition, styles);
    }

    private Map<Long, String> formatString(Map<Long, Media> id2Media, Map<Long, AdPosition> id2AdPosition, List<Style> styles) {
        Map<Long, String> res = new HashMap<>();
        for (Style style : styles) {
            if (Objects.isNull(style)) {
                continue;
            }
            AdPosition adPosition = id2AdPosition.get(style.getAdPositionId());
            Media media = id2Media.get(adPosition.getMediaId());
            res.put(style.getId(), String.format("%s_%s_%s", media.getName(), adPosition.getName(), style.getName()));
        }
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(AdMappingGroupSaveVO adMappingGroupSaveVO) {
        saveOrUpdatePreCheck(adMappingGroupSaveVO);
        AdMappingGroup adMappingGroup = adMappingGroupMap.saveVO2DO(adMappingGroupSaveVO);
        saveOrUpdate(adMappingGroup);
        saveOrUpdateAdMapping(adMappingGroupSaveVO, adMappingGroup.getId());
    }

    private void saveOrUpdateAdMapping(AdMappingGroupSaveVO adMappingGroupSaveVO, Long adMappingGroupId) {
        AdMappingGroup adMappingGroup = getById(adMappingGroupId);
        adMappingService.removeByAdMappingGroupId(adMappingGroup.getId());
        Map<Integer, Set<Long>> os2SourceStyleIds = getOs2StyleIdMap(adMappingGroupSaveVO.getSourceStyleIds());
        Map<Integer, Set<Long>> os2SMappingStyleIds = getOs2StyleIdMap(adMappingGroupSaveVO.getMappingStyleIds());
        if (!os2SourceStyleIds.keySet().equals(os2SMappingStyleIds.keySet())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "原始位置的客户端类型与映射后位置客户端类型不一致，请检查");
        }
        List<AdMapping> adMappings = new ArrayList<>();
        for (Integer os : os2SMappingStyleIds.keySet()) {
            Set<Long> sourceStyleIds = os2SourceStyleIds.get(os);
            Set<Long> mappingStyleIds = os2SMappingStyleIds.get(os);
            if (mappingStyleIds.size() > 1) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "映射后位置均为相同客户端位置，请修改");
            }
            for (Long sourceStyleId : sourceStyleIds) {
                for (Long mappingStyleId : mappingStyleIds) {
                    AdMapping adMapping = new AdMapping();
                    adMapping.setSourceStyleId(sourceStyleId);
                    adMapping.setMappingStyleId(mappingStyleId);
                    adMapping.setAdMappingGroupId(adMappingGroup.getId());
                    adMapping.setAdGroupId(adMappingGroup.getAdGroupId());
                    adMapping.setName(adMappingGroup.getName());
                    adMapping.setStatus(adMappingGroup.getStatus());
                    adMappings.add(adMapping);
                }
            }
        }
        adMappingService.saveBatch(adMappings);
    }

    private Map<Integer, Set<Long>> getOs2StyleIdMap(Set<Long> styleIds) {
        Map<Integer, Set<Long>> res = new HashMap<>();
        for (Long styleId : styleIds) {
            res.computeIfAbsent(mediaService.getOsByStyleId(styleId), (k) -> new HashSet<>()).add(styleId);
        }
        return res;
    }

    private void saveOrUpdatePreCheck(AdMappingGroupSaveVO adMappingGroupSaveVO) {
        if (nameExists(adMappingGroupSaveVO.getId(), adMappingGroupSaveVO.getName())) {
            throw new CustomException(ResponseType.NAME_REPEATED_EXCEPTION, "映射名称不可重复");
        }
        if (CollectionUtils.isEmpty(adMappingGroupSaveVO.getSourceStyleIds())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "原始位置不能为空");
        }
        if (CollectionUtils.isEmpty(adMappingGroupSaveVO.getMappingStyleIds())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "映射后位置不可为空");
        }
        if (adMappingGroupSaveVO.getMappingStyleIds().size() > 2) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "映射后位置仅支持iOS端和安卓端各选1个，请修改");
        }
        if (new LambdaQueryChainWrapper<>(adMappingService.getBaseMapper())
                .eq(AdMapping::getAdGroupId, adMappingGroupSaveVO.getAdGroupId())
                .in(AdMapping::getSourceStyleId, adMappingGroupSaveVO.getSourceStyleIds())
                .ne(AdMapping::getStatus, 2)
                .ne(Objects.nonNull(adMappingGroupSaveVO.getId()), AdMapping::getAdMappingGroupId, adMappingGroupSaveVO.getId())
                .exists()) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "当前推广组和原始广告位下已有重复的映射，无法保存，请修改");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(UpdateStatusVO vo) {
        AdMappingGroup adMappingGroup = getById(vo.getId());
        if (adMappingGroup.getStatus().equals(2)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "已删除的映射不予许修改状态");
        }
        this.updateById(adMappingGroupMap.updateStatusVO2DO(vo));
        adMappingService.updateStatusByAdMappingGroupId(vo.getId(), vo.getStatus());
    }

    @Override
    public boolean nameExists(Long id, String name) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .ne(Objects.nonNull(id), AdMappingGroup::getId, id)
                .eq(AdMappingGroup::getName, name)
                .exists();
    }

    @Override
    public AdMappingGroup getValidById(Long id) {
        AdMappingGroup adMappingGroup = this.getById(id);
        if (isSourceStyleInvalid(adMappingGroup)) {
            adMappingGroup.setSourceStyleIds(null);
        }
        return adMappingGroup;
    }

    private boolean isSourceStyleInvalid(AdMappingGroup adMappingGroup) {
        return !adContentRelationService.listValidByAdGroupIds(Collections.singletonList(adMappingGroup.getAdGroupId()))
                .stream().map(AdContentRelation::getStyleId)
                .collect(Collectors.toSet())
                .containsAll(adMappingGroup.getSourceStyleIds());
    }
}
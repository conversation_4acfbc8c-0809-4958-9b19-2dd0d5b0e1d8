package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AdMappingCriteriaQueryVO {
    @ApiModelProperty(value = "当前页面", required = true)
    @NotNull(message = "当前页面不能为空")
    private Long current;
    @ApiModelProperty(value = "页面大小", required = true)
    @NotNull(message = "页面大小不能为空")
    private Long size;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("推广组id")
    private String adGroupId;
    @ApiModelProperty("推广组名称")
    private String adGroupName;
    @ApiModelProperty("客户id")
    private Long customer;
    @ApiModelProperty("创建人id")
    private String creator;
    @ApiModelProperty("原始投放样式id")
    private List<Long> sourceStyleIds;
    @ApiModelProperty("映射后样式id")
    private List<Long> mappingStyleIds;
    @ApiModelProperty("状态 0-开启，1-暂停，2-删除")
    private List<Integer> status;
}

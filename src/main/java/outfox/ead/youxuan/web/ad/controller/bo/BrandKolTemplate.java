package outfox.ead.youxuan.web.ad.controller.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2022年02月13日 9:55 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BrandKolTemplate extends BaseTemplate{
    @ExcelProperty(value = "公司名称")
    @Size(max = 50, message = "公司名称不得超过50个字")
    @NotNull(message = "公司名称不能为空")
    private String companyName;
    @ExcelProperty(value = "媒体账户id")
    @NotNull(message = "媒体账户id不能为空")
    private String appUserId;
    @ExcelProperty(value = "内容标签")
    private String contentTagName;

    @ExcelProperty(value = "所属MCN")
    @Size(max = 20,message = "mcn不可超过20个字")
    private String mcn;

    @ExcelProperty(value = "责任运营优选账户")
    private String kolOperatorUsername;

    @ExcelProperty(value = "主页链接")
    private String homepageLink;
}

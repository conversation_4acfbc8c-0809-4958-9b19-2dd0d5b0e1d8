package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.AdPosition;
import outfox.ead.youxuan.entity.Style;
import outfox.ead.youxuan.web.ad.controller.vo.StyleByIdVO;
import outfox.ead.youxuan.web.ad.controller.vo.StyleCountByNameVO;
import outfox.ead.youxuan.web.ad.controller.vo.StyleListVO;
import outfox.ead.youxuan.web.ad.controller.vo.StyleSaveOrUpdateVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/17/10:54
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface StyleMapper {
    @Mapping(target = "name", expression = "java(vo.getName().trim())")
    Style saveOrUpdateVoToDo(StyleSaveOrUpdateVO vo);

    StyleCountByNameVO doToCountByName(StyleSaveOrUpdateVO styleSaveOrUpdateVO);

    StyleByIdVO doToStyleByIdVO(Style style);

    default List<StyleListVO> doToStyleListVO(List<Style> styles, Map<Long, AdPosition> idToAdPositionMap, Map<Long, String> idToMediaNameMap) {
        if (styles == null) {
            return null;
        }
        List<StyleListVO> list = new ArrayList<>(styles.size());
        for (Style style : styles) {
            list.add(do2StyleListVO(style, idToAdPositionMap, idToMediaNameMap));
        }
        return list;
    }

    @Mapping(target = "adPositionName", expression = "java(idToAdPositionMap.get(style.getAdPositionId()).getName())")
    @Mapping(target = "mediaId", expression = "java(idToAdPositionMap.get(style.getAdPositionId()).getMediaId())")
    @Mapping(target = "mediaName", expression = "java(idToMediaNameMap.get(idToAdPositionMap.get(style.getAdPositionId()).getMediaId()))")
    StyleListVO do2StyleListVO(Style style, Map<Long, AdPosition> idToAdPositionMap, Map<Long, String> idToMediaNameMap);

    StyleSaveOrUpdateVO doToSaveOrUpdateVO(Style style);
}

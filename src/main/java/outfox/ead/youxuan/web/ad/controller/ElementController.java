package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.vo.ElementListVO;
import outfox.ead.youxuan.web.ad.service.ElementService;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Collection;

/**
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/20 17:09
 */
@RestController
@RequestMapping("/element")
@BaseResponse
@Validated
@AllArgsConstructor
@AccessControl(roles = RoleEnum.AD_OPERATOR)
@Api(tags = "元素接口")
public class ElementController {
    private final ElementService elementService;

    @GetMapping
    public Collection<ElementListVO> getElementList(@RequestParam
                                                    @Max(value = 3, message = "元素类型错误")
                                                    @Min(value = 1, message = "元素类型错误") Integer type) {
        return elementService.getValidByType(type);
    }
}

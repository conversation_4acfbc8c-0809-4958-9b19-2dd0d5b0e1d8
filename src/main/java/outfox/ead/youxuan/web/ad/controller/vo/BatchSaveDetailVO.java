package outfox.ead.youxuan.web.ad.controller.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年02月22日 11:43 上午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchSaveDetailVO {
    private Long total = 0L;
    private Long groupIdCount = 0L;
    private Long success = 0L;
    private Long skip = 0L;
    private Long fail = 0L;
    private List<FailSaveInfoVO> failInfos = new ArrayList<>(0);

    public void total(Long total) {
        this.total += total;
    }

    public void success(Long success) {
        this.success += success;
    }
    public void fail(Long fail) {
        this.fail += fail;
    }

    public void skip() {
        this.skip += 1L;
    }

    public void skip(Long skip) {
        this.skip += skip;
    }

    public void failInfo(FailSaveInfoVO failSaveInfoVO) {
        this.failInfos.add(failSaveInfoVO);
    }

    public void add(BatchSaveDetailVO batchSaveDetailVO) {
        this.total += batchSaveDetailVO.total;
        this.groupIdCount += batchSaveDetailVO.groupIdCount;
        this.success += batchSaveDetailVO.success;
        this.fail += batchSaveDetailVO.fail;
        this.failInfos.addAll(batchSaveDetailVO.failInfos);
        this.skip += batchSaveDetailVO.skip;
    }
}

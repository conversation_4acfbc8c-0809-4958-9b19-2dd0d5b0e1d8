package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import outfox.ead.youxuan.entity.Element;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/25 12:15
 */
@Data
@NoArgsConstructor
public class ElementVO {
    @ApiModelProperty("长宽比")
    private String ratio;

    @ApiModelProperty("文字样式元素元素长度")
    private Integer length;

    @ApiModelProperty("图片样式元素高度")
    private Integer height;

    @ApiModelProperty("图片样式元素宽度")
    private Integer width;

    /**
     * 媒体格式格式
     * MP4,JPG
     */
    private String mimeType;

    public ElementVO(Element a) {
        this.ratio = a.getRatio();
        this.length = a.getLength();
        this.height = a.getHeight();
        this.width = a.getWidth();
        this.mimeType = a.getMimeType();
    }
}

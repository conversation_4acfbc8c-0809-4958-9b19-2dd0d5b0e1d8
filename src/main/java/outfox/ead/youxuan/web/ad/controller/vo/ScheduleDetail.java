package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * 排期表一天详细的信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleDetail {
    @ApiModelProperty("排期表日期")
    private LocalDate exactDate;
    @ApiModelProperty("轮播数")
    private Integer displayTimes;
    @ApiModelProperty("存量")
    private Integer displayStock;
    @ApiModelProperty("已售")
    private Double sale;
    @ApiModelProperty("已售比例")
    private Double displaySalesRatio;
    @ApiModelProperty("状态 0-全部售出 1-部分售出 2-暂停 3-空闲")
    private Integer status;
}

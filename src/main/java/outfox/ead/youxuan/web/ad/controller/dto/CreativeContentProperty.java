package outfox.ead.youxuan.web.ad.controller.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/14 19:48
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreativeContentProperty {
    @ApiModelProperty("图片比例")
    @EqualsAndHashCode.Exclude
    private String ratio;
    @ApiModelProperty("文字样式元素元素长度")
    private Integer length;
    @ApiModelProperty("图片样式元素高度")
    private Integer height;
    @ApiModelProperty("图片样式元素宽度")
    private Integer width;
    @ApiModelProperty("媒体格式 mp4,jpg等等")
    @EqualsAndHashCode.Exclude
    private String mimeType;
    @ApiModelProperty("文本内容")
    @EqualsAndHashCode.Exclude
    private String text;
    @ApiModelProperty("图片或视频链接")
    @EqualsAndHashCode.Exclude
    private String url;
    @ApiModelProperty("图片或视频链接数组")
    @EqualsAndHashCode.Exclude
    private List<String> urls;

    public void setOldData(CreativeContentProperty oldProperty) {
        this.text = oldProperty.getText();
        this.url = oldProperty.getUrl();
        this.urls = oldProperty.getUrls();
        this.mimeType = oldProperty.getMimeType();
    }
}

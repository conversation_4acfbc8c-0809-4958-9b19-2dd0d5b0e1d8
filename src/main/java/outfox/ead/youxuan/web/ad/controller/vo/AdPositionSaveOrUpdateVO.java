package outfox.ead.youxuan.web.ad.controller.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/24/17:58
 */
@Data
public class AdPositionSaveOrUpdateVO {

    @ApiModelProperty("广告位主键")
    private Long id;

    @ApiModelProperty("媒体ID")
    @NotNull(message = "媒体id不能为空")
    private Long mediaId;

    @ApiModelProperty("广告位名称")
    @NotBlank(message = "广告位名称不能为空白")
    private String name;

    @ApiModelProperty("广告位类型")
    @NotNull(message = "广告位类型不能为空")
    @Min(value = 0, message = "广告位类型错误")
    @Max(value = 6, message = "广告位类型错误")
    private Integer type;


    @ApiModelProperty("支持轮播图片数量")
    @Min(value = 1, message = "轮播数设置错误")
    @Max(value = 127, message = "轮播数超过最大上限，请修改")
    private Integer displayTimes;

    /**
     * 推广标类型 <p>
     * 1-落地页推广<p>
     * 2-应用直达<p>
     * 3-小程序推广
     */
    @NotNull(message = "推广标类型不能为空")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> promotionType;

    @ApiModelProperty("查词定向 0-不支持 1-支持")
    private boolean supportAdvertisingByKeywords;

    @ApiModelProperty("状态")
    @Min(value = 0, message = "状态类型错误")
    @Max(value = 2, message = "状态类型错误")
    @NotNull(message = "状态不能为空")
    private Integer status;
}

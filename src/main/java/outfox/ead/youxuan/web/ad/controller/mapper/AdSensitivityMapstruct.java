package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.AdSensitivity;
import outfox.ead.youxuan.web.ad.controller.vo.AdSensitivityDetailVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025-05-19 18:25
 **/
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface AdSensitivityMapstruct {

    @Mapping(target = "modifierName", expression = "java(userIdToNameMap.get(adSensitivity.getModifier()))")
    @Mapping(target = "modifierTime", expression = "java(adSensitivity.getLastModTime().format(java.time.format.DateTimeFormatter.ofPattern(outfox.ead.youxuan.constants.Constants.MIN_FORMAT)))")
    @Mapping(target = "adPositionName", expression = "java(adPositionIdToNameMap.get(adSensitivity.getAdPositionId()))")
    AdSensitivityDetailVO toDetailVO(AdSensitivity adSensitivity, Map<Long, String> userIdToNameMap, Map<Long, String> adPositionIdToNameMap);
}
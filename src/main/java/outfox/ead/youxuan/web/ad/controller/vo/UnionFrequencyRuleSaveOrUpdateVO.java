package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;


@Data
public class UnionFrequencyRuleSaveOrUpdateVO {

    @ApiModelProperty("规则id")
    private Long id;

    @ApiModelProperty("规则名称")
    private String name;

    @NotNull(message = "adPlanIdList不能为null")
    @ApiModelProperty("关联的广告计划ids")
    private List<Long> adPlanIdList;

    @ApiModelProperty("频控方式:0-不频控 1-按天频控 2-按周频控 3-按投放周期频控")
    @Min(value = 1, message = "频控方式类型错误")
    @Max(value = 3, message = "频控方式类型错误")
    @NotNull(message = "frequencyType不能为null")
    private Integer frequencyType;

    @ApiModelProperty("频控次数")
    @Min(value = 1, message = "频控次数错误")
    @Max(value = 20, message = "频控次数错误")
    @NotNull(message = "frequencyLimit次数错误")
    private Integer frequencyLimit;

}

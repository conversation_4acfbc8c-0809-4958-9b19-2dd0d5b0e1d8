package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.core.validator.Phone;
import outfox.ead.youxuan.web.ad.controller.bo.LoginUser;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年02月09日 3:44 下午
 */
@Data
public class UserDetailVO {
    private Long id;
    @ApiModelProperty("优选id")
    private Long userId;
    @ApiModelProperty("账户昵称")
    @Size(max = 20, message = "昵称最多20个字符")
    @NotNull
    private String nickname;
    @ApiModelProperty("账户头像")
    private String avatar;
    @ApiModelProperty("联系人姓名")
    @Size(max = 20, message = "联系人姓名最多20个字符")
    private String name;
    @ApiModelProperty("联系电话")
    @Phone
    private String phone;
    @ApiModelProperty("联系邮箱")
    @Email(regexp = "^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$"
            , message = "联系邮箱格式不正确，请重新输入")
    private String email;
    @ApiModelProperty("公司名称")
    @Size(max = 50)
    private String companyName;
    @ApiModelProperty("行业信息")
    private Integer industry;
    @ApiModelProperty("用户信息完善的阶段")
    private Integer stage;
    @ApiModelProperty("指派任务权限")
    private Boolean taskPermission;
    @ApiModelProperty("投稿任务权限")
    private Boolean postTaskPermission;
    @ApiModelProperty("橱窗是否开通")
    private Boolean isProductWindowOpen;
    @ApiModelProperty("是否绑定了词典账户")
    private Boolean isBindDict;
    @ApiModelProperty("是否通过了资质认证")
    private Boolean verified;
    @ApiModelProperty("角色")
    private List<String> roleKeys;
    private LoginUser loginUser;
}

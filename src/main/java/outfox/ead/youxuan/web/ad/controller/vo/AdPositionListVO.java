package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: 李梦杰
 * @date: 2021/8/30/18:11
 * @description:
 */
@Data
public class AdPositionListVO {
    @ApiModelProperty("广告位主键")
    private Long id;

    @ApiModelProperty("广告位名称")
    private String name;

    @ApiModelProperty("媒体id")
    private Long mediaId;
    @ApiModelProperty("媒体名称")
    private String mediaName;

    @ApiModelProperty("支持轮播图片数量")
    private Integer displayTimes;

    @ApiModelProperty("创意内容类型 0-信息流 1-开屏 2-插屏 3-焦点图 4-激励视频 5-横幅 6-自定义")
    private Integer type;

    @ApiModelProperty("状态")
    private Integer status;

}

package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.entity.AdDeliveryTime;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface AdDeliveryTimeService extends YouxuanService<AdDeliveryTime> {
    /**
     * 通过推广计划id删除
     *
     * @param adPlanId 推广计划id
     */
    void removeByAdPlanId(Long adPlanId);

    /**
     * 投放时间列表
     *
     * @param adPlanId 推广计划id
     * @return 投放时间列表
     */
    List<AdDeliveryTime> listByAdPlanId(Long adPlanId);

    /**
     * 根据推广计划主键列表查询对应的数据
     *
     * @param adPlanIds 推广计划id列表
     * @return 投放时间列表
     */
    List<AdDeliveryTime> listByAdPlanIds(Collection<Long> adPlanIds);

    /**
     * 查询命中时间的发送计划，通过推广计划id聚合
     *
     * @param date 时间
     * @return 投放时间列表
     */
    List<AdDeliveryTime> listByTimeGroupById(LocalDateTime date);

    /**
     * 拿到重叠时间
     *
     * @param adDeliveryTimes 时间段列表
     * @param excludeAdPlanId 需要排除掉的推广计划id，用于修改的时候资源校验
     * @return 投放时间列表
     */
    List<AdDeliveryTime> listOverlapDeliveryTimeByAdDeliveryTimesExcludeAdPlan(Collection<AdDeliveryTime> adDeliveryTimes, Long excludeAdPlanId);

    /**
     * 通过时间段查询
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 投放时间列表
     */
    List<AdDeliveryTime> listByTimeInterval(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 通过推广计划id列表以及开始结束时间查询投放时间列表
     *
     * @param adPlanIds 推广计划id列表
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 投放时间列表
     */
    List<AdDeliveryTime> listByAdPlanIdsAndTimeInterval(Collection<Long> adPlanIds, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 通过planIds查询已投放过的plan
     *
     * @param adPlanIds 推广计划id列表
     * @return 推广计划id
     */
    Set<Long> listDeliveredByAdPlanIds(Collection<Long> adPlanIds);

    /**
     * 历史有投放过 = 投放结束 + 投放中
     *
     * @param adPlanIds 推广计划id列表
     * @return true-投放过
     */
    boolean isDelivered(Collection<Long> adPlanIds);

    /**
     * 投放中
     *
     * @param adPlanIds 推广计划id列表
     * @return true-投放中
     */
    boolean isDelivering(Collection<Long> adPlanIds);

    /**
     * 在投
     *
     * @param adPlanIds 推广计划id列表
     * @return true-投放中
     */
    boolean isInDelivery(Set<Long> adPlanIds);
}

package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.youxuan.constants.InteractionTypeEnum;
import outfox.ead.youxuan.constants.PromotionStatusEnum;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.AdSensitivity;
import outfox.ead.youxuan.entity.AdGroup;
import outfox.ead.youxuan.entity.User;
import outfox.ead.youxuan.entity.AdPosition;
import outfox.ead.youxuan.entity.Media;
import outfox.ead.youxuan.mapper.youxuan.AdSensitivityMapper;
import outfox.ead.youxuan.mapper.youxuan.AdPositionMapper;
import outfox.ead.youxuan.web.ad.controller.mapper.AdSensitivityMapstruct;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.AdGroupService;
import outfox.ead.youxuan.web.ad.service.AdSensitivityService;
import outfox.ead.youxuan.web.ad.service.UserService;
import outfox.ead.youxuan.web.ad.service.MediaService;

import java.util.*;
import java.util.stream.Collectors;
import java.util.function.Function;

/**
 * 开屏灵敏度配置 Service 实现类
 *
 * <AUTHOR>
 * @create 2024-03-21
 **/
@RequiredArgsConstructor
@Service
@Slf4j
public class AdSensitivityServiceImpl extends YouxuanServiceImpl<AdSensitivityMapper, AdSensitivity>
        implements AdSensitivityService {

    private final AdSensitivityMapstruct adSensitivityMapstruct;
    private final UserService userService;
    private final AdGroupService adGroupService;
    private final MediaService mediaService;
    private final AdPositionMapper adPositionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAdSensitivity(AdSensitivityCreateVO adSensitivityCreateVO) {
        // 1.校验交互类型对应的灵敏度配置项
        validateSensitivityConfig(adSensitivityCreateVO.getClickType(), adSensitivityCreateVO.getSlideAngle(),
                adSensitivityCreateVO.getShakeSpeed(), adSensitivityCreateVO.getRotationAngle());

        // 2.查询所有配置并按adGroupId、clickType和adPositionId分组
        Map<Long, Map<Integer, Map<Long, List<AdSensitivity>>>> groupedConfigs = new LambdaQueryChainWrapper<>(baseMapper)
                .in(AdSensitivity::getAdGroupId, adSensitivityCreateVO.getAdGroupIds())
                .list()
                .stream()
                .collect(Collectors.groupingBy(
                        AdSensitivity::getAdGroupId,
                        Collectors.groupingBy(
                                AdSensitivity::getClickType,
                                Collectors.groupingBy(AdSensitivity::getAdPositionId)
                        )
                ));

        Map<Long, Set<Long>> adPosition2Groups = adGroupService
                .getSupportedAdGroupsByInteractionTypeAndPositions(adSensitivityCreateVO.getClickType(), adSensitivityCreateVO.getAdPositionIds());

        List<AdSensitivity> toUpdate = new ArrayList<>();
        List<AdSensitivity> toInsert = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>();

        // 3.处理每个推广组
        for (Long adGroupId : adSensitivityCreateVO.getAdGroupIds()) {
            Map<Integer, Map<Long, List<AdSensitivity>>> groupConfigs = groupedConfigs.getOrDefault(adGroupId, Collections.emptyMap());
            // 需要删除的配置
            groupConfigs.entrySet().stream()
                    .filter(entry -> !entry.getKey().equals(adSensitivityCreateVO.getClickType()))
                    .flatMap(entry -> entry.getValue().values().stream())
                    .flatMap(List::stream)
                    .filter(sensitivity -> !sensitivity.getIsDeleted())
                    .map(AdSensitivity::getId)
                    .forEach(toDelete::add);
            // 处理每个广告位
            for (Long adPositionId : adSensitivityCreateVO.getAdPositionIds()) {
                // 如果推广组不支持该广告位，则跳过
                if (!adPosition2Groups.getOrDefault(adPositionId, Collections.emptySet()).contains(adGroupId)) {
                    log.info("saveAdSensitivity: clickType is {},AdGroup {} does not support AdPosition {}", adSensitivityCreateVO.getClickType(), adGroupId, adPositionId);
                    continue;
                }

                // 获取当前adGroupId、clickType和adPositionId的配置列表
                List<AdSensitivity> positionConfigs = groupConfigs
                        .getOrDefault(adSensitivityCreateVO.getClickType(), Collections.emptyMap())
                        .getOrDefault(adPositionId, Collections.emptyList());

                // 优先查找未删除的记录
                AdSensitivity existingConfig = positionConfigs.stream()
                        .min((a, b) -> Boolean.compare(a.getIsDeleted(), b.getIsDeleted()))
                        .orElse(null);

                if (existingConfig != null) {
                    existingConfig.setShakeSpeed(adSensitivityCreateVO.getShakeSpeed());
                    existingConfig.setSlideAngle(adSensitivityCreateVO.getSlideAngle());
                    existingConfig.setRotationAngle(adSensitivityCreateVO.getRotationAngle());
                    existingConfig.setIsDeleted(false);
                    toUpdate.add(existingConfig);
                } else {
                    AdSensitivity newConfig = new AdSensitivity();
                    newConfig.setClickType(adSensitivityCreateVO.getClickType());
                    newConfig.setAdGroupId(adGroupId);
                    newConfig.setAdPositionId(adPositionId);
                    newConfig.setShakeSpeed(adSensitivityCreateVO.getShakeSpeed());
                    newConfig.setSlideAngle(adSensitivityCreateVO.getSlideAngle());
                    newConfig.setRotationAngle(adSensitivityCreateVO.getRotationAngle());
                    newConfig.setIsDeleted(false);
                    toInsert.add(newConfig);
                }
            }
        }

        if (!toDelete.isEmpty()) {
            logicDeleteByIds(toDelete);
        }
        if (!toUpdate.isEmpty()) {
            updateBatchById(toUpdate);
        }
        if (!toInsert.isEmpty()) {
            saveBatch(toInsert);
        }
    }

    /**
     * 校验交互类型对应的灵敏度配置项
     */
    private void validateSensitivityConfig(Integer clickType, Integer slideAngle, Integer shakeSpeed, Integer rotationAngle) {
        InteractionTypeEnum interactionTypeEnum = InteractionTypeEnum.fromCode(clickType);
        if (interactionTypeEnum == null) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS.getCode(), "不支持的交互类型");
        }

        switch (interactionTypeEnum) {
            case SHAKABLE:
                if (rotationAngle == null || rotationAngle < 0 || rotationAngle > 180) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS.getCode(), "摇动角度请输入0-180的整数值");
                }
                if (shakeSpeed == null || shakeSpeed <= 10) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS.getCode(), "摇动加速度请输入大于10的整数值");
                }
                break;
            case SLIDE_INTERACT:
                if (slideAngle == null || slideAngle < 0 || slideAngle > 360) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS.getCode(), "滑动角度请输入0-360的整数值");
                }
                break;
            case TWIST:
                if (rotationAngle == null || rotationAngle < 0 || rotationAngle > 180) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS.getCode(), "扭转角度请输入0-180的整数值");
                }
                break;
            case THREE_IN_ONE:
                if (rotationAngle == null || rotationAngle < 0 || rotationAngle > 180) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS.getCode(), "扭转角度请输入0-180的整数值");
                }
                if (slideAngle == null || slideAngle < 0 || slideAngle > 360) {
                    throw new CustomException(ResponseType.INVALID_PARAMETERS.getCode(), "滑动角度请输入0-360的整数值");
                }
                break;
            default:
                throw new CustomException(ResponseType.INVALID_PARAMETERS.getCode(), "不支持的交互类型");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAdSensitivity(List<Long> ids) {
        logicDeleteByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAdSensitivity(AdSensitivityUpdateVO adSensitivityUpdateVO) {
        AdSensitivity adSensitivity = getById(adSensitivityUpdateVO.getId());
        // 1. 校验交互类型对应的灵敏度配置项
        validateSensitivityConfig(adSensitivity.getClickType(), adSensitivityUpdateVO.getSlideAngle(),
                adSensitivityUpdateVO.getShakeSpeed(), adSensitivityUpdateVO.getRotationAngle());
        // 2. 更新
        adSensitivity.setShakeSpeed(adSensitivityUpdateVO.getShakeSpeed());
        adSensitivity.setSlideAngle(adSensitivityUpdateVO.getSlideAngle());
        adSensitivity.setRotationAngle(adSensitivityUpdateVO.getRotationAngle());
        updateById(adSensitivity);
    }

    @Override
    public AdSensitivityDetailVO getAdSensitivityDetail(Long id) {
        AdSensitivity adSensitivity = getById(id);
        Map<Long, String> userIdToNameMap = getUserIdToName(Collections.singletonList(adSensitivity.getModifier()));
        Map<Long, String> adPositionIdToNameMap = getAdSensitivitySupportPositions().stream()
                .collect(Collectors.toMap(AdSensitivitySupportPositionVO::getAdPositionId, AdSensitivitySupportPositionVO::getAdPositionName));
        return adSensitivityMapstruct.toDetailVO(adSensitivity, userIdToNameMap, adPositionIdToNameMap);
    }

    private Map<Long, String> getUserIdToName(List<Long> userIds) {
        return userService.listByIds(userIds).stream()
                .collect(Collectors.toMap(User::getId, User::getUsername));
    }

    /**
     * 逻辑删除
     *
     * @param ids
     */
    private void logicDeleteByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        List<AdSensitivity> toLogicDelete = ids.stream()
                .map(id -> {
                    AdSensitivity entity = new AdSensitivity();
                    entity.setId(id);
                    entity.setIsDeleted(true);
                    return entity;
                })
                .collect(Collectors.toList());
        updateBatchById(toLogicDelete);
    }

    @Override
    public IPage<AdSensitivityDetailVO> getAdSensitivityList(AdSensitivityQueryVO adSensitivityQueryVO) {
        // 获取支持灵敏度配置的广告位ID列表
        List<Long> supportedAdPositionIds = getAdSensitivitySupportPositions().stream()
                .map(AdSensitivitySupportPositionVO::getAdPositionId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(supportedAdPositionIds)) {
            return new Page<>();
        }

        LambdaQueryWrapper<AdSensitivity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(adSensitivityQueryVO.getClickType() != null, AdSensitivity::getClickType, adSensitivityQueryVO.getClickType())
                .eq(adSensitivityQueryVO.getAdPositionId() != null, AdSensitivity::getAdPositionId, adSensitivityQueryVO.getAdPositionId())
                .eq(adSensitivityQueryVO.getAdGroupId() != null, AdSensitivity::getAdGroupId, adSensitivityQueryVO.getAdGroupId())
                .eq(AdSensitivity::getIsDeleted, false)
                .in(AdSensitivity::getAdPositionId, supportedAdPositionIds)  // 新增：只查询支持的广告位
                .notIn(AdSensitivity::getAdGroupId, new LambdaQueryWrapper<AdGroup>()
                        .select(AdGroup::getId)
                        .in(AdGroup::getStatus, Arrays.asList(PromotionStatusEnum.DELIVER_FINISH.getCode(), PromotionStatusEnum.DELETED.getCode())))
                .orderByDesc(AdSensitivity::getLastModTime);
        Page<AdSensitivity> page = new Page<>(adSensitivityQueryVO.getCurrent(), adSensitivityQueryVO.getSize());
        IPage<AdSensitivity> adSensitivityPage = page(page, wrapper);

        Map<Long, String> userIdToNameMap = getUserIdToName(
                adSensitivityPage.getRecords().stream().map(AdSensitivity::getModifier).collect(Collectors.toList())
        );
        Map<Long, String> adPositionIdToNameMap = getAdSensitivitySupportPositions().stream()
                .collect(Collectors.toMap(AdSensitivitySupportPositionVO::getAdPositionId, AdSensitivitySupportPositionVO::getAdPositionName));
        return adSensitivityPage.convert(adSensitivity ->
                adSensitivityMapstruct.toDetailVO(adSensitivity, userIdToNameMap, adPositionIdToNameMap)
        );
    }

    @Override
    public List<AdSensitivitySupportPositionVO> getAdSensitivitySupportPositions() {
        List<AdPosition> adPositions = new LambdaQueryChainWrapper<>(adPositionMapper)
                .eq(AdPosition::getHasSensitivity, true)
                .list();
        if (CollectionUtils.isEmpty(adPositions)) {
            return Collections.emptyList();
        }

        Map<Long, Media> mediaMap = mediaService.listValidByIds(adPositions.stream()
                        .map(AdPosition::getMediaId)
                        .collect(Collectors.toSet()))
                .stream()
                .collect(Collectors.toMap(Media::getId, Function.identity()));

        return adPositions.stream()
                .map(adPosition -> {
                    AdSensitivitySupportPositionVO vo = new AdSensitivitySupportPositionVO();
                    vo.setAdPositionId(adPosition.getId());
                    Media media = mediaMap.get(adPosition.getMediaId());
                    vo.setAdPositionName(String.format("%s(%s)", media.getName(), adPosition.getName()));
                    return vo;
                })
                .collect(Collectors.toList());
    }
}
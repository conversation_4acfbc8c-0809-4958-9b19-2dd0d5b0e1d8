package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.AccountTypeEnum;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.annotation.DistributedLock;
import outfox.ead.youxuan.core.annotation.DistributedLockKey;
import outfox.ead.youxuan.core.config.security.JwtSecurityToken;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.util.CookieVerifyUtil;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.bo.LoginUserVO;
import outfox.ead.youxuan.web.ad.controller.mapper.UserMapper;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.controller.vo.UserRelationVO;
import outfox.ead.youxuan.web.ad.service.UserRelationService;
import outfox.ead.youxuan.web.ad.service.UserService;
import outfox.ead.youxuan.web.kol.service.DictService;

import javax.validation.constraints.NotNull;

import static outfox.ead.youxuan.constants.Constants.BIND;
import static outfox.ead.youxuan.constants.Constants.UNBIND;
import static outfox.ead.youxuan.constants.ResponseType.INVALID_PARAMETERS;
import static outfox.ead.youxuan.constants.RoleEnum.SPONSOR;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/11 19:58
 */
@RestController
@AllArgsConstructor
@Validated
@BaseResponse
@Slf4j
@Api(tags = "用户接口")
@RequestMapping("/user")
public class UserController {
    private final UserService userService;
    private final UserRelationService userRelationService;
    private final DictService dictService;
    private final UserMapper userMapper;

    @PostMapping("bind")
    @ApiOperation("绑定用户")
    @AccessControl(roles = {SPONSOR, RoleEnum.BRAND_KOL})
    public void bindUser(String token) {
        long id = JwtSecurityToken.getClaimsFromToken(token).getUserId();
        Long currentUserId = SecurityUtil.getUserId();
        assert currentUserId != null;
        Long kolUserId = SecurityUtil.checkCurrentRole(RoleEnum.BRAND_KOL) ? currentUserId : id;
        Long sponsorUserId = SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) ? currentUserId : id;
        userRelationService.bindUser(kolUserId, sponsorUserId);
    }

    @PostMapping("rebind")
    @ApiOperation("重新绑定用户")
    @AccessControl(roles = {SPONSOR, RoleEnum.BRAND_KOL})
    public void rebindUser(Long relationId) {
        userRelationService.rebind(relationId, BIND);
    }

    @PostMapping("unbind")
    @ApiOperation("解除绑定用户")
    @AccessControl(roles = {SPONSOR, RoleEnum.BRAND_KOL})
    public void unbindUser(Long relationId) {
        userRelationService.updateStatusById(relationId, UNBIND);
    }

    @GetMapping("relations")
    @ApiOperation("分页获取用户绑定关系")
    public PageVO<UserRelationVO> getUserRelation(@NotNull @ApiParam("页数") Long current,
                                                  @NotNull @ApiParam("条数") Long size) {
        if (SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR)) {
            return userRelationService.pageBySponsorUserId(SecurityUtil.getUserId(), current, size);
        } else {
            return userRelationService.pageByKolUserId(SecurityUtil.getUserId(), current, size);
        }
    }

    @GetMapping("currentUser")
    public LoginUserVO getUserInfo() {
        return userMapper.loginUser2Vo(SecurityUtil.getLoginUser());
    }

    @ApiOperation("绑定账户")
    @GetMapping("bindAccount")
    public void bindAccount(@ApiParam("0-163邮箱 1-词典uid") AccountTypeEnum accountTypeEnum,
                            @RequestHeader(value = HttpHeaders.COOKIE) String cookie) {
        if (accountTypeEnum.equals(AccountTypeEnum.MAIL_163)) {
            userService.sendActivationBindEmail(CookieVerifyUtil.verifyCookieWithEmailAndGetEmail(cookie), SecurityUtil.getUserId());
        } else if (accountTypeEnum.equals(AccountTypeEnum.DICT_UID)) {
            userService.bindByDictUid(SecurityUtil.getUserId(), dictService.validAndGetUid(cookie));
        } else {
            throw new CustomException(INVALID_PARAMETERS);
        }
    }

    @ApiOperation("激活绑定163邮箱")
    @GetMapping("activeBindAccount")
    @DistributedLock
    public String activeBindAccount(@DistributedLockKey String activeBindToken) {
        return userService.activeBindAccount(activeBindToken);
    }

    @ApiOperation("解绑账户")
    @GetMapping("unbindAccount")
    public void unbindAccount(@ApiParam("0-163邮箱 1-词典uid") AccountTypeEnum accountTypeEnum) {
        if (accountTypeEnum.equals(AccountTypeEnum.MAIL_163)) {
            userService.unbindUsername(SecurityUtil.getUserId());
        } else if (accountTypeEnum.equals(AccountTypeEnum.DICT_UID)) {
            userService.unbindDictUid(SecurityUtil.getUserId());
        } else {
            throw new CustomException(INVALID_PARAMETERS);
        }
    }
}

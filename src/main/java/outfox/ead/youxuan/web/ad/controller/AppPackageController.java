package outfox.ead.youxuan.web.ad.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.entity.AppPackage;
import outfox.ead.youxuan.web.ad.service.AppPackageService;

import java.util.List;

@RestController
@BaseResponse
@AllArgsConstructor
@Validated
@Api(tags = "应用及包名信息接口")
@RequestMapping("/appPackage")
public class AppPackageController {
    private final AppPackageService appPackageService;

    /**
     * 获取应用信息列表
     *
     * @return 应用id及名称
     */
    @GetMapping
    @ApiOperation(value = "获取全部应用信息")
    public List<AppPackage> getAllAppPackageList() {
        return appPackageService.listValid();
    }
}

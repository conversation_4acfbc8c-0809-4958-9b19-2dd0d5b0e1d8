package outfox.ead.youxuan.web.ad.service;


import outfox.ead.youxuan.entity.AdPosition;
import outfox.ead.youxuan.web.ad.controller.vo.*;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface AdPositionService extends YouxuanService<AdPosition> {
    /**
     * 条件查询广告位（模糊查询）、状态 媒体ID（精确查询) 媒体名称（模糊查询）、广告位ID（精确查询)，广告位名称（模糊查询）
     *
     * @param adPosition 广告位
     * @return List<AdPositionListVO>
     */
    PageVO<AdPositionListVO> pageList(AdPositionCriteriaQueryVO adPosition);

    /**
     * 插入/修改广告位
     *
     * @param adPosition 广告位
     * @return 影响行数
     */
    Long saveOrUpdate(AdPositionSaveOrUpdateVO adPosition);

    /**
     * 通过推广标的查询有效广告位
     *
     * @param promotionTarget 推广标类型
     * @return 广告位
     */
    List<AdPosition> listValid(Integer promotionTarget);

    /**
     * 统计重复名字
     *
     * @param name    名字
     * @param id      id
     * @param mediaId 媒体id
     */
    void nameRepeat(String name, Long id, Long mediaId);

    /**
     * 批量修改
     *
     * @param adPositions 广告位列表
     * @return 修改完的信息
     */
    String saveOrUpdateBatch(BatchUpdateStatusVO adPositions);

    /**
     * 根据id列表查询未删除广告位
     *
     * @param adPositionIds id列表
     * @return 广告位列表
     */
    List<AdPosition> listNotDeleteByIds(Collection<Long> adPositionIds);

    /**
     * 查询所有的未删除广告位
     *
     * @return 广告位列表
     */
    List<AdPosition> listValid();

    /**
     * 通过媒体id查询广告位
     *
     * @param mediaIds 媒体ID列表
     * @return 广告位
     */
    List<AdPosition> listValidByMediaIds(Collection<Long> mediaIds);

    /**
     * 通过名字模糊查询广告位
     *
     * @param adPositionName 广告位名称
     * @return 广告位列表
     */
    List<AdPosition> listLikeName(String adPositionName);

    /**
     * 通过媒体id查询
     *
     * @param styleIds 媒体id列表
     * @return 广告位列表
     */
    List<AdPosition> listByStyleIds(List<Long> styleIds);

    /**
     * 广告位目前未删除的个数
     *
     * @return int
     */
    long countAllValid();

    /**
     * 获取Vo 用于修改时获取信息
     *
     * @param id id
     * @return vo
     */
    AdPositionByIdVO getVoById(Long id);

    /**
     * 广告位轮播数修改校验，不得低于投放完成和在投的最小轮播数
     *
     * @param id           广告位ID
     * @param displayTimes 轮播数
     */
    void displayTimeValidate(Long id, Integer displayTimes);

    /**
     * 根据广告位ID获取未删除广告位
     *
     * @param adPositionId 广告位ID
     * @return 广告位
     */
    AdPosition getNotDeleteById(Long adPositionId);

    /**
     * 通过媒体Id查询未删除的广告位
     *
     * @param mediaId 媒体id
     * @return 广告位列表
     */
    List<AdPosition> listNotDeleteByMediaId(Long mediaId);

    /**
     * 通过名字模糊查询广告位
     * 通过媒体id查询广告位
     *
     * @param name     名字
     * @param mediaIds 媒体id
     * @param resourceStatus
     * @return 广告位列表
     */
    List<AdPosition> list(String name, Set<Long> mediaIds, List<Integer> resourceStatus);
}

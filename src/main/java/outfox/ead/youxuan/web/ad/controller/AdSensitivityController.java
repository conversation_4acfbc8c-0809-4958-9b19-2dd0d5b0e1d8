package outfox.ead.youxuan.web.ad.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.AdSensitivityService;

import java.util.List;

/**
 * 开屏灵敏度配置 Controller
 *
 * <AUTHOR>
 * @create 2024-03-21
 **/
@BaseResponse
@Validated
@Api(tags = "开屏灵敏度配置")
@RestController
@RequestMapping("/ad-sensitivity")
@RequiredArgsConstructor
public class AdSensitivityController {

    private final AdSensitivityService adSensitivityService;

    @ApiOperation("新增开屏灵敏度配置")
    @PostMapping
    public void saveAdSensitivity(@RequestBody AdSensitivityCreateVO adSensitivityCreateVO) {
        adSensitivityService.saveAdSensitivity(adSensitivityCreateVO);
    }

    @ApiOperation("删除开屏灵敏度配置")
    @DeleteMapping
    public void deleteSensitivity(@RequestBody List<Long> ids) {
        adSensitivityService.deleteAdSensitivity(ids);
    }

    @ApiOperation("修改开屏灵敏度配置")
    @PutMapping
    public void updateAdSensitivity(@RequestBody AdSensitivityUpdateVO adSensitivityUpdateVO) {
        adSensitivityService.updateAdSensitivity(adSensitivityUpdateVO);
    }

    @ApiOperation("查询开屏灵敏度配置详情")
    @GetMapping("/{id}")
    public AdSensitivityDetailVO getAdSensitivityDetail(@PathVariable Long id) {
        return adSensitivityService.getAdSensitivityDetail(id);
    }

    @ApiOperation("分页查询开屏灵敏度配置")
    @GetMapping("/page")
    public IPage<AdSensitivityDetailVO> getAdSensitivityList(AdSensitivityQueryVO adSensitivityQueryVO) {
        return adSensitivityService.getAdSensitivityList(adSensitivityQueryVO);
    }

    @ApiOperation("获取开屏灵敏度配置支持的广告位")
    @GetMapping("/positions")
    public List<AdSensitivitySupportPositionVO> getAdSensitivitySupportPositions() {
        return adSensitivityService.getAdSensitivitySupportPositions();
    }
} 
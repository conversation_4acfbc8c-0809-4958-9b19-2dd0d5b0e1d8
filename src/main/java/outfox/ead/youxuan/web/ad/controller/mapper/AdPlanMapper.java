package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.AdPlan;
import outfox.ead.youxuan.entity.Customer;
import outfox.ead.youxuan.web.ad.controller.vo.AdPlanByIdVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdPlanListVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdPlanSaveOrUpdateVO;
import outfox.ead.youxuan.web.ad.controller.vo.IdToNameVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/11 17:19
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface AdPlanMapper {
    @Mapping(source = "customer.name", target = "customerName")
    @Mapping(source = "adPlan.id", target = "id")
    @Mapping(source = "adPlan.name", target = "name")
    @Mapping(source = "adPlan.status", target = "status")
    @Mapping(source = "adPlan.status", target = "deliverStatus")
    AdPlanByIdVO doToAdPlanByIdVo(AdPlan adPlan, Customer customer);

    @Mapping(target = "name", expression = "java(vo.getName().trim())")
    AdPlan adPlanSaveOrUpdateVoToDo(AdPlanSaveOrUpdateVO vo);

    List<IdToNameVO> listDoToIdToNameVo(List<AdPlan> adPlan);

    @Mapping(target = "adDeliverySlot", expression = "java(adPlan.getAdDeliveryInterval())")
    @Mapping(target = "customerName", expression = "java(idToCustomerName.get(adPlan.getCustomerId()))")
    AdPlanListVO doToAdPlanListVo(AdPlan adPlan, Map<Long, String> idToCustomerName);

    default List<AdPlanListVO> listDoToAdPlanListVo(List<AdPlan> adPlans, Map<Long, String> idToCustomerName) {
        if (adPlans == null) {
            return null;
        }

        List<AdPlanListVO> list = new ArrayList<AdPlanListVO>(adPlans.size());
        for (AdPlan adPlan : adPlans) {
            list.add(doToAdPlanListVo(adPlan, idToCustomerName));
        }

        return list;
    }

    AdPlanSaveOrUpdateVO toSaveVO(AdPlanByIdVO sourceAdPlan);
}

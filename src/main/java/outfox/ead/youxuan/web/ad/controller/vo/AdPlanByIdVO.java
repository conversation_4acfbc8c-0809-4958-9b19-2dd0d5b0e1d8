package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/19 11:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdPlanByIdVO {
    @ApiModelProperty("推广计划主键")
    private Long id;

    @ApiModelProperty("推广目标 1-落地页推广 2-应用直达 3-小程序推广")
    private Integer promotionTarget;

    @ApiModelProperty("推广计划名称")
    private String name;

    @ApiModelProperty("客户id")
    private Long customerId;
    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("计费模式 0-CPT 1-CPM")
    private Integer billingType;

    @ApiModelProperty("投放类型")
    private Integer deliveryType;

    @ApiModelProperty("日期是否连续 0-连续 1-不连续")
    private Integer dateContinuous;

    @ApiModelProperty(value = "投放日期 存储不连续的日期")
    private List<String> adDeliveryDate;

    @ApiModelProperty(value = "投放开始日期 连续日期只需要存储开始和结束时间")
    private LocalDate adOpenDate;

    @ApiModelProperty("投放结束时间")
    private LocalDate adCloseDate;

    @ApiModelProperty("投放时段 0-不限 1-指定时段")
    private Integer timeOrientation;

    @ApiModelProperty("投放时段")
    private List<List<Integer>> timeDest;

    @ApiModelProperty("地域定向 0-不限 1-按区域")
    private Integer regionalOrientation;

    @ApiModelProperty("地域定向流量范围 0-全部 1-内部流量")
    private Integer regionalOrientationScope;

    @ApiModelProperty("投放目标地址国际")
    private List<Integer> internationalRegionalDest;

    @ApiModelProperty("投放目标地址国内")
    private List<Integer> inlandRegionalDest;

    @ApiModelProperty("计划状态 0-投放中，1-投放结束，2-即将开始，3-暂停，4已删除")
    private Integer status;

    @ApiModelProperty("投放状态 0-投放中，1-投放结束，2-即将开始，3-暂停，4已删除")
    private Integer deliverStatus;

    @ApiModelProperty("频控方式 0-不频控 1-按天频控 2-按周频控 3-按投放周期频控")
    private Integer frequencyType;

    @ApiModelProperty("频控次数")
    private Integer frequencyLimit;
}

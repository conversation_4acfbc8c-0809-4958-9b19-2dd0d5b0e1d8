package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.constants.InteractionTypeEnum;
import outfox.ead.youxuan.web.ad.controller.dto.MediaResource;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 推广组详细信息
 * 信息从adPlan，adContent，adPositionSchedule获取进行拼接
 *
 * <AUTHOR> <EMAIL>
 * @date 2021/8/25 9:43
 */
@Data
public class AdGroupByIdVO {
    @ApiModelProperty("推广组主键")
    private Long id;
    @ApiModelProperty("推广计划主键")
    private Long adPlanId;
    @ApiModelProperty("计费模式 0-CPT 1-CPM")
    private Integer billingType;
    @ApiModelProperty("投放方式 0-标准投放 1-PD 2-PDB")
    private Integer deliveryType;
    @ApiModelProperty("推广组名称")
    private String name;
    @ApiModelProperty("投放状态 0-投放中，1-投放结束，2-即将开始，3-暂停，4已删除")
    private Integer status;
    @ApiModelProperty("投放状态 0-投放中，1-投放结束，2-即将开始，3-暂停，4已删除")
    private Integer deliverStatus;
    @ApiModelProperty("cpm 出价")
    private Integer cpmPrice;
    @ApiModelProperty("总展示量")
    private Integer sumDisplayCount;
    @ApiModelProperty("日展示量上限")
    private Integer dailyDisplayLimit;
    @ApiModelProperty("投放速度,0代表快速，1代表均匀")
    private Integer deliverySpeed;
    @ApiModelProperty("订单id")
    private String dealId;
    @ApiModelProperty("订单备注")
    private String dealRemark;
    @ApiModelProperty("dsp id")
    private String dspId;
    @ApiModelProperty("打底广告，0不填充，1填充")
    private Integer basePadding;
    @ApiModelProperty("轮播信息，投放位置")
    private List<MediaResource> carouselInfo;
    @ApiModelProperty("创意内容")
    private List<AdContentInitialVO> creativeContentContents;
    @ApiModelProperty("开机首刷，0不开启，1开启")
    private Integer bootFirstRefresh;
    @ApiModelProperty("开屏回收，0不开启，1开启")
    private Integer openScreenRecycle;
    @ApiModelProperty("跳转添加类型")
    private Integer brandClkType;
    @ApiModelProperty("落地页链接")
    private String landingPageLink;
    @ApiModelProperty("深链接")
    private String deeplinkUrl;
    @ApiModelProperty("小程序APPID")
    private String wechatAppId;
    @ApiModelProperty("小程序原始id")
    private String wechatOriginalId;
    @ApiModelProperty("查词定向列表")
    private List<String> advertisingKeywordList;
    @ApiModelProperty("全球发音文件列表")
    private List<String> audioUrls;
    @ApiModelProperty("目标路径")
    private String wechatPath;
    @ApiModelProperty("曝光监测链接")
    private String expoDeteLink;
    @ApiModelProperty("点击监测链接")
    private String clickDeteLink;
    @ApiModelProperty("消息")
    private String msg;
    @ApiModelProperty("是否屏蔽青少年，0-否，1-是")
    private Boolean youthMode;
    @ApiModelProperty("性别定向 0-无 1-男 2-女")
    private Integer genderOrientation;
    @ApiModelProperty("年龄定向")
    private List<List<Integer>> ageOrientation;
    @ApiModelProperty("人群标签")
    private List<Integer> crowdLabel;
    @ApiModelProperty(value = "无设备号过滤")
    private Boolean mustDeviceId;
    @ApiModelProperty("定向模式 0-无 1-扩展人群定向 2-精准人群定向")
    private Integer orientationMode;
    @ApiModelProperty("监测链接是否需要宏替换")
    private Boolean replaceMacro;
    @ApiModelProperty("是否开启扩量推广")
    private Boolean expansionPromotion;
    /**
     * {@link InteractionTypeEnum#getCode()}
     */
    @ApiModelProperty("交互类型, 0:无，1:摇一摇，2:滑动互动，3:双link，4:三link,5:扭一扭")
    private Integer interactionType;
    @ApiModelProperty("落地页链接1")
    private String landingPageLink1;
    @ApiModelProperty("深链接1")
    private String deeplinkUrl1;
    @ApiModelProperty("小程序APPID1")
    private String wechatAppId1;
    @ApiModelProperty("小程序原始id1")
    private String wechatOriginalId1;
    @ApiModelProperty("目标路径1")
    private String wechatPath1;
    @ApiModelProperty("落地页链接2")
    private String landingPageLink2;
    @ApiModelProperty("深链接2")
    private String deeplinkUrl2;
    @ApiModelProperty("小程序APPID2")
    private String wechatAppId2;
    @ApiModelProperty("小程序原始id2")
    private String wechatOriginalId2;
    @ApiModelProperty("目标路径2")
    private String wechatPath2;
    @ApiModelProperty("是否开启应用定向，1-开启，0-不开启")
    private Boolean appInstalledOrientation;
    @ApiModelProperty("定向应用id列表")
    private List<Long> installedAppPackageIds;
    @ApiModelProperty("微信小程序定向")
    private Boolean wechatOrientation;
    @ApiModelProperty(value = "测试直达状态")
    private Boolean testDirect;
    @ApiModelProperty("直达有效开始时间")
    private LocalDateTime directStartTime;
    @ApiModelProperty("直达有效结束时间")
    private LocalDateTime directEndTime;
    @ApiModelProperty("是否开启自定义人群包")
    private Boolean openCustomCrowdPack;
    @ApiModelProperty("true-定向人群投放 false-排除人群投放")
    private Boolean includeCrowdPack;
    @ApiModelProperty("人群包id列表")
    private Set<Long> crowdPackIds;
    @ApiModelProperty("是否开启h5合成链接")
    private Boolean h5TransitUrl;
    @ApiModelProperty("是否开启deeplink合成链接")
    private Boolean dpTransitUrl;
    @ApiModelProperty("合成链接中目标小程序的appId")
    private String transitTargetWechatAppId;
    @ApiModelProperty("品牌cpm广告消费类型，0-正常消费，1-匀速消费，2-加速消费(默认0)")
    private Integer cpmCostType;
    @ApiModelProperty("cpm广告加速系数，当广告模式为加速模式时生效，范围为1-100，默认为1")
    private Integer cpmAccelerateRatio;
    @ApiModelProperty("是否允许caid作为有效设备号类型")
    private Boolean allowCaid;
    @ApiModelProperty("是否支持全链路投放")
    private Boolean fullChannel;
    @ApiModelProperty("是否上报第三方曝光检测")
    private Boolean reportExpoDeteLink;
}

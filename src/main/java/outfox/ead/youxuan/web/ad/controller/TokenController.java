package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.config.security.JwtSecurityToken;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.util.SecurityUtil;

/**
 * <AUTHOR>
 * @date 2022年08月29日 10:38
 */
@BaseResponse
@RestController
@AllArgsConstructor
@Validated
@Api(tags = {"jwt"})
@RequestMapping("/jwt")
public class TokenController {
    @GetMapping
    @ApiOperation("获取单角色token")
    public String resetToken(RoleEnum roleEnum) {
        Role role = SecurityUtil.getLoginUser()
                .getRoles()
                .stream()
                .filter(r -> r.getRoleKey().equals(roleEnum.getRoleKey())).findFirst()
                .orElseThrow(() -> new CustomException(ResponseType.INVALID_PARAMETERS, "角色不存在"));
        SecurityUtil.getLoginUser().setCurrentRole(role);
        return JwtSecurityToken.generateToken(SecurityUtil.getLoginUser());
    }
}

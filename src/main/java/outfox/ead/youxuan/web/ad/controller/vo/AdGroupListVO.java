package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.web.ad.controller.dto.DateSlot;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/26 16:46
 */
@Data
public class AdGroupListVO {
    @ApiModelProperty("组ID")
    private Long id;

    @ApiModelProperty("推广组名称")
    private String name;

    @ApiModelProperty("推广计划主键")
    private Long adPlanId;
    @ApiModelProperty("计划名称")
    private String adPlanName;
    @ApiModelProperty("广告创意，视频取cover，图片取mainImage")
    private String creative = "";
    @ApiModelProperty("投放位置")
    private List<String> deliveryPosition;

    @ApiModelProperty("状态 0-投放中、1-投放结束、2-即将开启、3-暂停、4-未暂停、5-已删除、6-未删除")
    private Integer status;

    @ApiModelProperty("展示数")
    private Long imprNum;

    @ApiModelProperty("点击数")
    private Long clickNum;

    @ApiModelProperty("点击率")
    private Double clickRate;

    @ApiModelProperty("投放时间")
    private List<DateSlot> adDeliverySlot;

    @ApiModelProperty(value ="无设备号过滤")
    private Boolean mustDeviceId;

    @ApiModelProperty(value ="测试直达状态")
    private Boolean testDirect;

    @ApiModelProperty("直达有效开始时间")
    private LocalDateTime directStartTime;

    @ApiModelProperty("直达有效结束时间")
    private LocalDateTime directEndTime;
}

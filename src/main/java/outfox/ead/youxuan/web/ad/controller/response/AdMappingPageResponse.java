package outfox.ead.youxuan.web.ad.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AdMappingPageResponse {
    private Long id;
    private String name;
    private int status;
    private String sourceStyle;
    private String mappingStyle;
    private Long adGroupId;

    private String adGroupName;
    @ApiModelProperty("状态 0-投放中、1-投放结束、2-即将开启、3-暂停、4-未暂停、5-已删除、6-未删除")
    private int adGroupStatus;
    @ApiModelProperty("客户名称")
    private String customerName;
    @ApiModelProperty("客户id")
    private String customer;
    @ApiModelProperty("创建人id")
    private Long creator;
    @ApiModelProperty("创建人名")
    private String creatorName;
    @ApiModelProperty("编辑者")
    private String modifier;
}

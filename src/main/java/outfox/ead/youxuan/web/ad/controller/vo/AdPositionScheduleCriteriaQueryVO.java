package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;

import java.time.LocalDate;

/**
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/20 16:31
 */
@Data
public class AdPositionScheduleCriteriaQueryVO {
    @ApiModelProperty("广告位主键")
    private String adPositionIds;
    @ApiModelProperty("计费方式 0-CPT 1-CPM")
    private Integer billingType;
    @ApiModelProperty("排期表开始时间")
    private LocalDate startDate;
    @ApiModelProperty("排期表结束时间")
    private LocalDate endDate;
    @NonNull
    private Long current;
    @NonNull
    private Long size;
}

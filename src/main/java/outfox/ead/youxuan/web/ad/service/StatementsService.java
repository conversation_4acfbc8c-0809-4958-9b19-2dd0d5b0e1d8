package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.web.ad.controller.vo.AccountStatementsResponseVO;
import outfox.ead.youxuan.web.ad.controller.vo.AccountStatementsVO;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2021/9/13/11:50
 */
public interface StatementsService {
    /**
     * 用于数据趋势图
     *
     * @param accountStatementsVO 数据细分输入无用于获取全部数据
     * @return 数据列表
     */
    List<AccountStatementsResponseVO> list(AccountStatementsVO accountStatementsVO) throws CloneNotSupportedException, ExecutionException, InterruptedException;

    /**
     * 下载报表
     *
     * @param request             rq
     * @param response            res
     * @param accountStatementsVO 查询条件
     * @throws IOException                ignore
     * @throws CloneNotSupportedException ignore
     */
    void download(HttpServletRequest request, HttpServletResponse response, AccountStatementsVO accountStatementsVO) throws IOException, CloneNotSupportedException, ExecutionException, InterruptedException;

    /**
     * 分页获取不同条件下的表格
     *
     * @param accountStatementsVO 查询条件
     * @return 分页列表
     */
    PageVO<AccountStatementsResponseVO> pageList(AccountStatementsVO accountStatementsVO) throws CloneNotSupportedException, ExecutionException, InterruptedException;
}

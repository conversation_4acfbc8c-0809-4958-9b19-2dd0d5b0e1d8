package outfox.ead.youxuan.web.ad.controller.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import outfox.ead.youxuan.entity.UnionFrequencyRule;
import outfox.ead.youxuan.web.ad.controller.dto.DateSlot;

import java.util.List;

/**
 * 推广计划列表VO
 *
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/19 11:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdPlanListVO {
    @ApiModelProperty("推广计划主键")
    private Long id;

    @ApiModelProperty("推广计划名称")
    private String name;

    @ApiModelProperty("客户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long customerId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("计费方式 0-CPT 1-CPM")
    private Integer billingType;

    @ApiModelProperty("投放方式 0-标准投放 1-PD 2-PDB")
    private Integer deliveryType;

    @ApiModelProperty("推广目标 1-落地页推广 2-应用直达 3-小程序推广")
    private Integer promotionTarget;

    @ApiModelProperty("投放时间")
    private List<DateSlot> adDeliverySlot;

    @ApiModelProperty("状态 0-投放中、1-投放结束、2-即将开启、3-暂停、4-未暂停、5-已删除、6-未删除")
    private Integer status;

    @ApiModelProperty("展示数")
    private Long imprNum;

    @ApiModelProperty("点击数")
    private Long clickNum;

    @ApiModelProperty("点击率")
    private Double clickRate;

    @ApiModelProperty("频控方式 0-不频控 1-按天频控 2-按周频控 3-按投放周期频控")
    private Integer frequencyType;

    @ApiModelProperty("频控次数")
    private Integer frequencyLimit;

    /**
     * {@link UnionFrequencyRule}
     */
    @ApiModelProperty("关联的有效联合频控id")
    private Long validUnionFrequencyRuleId;
}

package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-06-06 18:03
 **/
@Data
public class AdSensitivityCreateVO {

    @ApiModelProperty(value = "广告点击交互类型，1：摇一摇，2：滑动互动，5：扭一扭, 6:三合一", required = true)
    @NotNull(message = "交互类型不能为空")
    private Integer clickType;

    @ApiModelProperty(value = "推广组ID列表", required = true)
    @NotEmpty(message = "推广组列表不能为空")
    private List<Long> adGroupIds;

    @ApiModelProperty(value = "广告位ID列表", required = true)
    @NotEmpty(message = "广告位列表不能为空")
    private List<Long> adPositionIds;

    @ApiModelProperty(value = "灵敏度配置-摇动加速度")
    private Integer shakeSpeed;

    @ApiModelProperty(value = "灵敏度配置-滑动角度")
    private Integer slideAngle;

    @ApiModelProperty(value = "灵敏度配置-摇动角度/扭转角度")
    private Integer rotationAngle;
}

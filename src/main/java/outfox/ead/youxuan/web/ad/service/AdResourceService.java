package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.constants.InteractionTypeEnum;
import outfox.ead.youxuan.entity.AdContentRelation;
import outfox.ead.youxuan.entity.AdDeliveryTime;
import outfox.ead.youxuan.web.ad.controller.dto.MediaResource;
import outfox.ead.youxuan.web.ad.controller.dto.PromotionResource;
import outfox.ead.youxuan.web.ad.controller.dto.ScheduleResourceDetail;
import outfox.ead.youxuan.web.ad.controller.vo.AdContentInitialVO;
import outfox.ead.youxuan.web.ad.controller.vo.MediaAdPositionVO;
import outfox.ead.youxuan.web.ad.controller.vo.PageLikeNameVO;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/24 12:08
 */
public interface AdResourceService {
    /**
     * 新建推广组的时候，获取投放位置列表
     *
     * @param adPlanId  推广计划id
     * @param adGroupId
     * @param name
     * @return 资源列表
     */
    Collection<MediaResource> listResource(Integer adPlanId, Long adGroupId, String name);

    /**
     * 通过名称模糊查询返回资源列表
     * <p>用于广告位新建
     *
     * @param mediaAdPositionVO 查询VO
     * @param level             层级
     * @return 资源分页
     */
    PageVO<MediaResource> listResourceLikeName(MediaAdPositionVO mediaAdPositionVO, Integer level);

    /**
     * 用于新建推广组初始化轮播信息
     *
     * @param styleIds  样式id
     * @param adPlanId  推广计划id
     * @param adGroupId 推广组id  （修改的时候，需要将自身占有的资源看作是空闲资源）
     * @return 资源列表
     */
    List<MediaResource> initialCarouselInfo(List<Long> styleIds, Long adPlanId, Long adGroupId);

    /**
     * 用于新建推广组初始化创意内容
     *
     * @param styleIds          样式id列表
     * @param adGroupId         推广组id
     * @param interactionType   {@link InteractionTypeEnum#getCode()}
     * @return 创意内容列表
     */
    List<AdContentInitialVO> initialCreativeContents(List<Long> styleIds, Long adGroupId, Integer interactionType);


    /**
     * 填充排期需要的相关资源
     * <p>轮播资源精确到天</p>
     *
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param billingType   计费方式
     * @param adPositionIds 需要check的广告位
     * @return 排期详情列表
     */
    Collection<ScheduleResourceDetail> getScheduleResourceDetails(LocalDate startDate,
                                                                  LocalDate endDate,
                                                                  Integer billingType,
                                                                  Collection<Long> adPositionIds);

    /**
     * 用于修改广告位轮播数时，做校验
     *
     * @param adPositionId 广告位id
     * @return 广告位目前已售的最大轮播数
     */
    Integer getPositionSoldDisplayTimes(Long adPositionId);


    /**
     * 校验轮播资源是否足够
     * <p>在修改推广 计划/组 的时候，校验资源要排除掉推广 计划/组 本身的资源占有
     *
     * @param relations                 购买关联数据  relation可能是新增的也可能是原本就有的
     * @param adDeliveryTimes           投放时间
     * @param regionalOrientation       地域定向
     * @param inlandRegionalDest        国内城市
     * @param internationalRegionalDest 海外城市
     * @param excludeAdPlanId           排除掉的推广计划(修改推广计划的时候，校验资源，需要排除该推广计划)
     * @param excludeAdGroupId          排除掉的推广组(修改推广组的时候，校验资源，需要排除该推广组)
     * @param fullChannel               推广组是否开启全链路
     * @return true-充足 false-不足
     */
    Boolean resourcesSufficient(Collection<AdContentRelation> relations,
                                Collection<AdDeliveryTime> adDeliveryTimes,
                                Integer regionalOrientation,
                                Collection<Integer> inlandRegionalDest,
                                Collection<Integer> internationalRegionalDest,
                                Long excludeAdPlanId,
                                Long excludeAdGroupId,
                                Boolean fullChannel);

    /**
     * 通过名字查询推广计划推广组层级列表
     *
     * @param pageLikeNameVO 分页条件以及name
     * @return 推广计划推广组层级列表
     */
    PageVO<PromotionResource> listPromotionResourceLikeName(PageLikeNameVO pageLikeNameVO);

}

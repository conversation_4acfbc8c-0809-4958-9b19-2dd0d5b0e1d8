package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.AdPosition;
import outfox.ead.youxuan.web.ad.controller.vo.AdPositionByIdVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdPositionCountByNameVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdPositionListVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdPositionSaveOrUpdateVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/17/12:05
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface AdPositionMapper {
    @Mapping(target = "name", expression = "java(vo.getName().trim())")
    AdPosition saveOrUpdateVoToDo(AdPositionSaveOrUpdateVO vo);

    AdPositionCountByNameVO doToCountByName(AdPosition adPosition);

    AdPositionByIdVO doToById(AdPosition adPosition);

    default List<AdPositionListVO> doToAdPositionListVO(List<AdPosition> adPositions, Map<Long, String> idToMediaName) {
        if (adPositions == null) {
            return null;
        }
        List<AdPositionListVO> list = new ArrayList<>(adPositions.size());
        for (AdPosition adPosition : adPositions) {
            list.add(do2AdPositionListVO(adPosition, idToMediaName));
        }
        return list;
    }

    @Mapping(target = "mediaName", expression = "java(idToMediaName.get(adPosition.getMediaId()))")
    AdPositionListVO do2AdPositionListVO(AdPosition adPosition, Map<Long, String> idToMediaName);
}

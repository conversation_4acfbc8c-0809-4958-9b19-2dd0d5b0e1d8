package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.vo.AdPositionScheduleCriteriaQueryVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdPositionScheduleVO;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.controller.vo.ResourceSalesDetailsVO;
import outfox.ead.youxuan.web.ad.service.AdPositionScheduleService;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/20 15:21
 */
@AllArgsConstructor
@RestController
@BaseResponse
@Api(tags = {"资源排期"})
@RequestMapping("/ad_position_schedule")
@AccessControl(roles = RoleEnum.AD_OPERATOR)
public class AdPositionScheduleController {
    private final AdPositionScheduleService adPositionScheduleService;

    /**
     * 用于资源排期页面<p>
     * 获取广告位排期表
     */
    @GetMapping
    @ApiOperation("获取广告位排期表")
    public PageVO<AdPositionScheduleVO> getAdPositionSchedule(AdPositionScheduleCriteriaQueryVO adPositionSchedule) {
        return adPositionScheduleService.getAdPositionSchedule(adPositionSchedule);
    }


    /**
     * 用于资源排期页面<p>
     * 获取资源详情
     */
    @GetMapping("/ad_resource_sales_details")
    @ApiOperation("获取资源详情")
    public List<ResourceSalesDetailsVO> getAdResourceSalesDetails(LocalDate date,
                                                                  @ApiParam("广告位id") Long adPositionId,
                                                                  @ApiParam("计费方式 0-CPT 1-CPM") Integer billingType) {
        return adPositionScheduleService.getAdResourceSalesDetails(date, adPositionId, billingType);
    }
}

package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.vo.AccountStatementsResponseVO;
import outfox.ead.youxuan.web.ad.controller.vo.AccountStatementsVO;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.StatementsService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2021/9/13/11:48
 */
@BaseResponse
@RestController
@AllArgsConstructor
@Validated
@Api(tags = "报表")
@RequestMapping("/statements")
@AccessControl(roles = RoleEnum.AD_OPERATOR)
public class StatementsController {
    private final StatementsService statementsService;

    @GetMapping
    @ApiOperation("获取报表数据")
    public List<AccountStatementsResponseVO> getStatements(AccountStatementsVO accountStatementsVO) throws CloneNotSupportedException, ExecutionException, InterruptedException {
        return statementsService.list(accountStatementsVO);
    }

    @GetMapping("/download")
    @ApiOperation("下载Excel")
    public void getExcel(HttpServletRequest request, HttpServletResponse response, AccountStatementsVO accountStatementsVO) throws IOException, CloneNotSupportedException, ExecutionException, InterruptedException {
        statementsService.download(request, response, accountStatementsVO);
    }

    @GetMapping("/page")
    @ApiOperation("分页获取报表数据")
    public PageVO<AccountStatementsResponseVO> getPageList(AccountStatementsVO accountStatementsVO) throws CloneNotSupportedException, ExecutionException, InterruptedException {
        return statementsService.pageList(accountStatementsVO);
    }
}

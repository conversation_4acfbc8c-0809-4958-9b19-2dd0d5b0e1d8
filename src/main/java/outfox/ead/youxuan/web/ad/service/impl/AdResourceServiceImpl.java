package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.constants.InteractionTypeEnum;
import outfox.ead.youxuan.constants.ResourceStatusEnum;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.annotation.ExecuteTime;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.util.AreaUtil;
import outfox.ead.youxuan.util.JacksonUtil;
import outfox.ead.youxuan.util.LocalDateUtil;
import outfox.ead.youxuan.web.ad.controller.dto.*;
import outfox.ead.youxuan.web.ad.controller.mapper.StyleElementMapper;
import outfox.ead.youxuan.web.ad.controller.vo.AdContentInitialVO;
import outfox.ead.youxuan.web.ad.controller.vo.MediaAdPositionVO;
import outfox.ead.youxuan.web.ad.controller.vo.PageLikeNameVO;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.service.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.Constants.*;
import static outfox.ead.youxuan.constants.PromotionStatusEnum.DELIVER_PAUSE;
import static outfox.ead.youxuan.constants.ResponseType.INVALID_PARAMETERS;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/24 12:08
 */
@Service
@AllArgsConstructor
@Slf4j
public class AdResourceServiceImpl implements AdResourceService {
    private final AdPlanService adPlanService;
    private final AdGroupService adGroupService;
    private final AdContentService adContentService;
    private final AdContentRelationService adContentRelationService;


    private final MediaService mediaService;
    private final AdPositionService adPositionService;
    private final StyleService styleService;

    private final AdDeliveryTimeService adDeliveryTimeService;

    private final StyleElementMapper styleElementMapper;

    @Override
    public Collection<MediaResource> listResource(Integer adPlanId, Long adGroupId, String name) {

        // 有效的资源列表
        Collection<MediaResource> resources = listThreeLevelResourceLikeName(name, null, null, Collections.singletonList(ResourceStatusEnum.VALID.getCode())).getRecords();
        resources.forEach(mediaResource -> mediaResource.getAdPositionList()
                .removeIf(positionResource -> CollectionUtils.isEmpty(positionResource.getStyleList()))
        );

        if (Objects.nonNull(adPlanId)) {
            AdPlan adPlan = adPlanService.getById(adPlanId);
            Set<Long> adPositionIds = adPositionService.listValid(adPlan.getPromotionTarget()).stream().map(AdPosition::getId).collect(Collectors.toSet());
            resources.forEach(mediaResource -> mediaResource.getAdPositionList()
                    .removeIf(positionResource -> !adPositionIds.contains(positionResource.getId())));
        }
        if (Objects.nonNull(adGroupId)) {
            List<Long> styleIds = adContentRelationService.listValidByAdGroupIds(Collections.singletonList(adGroupId)).stream().map(AdContentRelation::getStyleId).collect(Collectors.toList());
            resources.forEach(mediaResource -> mediaResource.getAdPositionList()
                    .forEach(adPositionResource ->
                            adPositionResource.getStyleList().removeIf(styleResource ->
                                    !styleIds.contains(styleResource.getId())
                            )
                    )
            );
            resources.forEach(mediaResource -> mediaResource.getAdPositionList().removeIf(adPositionResource -> CollectionUtils.isEmpty(adPositionResource.getStyleList())));
        }
        resources.removeIf(mediaResource -> CollectionUtils.isEmpty(mediaResource.getAdPositionList()));
        return resources;
    }

    /**
     * 根据传入的相关resource进行层级组合
     *
     * @param medias      媒体
     * @param adPositions 广告位
     * @param styles      样式
     * @return 组合完成的资源列表
     */
    private List<MediaResource> aggregateResources(List<Media> medias, List<AdPosition> adPositions, List<Style> styles) {
        if (Objects.isNull(medias) || medias.isEmpty()) {
            return Lists.emptyList();
        }
        List<MediaResource> mediaResources = new ArrayList<>();
        Map<Long, MediaResource> idToMediaResource = new HashMap<>();
        for (Media media : medias) {
            MediaResource mediaResource = new MediaResource();
            mediaResource.setId(media.getId());
            mediaResource.setName(media.getName());
            mediaResource.setAdPositionList(new ArrayList<>());
            mediaResources.add(mediaResource);
            idToMediaResource.put(media.getId(), mediaResource);
        }
        if (ObjectUtils.isEmpty(adPositions)) {
            return mediaResources;
        }
        Map<Long, AdPositionResource> idToAdPositionResource = new HashMap<>();
        for (AdPosition adPosition : adPositions) {
            AdPositionResource adPositionResource = new AdPositionResource();
            adPositionResource.setId(adPosition.getId());
            adPositionResource.setName(adPosition.getName());
            adPositionResource.setType(adPosition.getType());
            adPositionResource.setStyleList(new ArrayList<>());
            MediaResource mediaResource = idToMediaResource.get(adPosition.getMediaId());
            if (ObjectUtils.isEmpty(mediaResource)) {
                continue;
            }
            List<AdPositionResource> adPositionList = mediaResource.getAdPositionList();
            adPositionList.add(adPositionResource);
            idToAdPositionResource.put(adPosition.getId(), adPositionResource);
        }
        if (ObjectUtils.isEmpty(styles)) {
            return mediaResources;
        }
        for (Style style : styles) {
            StyleResource styleResource = new StyleResource();
            styleResource.setId(style.getId());
            styleResource.setName(style.getName());
            AdPositionResource adPositionResource = idToAdPositionResource.get(style.getAdPositionId());
            if (ObjectUtils.isEmpty(adPositionResource)) {
                continue;
            }
            List<StyleResource> styleList = adPositionResource.getStyleList();
            styleList.add(styleResource);
        }
        return mediaResources;
    }


    @Override
    public PageVO<MediaResource> listResourceLikeName(MediaAdPositionVO mediaAdPositionVO, Integer level) {
        if (Objects.isNull(level) || level.equals(2)) {
            return listTwoLevelResourceLikeName(mediaAdPositionVO.getName(),
                    mediaAdPositionVO.getCurrent(),
                    mediaAdPositionVO.getSize(),
                    Arrays.asList(ResourceStatusEnum.VALID.getCode(), ResourceStatusEnum.PAUSE.getCode()));
        } else if (level.equals(3)) {
            // 查询未删除的资源
            return listThreeLevelResourceLikeName(mediaAdPositionVO.getName(),
                    mediaAdPositionVO.getCurrent(),
                    mediaAdPositionVO.getSize(),
                    Arrays.asList(ResourceStatusEnum.VALID.getCode(), ResourceStatusEnum.PAUSE.getCode()));
        } else {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "层级选择错误");
        }
    }

    private PageVO<MediaResource> listTwoLevelResourceLikeName(String name, Long current, Long size, List<Integer> resourceStatus) {
        List<Media> medias = mediaService.listByName(name, resourceStatus);
        Set<Long> mediaIds = medias.stream().map(Media::getId).collect(Collectors.toSet());

        List<AdPosition> adPositions = adPositionService.list(name, mediaIds, resourceStatus);
        mediaIds = adPositions.stream().map(AdPosition::getMediaId).collect(Collectors.toSet());
        if (ObjectUtils.allNotNull(current, size)) {
            Page<Media> mediaPage = mediaService.pageListInIds(mediaIds, current, size);
            medias = mediaPage.getRecords();
            List<MediaResource> resources = aggregateResources(medias, adPositions, null);
            return new PageVO<>(mediaPage.getCurrent(), mediaPage.getSize(), resources, mediaPage.getTotal());
        } else {
            medias = mediaService.listByIds(mediaIds);
            List<MediaResource> resources = aggregateResources(medias, adPositions, null);
            return new PageVO<>(0L, (long) medias.size(), resources, (long) medias.size());
        }
    }

    private PageVO<MediaResource> listThreeLevelResourceLikeName(String name, Long current, Long size, List<Integer> resourceStatus) {
        List<Media> medias = mediaService.listByName(name, resourceStatus);
        Set<Long> mediaIds = medias.stream().map(Media::getId).collect(Collectors.toSet());
        List<AdPosition> adPositions = adPositionService.list(name, mediaIds, resourceStatus);
        Set<Long> adPositionIds = adPositions.stream().map(AdPosition::getId).collect(Collectors.toSet());
        List<Style> styles = styleService.list(name, adPositionIds, resourceStatus);

        adPositionIds = styles.stream().map(Style::getAdPositionId).collect(Collectors.toSet());
        adPositions = adPositionService.listByIds(adPositionIds);
        mediaIds = adPositions.stream().map(AdPosition::getMediaId).collect(Collectors.toSet());
        if (ObjectUtils.allNotNull(current, size)) {
            Page<Media> mediaPage = mediaService.pageListInIds(mediaIds, current, size);
            medias = mediaPage.getRecords();
            List<MediaResource> resources = aggregateResources(medias, adPositions, styles);
            return new PageVO<>(mediaPage.getCurrent(), mediaPage.getSize(), resources, mediaPage.getTotal());
        } else {
            medias = mediaService.listByIds(mediaIds);
            List<MediaResource> resources = aggregateResources(medias, adPositions, styles);
            return new PageVO<>(0L, (long) medias.size(), resources, (long) medias.size());
        }
    }


    /**
     * 拼接资源数据，计算出广告位目前已售最大值。
     *
     * @param styleIds  购买的样式id
     * @param adPlanId  推广计划id
     * @param adGroupId 推广组id  （修改的时候，需要将自身占有的资源看作是空闲资源）
     * @return 资源列表
     */
    @Override
    public List<MediaResource> initialCarouselInfo(List<Long> styleIds, Long adPlanId, Long adGroupId) {
        // 1.查出需要计算的广告位
        List<Style> styles = styleService.listValidByIds(styleIds);
        List<Long> adPositionIds = styles.stream().map(Style::getAdPositionId).collect(Collectors.toList());
        List<AdPosition> adPositions = adPositionService.listNotDeleteByIds(adPositionIds);

        // 2.获取广告位售卖最大值
        Map<Long, Integer> adPositionIdToSoldMax = getAdPositionIdToSoldMax(adPositionIds, adGroupId, adPlanId);

        // 3.封装轮播资源对象
        List<Long> mediaIds = adPositions.stream().map(AdPosition::getMediaId).collect(Collectors.toList());
        List<Media> medias = mediaService.listByIds(mediaIds);
        List<MediaResource> mediaResources = aggregateResources(medias, adPositions, styles);

        Map<Long, Integer> styleIdToDisplay = getStyleIdToDisplay(adGroupId);
        Map<Long, AdPosition> idToAdPosition = adPositions
                .stream().collect(Collectors.toMap(AdPosition::getId, Function.identity()));
        // 4.填充轮播属性
        for (MediaResource mediaResource : mediaResources) {
            for (AdPositionResource adPositionResource : mediaResource.getAdPositionList()) {
                AdPosition adPosition = idToAdPosition.get(adPositionResource.getId());
                adPositionResource.setCarouselStock(adPosition.getDisplayTimes());
                Integer displayTimes = adPosition.getDisplayTimes();
                adPositionResource.setCarouseRemain(
                        displayTimes - (adPositionIdToSoldMax.get(adPosition.getId()) == null ? 0 : adPositionIdToSoldMax.get(adPosition.getId())));
                for (StyleResource styleResource : adPositionResource.getStyleList()) {
                    if (Objects.nonNull(styleIdToDisplay.get(styleResource.getId()))) {
                        styleResource.setDisplayWeight(styleIdToDisplay.get(styleResource.getId()));
                    }
                }
            }
        }
        return mediaResources;
    }

    private Map<Long, Integer> getAdPositionIdToSoldMax(List<Long> adPositionIds,
                                                        Long adGroupId,
                                                        Long adPlanId) {
        Map<Long, Integer> adPositionIdToSoldMax = new HashMap<>();

        List<Style> allStyles = styleService.listNotDeletedByAdPositionIds(adPositionIds);
        List<Long> allStyleIds = allStyles.stream().map(Style::getId).collect(Collectors.toList());
        List<AdContentRelation> adContentRelations = adContentRelationService.listValidByStyleIdsAndFilterByAdGroupId(allStyleIds, adGroupId);
        // 如果没有购买关系 不需要计算
        if (!adContentRelations.isEmpty()) {
            AdPlan adPlan = adPlanService.getById(adPlanId);
            List<AdGroup> adGroups = adGroupService.listByIds(adContentRelations.stream().map(AdContentRelation::getAdGroupId).collect(Collectors.toSet()));
            Set<Long> adPlanIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toSet());

            // 广告位具体的投放时间
            List<AdDeliveryTime> targetDeliveryTimes = adPlanService.getAdDeliveryTimes(adPlan.getId(),
                    adPlan.getTimeOrientation(), adPlan.getAdDeliveryInterval(), adPlan.getTimeDest(), adPlan.getAdOpenDate());

            // 重叠并且投了相关的样式 的投放时间
            List<AdDeliveryTime> overlapDeliveryTimes = adDeliveryTimeService
                    .listOverlapDeliveryTimeByAdDeliveryTimesExcludeAdPlan(targetDeliveryTimes, null)
                    .stream()
                    .filter(a -> adPlanIds.contains(a.getAdPlanId()))
                    .collect(Collectors.toList());

            Set<Long> overlapAdPlanIds = overlapDeliveryTimes.stream().map(AdDeliveryTime::getAdPlanId).collect(Collectors.toSet());
            List<AdPlan> overlapAdPlans = adPlanService.listNotDeleteByIdsAndBillingType(overlapAdPlanIds, CPT);
            for (AdPlan plan : overlapAdPlans) {
                if (plan.getRegionalOrientation().equals(REGIONAL_ORIENTATION)) {
                    plan.setInlandRegionalDest(AreaUtil.getSubCityIds(LEVEL_INLAND, plan.getInlandRegionalDest()));
                    plan.setInternationalRegionalDest(AreaUtil.getSubCityIds(LEVEL_INTERNATION, plan.getInternationalRegionalDest()));
                }
            }
            // 通过CPT计划反向过滤
            Set<Long> planIds = overlapAdPlans.stream().map(AdPlan::getId).collect(Collectors.toSet());
            adGroups = adGroups.stream().filter(adGroup -> planIds.contains(adGroup.getAdPlanId())).collect(Collectors.toList());
            Set<Long> groupIds = adGroups.stream().map(AdGroup::getId).collect(Collectors.toSet());
            adContentRelations = adContentRelations.stream().filter(adContentRelation -> groupIds.contains(adContentRelation.getAdGroupId())).collect(Collectors.toList());
            // 需要check的地域
            List<Integer> inlandCities = new ArrayList<>();
            List<Integer> internationalCities = new ArrayList<>();
            if (adPlan.getRegionalOrientation().equals(REGIONAL_ORIENTATION)) {
                inlandCities.addAll(AreaUtil.getSubCityIds(LEVEL_INLAND, adPlan.getInlandRegionalDest()));
                internationalCities.addAll(AreaUtil.getSubCityIds(LEVEL_INTERNATION, adPlan.getInternationalRegionalDest()));
            } else {
                inlandCities.addAll(AreaUtil.getSubCityIds(LEVEL_INLAND));
                internationalCities.addAll(AreaUtil.getSubCityIds(LEVEL_INTERNATION));
            }
            Map<Integer, Set<AdDeliveryTime>> inlandCityIdToAdDeliveryTimes = getCityIdToAdDeliveryTimes(
                    overlapDeliveryTimes, overlapAdPlans, inlandCities, LEVEL_INLAND);
            Map<Integer, Set<AdDeliveryTime>> internationalCityIdToAdDeliveryTimes = getCityIdToAdDeliveryTimes(
                    overlapDeliveryTimes, overlapAdPlans, internationalCities, LEVEL_INTERNATION);
            adPositionIdToSoldMax = getIdToSoldMax(targetDeliveryTimes,
                    inlandCityIdToAdDeliveryTimes,
                    internationalCityIdToAdDeliveryTimes,
                    adGroups,
                    adContentRelations,
                    adPlanId,
                    overlapAdPlans.stream().collect(Collectors.toMap(AdPlan::getId, Function.identity()))
            );
        }
        return adPositionIdToSoldMax;
    }

    /**
     * 推广组原先购买的轮播数
     *
     * @param adGroupId 推广组id
     * @return 样式id -> 轮播数
     */
    private Map<Long, Integer> getStyleIdToDisplay(Long adGroupId) {
        if (Objects.nonNull(adGroupId)) {
            List<AdContentRelation> adContentRelations = adContentRelationService.listByAdGroupId(adGroupId);
            Map<Long, Integer> styleIdToDisplay = new HashMap<>();
            for (AdContentRelation adContentRelation : adContentRelations) {
                Integer num = styleIdToDisplay.getOrDefault(adContentRelation.getStyleId(), 0);
                styleIdToDisplay.put(adContentRelation.getStyleId(), num + adContentRelation.getDisplayWeight());
            }
            return styleIdToDisplay;
        } else {
            return Collections.emptyMap();
        }
    }

    /**
     * @param targetDeliveryTimes                  需要计算的投放时间
     * @param inlandCityIdToAdDeliveryTimes        国内城市id ->  重叠投放时间
     * @param internationalCityIdToAdDeliveryTimes 海外城市id  ->  重叠投放时间
     * @param adGroups                             推广组列表
     * @param adContentRelations                   购买关联列表
     * @param targetAdPlanId                       目标推广计划id,如果求所有的广告位最大值则传null
     */
    private Map<Long, Integer> getIdToSoldMax(List<AdDeliveryTime> targetDeliveryTimes,
                                              Map<Integer, Set<AdDeliveryTime>> inlandCityIdToAdDeliveryTimes,
                                              Map<Integer, Set<AdDeliveryTime>> internationalCityIdToAdDeliveryTimes,
                                              List<AdGroup> adGroups,
                                              List<AdContentRelation> adContentRelations,
                                              Long targetAdPlanId,
                                              Map<Long, AdPlan> idToAdPlan) {
        Map<Long, Map<Long, Integer>> adPlanIdToAdPositionIdToBought = getAdPlanIdToAdPositionIdToBought(adContentRelations, adGroups);
        Map<Long, Integer> adPositionIdToSoldMax = new HashMap<>();
        populateAdPositionIdToSoldMax(targetDeliveryTimes,
                inlandCityIdToAdDeliveryTimes,
                adPlanIdToAdPositionIdToBought,
                adPositionIdToSoldMax,
                targetAdPlanId,
                idToAdPlan);
        populateAdPositionIdToSoldMax(targetDeliveryTimes,
                internationalCityIdToAdDeliveryTimes,
                adPlanIdToAdPositionIdToBought,
                adPositionIdToSoldMax,
                targetAdPlanId,
                idToAdPlan);
        return adPositionIdToSoldMax;
    }

    /**
     * @param targetDeliveryTimes            需要计算的时间段
     * @param cityIdToAdDeliveryTimes        城市id和重叠的时间段map
     * @param adPlanIdToAdPositionIdToBought 推广计划买的广告位轮播数映射
     * @param adPositionIdToSoldMax          广告位售卖最大轮播数map
     * @param targetPlanId                   目前计划id
     * @param idToAdPlan                     id->plan
     */
    public void populateAdPositionIdToSoldMax(Collection<AdDeliveryTime> targetDeliveryTimes,
                                              Map<Integer, Set<AdDeliveryTime>> cityIdToAdDeliveryTimes,
                                              Map<Long, Map<Long, Integer>> adPlanIdToAdPositionIdToBought,
                                              Map<Long, Integer> adPositionIdToSoldMax,
                                              Long targetPlanId,
                                              Map<Long, AdPlan> idToAdPlan) {
        if (Objects.isNull(targetPlanId)) {
            for (Long planId : adPlanIdToAdPositionIdToBought.keySet()) {
                Map<Long, Integer> positionIdToBought = adPlanIdToAdPositionIdToBought.get(planId);
                for (Long positionId : positionIdToBought.keySet()) {
                    adPositionIdToSoldMax.put(positionId, Math.max(adPositionIdToSoldMax.getOrDefault(positionId, 0), positionIdToBought.get(positionId)));
                }
            }
        }

        for (Integer cityId : cityIdToAdDeliveryTimes.keySet()) {
            List<Set<AdDeliveryTime>> overlapPlan = getOverlapPlan(targetDeliveryTimes, cityIdToAdDeliveryTimes, cityId);
            for (Set<AdDeliveryTime> deliveryTimes : overlapPlan) {
                Map<Long, Integer> adPositionIdToDisplay = new HashMap<>();
                for (AdDeliveryTime deliveryTime : deliveryTimes) {
                    Long adPlanId = deliveryTime.getAdPlanId();
                    // CPM不计算
                    if (Objects.isNull(idToAdPlan.get(adPlanId)) || idToAdPlan.get(adPlanId).getBillingType().equals(CPM)) {
                        continue;
                    }
                    Map<Long, Integer> adPositionIdToBought = adPlanIdToAdPositionIdToBought.getOrDefault(adPlanId, Collections.emptyMap());
                    for (Long adPositionId : adPositionIdToBought.keySet()) {
                        Integer bought = adPositionIdToDisplay.getOrDefault(adPositionId, 0) + adPositionIdToBought.get(adPositionId);
                        adPositionIdToDisplay.put(adPositionId, bought);
                        if (adPositionIdToSoldMax.getOrDefault(adPositionId, 0) < bought) {
                            adPositionIdToSoldMax.put(adPositionId, bought);
                        }
                    }
                }
            }
        }
    }

    @Override
    public Collection<ScheduleResourceDetail> getScheduleResourceDetails(LocalDate startDate,
                                                                         LocalDate endDate,
                                                                         Integer billingType,
                                                                         Collection<Long> adPositionIds) {
        // 需要check的重叠时间
        LocalDateTime startTime = LocalDateTime.of(startDate, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(endDate, LocalTime.MAX);
        List<AdDeliveryTime> overlapDeliveryTime = adDeliveryTimeService.listByTimeInterval(startTime, endTime);

        // 在指定时间段内符合条件的推广计划
        Set<AdPlan> overlapAdPlans = new HashSet<>(adPlanService.listNotDeleteByIdsAndBillingType(overlapDeliveryTime
                        .stream()
                        .map(AdDeliveryTime::getAdPlanId)
                        .collect(Collectors.toSet()),
                billingType));
        Set<Long> overlapAdPlanIds = overlapAdPlans.stream().map(AdPlan::getId).collect(Collectors.toSet());

        List<AdGroup> adGroups = adGroupService.listNoteDeleteByAdPlanIds(overlapAdPlanIds);

        // 得出符合时间段且投了目标广告位的推广和推广组
        List<Style> styles = styleService.listNotDeletedByAdPositionIds(adPositionIds);
        Set<Long> styleIds = styles.stream().map(Style::getId).collect(Collectors.toSet());
        List<AdContentRelation> adContentRelations = adContentRelationService.listByStyleIdsAndGroupIds(styleIds, adGroups.stream().map(AdGroup::getId).collect(Collectors.toSet()));
        Set<Long> adGroupIds = adContentRelations.stream().map(AdContentRelation::getAdGroupId).collect(Collectors.toSet());

        adGroups = adGroupService.listNotDeleteByIds(adGroupIds);
        Set<Long> planIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toSet());

        Map<Long, Map<Long, PositionProperty>> adPlanIdToAdPositionIdToProperty = getAdPlanIdToAdPositionIdToProperty(adContentRelations,
                getPlanIdToDays(planIds),
                adGroups,
                billingType,
                adPositionIds);

        overlapDeliveryTime = overlapDeliveryTime.stream().filter(adDeliveryTime -> planIds.contains(adDeliveryTime.getAdPlanId())).collect(Collectors.toList());

        Map<Long, AdPlan> idToAdPlan = overlapAdPlans.stream().collect(Collectors.toMap(AdPlan::getId, Function.identity()));
        Map<Long, Integer> planIdToCityNum = getPlanIdToCityNum(overlapAdPlans);

        List<ResourceDetail> resourceDetails = new ArrayList<>();
        for (AdDeliveryTime adDeliveryTime : overlapDeliveryTime) {
            AdPlan plan = idToAdPlan.get(adDeliveryTime.getAdPlanId());
            Map<Long, PositionProperty> positionIdToProperty = adPlanIdToAdPositionIdToProperty.get(plan.getId());
            // 这个推广计划没有有效的购买记录
            if (Objects.isNull(positionIdToProperty)) {
                continue;
            }
            LocalDateTime deliveryStartTime = adDeliveryTime.getStartTime();
            LocalDateTime deliveryEndTime = adDeliveryTime.getEndTime();
            while (deliveryStartTime.isBefore(deliveryEndTime)) {
                if (LocalDateUtil.inTheInterval(deliveryStartTime, startTime, endTime)) {
                    populateScheduleDetails(positionIdToProperty,
                            deliveryStartTime,
                            planIdToCityNum.get(plan.getId()),
                            resourceDetails,
                            billingType);
                }
                deliveryStartTime = deliveryStartTime.plusHours(1);
            }
        }
        Map<String, ScheduleResourceDetail> scheduleResourceDetails = new LinkedHashMap<>();
        for (ResourceDetail resourceDetail : resourceDetails) {
            String key = resourceDetail.getDateTime().toLocalDate().toString() + resourceDetail.getAdPositionId();
            ScheduleResourceDetail detail = new ScheduleResourceDetail(resourceDetail);
            ScheduleResourceDetail scheduleResourceDetail = scheduleResourceDetails.getOrDefault(key, null);
            if (scheduleResourceDetail != null) {
                if (billingType.equals(CPT)) {
                    detail.setWeight(detail.getWeight() + scheduleResourceDetail.getWeight());
                    scheduleResourceDetails.put(key, detail);
                } else {
                    if (detail.getSale() > scheduleResourceDetail.getSale()) {
                        scheduleResourceDetails.put(key, detail);
                    }
                }
            } else {
                scheduleResourceDetails.put(key, detail);
            }
        }
        return scheduleResourceDetails.values();
    }

    /**
     * 计算推广计划投放广告的城市数量
     */
    private Map<Long, Integer> getPlanIdToCityNum(Collection<AdPlan> overlapAdPlans) {
        HashMap<Long, Integer> planIdToCityNum = new HashMap<>();
        for (AdPlan plan : overlapAdPlans) {
            if (plan.getRegionalOrientation().equals(REGIONAL_ORIENTATION)) {
                planIdToCityNum.put(plan.getId(), AreaUtil.getSubCityIds(LEVEL_INLAND, plan.getInlandRegionalDest()).size() + AreaUtil.getSubCityIds(LEVEL_INLAND, plan.getInternationalRegionalDest()).size());
            } else {
                planIdToCityNum.put(plan.getId(), AreaUtil.ALL_INLAND_SUB_CITY_IDS.size() + AreaUtil.ALL_INTERNATION_SUB_CITY_IDS.size());
            }
        }
        return planIdToCityNum;
    }

    /**
     * 计算推广计划投放的天数
     */
    private Map<Long, Integer> getPlanIdToDays(Set<Long> overlapAdPlanIds) {
        List<AdDeliveryTime> times = adDeliveryTimeService.listByAdPlanIds(overlapAdPlanIds);
        Map<Long, Integer> planIdToDays = new HashMap<>();
        Map<Long, Set<LocalDate>> planIdToDates = new HashMap<>();
        for (AdDeliveryTime time : times) {
            planIdToDates.computeIfAbsent(time.getAdPlanId(), k -> new HashSet<>()).addAll(getLocalDateFromDeliveryTime(time));
        }
        for (Long planId : planIdToDates.keySet()) {
            planIdToDays.put(planId, planIdToDates.get(planId).size());
        }
        return planIdToDays;
    }

    private Set<LocalDate> getLocalDateFromDeliveryTime(AdDeliveryTime time) {
        Set<LocalDate> localDates = new HashSet<>();
        LocalDateTime startTime = time.getStartTime();
        LocalDateTime endTime = time.getEndTime();
        while (!startTime.isAfter(endTime)) {
            localDates.add(startTime.toLocalDate());
            startTime = startTime.plusDays(1);
        }
        return localDates;
    }

    /**
     * 推广计划 购买的广告位的属性
     */
    private Map<Long, Map<Long, PositionProperty>> getAdPlanIdToAdPositionIdToProperty(List<AdContentRelation> adContentRelations,
                                                                                       Map<Long, Integer> planIdToDays,
                                                                                       List<AdGroup> adGroups,
                                                                                       Integer billingType,
                                                                                       Collection<Long> adPositionIds) {
        Map<Long, Map<Long, PositionProperty>> adPlanIdToAdPositionIdToProperty = new HashMap<>();
        Map<Long, AdGroup> idToAdGroup = adGroups.stream().collect(Collectors.toMap(AdGroup::getId, Function.identity()));
        List<Style> styles = styleService.listNotDeletedByIdsAndAdPositionIds(adContentRelations.stream()
                .map(AdContentRelation::getStyleId).collect(Collectors.toSet()), adPositionIds);
        Map<Long, Style> idToStyle = styles.stream().collect(Collectors.toMap(Style::getId, Function.identity()));
        Map<Long, Long> adGroupIdToStyleNum = getAdGroupIdToStyleNum(billingType, idToAdGroup);
        for (AdContentRelation relation : adContentRelations) {
            Long adGroupId = relation.getAdGroupId();
            AdGroup adGroup = idToAdGroup.get(adGroupId);
            Style style = idToStyle.get(relation.getStyleId());
            if (!ObjectUtils.allNotNull(adGroup, style)) {
                continue;
            }
            Map<Long, PositionProperty> adPositionIdToProperty = adPlanIdToAdPositionIdToProperty.computeIfAbsent(adGroup.getAdPlanId(), k -> new HashMap<>());
            PositionProperty property = adPositionIdToProperty.getOrDefault(style.getAdPositionId(), new PositionProperty(0L, 0, adGroup.getStatus()));
            if (billingType.equals(CPT)) {
                property.setCptBought(property.getCptBought() + relation.getDisplayWeight());
            } else {
                try {
                    long bought = adGroup.getSumDisplayCount()
                            / planIdToDays.get(adGroup.getAdPlanId())
                            / adGroupIdToStyleNum.get(adGroupId);
                    property.setCpmBought(property.getCpmBought() + bought);
                } catch (NullPointerException e) {
                    log.error("计算轮播出错:adGroupId:{},adPlanId:{}", adGroupId, adGroup.getAdPlanId());
                }
            }
            if (property.getStatus() == DELIVER_PAUSE.getCode() && property.getStatus() != adGroup.getStatus()) {
                property.setStatus(adGroup.getStatus());
            }
            adPositionIdToProperty.put(style.getAdPositionId(), property);
        }
        return adPlanIdToAdPositionIdToProperty;
    }

    /**
     * 获取推广组买了几个样式
     */
    private Map<Long, Long> getAdGroupIdToStyleNum(Integer billingType, Map<Long, AdGroup> idToAdGroup) {
        Map<Long, Long> adGroupIdToStyleNum = new HashMap<>();
        if (billingType.equals(CPM)) {
            List<AdContentRelation> relations = adContentRelationService.listByAdGroupIds(idToAdGroup.keySet());
            adGroupIdToStyleNum = relations.stream().collect(Collectors.groupingBy(AdContentRelation::getAdGroupId, Collectors.counting()));
        }
        return adGroupIdToStyleNum;
    }

    private void populateScheduleDetails(Map<Long, PositionProperty> positionIdToProperty,
                                         LocalDateTime startTime,
                                         Integer cityNum,
                                         List<ResourceDetail> resourceDetails,
                                         Integer billingType) {
        for (Long positionId : positionIdToProperty.keySet()) {
            ResourceDetail detail = new ResourceDetail();
            detail.setDateTime(startTime);
            detail.setAdPositionId(positionId);
            PositionProperty property = positionIdToProperty.get(positionId);
            detail.setSale(property.getCpmBought());
            detail.setWeight(property.getCptBought() * cityNum);
            detail.setStatus(property.getStatus());
            if (resourceDetails.contains(detail)) {
                for (ResourceDetail o : resourceDetails) {
                    if (o.equals(detail)) {
                        if (billingType.equals(CPT)) {
                            detail.setWeight(property.getCptBought() * cityNum);
                            o.setWeight(o.getWeight() + detail.getWeight());
                        } else {
                            o.setSale(o.getSale() + detail.getSale());
                        }
                        // 已有状态是暂停，新增状态不是暂停，那么就修改状态
                        // 已有状态不是暂停，新增状态无论是什么都不影响后续逻辑
                        if (o.getStatus().equals(DELIVER_PAUSE.getCode()) && !o.getStatus().equals(detail.getStatus())) {
                            o.setStatus(detail.getStatus());
                        }
                        break;
                    }
                }
            } else {
                resourceDetails.add(detail);
            }
        }
    }


    /**
     * 获取  cityId ->  AdDeliveryTimes
     */
    private Map<Integer, Set<AdDeliveryTime>> getCityIdToAdDeliveryTimes(Collection<AdDeliveryTime> overlapDeliveryTimes,
                                                                         List<AdPlan> overlapAdPlans,
                                                                         List<Integer> cityIds,
                                                                         Integer level) {
        // 城市id和命中该城市的推广计划id map
        Map<Integer, List<Long>> cityIdToAdPlanIds = new HashMap<>();
        for (Integer cityId : cityIds) {
            for (AdPlan overlapAdPlan : overlapAdPlans) {
                if (adPlanContainsCity(cityId, overlapAdPlan, level)) {
                    cityIdToAdPlanIds.computeIfAbsent(cityId, k -> new ArrayList<>()).add(overlapAdPlan.getId());
                }
            }
        }
        Map<Integer, Set<AdDeliveryTime>> cityIdToAdDeliveryTimes = new HashMap<>();
        for (Integer inlandCityId : cityIdToAdPlanIds.keySet()) {
            for (AdDeliveryTime time : overlapDeliveryTimes) {
                if (cityIdToAdPlanIds.get(inlandCityId).contains(time.getAdPlanId())) {
                    cityIdToAdDeliveryTimes.computeIfAbsent(inlandCityId, k -> new HashSet<>()).add(time);
                }
            }
        }
        return cityIdToAdDeliveryTimes;
    }

    /**
     * 判断推广计划是否包含该城市
     *
     * @param cityId        城市id
     * @param overlapAdPlan 重叠的推广计划
     * @param level         国内/海外
     * @return true-包含
     */
    private boolean adPlanContainsCity(Integer cityId, AdPlan overlapAdPlan, Integer level) {
        if (overlapAdPlan.getRegionalOrientation().equals(NO_REGIONAL_ORIENTATION)) {
            return true;
        } else {
            if (level.equals(LEVEL_INTERNATION) && overlapAdPlan.getInternationalRegionalDest().contains(cityId)) {
                return true;
            } else {
                return level.equals(LEVEL_INLAND) && overlapAdPlan.getInlandRegionalDest().contains(cityId);
            }
        }
    }

    /**
     * 根据广告位id获取最小轮播数
     */
    @Override
    public Integer getPositionSoldDisplayTimes(Long adPositionId) {
        List<Style> styles = styleService.listNotDeletedByAdPositionId(adPositionId);
        List<Long> styleIds = styles.stream().map(Style::getId).collect(Collectors.toList());
        List<AdContentRelation> adContentRelations = adContentRelationService.listByStyleIdsAndFilterByAdGroupId(styleIds, null);
        List<Long> adGroupIds = adContentRelations.stream().map(AdContentRelation::getAdGroupId).collect(Collectors.toList());
        List<AdGroup> adGroups = adGroupService.listNotDeleteByIds(adGroupIds);
        List<Long> adPlanIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toList());
        List<AdPlan> adPlans = adPlanService.listNotDeleteByIdsAndBillingType(adPlanIds, CPT);
        for (AdPlan plan : adPlans) {
            if (plan.getRegionalOrientation().equals(REGIONAL_ORIENTATION)) {
                plan.setInlandRegionalDest(AreaUtil.getSubCityIds(LEVEL_INLAND, plan.getInlandRegionalDest()));
                plan.setInternationalRegionalDest(AreaUtil.getSubCityIds(LEVEL_INTERNATION, plan.getInternationalRegionalDest()));
            }
        }
        // 通过CPT计划反向过滤
        Set<Long> planIds = adPlans.stream().map(AdPlan::getId).collect(Collectors.toSet());
        adGroups = adGroups.stream().filter(adGroup -> planIds.contains(adGroup.getAdPlanId())).collect(Collectors.toList());
        Set<Long> groupIds = adGroups.stream().map(AdGroup::getId).collect(Collectors.toSet());
        adContentRelations = adContentRelations.stream().filter(adContentRelation -> groupIds.contains(adContentRelation.getAdGroupId())).collect(Collectors.toList());
        List<AdDeliveryTime> targetDeliveryTimes = adDeliveryTimeService.listByAdPlanIdsAndTimeInterval(adPlanIds, null, null);

        Map<Integer, Set<AdDeliveryTime>> inlandCityIdToAdDeliveryTimes = getCityIdToAdDeliveryTimes(targetDeliveryTimes,
                adPlans, new ArrayList<>(AreaUtil.getSubCityIds(LEVEL_INLAND)), LEVEL_INLAND);
        Map<Integer, Set<AdDeliveryTime>> internationalCityIdToAdDeliveryTimes = getCityIdToAdDeliveryTimes(targetDeliveryTimes,
                adPlans, new ArrayList<>(AreaUtil.getSubCityIds(LEVEL_INTERNATION)), LEVEL_INTERNATION);
        // 广告位资源最大售出
        Map<Long, Integer> adPositionIdToSoldMax = getIdToSoldMax(targetDeliveryTimes,
                inlandCityIdToAdDeliveryTimes,
                internationalCityIdToAdDeliveryTimes,
                adGroups,
                adContentRelations,
                null,
                adPlans.stream().collect(Collectors.toMap(AdPlan::getId, Function.identity()))
        );
        return adPositionIdToSoldMax.getOrDefault(adPositionId, 0);
    }


    @Override
    public List<AdContentInitialVO> initialCreativeContents(List<Long> styleIds, Long adGroupId, Integer interactionType) {
        List<Style> styles = styleService.listByIds(styleIds);
        List<Long> adPositionIds = styles.stream().map(Style::getAdPositionId).collect(Collectors.toList());
        List<AdPosition> adPositions = adPositionService.listByIds(adPositionIds);

        Map<Integer, AdContentInitialVO> typeToAdContentInitialVO = new HashMap<>();

        for (AdPosition adPosition : adPositions) {
            AdContentInitialVO adContentInitialVO = typeToAdContentInitialVO
                    .computeIfAbsent(adPosition.getType(), k -> {
                        AdContentInitialVO vo = new AdContentInitialVO();
                        vo.setType(adPosition.getType());
                        return vo;
                    });
            for (Style style : styles) {
                if (style.getAdPositionId().equals(adPosition.getId())) {
                    // 展示时间取并集
                    adContentInitialVO.getShowTimeList().addAll(style.getShowTime());
                    populateCreativeContents(style, adContentInitialVO);
                }
            }
        }
        populateMultiLinkClkText(interactionType, typeToAdContentInitialVO);
        if (Objects.nonNull(adGroupId) && adGroupId > 0) {
            Map<Integer, AdContent> typeToAdContent = adContentService.listValidByAdGroupId(adGroupId).stream().collect(Collectors.toMap(AdContent::getType, Function.identity()));
            for (Integer type : typeToAdContentInitialVO.keySet()) {
                AdContentInitialVO initialVO = typeToAdContentInitialVO.get(type);
                AdContent adContent = typeToAdContent.get(type);
                if (Objects.isNull((adContent))) {
                    continue;
                }
                initialVO.setShowTime(adContent.getShowTime());
                populateOldData(initialVO.getImageCreativeContents(), adContent.getImageCreativeContents());
                populateOldData(initialVO.getTextCreativeContents(), adContent.getTextCreativeContents());
                populateOldData(initialVO.getVideoCreativeContents(), adContent.getVideoCreativeContents());
            }
        }
        return new ArrayList<>(typeToAdContentInitialVO.values());
    }

    /**
     * 填充老数据
     */
    private void populateOldData(Set<CreativeContent> creativeContents, Set<CreativeContent> oldCreativeContents) {
        for (CreativeContent imageCreativeContent : creativeContents) {
            if (oldCreativeContents.contains(imageCreativeContent)) {
                for (CreativeContent content : oldCreativeContents) {
                    if (content.equals(imageCreativeContent)) {
                        imageCreativeContent.copyOldData(content);
                    }
                }
            }
        }
    }

    private void populateMultiLinkClkText(Integer interactionType, Map<Integer, AdContentInitialVO> typeToAdContentInitialVO) {
        AdContentInitialVO openScreenAdContentInitialVO = typeToAdContentInitialVO.get(OPEN_SCREEN);
        if (openScreenAdContentInitialVO != null) {
            Set<CreativeContent> textCreativeContents = openScreenAdContentInitialVO.getTextCreativeContents();
            Set<CreativeContent> additionalContents = new HashSet<>();
            for (CreativeContent creativeContent : textCreativeContents) {
                if (creativeContent.getElementKey().equals(CLK_TEXT)) {
                    if (InteractionTypeEnum.DOUBLE_LINK.getCode() == interactionType) {
                        CreativeContent newContent1 = JacksonUtil.deepCopy(creativeContent, CreativeContent.class);
                        newContent1.setElementKey(CLK_TEXT_1);
                        additionalContents.add(newContent1);
                    }
                    if (InteractionTypeEnum.THREE_LINK.getCode() == interactionType) {
                        CreativeContent newContent1 = JacksonUtil.deepCopy(creativeContent, CreativeContent.class);
                        newContent1.setElementKey(CLK_TEXT_1);
                        additionalContents.add(newContent1);
                        CreativeContent newContent2 = JacksonUtil.deepCopy(creativeContent, CreativeContent.class);
                        newContent2.setElementKey(CLK_TEXT_2);
                        additionalContents.add(newContent2);
                    }
                }
            }
            textCreativeContents.addAll(additionalContents);
        }
    }

    /**
     * 将样式 根据种类填充，同时根据key做聚合
     *
     * @param style     样式
     * @param initialVO 初始化VO
     */
    private void populateCreativeContents(Style style, AdContentInitialVO initialVO) {
        for (StyleElement styleElement : style.getTextStyleContent()) {
            populateCreative(styleElement, initialVO.getTextCreativeContents());
        }
        for (StyleElement styleElement : style.getPicStyleContent()) {
            populateCreative(styleElement, initialVO.getImageCreativeContents());
        }
        for (StyleElement styleElement : style.getVideoStyleContent()) {
            populateCreative(styleElement, initialVO.getVideoCreativeContents());
        }
    }

    /**
     * 填充广告创意
     *
     * @param styleElement     样式元素
     * @param creativeContents 推广创意内容（填充目标）
     */
    private void populateCreative(StyleElement styleElement, Set<CreativeContent> creativeContents) {
        CreativeContent creativeContent = getCreativeContent(styleElement);
        CreativeContentProperty property = styleElementMapper.styleElementToCreativeContentProperty(styleElement);
        if (creativeContents.contains(creativeContent)) {
            for (CreativeContent content : creativeContents) {
                if (content.equals(creativeContent)) {
                    content.getProperties().add(property);
                    break;
                }
            }
        } else {
            creativeContent.getProperties().add(property);
            creativeContents.add(creativeContent);
        }
    }

    /**
     * 通过样式元素  填充 创意内容基本属性
     *
     * @param styleElement 央视元素
     * @return 创意内容
     */
    private CreativeContent getCreativeContent(StyleElement styleElement) {
        CreativeContent creativeContent = new CreativeContent();
        creativeContent.setName(styleElement.getName());
        creativeContent.setType(styleElement.getType());
        creativeContent.setElementKey(styleElement.getElementKey());
        return creativeContent;
    }

    /**
     * 得到投放时间有重合的列表
     *
     * @param adDeliveryTimes 投放时间
     * @return 投放时间重合列表集合
     */
    public List<Set<AdDeliveryTime>> getOverlapTimes(Collection<AdDeliveryTime> adDeliveryTimes) {
        List<Set<AdDeliveryTime>> res = new ArrayList<>();
        List<Pair<AdDeliveryTime, Integer>> pairs = new ArrayList<>();
        for (AdDeliveryTime time : adDeliveryTimes) {
            //记录当前时间段的开始标记点
            pairs.add(new ImmutablePair<>(time, 0));
            //记录当前时间段的结束标记点
            pairs.add(new ImmutablePair<>(time, 1));
        }
        pairs.sort((o1, o2) -> {
            LocalDateTime val1, val2;
            //因为时间段是一个左闭右开区间，所以当pair代表一个结束时间点时，将这个时间点前移1s，保证不会错误的计算重合
            val1 = o1.getValue() == 0 ? o1.getKey().getStartTime() : o1.getKey().getEndTime();
            val2 = o2.getValue() == 0 ? o2.getKey().getStartTime() : o2.getKey().getEndTime();
            //当val1==val2时，因为之前的结束时间已经前移了1s，所以若val1,val2一个是开始时间一个是结束时间，
            //则需要让代表开始时间的标记点排在代表结束时间标记点的前面，这样在接下来计算重合时，就可以包含这段重合
            return val1.isEqual(val2) ? o1.getValue().compareTo(o2.getValue()) : val1.compareTo(val2);
        });

        Set<AdDeliveryTime> tempSet = new HashSet<>();
        for (Pair<AdDeliveryTime, Integer> pair : pairs) {
            if (pair.getValue() == 0) {
                //遇到一个新的开始标记点，说明进入一个新的时间段
                tempSet.add(pair.getKey());
            } else {
                //遇到一个结束标记点说明有一个时间段将要结束，在结束之前先判断是否需要加到结果列表中
                boolean flag = true;
                for (Set<AdDeliveryTime> set : res) {
                    //因为最后要得到的结果是一个时间段重叠的集合，所以若结果中已有一个集合包含当前集合全部时间段
                    //则当前集合没必要加到结果中
                    if (set.containsAll(tempSet)) {
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    res.add(new HashSet<>(tempSet));
                }
                tempSet.remove(pair.getKey());
            }
        }
        return res;
    }

    /**
     * @param relations                 购买关联数据
     * @param adDeliveryTimes           投放时间
     * @param regionalOrientation       地域定向
     * @param inlandRegionalDest        国内城市
     * @param internationalRegionalDest 海外城市
     * @param excludeAdPlanId           排除掉的推广计划(修改推广计划的时候，校验资源，需要排除该推广计划)
     * @param excludeAdGroupId          排除掉的推广组(修改推广组的时候，校验资源，需要排除该推广组)
     * @param fullChannel               是否是全链路广告
     * @return false-资源不足
     */
    @Override
    public Boolean resourcesSufficient(Collection<AdContentRelation> relations,
                                       Collection<AdDeliveryTime> adDeliveryTimes,
                                       Integer regionalOrientation,
                                       Collection<Integer> inlandRegionalDest,
                                       Collection<Integer> internationalRegionalDest,
                                       Long excludeAdPlanId,
                                       Long excludeAdGroupId,
                                       Boolean fullChannel) {
        // 1.保证购买关系是有效的
        Set<Long> styleIds = relations.stream().map(AdContentRelation::getStyleId).collect(Collectors.toSet());
        List<Style> styles = styleService.listNotDeletedByIds(styleIds);
        relations = relations.stream().filter(a -> styleIds.contains(a.getStyleId())).collect(Collectors.toList());
        // 2.获取每个广告位轮播数
        List<Long> adPositionIds = styles.stream().map(Style::getAdPositionId).collect(Collectors.toList());
        List<AdPosition> adPositions = adPositionService.listNotDeleteByIds(adPositionIds);
        Map<Long, AdPosition> idToAdPosition = adPositions
                .stream().collect(Collectors.toMap(AdPosition::getId, Function.identity()));
        // 全链路投放，不校验轮播
        Set<Long> needPositionIds = adPositions.stream().filter(p -> !p.getFullChannel() || !fullChannel).map(AdPosition::getId).collect(Collectors.toSet());
        Map<Long, Integer> adPositionIdToNeed = getAdPositionIdToNeed(relations, styles, needPositionIds);
        // 3.购买过相关广告位的资源
        List<Style> allStyles = styleService.listNotDeletedByAdPositionIds(needPositionIds);
        List<Long> allStyleIds = allStyles.stream().map(Style::getId).collect(Collectors.toList());
        List<AdContentRelation> adContentRelations = adContentRelationService.listByStyleIdsAndFilterByAdGroupId(allStyleIds, excludeAdGroupId);
        if (Objects.nonNull(excludeAdPlanId)) {
            Set<Long> adGroupIds = adGroupService.listValidByAdPlanId(excludeAdPlanId).stream().map(AdGroup::getId).collect(Collectors.toSet());
            adContentRelations = adContentRelations.stream().filter(a -> !adGroupIds.contains(a.getAdGroupId())).collect(Collectors.toList());
        }
        Set<Long> adGroupIds = adContentRelations.stream().map(AdContentRelation::getAdGroupId).collect(Collectors.toSet());
        List<AdGroup> adGroups = adGroupService.listNotDeleteByIds(adGroupIds);
        Set<Long> adPlanIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toSet());

        // 4.获取投放时间列表
        List<AdPlan> overlapAdPlans = adPlanService.listNotDeleteByIdsAndBillingType(adPlanIds, CPT);
        Set<AdDeliveryTime> overlapDeliveryTimes = adDeliveryTimeService
                .listOverlapDeliveryTimeByAdDeliveryTimesExcludeAdPlan(adDeliveryTimes, excludeAdPlanId)
                .stream().filter(a -> adPlanIds.contains(a.getAdPlanId())).collect(Collectors.toSet());

        // 通过CPT计划反向过滤
        Set<Long> planIds = overlapAdPlans.stream().map(AdPlan::getId).collect(Collectors.toSet());
        adGroups = adGroups.stream().filter(adGroup -> planIds.contains(adGroup.getAdPlanId())).collect(Collectors.toList());
        Set<Long> groupIds = adGroups.stream().map(AdGroup::getId).collect(Collectors.toSet());
        adContentRelations = adContentRelations.stream().filter(adContentRelation -> groupIds.contains(adContentRelation.getAdGroupId())).collect(Collectors.toList());

        for (AdPlan plan : overlapAdPlans) {
            if (plan.getRegionalOrientation().equals(REGIONAL_ORIENTATION)) {
                plan.setInlandRegionalDest(AreaUtil.getSubCityIds(LEVEL_INLAND, plan.getInlandRegionalDest()));
                plan.setInternationalRegionalDest(AreaUtil.getSubCityIds(LEVEL_INTERNATION, plan.getInternationalRegionalDest()));
            }
        }

        Map<Long, Map<Long, Integer>> adPlanIdToAdPositionIdToBought =
                getAdPlanIdToAdPositionIdToBought(adContentRelations, adGroups);

        // 需要check的地域
        List<Integer> inlandCities = new ArrayList<>();
        List<Integer> internationalCities = new ArrayList<>();
        if (regionalOrientation.equals(REGIONAL_ORIENTATION)) {
            inlandCities.addAll(AreaUtil.getSubCityIds(LEVEL_INLAND, inlandRegionalDest));
            internationalCities.addAll(AreaUtil.getSubCityIds(LEVEL_INTERNATION, internationalRegionalDest));
        } else {
            inlandCities.addAll(AreaUtil.getSubCityIds(LEVEL_INLAND));
            internationalCities.addAll(AreaUtil.getSubCityIds(LEVEL_INTERNATION));
        }

        return adPositionResourceCheck(adDeliveryTimes,
                getCityIdToAdDeliveryTimes(overlapDeliveryTimes, overlapAdPlans, inlandCities, LEVEL_INLAND),
                idToAdPosition,
                adPositionIdToNeed,
                adPlanIdToAdPositionIdToBought,
                overlapAdPlans.stream().collect(Collectors.toMap(AdPlan::getId, Function.identity())))
                &&
                adPositionResourceCheck(adDeliveryTimes,
                        getCityIdToAdDeliveryTimes(overlapDeliveryTimes, overlapAdPlans, internationalCities, LEVEL_INTERNATION),
                        idToAdPosition,
                        adPositionIdToNeed,
                        adPlanIdToAdPositionIdToBought,
                        overlapAdPlans.stream().collect(Collectors.toMap(AdPlan::getId, Function.identity())));
    }

    /**
     * 获取推广计划 已购的广告位的轮播数
     *
     * @param adContentRelations 关系
     * @param adGroups           推广组列表
     * @return 推广计划 已购的广告位的轮播数
     */
    private Map<Long, Map<Long, Integer>> getAdPlanIdToAdPositionIdToBought(List<AdContentRelation> adContentRelations,
                                                                            List<AdGroup> adGroups) {
        Map<Long, Map<Long, Integer>> adPlanIdToAdPositionIdToBought = new HashMap<>();
        Map<Long, AdGroup> idToAdGroup = adGroups.stream().collect(Collectors.toMap(AdGroup::getId, Function.identity()));
        Map<Long, Style> idToStyle = styleService.listNotDeletedByIds(adContentRelations.stream().map(AdContentRelation::getStyleId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(Style::getId, Function.identity()));
        Map<Long, AdPosition> idToAdPosition = adPositionService.listByIds(idToStyle.values().stream().map(Style::getAdPositionId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(AdPosition::getId, Function.identity()));

        for (AdContentRelation relation : adContentRelations) {
            Long adGroupId = relation.getAdGroupId();
            AdGroup adGroup = idToAdGroup.get(adGroupId);
            Style style = idToStyle.get(relation.getStyleId());
            AdPosition adPosition = idToAdPosition.get(style.getAdPositionId());
            // 样式或推广组被删除，释放资源
            // 如果推广组是全链路广告，不占用轮播
            if (!ObjectUtils.allNotNull(adGroup, style, adPosition)
                    || (adGroup.getFullChannel() && adPosition.getFullChannel())
            ) {
                continue;
            }
            Map<Long, Integer> adPositionIdToBought = adPlanIdToAdPositionIdToBought.computeIfAbsent(adGroup.getAdPlanId(), k -> new HashMap<>());
            Integer bought = adPositionIdToBought.getOrDefault(style.getAdPositionId(), 0);
            adPositionIdToBought.put(style.getAdPositionId(), bought + relation.getDisplayWeight());
        }
        return adPlanIdToAdPositionIdToBought;
    }


    /**
     * @param adDeliveryTimes                投放时间
     * @param cityIdToAdDeliveryTimes        城市和重叠推广时间map
     * @param idToAdPosition                 未被删除的广告位map
     * @param adPositionIdToNeed             广告位购买数量map
     * @param adPlanIdToAdPositionIdToBought 推广计划购买的广告位轮播数
     * @return false-资源不足
     */
    @ExecuteTime("adPositionResourceCheck")
    private boolean adPositionResourceCheck(Collection<AdDeliveryTime> adDeliveryTimes,
                                            Map<Integer, Set<AdDeliveryTime>> cityIdToAdDeliveryTimes,
                                            Map<Long, AdPosition> idToAdPosition,
                                            Map<Long, Integer> adPositionIdToNeed,
                                            Map<Long, Map<Long, Integer>> adPlanIdToAdPositionIdToBought,
                                            Map<Long, AdPlan> idToAdPlan) {
        // 先检查单独购买的是否已经超出，如果没超出再检验重叠的
        for (Long positionId : adPositionIdToNeed.keySet()) {
            if (adPositionIdToNeed.get(positionId) > idToAdPosition.get(positionId).getDisplayTimes()) {
                return false;
            }
        }
        for (Integer cityId : cityIdToAdDeliveryTimes.keySet()) {
            List<Set<AdDeliveryTime>> overlapPlan = getOverlapPlan(adDeliveryTimes, cityIdToAdDeliveryTimes, cityId);
            for (Set<AdDeliveryTime> deliveryTimes : overlapPlan) {
                Map<Long, Integer> adPositionIdToDisplay = new HashMap<>();
                for (AdDeliveryTime deliveryTime : deliveryTimes) {
                    Long adPlanId = deliveryTime.getAdPlanId();
                    if (Objects.isNull(idToAdPlan.get(adPlanId)) || idToAdPlan.get(adPlanId).getBillingType().equals(CPM)) {
                        continue;
                    }
                    Map<Long, Integer> adPositionIdToBought = adPlanIdToAdPositionIdToBought.getOrDefault(adPlanId, Collections.emptyMap());
                    for (Long adPositionId : adPositionIdToBought.keySet()) {
                        Integer bought = adPositionIdToDisplay.getOrDefault(adPositionId, 0) + adPositionIdToBought.get(adPositionId);
                        adPositionIdToDisplay.put(adPositionId, bought);
                        if (bought + adPositionIdToNeed.get(adPositionId) > idToAdPosition.get(adPositionId).getDisplayTimes()) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取有重叠的时间区间集合
     */
    private List<Set<AdDeliveryTime>> getOverlapPlan(Collection<AdDeliveryTime> adDeliveryTimes, Map<Integer, Set<AdDeliveryTime>> cityIdToAdDeliveryTimes, Integer cityId) {
        Set<AdDeliveryTime> times = cityIdToAdDeliveryTimes.get(cityId);
        times.addAll(adDeliveryTimes);
        List<Set<AdDeliveryTime>> overlapPlan = getOverlapTimes(times);
        // 如果重叠的集合不包含需要测量的时间段就排除
        overlapPlan = overlapPlan.stream().filter(s -> !CollectionUtils.intersection(s, adDeliveryTimes).isEmpty()).collect(Collectors.toList());
        return overlapPlan;
    }


    /**
     * 计算广告位的购买数量
     */
    private Map<Long, Integer> getAdPositionIdToNeed(Collection<AdContentRelation> relations, List<Style> styles, Set<Long> needPositionIds) {
        Map<Long, Integer> adPositionIdToNeed = new HashMap<>();
        Map<Long, Style> idToStyle = styles.stream().collect(Collectors.toMap(Style::getId, Function.identity()));

        for (AdContentRelation relation : relations) {
            Long adPositionId = idToStyle.get(relation.getStyleId()).getAdPositionId();
            if (!needPositionIds.contains(adPositionId)) {
                continue;
            }
            Integer display = adPositionIdToNeed.getOrDefault(adPositionId, 0);
            if (Objects.isNull(relation.getDisplayWeight())) {
                throw new CustomException(INVALID_PARAMETERS, "购买轮播数不能为空");
            }
            display += relation.getDisplayWeight();
            adPositionIdToNeed.put(adPositionId, display);
        }
        return adPositionIdToNeed;
    }

    @Override
    public PageVO<PromotionResource> listPromotionResourceLikeName(PageLikeNameVO pageLikeNameVO) {
        List<AdPlan> adPlans = adPlanService.listNotDeleteByNameAndDeliveryType(pageLikeNameVO.getName(), pageLikeNameVO.getDeliveryType());
        Set<Long> adPlanIds = adPlans.stream().map(AdPlan::getId).collect(Collectors.toSet());
        List<AdGroup> adGroups;
        if (Objects.isNull(pageLikeNameVO.getAdGroupId())) {
            adGroups = adGroupService.listNotDeletedAdGroupsLikeNameOrInAdPlanIds(pageLikeNameVO.getName(), adPlanIds);
        } else {
            adGroups = adGroupService.listByIds(Collections.singleton(pageLikeNameVO.getAdGroupId()));
        }
        adPlanIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toSet());
        if (ObjectUtils.allNotNull(pageLikeNameVO.getCurrent(), pageLikeNameVO.getSize())) {
            Page<AdPlan> page = adPlanService.pageInIdsAndDeliveryType(adPlanIds, pageLikeNameVO.getDeliveryType(), pageLikeNameVO.getCurrent(), pageLikeNameVO.getSize());
            adPlans = page.getRecords();
            List<PromotionResource> resources = aggregatePromotionResources(adPlans, adGroups);
            return new PageVO<>(page.getCurrent(), pageLikeNameVO.getSize(), resources, page.getTotal());
        } else {
            adPlans = adPlanService.listByIdsAndDeliveryType(adPlanIds, pageLikeNameVO.getDeliveryType());
            List<PromotionResource> resources = aggregatePromotionResources(adPlans, adGroups);
            resources.removeIf(a -> CollectionUtils.isEmpty(a.getAdGroupList()));
            return new PageVO<>(0L, (long) adPlans.size(), resources, (long) adPlans.size());
        }
    }

    private List<PromotionResource> aggregatePromotionResources(List<AdPlan> adPlans, List<AdGroup> adGroups) {
        if (ObjectUtils.isEmpty(adPlans)) {
            return Lists.emptyList();
        }
        List<PromotionResource> adPlanResource = new ArrayList<>();
        Map<Long, PromotionResource> idToAdPlanResource = new HashMap<>();
        for (AdPlan adPlan : adPlans) {
            PromotionResource promotionResource = new PromotionResource(adPlan.getId(), adPlan.getName(), new ArrayList<>());
            adPlanResource.add(promotionResource);
            idToAdPlanResource.put(adPlan.getId(), promotionResource);
        }
        if (ObjectUtils.isEmpty(adGroups)) {
            return adPlanResource;
        }
        for (AdGroup adGroup : adGroups) {
            PromotionResource resource;
            if ((resource = idToAdPlanResource.get(adGroup.getAdPlanId())) != null) {
                resource.getAdGroupList().add(new PromotionResource(adGroup.getId(), adGroup.getName(), null));
            }
        }
        return adPlanResource;
    }
}

package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: 李梦杰
 * @date: 2021/8/23/18:35
 * @description:
 */
@Data
public class StyleCriteriaQueryVO {
    private String id;
    private String name;
    @ApiModelProperty(value = "广告位id")
    private String adPositionId;
    @ApiModelProperty(value = "广告位名字")
    private String adPositionName;
    @ApiModelProperty(value = "媒体id")
    private String mediaId;
    @ApiModelProperty(value = "媒体名字")
    private String mediaName;
    private Integer status;
    @ApiModelProperty(value = "当前页面", required = true)
    @NotNull(message = "当前页面")
    private Long current;
    @ApiModelProperty(value = "页面大小", required = true)
    @NotNull(message = "页面大小不能为空")
    private Long size;
}

package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.entity.YexDsp;
import outfox.ead.youxuan.web.ad.controller.dto.MediaResource;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/14 19:27
 */
@Data
public class InitialVO {
    @ApiModelProperty("轮播数据")
    List<MediaResource> carouselInfo;
    @ApiModelProperty("创意内容")
    List<AdContentInitialVO> adContents;
    @ApiModelProperty("是否开启开屏回收 0-不支持 1-支持")
    @Min(value = 0, message = "开屏回收类型错误")
    @Max(value = 1, message = "开屏回收类型错误")
    private Integer openScreenRecycling;
    @ApiModelProperty("是否开启开机首刷 0-不支持 1-支持")
    @Min(value = 0, message = "开机首刷类型错误")
    @Max(value = 1, message = "开机首刷类型错误")
    private Integer bootFirstRefresh;
    @ApiModelProperty("样式是否支持开屏回收 0-不支持 1-支持")
    private Integer supportOpenScreenRecycling;
    @ApiModelProperty("样式是否支持开启开机首刷 0-不支持 1-支持")
    private Integer supportBootFirstRefresh;
    @ApiModelProperty("是否支持定向查词")
    private Boolean supportAdvertisingByKeywords;
    @ApiModelProperty("是否支持人群定向")
    private Boolean supportAdvertisingByCrowd;
    @ApiModelProperty("是否支持摇一摇")
    private Boolean supportShakable;
    @ApiModelProperty("是否支持扭一扭")
    private Boolean supportTwist;
    @ApiModelProperty("是否支持滑动交互")
    private Boolean slideInteract;
    @ApiModelProperty("是否支持双link")
    private Boolean doubleLink;
    @ApiModelProperty("是否支持三link")
    private Boolean threeLink;
    @ApiModelProperty("是否支持三合一交互")
    private Boolean supportThreeInOne;
    @ApiModelProperty("选择的资源状态变更信息")
    private String msg;
    @ApiModelProperty("是否需要青少年模式选项")
    private Boolean isNeedYouthMode;
    @ApiModelProperty("DSP列表")
    private List<YexDsp> dspList;
    @ApiModelProperty("是否支持全链路投放")
    private Boolean supportFullChannel = false;
    @ApiModelProperty("是否是音频广告")
    private Boolean isAudio = false;
    @ApiModelProperty("是否支持全屏点击")
    private Boolean supportFullClick = false;
    @ApiModelProperty("全屏点击是否开启")
    private Boolean fullClickIsOpen = false;
    @ApiModelProperty("全屏点击是否限制地域；true:不限制地域：false:限制地域")
    private Boolean fullClickIsAllCity = false;
    @ApiModelProperty("全屏点击支持的城市等级")
    private List<Integer> fullClickClassCities;
    @ApiModelProperty("广告位灵敏度配置列表")
    private List<SensitivityConfig> adPositionSensitivityConfigs;

    @Data
    public static class SensitivityConfig {
        @ApiModelProperty("广告位ID")
        private Long adPositionId;

        @ApiModelProperty("广告位名称")
        private String adPositionName;

        @ApiModelProperty("灵敏度配置-摇动加速度")
        private Integer shakeSpeed;

        @ApiModelProperty("灵敏度配置-滑动角度")
        private Integer slideAngle;

        @ApiModelProperty("灵敏度配置-摇动角度/扭转角度")
        private Integer rotationAngle;
    }

}

package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.AdPositionService;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * @author: 李梦杰
 * @date: 2021/8/19/12:08
 * @description:
 */
@RestController
@BaseResponse
@Validated
@Api(tags = {"广告位"})
@AllArgsConstructor
@RequestMapping("/ad_position")
public class AdPositionController {
    private final AdPositionService adPositionService;

    @GetMapping("/{id}")
    @AccessControl(roles = RoleEnum.ADMIN)
    public AdPositionByIdVO getAdPosition(@PathVariable("id") @NotNull(message = "id不能为空") Long id) {
        return adPositionService.getVoById(id);
    }

    /**
     * 条件查询广告位
     *
     * @param adPosition 广告位
     * @return 列表
     */
    @GetMapping
    public PageVO<AdPositionListVO> getAdPositionList(AdPositionCriteriaQueryVO adPosition) {
        return adPositionService.pageList(adPosition);
    }

    /**
     * 插入/修改广告位
     *
     * @param adPosition 广告位
     */
    @PostMapping
    @AccessControl(roles = RoleEnum.ADMIN)
    public Long saveOrUpdateAdPosition(@Valid @RequestBody AdPositionSaveOrUpdateVO adPosition) {
        return adPositionService.saveOrUpdate(adPosition);

    }

    /**
     * 批量修改广告位状态
     *
     * @param adPositions 广告位id,status列表
     */
    @PutMapping("/batch_update_status")
    @AccessControl(roles = RoleEnum.ADMIN)
    public String batchUpdateStatus(@Valid @RequestBody BatchUpdateStatusVO adPositions) {
        return adPositionService.saveOrUpdateBatch(adPositions);

    }

    /**
     * 校验名字是否重复
     *
     * @param countByNameVO 广告位名字
     * @return apiResponse
     */
    @GetMapping("/name_repeat")
    public void repeatAdPositionName(AdPositionCountByNameVO countByNameVO) {
        adPositionService.nameRepeat(countByNameVO.getName(),
                countByNameVO.getId(),
                countByNameVO.getMediaId());
    }

    /**
     * 校验广告位轮播数
     *
     * @param id           广告位id
     * @param displayTimes 轮播数
     * @return apiResponse
     */
    @GetMapping("/displayTimeValidate")
    public void displayTimeValidate(Long id, Integer displayTimes) {
        adPositionService.displayTimeValidate(id, displayTimes);
    }
}

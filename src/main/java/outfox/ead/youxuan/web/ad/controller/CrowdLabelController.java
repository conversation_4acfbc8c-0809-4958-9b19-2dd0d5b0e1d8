package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.util.CrowdLabelUtil;
import outfox.ead.youxuan.web.ad.controller.dto.CrowdLabel;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022年02月14日 11:17 上午
 */
@RestController
@RequestMapping("/crowdLabel")
@BaseResponse
@AllArgsConstructor
@Api(tags = "人群标签")
@Slf4j
public class CrowdLabelController {
    @GetMapping
    @Operation(method = "返回人群标签列表", description = "返回人群标签列表")
    public Collection<CrowdLabel> listCrowd() {
        return CrowdLabelUtil.getCrowdLabels();
    }
}

package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.web.ad.controller.vo.ClassCitiesVO;
import outfox.ead.youxuan.web.ad.service.LocationService;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-09-12 18:22
 */
@RestController
@AllArgsConstructor
@BaseResponse
@Validated
@RequestMapping("/location")
@Api(tags = {"位置信息"})
@AccessControl(roles = RoleEnum.AD_OPERATOR)
public class LocationController {
    private final LocationService locationService;

    @GetMapping("/all_class_cities")
    @ApiOperation("获取城市等级信息")
    public List<ClassCitiesVO> allClassCities() {
        return locationService.allClassCities();
    }
}
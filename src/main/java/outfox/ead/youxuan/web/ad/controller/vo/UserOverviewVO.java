package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年02月08日 5:11 下午
 */
@Data
public class UserOverviewVO {
    @ApiModelProperty("昵称")
    private String nickname;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("公司全称")
    private String company;
    @ApiModelProperty("id")
    private Long userId;
    @ApiModelProperty("阶段")
    private Integer stage;
    @ApiModelProperty("任务中心权限")
    private Boolean taskPermission;
    @ApiModelProperty("橱窗是否开通")
    private Boolean isProductWindowOpen;
}

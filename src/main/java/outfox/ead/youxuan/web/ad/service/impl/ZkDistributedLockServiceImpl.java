package outfox.ead.youxuan.web.ad.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.locks.InterProcessLock;
import org.apache.curator.framework.recipes.locks.InterProcessMutex;
import org.apache.curator.framework.recipes.locks.InterProcessReadWriteLock;
import org.apache.zookeeper.KeeperException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.annotation.DistributedLock;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.web.ad.service.DistributedLockService;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 针对表【DistributedLock】的数据库操作Service实现
 * @createDate 2022-07-19 17:31:56
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ZkDistributedLockServiceImpl implements DistributedLockService {
    private final CuratorFramework client;

    @Value("${zookeeper.lock.path}")
    private String lockBasePath;

    private final ThreadLocal<Map<String, Pair<InterProcessLock, Integer>>> MUTEX_LOCK_MAP = new ThreadLocal<>();
    private final ThreadLocal<Map<String, Pair<InterProcessReadWriteLock, Integer>>> READ_WRITE_LOCK_MAP = new ThreadLocal<>();

    @Override
    public void mutexLock(String lockKey) {
        this.mutexLock(lockKey, -1, null);
    }

    @Override
    public void mutexLock(String lockKey, Integer time, TimeUnit timeUnit) {
        try {
            Map<String, Pair<InterProcessLock, Integer>> map = MUTEX_LOCK_MAP.get();
            if (Objects.isNull(map)) {
                map = new HashMap<>();
            }
            InterProcessLock lock;
            Pair<InterProcessLock, Integer> pair = map.get(lockKey);
            if (Objects.isNull(pair)) {
                lock = new InterProcessMutex(client, lockBasePath + "/" + lockKey);
            } else {
                lock = pair.getKey();
            }
            lock.acquire(time, timeUnit);
            pair = new Pair<>(lock, pair != null ? pair.getValue() + 1 : 1);
            map.put(lockKey, pair);
            MUTEX_LOCK_MAP.set(map);
        } catch (Exception e) {
            log.error("mutexLock error", e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "mutextLock Error");
        }
    }

    @Override
    public void unlock(String lockKey) {
        try {
            Map<String, Pair<InterProcessLock, Integer>> map = MUTEX_LOCK_MAP.get();
            assert map != null;
            Pair<InterProcessLock, Integer> pair = map.get(lockKey);
            assert pair != null;
            InterProcessLock lock = pair.getKey();
            lock.release();
            int count = pair.getValue() - 1;
            if (count > 0) {
                pair = new Pair<>(lock, count);
                map.put(lockKey, pair);
                MUTEX_LOCK_MAP.set(map);
            } else {
                map.remove(lockKey);
                // zk加锁后，会留下空的父节点，这里采取解锁时，尝试删除
                try {
                    if (client.getChildren().forPath(lockBasePath + "/" + lockKey).isEmpty()) {
                        client.delete()
                                .guaranteed()
                                .forPath(lockBasePath + "/" + lockKey);
                    }
                } catch (KeeperException.NotEmptyException e) {
                    log.debug("parent node is not empty,skip delete");
                }
                if (map.isEmpty()) {
                    MUTEX_LOCK_MAP.remove();
                }
            }
        } catch (Exception e) {
            log.error("unlock error", e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "unlock Error");
        }
    }

    @Override
    public void readWriteLock(String lockKey, Integer lockType) {
        this.readWriteLock(lockKey, -1, null, lockType);
    }

    @Override
    public void readWriteLock(String lockKey, Integer time, TimeUnit timeUnit, Integer lockType) {
        try {
            Map<String, Pair<InterProcessReadWriteLock, Integer>> map = READ_WRITE_LOCK_MAP.get();
            if (Objects.isNull(map)) {
                map = new HashMap<>();
            }
            InterProcessReadWriteLock lock;
            Pair<InterProcessReadWriteLock, Integer> pair = map.get(lockKey);
            if (Objects.isNull(pair)) {
                lock = new InterProcessReadWriteLock(client, lockBasePath + "/" + lockKey);
            } else {
                lock = pair.getKey();
            }
            if (lockType.equals(DistributedLock.READ_LOCK)) {
                lock.readLock().acquire();
            } else if (lockType.equals(DistributedLock.WRITE_LOCK)) {
                lock.writeLock().acquire();
            } else {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "没有该模式");
            }
            pair = new Pair<>(lock, pair != null ? pair.getValue() + 1 : 1);
            map.put(lockKey, pair);
            READ_WRITE_LOCK_MAP.set(map);
        } catch (Exception e) {
            log.error("readWriteLock error", e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "readWriteLock Error");
        }
    }

    @Override
    public void readWriteUnLock(String lockKey, Integer lockType) {
        try {
            Map<String, Pair<InterProcessReadWriteLock, Integer>> map = READ_WRITE_LOCK_MAP.get();
            assert map != null;
            Pair<InterProcessReadWriteLock, Integer> pair = map.get(lockKey);
            assert pair != null;
            InterProcessReadWriteLock lock = pair.getKey();

            if (lockType.equals(DistributedLock.READ_LOCK)) {
                lock.readLock().acquire();
            } else if (lockType.equals(DistributedLock.WRITE_LOCK)) {
                lock.writeLock().acquire();
            } else {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "没有该模式");
            }
            int count = pair.getValue() - 1;
            if (count > 0) {
                pair = new Pair<>(lock, count);
                map.put(lockKey, pair);
                READ_WRITE_LOCK_MAP.set(map);
            } else {
                map.remove(lockKey);
                if (map.isEmpty()) {
                    READ_WRITE_LOCK_MAP.remove();
                }
            }
        } catch (Exception e) {
            log.error("readWriteUnLock error", e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "readWriteUnLock Error");
        }
    }
}

package outfox.ead.youxuan.web.ad.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.rholder.retry.RetryException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.AdGroupService;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.concurrent.ExecutionException;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/25 9:36
 */
@RestController
@AllArgsConstructor
@BaseResponse
@Validated
@RequestMapping("/ad_group")
@Api(tags = {"推广组"})
@AccessControl(roles = RoleEnum.AD_OPERATOR)
public class AdGroupController {
    private final AdGroupService adGroupService;

    /**
     * 通过id查询推广组信息
     *
     * @param id 推广组id
     * @return 推广组详情
     */
    @ApiOperation("根据id获取详情用于修改")
    @GetMapping("/{id}")
    public AdGroupByIdVO getAdGroup(@PathVariable("id")
                                    @NotNull(message = "id不能为空") Long id) {
        return adGroupService.getAdGroupDetailById(id);
    }

    /**
     * 条件查询
     * 支持推广组名称（模糊查询）、推广组id（精确查询）、计划名称（模糊查询）、计划ID（精确查询)
     *
     * @param adGroupCriteriaQueryVO 查询条件
     * @return 推广组列表
     */
    @ApiOperation("分页列表查询")
    @GetMapping
    public PageVO<AdGroupListVO> getAdGroupList(@Valid AdGroupCriteriaQueryVO adGroupCriteriaQueryVO) {
        return adGroupService.page(adGroupCriteriaQueryVO);
    }

    /**
     * 保存/修改推广组信息
     *
     * @param adGroupSaveOrUpdateVO 推广组相关信息
     */
    @ApiOperation("保存修改推广组")
    @PostMapping
    public void saveOrUpdateAdGroup(@RequestBody @Valid AdGroupSaveOrUpdateVO adGroupSaveOrUpdateVO) throws ExecutionException, RetryException {
        adGroupService.saveOrUpdate(adGroupSaveOrUpdateVO);
    }

    @ApiOperation("批量修改状态")
    @PutMapping("/batch_update_status")
    public String batchUpdateStatus(@RequestBody @Valid BatchUpdateStatusVO batchUpdateStatusVO) {
        return adGroupService.batchUpdateStatusById(batchUpdateStatusVO.getIds(), batchUpdateStatusVO.getStatus());
    }

    @ApiOperation("修改测试直达状态")
    @PutMapping("/update_test_direct_status")
    public String updateTestDirectStatus(@RequestBody @Valid UpdateTestDirectStatusVO updateTestDirectStatusVO) {
        return adGroupService.updateTestDirectStatusById(updateTestDirectStatusVO.getAdGroupId(), updateTestDirectStatusVO.isTestDirect());
    }

    @GetMapping("/initial")
    @ApiOperation("初始化相关信息")
    public InitialVO initial(@Valid InitialRequestVO initialRequestVO) {
        return adGroupService.initialize(initialRequestVO.getStyleIds(),
                initialRequestVO.getAdPlanId(),
                initialRequestVO.getAdGroupId(),
                initialRequestVO.getMode(),
                initialRequestVO.getInteractionType());
    }


    /**
     * 校验推广计划名字是否重复
     *
     * @param name 推广计划名字
     */
    @GetMapping("/name_repeat")
    @ApiOperation("推广组名称重复接口")
    public void repeatAdGroupName(@NotBlank String name, @RequestParam(required = false) Long id, Long adPlanId) {
        if (adGroupService.nameRepeat(name, id, adPlanId)) {
            throw new CustomException(ResponseType.NAME_REPEATED_EXCEPTION, "名称重复，请修改");
        }
    }

    /**
     * 根据交互类型和广告位查询可用的推广组
     *
     * @param queryVO 查询条件
     * @return 可用的推广组列表
     */
    @PostMapping("/available")
    @ApiOperation("根据交互类型和广告位查询可用的推广组")
    public IPage<AdGroupSimpleVO> getAvailableAdGroups(@RequestBody @Valid AdGroupAvailableQueryVO queryVO) {
        return adGroupService.getAvailableAdGroups(queryVO.getInteractionType(), queryVO.getAdPositionIds(),
                queryVO.getAdGroupId(), queryVO.getCurrent(), queryVO.getSize());
    }
}

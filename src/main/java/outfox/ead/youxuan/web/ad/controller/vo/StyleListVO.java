package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: 李梦杰
 * @date: 2021/8/26/10:43
 * @description:
 */
@Data
public class StyleListVO {
    @ApiModelProperty("样式主键")
    private Long id;

    @ApiModelProperty("样式名称")
    private String name;

    @ApiModelProperty("广告位id")
    private Long adPositionId;

    @ApiModelProperty("广告位名称")
    private String adPositionName;

    @ApiModelProperty("媒体id")
    private Long mediaId;

    @ApiModelProperty("媒体名称")
    private String mediaName;

    @ApiModelProperty("状态,0-有效，1-暂停,2-已删除")
    private Integer status;

}

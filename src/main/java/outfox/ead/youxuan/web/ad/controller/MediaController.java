package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.entity.Media;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.MediaService;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/8/19/11:41
 */
@RestController
@BaseResponse
@AllArgsConstructor
@Api(tags = {"媒体"})
@Validated
@RequestMapping("/media")
@AccessControl(roles = RoleEnum.AD_OPERATOR)
public class MediaController {
    private final MediaService mediaService;

    @GetMapping("/{id}")
    public MediaResponseVO getMedia(@NotNull(message = "id不能为空") @PathVariable("id") Long id) {
        Media media = mediaService.getById(id);
        return new MediaResponseVO(media);
    }

    /**
     * 条件查询媒体
     *
     * @param media 媒体
     * @return List<MediaResponseVO>
     */
    @GetMapping
    public PageVO<MediaResponseVO> getMediaList(@Valid MediaCriteriaQueryVO media) {
        return mediaService.pageList(media);
    }

    /**
     * 插入/修改媒体
     *
     * @param media 媒体
     */
    @PostMapping
    public Long saveOrUpdateMedia(@Valid @RequestBody MediaSaveOrUpdateVO media) {
        return mediaService.saveOrUpdate(media);
    }

    /**
     * 批量修改媒体状态
     *
     * @param medias 媒体列表
     * @return 影响行数
     */
    @PutMapping("/batch_update_status")
    @AccessControl(roles = RoleEnum.ADMIN)
    public String batchUpdateStatus(@Valid @RequestBody BatchUpdateStatusVO medias) {
        return mediaService.batchUpdateStatusById(medias);

    }

    /**
     * 校验名字是否重复
     *
     * @param countByName 媒体名字,id
     * @return apiResponse
     */
    @GetMapping("/name_repeat")
    public void repeatMediaName(@Valid MediaCountByNameVO countByName) {
        mediaService.nameRepeat(countByName);
    }
}

package outfox.ead.youxuan.web.ad.service;

import com.baomidou.mybatisplus.extension.service.IService;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.entity.Role;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【Role】的数据库操作Service
 * @date 2022-01-27 11:33:40
 */
public interface RoleService extends IService<Role> {

    Collection<Role> listByUserId(Long userId);

    List<Role> listByRoleKeys(List<String> roleKeys);

    Role getByRoleKey(String roleKey);

    Boolean checkRole(Collection<Role> roles, RoleEnum roleEnum);

    Boolean checkRole(Long userId, RoleEnum roleEnum);

    Boolean checkRole(Collection<RoleEnum> roleEnums, String roleKey);
}

package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.UserRoleRelation;
import outfox.ead.youxuan.mapper.youxuan.UserRoleRelationMapper;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserRoleRelationService;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.ResponseType.ROLE_MUTUALLY_EXCLUSIVE;
import static outfox.ead.youxuan.constants.ResponseType.ROLE_REPEAT;

/**
 * <AUTHOR>
 * @description 针对表【UserRole】的数据库操作Service实现
 * @date 2022-01-27 18:09:38
 */
@Service
@AllArgsConstructor
public class UserRoleRelationServiceImpl extends ServiceImpl<UserRoleRelationMapper, UserRoleRelation>
        implements UserRoleRelationService {

    private final RoleService roleService;

    @Override
    public void logicDeleteByUserId(Long userId) {
        baseMapper.logicDeleteByUserId(userId);
    }

    @Override
    public List<UserRoleRelation> listByUserIdsAndRoles(Collection<Long> userIds, Collection<Role> roles) {
        if (userIds.isEmpty()) {
            return Lists.emptyList();
        }
        return baseMapper.listByUserIdsAndRoles(userIds, roles);
    }

    @Override
    public UserRoleRelation getByUserId(Long userId) {
        return baseMapper.getByUserId(userId);
    }

    @Override
    public List<UserRoleRelation> listByUserId(Long userId) {
        return baseMapper.listByUserid(userId);
    }

    @Override
    public void save(@Nonnull Long userId, @Nonnull String roleKey, boolean roleCanRepeat) {
        try {
            rolePreCheck(userId, roleKey);
        }catch (CustomException e) {
            if (roleCanRepeat && e.getCode() == ROLE_REPEAT.getCode()) {
                return;
            }else{
                throw e;
            }
        }
        Role role = roleService.getByRoleKey(roleKey);
        Long creator = SecurityUtil.getUserId() == null ? userId : SecurityUtil.getUserId();
        UserRoleRelation userRoleRelation = new UserRoleRelation(userId, role.getId());
        userRoleRelation.setCreator(creator);
        userRoleRelation.setModifier(creator);
        this.save(userRoleRelation);
    }

    /**
     * <ol>
     *     <li>角色不可重复注册</li>
     *     <li>KOL和BRAND_KOL为互斥角色，一个账户不能同时拥有</li>
     * </ol>
     * @param userId        -
     *
     * @param roleKey       角色
     */
    @Override
    public void rolePreCheck(@Nonnull Long userId, String roleKey) {
        Collection<Role> roles = roleService.listByUserId(userId);
        Set<String> roleKeySet = roles.stream().map(Role::getRoleKey).collect(Collectors.toSet());
        if (roleKeySet.contains(roleKey)) {
            throw new CustomException(ROLE_REPEAT,
                    String.format("该账号已注册%s账户，无法再注册%s，请更换账号或直接登录",
                            RoleEnum.roleKeyOf(roleKey).getName(),
                            RoleEnum.roleKeyOf(roleKey).getName()));
        } else if (roleKey.equals(RoleEnum.KOL.getRoleKey()) || roleKey.equals(RoleEnum.BRAND_KOL.getRoleKey())) {
            if (roleKeySet.contains(RoleEnum.KOL.getRoleKey()) || roleKeySet.contains(RoleEnum.BRAND_KOL.getRoleKey())) {
                throw new CustomException(ROLE_MUTUALLY_EXCLUSIVE,
                        String.format("该账号已注册%s账户，无法再注册%s，请更换账号或直接登录",
                                RoleEnum.roleKeyOf(roleKey).equals(RoleEnum.KOL) ? RoleEnum.BRAND_KOL.getName() : RoleEnum.KOL.getName(),
                                RoleEnum.roleKeyOf(roleKey).getName()));
            }
        }
    }

}





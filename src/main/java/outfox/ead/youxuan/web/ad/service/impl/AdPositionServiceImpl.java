package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.jsonwebtoken.lang.Collections;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.youxuan.constants.ResourceStatusEnum;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.mapper.youxuan.AdPositionMapper;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.Constants.AD_GROUP_DELIVERING;
import static outfox.ead.youxuan.constants.Constants.AD_GROUP_NOT_DELIVERING;
import static outfox.ead.youxuan.constants.ResourceStatusEnum.*;
import static outfox.ead.youxuan.constants.ResponseType.DISPLAY_TIMES_ILLEGAL_EXCEPTION;

/**
 * <AUTHOR>
 */

@Service
@RequiredArgsConstructor
public class AdPositionServiceImpl extends YouxuanServiceImpl<AdPositionMapper, AdPosition>
        implements AdPositionService {
    @Lazy
    private final MediaService mediaService;
    @Lazy
    private final StyleService styleService;
    @Lazy
    private final AdGroupService adGroupService;
    @Lazy
    private final AdResourceService adResourceService;
    @Lazy
    private final AdContentRelationService adContentRelationService;
    private final outfox.ead.youxuan.web.ad.controller.mapper.AdPositionMapper adPositionMapper;
    private final AdDeliveryTimeService adDeliveryTimeService;


    @Override
    public PageVO<AdPositionListVO> pageList(AdPositionCriteriaQueryVO adPositionVO) {
        LambdaQueryChainWrapper<AdPosition> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(Objects.nonNull(adPositionVO.getId()), AdPosition::getId, StringUtils.isNumeric(adPositionVO.getId()) ? adPositionVO.getId() : -1)
                .like(Objects.nonNull(adPositionVO.getName()), AdPosition::getName, adPositionVO.getName())
                .eq(Objects.nonNull(adPositionVO.getMediaId()), AdPosition::getMediaId, StringUtils.isNumeric(adPositionVO.getMediaId()) ? adPositionVO.getMediaId() : -1);
        if (Objects.nonNull(adPositionVO.getStatus())) {
            if (NOT_DELETED.getCode().equals(adPositionVO.getStatus())) {
                wrapper.ne(AdPosition::getStatus, DELETED.getCode());
            } else {
                wrapper.eq(AdPosition::getStatus, adPositionVO.getStatus());
            }
        }

        List<Long> medias = mediaService.list(new QueryWrapper<Media>()
                .lambda().like(Objects.nonNull(adPositionVO.getMediaName()), Media::getName, adPositionVO.getMediaName()))
                .stream().map(Media::getId).collect(Collectors.toList());
        if (medias.isEmpty()) {
            return new PageVO<>(adPositionVO.getCurrent(), adPositionVO.getSize(), Lists.emptyList(), 0L);
        }
        wrapper.in(AdPosition::getMediaId, medias);
        Page<AdPosition> page = wrapper.orderByDesc(AdPosition::getCreateTime).page(new Page<>(adPositionVO.getCurrent(), adPositionVO.getSize()));
        List<AdPosition> adPositions = page.getRecords();

        List<Long> mediaIds = adPositions.stream().map(AdPosition::getMediaId).collect(Collectors.toList());
        Map<Long, String> idToMediaNameMap = mediaService
                .listByIds(mediaIds)
                .stream()
                .collect(Collectors.toMap(Media::getId, Media::getName));

        List<AdPositionListVO> adPositionListDetails = adPositionMapper.doToAdPositionListVO(adPositions, idToMediaNameMap);
        return new PageVO<>(adPositionVO.getCurrent(), adPositionVO.getSize(), adPositionListDetails, page.getTotal());

    }

    @Override
    public Long saveOrUpdate(AdPositionSaveOrUpdateVO adPositionVO) {
        AdPosition adPosition = adPositionMapper.saveOrUpdateVoToDo(adPositionVO);
        saveOrUpdatePreCheck(adPosition);
        if (isNew(adPosition.getId())) {
            baseMapper.insert(adPosition);
        } else {
            baseMapper.updateById(adPosition);
        }
        return adPosition.getId();
    }

    private boolean isNew(Long id) {
        return id == null;
    }

    private void saveOrUpdatePreCheck(AdPosition adPosition) {
        nameRepeat(adPosition.getName(), adPosition.getId(), adPosition.getMediaId());
        Media media = mediaService.getById(adPosition.getMediaId());
        adPosition.setStatus(media.getStatus());
        if (media.getStatus().equals(DELETED.getCode())) {
            throw new CustomException(ResponseType.RESOURCES_DELETED_EXCEPTION);
        }
        if (isNew(adPosition.getId())) {
            adPosition.setStatus(media.getStatus());
        }
        if (isUpdate(adPosition.getId())) {
            updatePreCheck(adPosition);
        }
    }

    private void updatePreCheck(AdPosition adPosition) {
        displayTimeValidate(adPosition.getId(), adPosition.getDisplayTimes());
        // 在投
        if (isInDelivery(adPosition.getId())) {
            AdPosition oldPosition = this.getById(adPosition.getId());
            if (!adPosition.getPromotionType().equals(oldPosition.getPromotionType())) {
                throw new CustomException(ResponseType.AD_GROUP_PUT_IN_EXCEPTION);
            }
        }
    }


    /**
     * 投放中
     *
     * @param positionId 广告位id
     * @return true-投放中
     */
    private boolean isDelivering(Long positionId) {
        Set<Long> styleIds = styleService.listNotDeletedByAdPositionId(positionId).stream().map(Style::getId).collect(Collectors.toSet());
        if (styleIds.isEmpty()) {
            return false;
        }
        Set<Long> groupIds = adContentRelationService.listByStyleIds(styleIds)
                .stream()
                .map(AdContentRelation::getAdGroupId)
                .collect(Collectors.toSet());
        List<AdGroup> adGroups = adGroupService.listNotDeleteByIds(groupIds);
        Set<Long> adPlanIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toSet());
        if (adPlanIds.isEmpty()) {
            return false;
        }
        return adDeliveryTimeService.isDelivering(adPlanIds);
    }

    /**
     * 历史投放过
     *
     * @param positionId 广告位id
     * @return true-历史投放过
     */
    private boolean isDelivered(Long positionId) {
        Set<Long> styleIds = styleService.listNotDeletedByAdPositionId(positionId)
                .stream()
                .map(Style::getId)
                .collect(Collectors.toSet());
        Set<Long> groupIds = adContentRelationService.listByStyleIds(styleIds)
                .stream()
                .map(AdContentRelation::getAdGroupId)
                .collect(Collectors.toSet());
        List<AdGroup> adGroups = adGroupService.listNotDeleteByIds(groupIds);
        Set<Long> adPlanIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toSet());
        if (adPlanIds.isEmpty()) {
            return false;
        }
        return adDeliveryTimeService.isDelivered(adPlanIds);
    }

    /**
     * 在投 投放中+即将投放
     *
     * @param positionId 广告位id
     * @return true-在投
     */
    private boolean isInDelivery(Long positionId) {
        Set<Long> styleIds = styleService.listNotDeletedByAdPositionId(positionId).stream().map(Style::getId).collect(Collectors.toSet());
        Set<Long> groupIds = adContentRelationService.listByStyleIds(styleIds)
                .stream()
                .map(AdContentRelation::getAdGroupId)
                .collect(Collectors.toSet());
        List<AdGroup> adGroups = adGroupService.listNotDeleteByIds(groupIds);
        Set<Long> adPlanIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toSet());
        if (adPlanIds.isEmpty()) {
            return false;
        }
        return adDeliveryTimeService.isInDelivery(adPlanIds);
    }

    private boolean isUpdate(Long id) {
        return id != null;
    }


    @Override
    public List<AdPosition> listValid(Integer promotionTarget) {
        return baseMapper.listValid(promotionTarget);
    }

    @Override
    public void nameRepeat(String name, Long id, Long mediaId) {
        if (count(new QueryWrapper<AdPosition>().lambda().
                eq(StringUtils.isNotBlank(name), AdPosition::getName, name)
                .ne(Objects.nonNull(id), AdPosition::getId, id)
                .eq(Objects.nonNull(mediaId), AdPosition::getMediaId, mediaId)
        ) > 0) {
            throw new CustomException(ResponseType.NAME_REPEATED_EXCEPTION, "广告位名称重复，请修改");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrUpdateBatch(BatchUpdateStatusVO adPositionVO) {
        List<AdPosition> adPositions = this.listNotDeleteByIds(adPositionVO.getIds());
        updateBatchPreCheckFilterAdPositions(adPositions, adPositionVO.getStatus());
        if (adPositionVO.getStatus().equals(PAUSE.getCode())
                || adPositionVO.getStatus().equals(ResourceStatusEnum.DELETED.getCode())) {
            for (AdPosition adPosition : adPositions) {
                adPosition.setStatus(adPositionVO.getStatus());
            }
            List<Style> styles = styleService.listNotDeletedByAdPositionIds(adPositions
                    .stream().map(AdPosition::getId).collect(Collectors.toList()));
            for (Style style : styles) {
                if (!style.getStatus().equals(adPositionVO.getStatus())
                        || !style.getStatus().equals(DELETED.getCode())) {
                    style.setStatus(adPositionVO.getStatus());
                }
            }
            styleService.updateBatchById(styles);
        } else if (adPositionVO.getStatus().equals(VALID.getCode())) {
            List<Long> mediaIds = adPositions.stream().map(AdPosition::getMediaId).collect(Collectors.toList());
            Map<Long, Media> idToMediaMap = mediaService.listNotDeletedByIds(mediaIds).stream().collect(Collectors.toMap(Media::getId, Function.identity()));
            // 如果是暂停 不能修改为开启
            adPositions = adPositions.stream().filter(adPosition -> !idToMediaMap.get(adPosition.getMediaId()).getStatus().equals(PAUSE.getCode())).collect(Collectors.toList());
            for (AdPosition adPosition : adPositions) {
                adPosition.setStatus(idToMediaMap.get(adPosition.getMediaId()).getStatus());
            }

        }
        this.updateBatchById(adPositions);
        return batchUpdateMessage(adPositions, adPositionVO.getStatus());

    }

    private String batchUpdateMessage(List<AdPosition> adPositions, Integer status) {
        StringBuilder sb = new StringBuilder();
        sb.append(adPositions.size());
        if (status.equals(VALID.getCode())) {
            sb.append("个广告位状态已修改为开启");
        } else if (status.equals(DELETED.getCode())) {
            sb.append("个广告位状态及其样式状态已修改为删除");
        } else {
            sb.append("个广告位状态及其样式状态已修改为暂停");
        }
        return sb.toString();
    }

    @Override
    public List<AdPosition> listNotDeleteByIds(Collection<Long> adPositionId) {
        if (Collections.isEmpty(adPositionId)) {
            return Lists.emptyList();
        }
        return new LambdaQueryChainWrapper<>(baseMapper)
                .in(AdPosition::getId, adPositionId)
                .ne(AdPosition::getStatus, DELETED.getCode())
                .list();
    }

    @Override
    public List<AdPosition> listValid() {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .ne(AdPosition::getStatus, DELETED.getCode())
                .list();
    }

    @Override
    public List<AdPosition> listValidByMediaIds(Collection<Long> mediaIds) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .in(AdPosition::getMediaId, mediaIds)
                .ne(AdPosition::getStatus, DELETED.getCode())
                .list();
    }

    @Override
    public List<AdPosition> listLikeName(String adPositionName) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .like(AdPosition::getName, adPositionName)
                .list();
    }

    @Override
    public List<AdPosition> listByStyleIds(List<Long> styleIds) {
        return baseMapper.listByStyleIds(styleIds);
    }

    @Override
    public long countAllValid() {
        return baseMapper.countAllValid();
    }

    @Override
    public AdPositionByIdVO getVoById(Long id) {
        AdPositionByIdVO adPosition = adPositionMapper.doToById(getById(id));
        adPosition.setAdGroupDelivering(isInDelivery(id) ? AD_GROUP_DELIVERING : AD_GROUP_NOT_DELIVERING);
        return adPosition;
    }

    @Override
    public void displayTimeValidate(Long id, Integer displayTimes) {
        if (displayTimes < adResourceService.getPositionSoldDisplayTimes(id)) {
            throw new CustomException(DISPLAY_TIMES_ILLEGAL_EXCEPTION, "新轮播数小于在投轮播数，不可修改");
        }
    }

    @Override
    public AdPosition getNotDeleteById(Long adPositionId) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(AdPosition::getId, adPositionId)
                .ne(AdPosition::getStatus, DELETED.getCode())
                .one();
    }

    @Override
    public List<AdPosition> list(String name, Set<Long> mediaIds, List<Integer> resourceStatus) {
        return baseMapper.listNotDeletedPositionsLikeNameOrInMediaIds(name, mediaIds);
    }

    @Override
    public List<AdPosition> listNotDeleteByMediaId(Long mediaId) {
        return baseMapper.listNotDeleteByMediaId(mediaId);
    }

    private void updateBatchPreCheckFilterAdPositions(List<AdPosition> adPositions, Integer operation) {
        // 已删除的样式不能操作,操作和状态相同的不修改
        adPositions.removeIf(position -> position.getStatus().equals(DELETED.getCode()) || position.getStatus().equals(operation));
        if (operation.equals(ResourceStatusEnum.DELETED.getCode())) {
            // 历史投放过的不能删除
            adPositions.removeIf(position -> this.isDelivered(position.getId()));
            // 在投的不能删除
            adPositions.removeIf(position -> this.isInDelivery(position.getId()));
        } else if (operation.equals(PAUSE.getCode())) {
            // 在投的不能暂停
            adPositions.removeIf(position -> this.isInDelivery(position.getId()));
        } else {
            if (!operation.equals(VALID.getCode())) {
                throw new RuntimeException("状态错误");
            }
        }
    }
}





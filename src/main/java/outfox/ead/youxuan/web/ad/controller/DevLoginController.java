package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.web.ad.service.LoginService;

import javax.servlet.http.HttpServletResponse;

import static outfox.ead.youxuan.constants.PlatformEnum.YOUDAO_DICT;

/**
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/20 9:39
 */
@RestController
@RequestMapping("/login")
@AllArgsConstructor
@Slf4j
@Api(tags = {"开发生成登录token"})
@Profile("!online")
public class DevLoginController {
    private final LoginService loginService;

    @ApiOperation(value = "获取测试的Token")
    @GetMapping("/login_cookie_dev")
    public String loginByCookieDev(String username, RoleEnum roleEnum) {
        return loginService.getToken(username, roleEnum.getRoleKey());
    }

    @ApiOperation(value = "get test token by userid")
    @GetMapping("/userid_token")
    public String devLoginByUserId(Long userId, RoleEnum roleEnum) {
        return loginService.getToken(userId, roleEnum.getRoleKey());
    }

    @GetMapping("/login_uid")
    public Boolean loginByUid(String uid, HttpServletResponse response) {
        return loginService.loginByUid(uid, YOUDAO_DICT.getName(), response);
    }
}

package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.entity.UserRelation;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.controller.vo.UserRelationVO;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【UserRelation(用户单向绑定表)】的数据库操作Service
 * @date 2022-02-09 19:44:19
 */
public interface UserRelationService extends YouxuanService<UserRelation> {

    /**
     * 广告主绑定创作者账户
     * 目前优选是双向绑定
     *
     * @param kolUserId     -kol user id
     * @param sponsorUserId - sponsor user id
     */
    void bindUser(Long kolUserId, Long sponsorUserId);

    /**
     * 分页获取用户绑定关系
     *
     * @param kolUserId      -
     * @param current     -
     * @param size        -
     * @return 分页数据
     */
    PageVO<UserRelationVO> pageByKolUserId(Long kolUserId, Long current, Long size);

    /**
     * 获取有效UserRelation
     *
     * @param sponsorUserId
     * @return UserRelation
     */
    Boolean existsValidByUserIdAndBoundUserId(Long sponsorUserId, Long kolUserId);

    Boolean existValidByKolUserId(Long kolUserId);

    /**
     * 修改状态
     *
     * @param relationId 关系主键
     * @param status     状态
     */
    void updateStatusById(Long relationId, int status);

    void rebind(Long relationId, int bind);

    List<UserRelation> listValidByKolUserId(Long kolUserId);

    PageVO<UserRelationVO> pageBySponsorUserId(Long userId, Long current, Long size);

    Collection<UserRelation> listValidBySponsorId(Long sponsorId);
}

package outfox.ead.youxuan.web.ad.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.entity.AdMappingGroup;
import outfox.ead.youxuan.web.ad.controller.response.AdMappingPageResponse;
import outfox.ead.youxuan.web.ad.controller.vo.AdMappingCriteriaQueryVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdMappingGroupSaveVO;
import outfox.ead.youxuan.web.ad.controller.vo.UpdateStatusVO;
import outfox.ead.youxuan.web.ad.service.AdMappingGroupService;

import javax.validation.Valid;

@RestController
@AllArgsConstructor
@BaseResponse
@Validated
@RequestMapping("/ad_mapping")
@Api(tags = {"广告映射"})
@AccessControl(roles = {RoleEnum.ADMIN, RoleEnum.AD_OPERATOR})
public class AdMappingGroupController {
    private final AdMappingGroupService adMappingGroupService;

    @ApiOperation("分页列表查询")
    @GetMapping
    public IPage<AdMappingPageResponse> getAdGroupList(@Valid AdMappingCriteriaQueryVO adMappingCriteriaQueryVO) {
        return adMappingGroupService.page(adMappingCriteriaQueryVO);
    }

    @GetMapping("/{id}")
    public AdMappingGroup getById(@PathVariable Long id) {
        return adMappingGroupService.getValidById(id);
    }

    @ApiOperation("新建或修改映射")
    @PostMapping
    @LogRecord(
            success = "新建或修改映射,参数{{#vo}}",
            type = "conf",
            bizNo = "saveOrUpdateAdMapping"
    )
    public void saveOrUpdateAdMapping(@Valid AdMappingGroupSaveVO vo) {
        adMappingGroupService.saveOrUpdate(vo);
    }

    @ApiOperation("修改状态")
    @PutMapping
    @LogRecord(
            success = "修改状态,参数{{#vo}}",
            type = "conf",
            bizNo = "updateStatus"
    )
    public void updateStatus(@Valid UpdateStatusVO vo) {
        adMappingGroupService.updateStatus(vo);
    }

    @ApiOperation("名字是否重复")
    @GetMapping("/name-repeat")
    public boolean nameRepeat(@RequestParam(required = false) Long id, String name) {
        return adMappingGroupService.nameExists(id, name);
    }
}

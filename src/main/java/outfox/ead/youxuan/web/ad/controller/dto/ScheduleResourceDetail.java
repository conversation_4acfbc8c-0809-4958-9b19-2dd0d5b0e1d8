package outfox.ead.youxuan.web.ad.controller.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/10 11:41
 */
@Data
@EqualsAndHashCode
public class ScheduleResourceDetail {
    @ApiModelProperty("广告位Id")
    private Long adPositionId;
    @ApiModelProperty("日期")
    private LocalDate date;
    @ApiModelProperty("已售 CPM")
    @EqualsAndHashCode.Exclude
    private Long sale;
    /**
     * 购买的轮播数
     */
    @EqualsAndHashCode.Exclude
    private Integer weight;
    @EqualsAndHashCode.Exclude
    private Integer status;

    public ScheduleResourceDetail(ResourceDetail resourceDetail) {
        this.adPositionId = resourceDetail.getAdPositionId();
        this.date = resourceDetail.getDateTime().toLocalDate();
        this.sale = resourceDetail.getSale();
        this.weight = resourceDetail.getWeight();
        this.sale = resourceDetail.getSale();
        this.status = resourceDetail.getStatus();
    }
}

package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2025-06-06 18:03
 **/
@Data
public class AdSensitivityUpdateVO {

    @NotNull(message = "灵敏度配置ID不能为空")
    @ApiModelProperty(value = "灵敏度配置ID", required = true)
    private Long id;

    @ApiModelProperty("灵敏度配置-摇动加速度")
    private Integer shakeSpeed;

    @ApiModelProperty("灵敏度配置-滑动角度")
    private Integer slideAngle;

    @ApiModelProperty("灵敏度配置-摇动角度/扭转角度")
    private Integer rotationAngle;
}
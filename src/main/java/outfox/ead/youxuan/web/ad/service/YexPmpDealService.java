package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.entity.YexPmpDeal;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface YexPmpDealService extends YouxuanService<YexPmpDeal> {
    /**
     * 通过订单id查询有效的deal
     *
     * @param dealId 订单id
     * @return 订单列表
     */
    List<YexPmpDeal> listValidByDealId(String dealId);

    /**
     * 通过订单名称删除订单
     *
     * @param dealName 订单名称
     */
    void deleteByDealName(String dealName);

}

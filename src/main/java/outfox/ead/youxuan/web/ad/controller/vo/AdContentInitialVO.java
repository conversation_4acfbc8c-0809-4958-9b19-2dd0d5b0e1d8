package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.web.ad.controller.dto.CreativeContent;

import java.util.HashSet;
import java.util.Set;
import java.util.TreeSet;

/**
 * 用于初始化创意内容所需要的资源
 *
 * <AUTHOR> <EMAIL>
 * @date 2021/9/5 21:00
 */
@Data
public class AdContentInitialVO {
    private Integer id;
    @ApiModelProperty("创意内容类型 0-信息流 1-开屏 2-插屏 3-焦点图 4-激励视频 5-横幅 6-自定义")
    private Integer type;
    @ApiModelProperty("创意内容")
    private Set<CreativeContent> textCreativeContents = new TreeSet<>();
    private Set<CreativeContent> imageCreativeContents = new TreeSet<>();
    private Set<CreativeContent> videoCreativeContents = new TreeSet<>();
    @ApiModelProperty("展示时长")
    private Set<Integer> showTimeList = new HashSet<>();
    private Integer showTime;
}

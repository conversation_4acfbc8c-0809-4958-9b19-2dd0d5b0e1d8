package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/10 17:14
 */
@Data
public class AdPlanExcludeCityVO {
    private Long adPlanId;
    @NotNull(message = "billingType不能为null")
    @Min(value = 0, message = "计费模式类型错误")
    @Max(value = 1, message = "计费模式类型错误")
    @ApiModelProperty("计费模式 0-CPT 1-CPM")
    private Integer billingType;

    @ApiModelProperty("投放方式 0-标准投放 1-PD 2-PDB")
    @NotNull(message = "deliveryType不能为null")
    @Min(value = 0, message = "投放方式错误")
    @Max(value = 2, message = "投放方式错误")
    private Integer deliveryType;

    @NotNull(message = "dateContinuous不能为null")
    @Min(value = 0, message = "日期连续类型错误")
    @Max(value = 1, message = "日期连续类型错误")
    @ApiModelProperty("日期是否连续 0-连续 1-不连续")
    private Integer dateContinuous;

    @ApiModelProperty("投放时间")
    private List<String> adDeliveryDate;

    @ApiModelProperty("投放开始日期 连续日期只需要存储开始和结束时间")
    private LocalDate adOpenDate;

    @ApiModelProperty("投放结束时间")
    private LocalDate adCloseDate;

    @ApiModelProperty("投放时段 0-不限 1-指定时段")
    @NotNull
    private Integer timeOrientation;

    @ApiModelProperty("投放时间")
    private List<List<Integer>> timeDest;
}

package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/6 15:48
 */
@Data
public class AdPromotionPageVO<T> extends PageVO<T> {
    @ApiModelProperty("最终修改时间")
    private LocalDateTime lastRevisionDate;

    public AdPromotionPageVO(Long current, Long size, List<T> records, Long total, LocalDateTime lastRevisionDate) {
        super(current, size, records, total);
        this.lastRevisionDate = lastRevisionDate;
    }
}

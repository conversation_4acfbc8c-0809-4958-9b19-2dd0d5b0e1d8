package outfox.ead.youxuan.web.ad.controller.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021年12月02日 5:42 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PromotionResource {
    @ApiModelProperty("推广计划id")
    private Long id;
    @ApiModelProperty("推广计划名称")
    private String name;
    @ApiModelProperty("推广组源列表")
    private List<PromotionResource> adGroupList = new ArrayList<>();
}

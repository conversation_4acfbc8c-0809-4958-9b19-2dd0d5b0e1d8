package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.UserDetail;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.mapper.UserDetailMapper;
import outfox.ead.youxuan.web.ad.controller.vo.QualificationInfoVO;
import outfox.ead.youxuan.web.ad.controller.vo.UserDetailSaveVO;
import outfox.ead.youxuan.web.ad.controller.vo.UserDetailVO;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserDetailService;
import outfox.ead.youxuan.web.kol.service.AppAccountService;
import outfox.ead.youxuan.web.kol.service.DictService;

import javax.validation.Valid;
import java.util.Objects;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.ResponseType.INVALID_PARAMETERS;

/**
 * <AUTHOR>
 * @date 2022年02月09日 7:32 下午
 */
@RestController
@AllArgsConstructor
@Validated
@BaseResponse
@Slf4j
@Api(tags = "用户详情接口")
@RequestMapping("/userDetail")
public class UserDetailController {
    private final UserDetailService userDetailService;
    private final UserDetailMapper userDetailMapper;
    private final DictService dictService;
    private final AppAccountService appAccountService;
    private final RoleService roleService;

    @PostMapping("avatar")
    @ApiOperation("上传用户头像")
    public void uploadAvatar(String imageUrl) {
        userDetailService.uploadAvatar(imageUrl, SecurityUtil.getCurrentRole());
    }

    @GetMapping("currentUser")
    @ApiOperation("获取当前用户用户详情(只有广告主和机构创作者才会返回资质认证的字段)")
    public UserDetailVO getUserDetail() {
        Long userId = SecurityUtil.getUserId();
        UserDetailVO userDetailVO = null;
        // 如果用户在词典端开通橱窗权限 stage并未同步，所以查之前重新计算一下stage
        if (Objects.nonNull(SecurityUtil.getCurrentRole())) {
            userDetailService.calStageAndUpdate(userId, SecurityUtil.getCurrentRole());
            userDetailVO = userDetailMapper.do2Vo(userDetailService.getByUserId(userId, SecurityUtil.getCurrentRole()));
        }
        if (Objects.isNull(userDetailVO)) {
            userDetailVO = new UserDetailVO();
        }
        userDetailVO.setIsBindDict(appAccountService.isBindDict(userId));
        userDetailVO.setIsProductWindowOpen(dictService.isOpenProductWindow(userId));
        userDetailVO.setRoleKeys(SecurityUtil.getLoginUser().getRoles().stream().map(Role::getRoleKey).collect(Collectors.toList()));
        userDetailVO.setLoginUser(SecurityUtil.getLoginUser());
        return userDetailVO;
    }

    @PostMapping
    @ApiOperation("保存/更新用户详情")
    public void saveOrUpdate(@Valid UserDetailSaveVO userDetailSaveVO) {
        UserDetail userDetail = userDetailMapper.saveVo2Do(userDetailSaveVO);
        userDetail.setUserId(SecurityUtil.getUserId());
        dictService.sendCompanyDataByUserId(SecurityUtil.getUserId(), userDetail.getCompanyName(), SecurityUtil.getCurrentRole());
        userDetailService.insertOrUpdate(userDetail, SecurityUtil.getCurrentRole());
    }


    private static final Integer TASK = 0;
    private static final Integer PRODUCT_WINDOW = 1;
    private static final Integer POST_TASK = 2;

    @PostMapping("openPromotionPermission")
    @ApiOperation("开通推广权限")
    @AccessControl(roles = {RoleEnum.BRAND_KOL, RoleEnum.KOL})
    public void promotionPermission(@ApiParam(value = "0-指派中心  1-商品橱窗 2-投稿任务", required = true)
                                    @RequestParam Integer key) {
        Long userId = SecurityUtil.getUserId();
        if (key.equals(TASK)) {
            userDetailService.openTaskPermission(userId, SecurityUtil.getCurrentRole());
        } else if (key.equals(PRODUCT_WINDOW)
                && appAccountService.isBindDict(userId)
                && dictService.productWindowQualify(userId).getQualify()) {
            userDetailService.openProductWindow(userId, SecurityUtil.getCurrentRole());
        } else if (key.equals(POST_TASK)
                && appAccountService.isBindDict(userId)
                && dictService.productWindowQualify(userId).getQualify()){
            userDetailService.openPostTaskPermission(userId, SecurityUtil.getCurrentRole());
        }else{
            throw new CustomException(INVALID_PARAMETERS, "参数错误");
        }
    }

    @GetMapping("qualification")
    @ApiOperation("获取资质认证联系方式")
    @AccessControl(roles = {RoleEnum.BRAND_KOL, RoleEnum.SPONSOR})
    public QualificationInfoVO listQualificationContact() {
        return userDetailService.listQualificationContact(SecurityUtil.getUserId(), SecurityUtil.getCurrentRole());
    }
}

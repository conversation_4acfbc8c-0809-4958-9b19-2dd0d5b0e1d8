package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: 李梦杰
 * @date: 2021/8/23/18:29
 * @description:
 */
@Data
public class AdPositionCriteriaQueryVO {
    @ApiModelProperty(value = "当前页面", required = true)
    @NotNull(message = "当前页面不能为空")
    private Long current;
    @ApiModelProperty(value = "页面大小", required = true)
    @NotNull(message = "页面大小不能为空")
    private Long size;
    @ApiModelProperty(value = "广告位id")
    private String id;
    @ApiModelProperty(value = "媒体id")
    private String mediaId;
    @ApiModelProperty(value = "广告位名字")
    private String name;
    @ApiModelProperty(value = "媒体名字")
    private String mediaName;
    private Integer status;
}

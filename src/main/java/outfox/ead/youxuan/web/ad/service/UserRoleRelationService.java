package outfox.ead.youxuan.web.ad.service;

import com.baomidou.mybatisplus.extension.service.IService;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.UserRoleRelation;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【UserRole】的数据库操作Service
 * @date 2022-01-27 18:09:38
 */
public interface UserRoleRelationService extends IService<UserRoleRelation> {
    /**
     * 逻辑删除
     *
     * @param userId -
     */
    void logicDeleteByUserId(Long userId);

    /**
     * 通过用户Id列表批量查询
     *
     * @param userIds
     * @param kolRoles
     * @return
     */
    List<UserRoleRelation> listByUserIdsAndRoles(Collection<Long> userIds, Collection<Role> kolRoles);

    /**
     * 通过用户Id查询有效的记录
     *
     * @param userId
     * @return
     */
    UserRoleRelation getByUserId(Long userId);

    List<UserRoleRelation> listByUserId(Long userId);

    /**
     * 添加角色
     * @param userId -
     * @param roleKey -
     * @param roleCanRepeat 角色重复不抛出异常
     */
    void save(@Nonnull Long userId, @Nonnull String roleKey, boolean roleCanRepeat);

    /**
     * 新增角色校验
     * @param userId -
     * @param roleKey -
     */
    void rolePreCheck(@Nonnull Long userId, String roleKey);
}

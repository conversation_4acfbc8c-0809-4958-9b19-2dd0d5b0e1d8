package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.Media;
import outfox.ead.youxuan.web.ad.controller.vo.MediaCountByNameVO;
import outfox.ead.youxuan.web.ad.controller.vo.MediaCriteriaQueryVO;
import outfox.ead.youxuan.web.ad.controller.vo.MediaResponseVO;
import outfox.ead.youxuan.web.ad.controller.vo.MediaSaveOrUpdateVO;

/**
 * <AUTHOR>
 * @date 2021/9/17/10:54
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface MediaMapper {

    @Mapping(target = "name", expression = "java(vo.getName().trim())")
    Media saveOrUpdateVoToDo(MediaSaveOrUpdateVO vo);

    MediaCountByNameVO doToCountByName(MediaSaveOrUpdateVO mediaSaveOrUpdateVO);

    MediaCriteriaQueryVO doToMediaCriteriaQueryVO(Media media);

    MediaResponseVO doToMediaResponse(Media media);
}

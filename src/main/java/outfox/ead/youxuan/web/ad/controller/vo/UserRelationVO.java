package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年02月10日 11:33 上午
 */
@Data
public class UserRelationVO {
    private Long id;

    @ApiModelProperty("账户id")
    private Long userId;

    @ApiModelProperty("被绑定账户id")
    private Long boundUserId;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("状态 0-正常，1-暂停，2-删除")
    private Integer status;
}

package outfox.ead.youxuan.web.ad.service.impl;

import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.CustomCrowdPackRelation;
import outfox.ead.youxuan.mapper.youxuan.CustomCrowdPackRelationMapper;
import outfox.ead.youxuan.web.ad.service.CustomCrowdPackRelationService;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 针对表【CustomCrowdPackRelation(自定义人群包关联表)】的数据库操作Service实现
 * @createDate 2023-08-30 11:34:31
 */
@Service
public class CustomCrowdPackRelationServiceImpl extends YouxuanServiceImpl<CustomCrowdPackRelationMapper, CustomCrowdPackRelation>
        implements CustomCrowdPackRelationService {

    @Override
    public void saveOrUpdate(Long adGroupId, Collection<Long> crowdPackIds) {
        this.lambdaUpdate().eq(CustomCrowdPackRelation::getAdGroupId, adGroupId).remove();
        Optional.ofNullable(crowdPackIds).ifPresent(ids -> {
            List<CustomCrowdPackRelation> list = new ArrayList<>();
            for (Long crowdPackId : ids) {
                CustomCrowdPackRelation customCrowdPackRelation = new CustomCrowdPackRelation();
                customCrowdPackRelation.setAdGroupId(adGroupId);
                customCrowdPackRelation.setCustomCrowdPackId(crowdPackId);
                list.add(customCrowdPackRelation);
            }
            this.saveBatch(list);
        });
    }

    @Override
    public boolean existValidRelation(Long id) {
        return baseMapper.existValidRelation(id);
    }
}
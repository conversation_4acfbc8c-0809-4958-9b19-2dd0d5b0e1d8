package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.entity.BookingInfo;
import outfox.ead.youxuan.web.ad.service.BookingInfoService;

import javax.validation.Valid;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/10/8 14:18
 */
@BaseResponse
@RestController
@AllArgsConstructor
@Validated
@Api(tags = "预约咨询")
@RequestMapping("/booking")
public class BookingController {
    private final BookingInfoService bookingInfoService;

    @PostMapping
    @ApiOperation("预约咨询")
    public void booking(@Valid @RequestBody BookingInfo bookingInfo) {
        bookingInfoService.save(bookingInfo);
    }
}

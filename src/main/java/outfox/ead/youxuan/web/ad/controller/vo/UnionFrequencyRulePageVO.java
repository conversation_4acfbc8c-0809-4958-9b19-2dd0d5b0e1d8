package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class UnionFrequencyRulePageVO {
    @ApiModelProperty("规则id")
    private Long id;

    @ApiModelProperty("规则名称")
    private String name;

    @ApiModelProperty("关联的广告计划ids")
    private List<Long> adPlanIdList;

    @ApiModelProperty("联合频控状态")
    private Integer status;

    @ApiModelProperty("频控方式 0-不频控 1-按天频控 2-按周频控 3-按投放周期频控")
    private Integer frequencyType;

    @ApiModelProperty("频控次数")
    private Integer frequencyLimit;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("更新人")
    private String modifier;

    @ApiModelProperty("失效时间")
    private LocalDateTime endTime;


}
package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.entity.Area;
import outfox.ead.youxuan.util.AreaUtil;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/22 19:04
 */
@RestController
@BaseResponse
@AllArgsConstructor
@Validated
@Api(tags = "城市接口")
@RequestMapping("/area")
public class AreaController {
    /**
     * 获取地域信息列表
     *
     * @param level 层级
     * @return 地域信息列表
     */
    @GetMapping
    @ApiOperation(value = "获取城市层级列表")
    public List<Area> getAreaList(@RequestParam
                                  @Max(value = 1, message = "没有该层级，请选择国际层级或国内层级")
                                  @Min(value = 0, message = "没有该层级，请选择国际层级或国内层级")
                                  @ApiParam(value = "0-海外 1-国内") Integer level,
                                  @ApiParam(value = "true-包含直辖市下的区 false-不包含") boolean all) {
        return AreaUtil.getAllCities(level, all);
    }
}

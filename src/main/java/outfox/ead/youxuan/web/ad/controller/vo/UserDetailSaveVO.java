package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.core.validator.Phone;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2022年02月09日 3:44 下午
 */
@Data
public class UserDetailSaveVO {
    @ApiModelProperty("账户昵称")
    @Size(max = 20, message = "昵称最多20个字符")
    @NotNull(message = "账户昵称不能为空")
    private String nickname;
    @ApiModelProperty("账户头像")
    private String avatar;
    @ApiModelProperty("联系人姓名")
    @NotNull(message = "联系人姓名不能为空")
    @Size(max = 20, message = "联系人姓名最多20个字符")
    private String name;
    @ApiModelProperty("联系电话")
    @Phone
    private String phone;
    @ApiModelProperty("联系邮箱")
    @Email(regexp = "^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$"
            , message = "联系邮箱格式不正确，请重新输入")
    private String email;
    @ApiModelProperty("公司名称")
    @Size(max = 50, message = "公司名称不得超过50个字")
    private String companyName;
    @ApiModelProperty("行业信息")
    private Integer industry;
}

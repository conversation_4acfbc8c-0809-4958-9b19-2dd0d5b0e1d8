package outfox.ead.youxuan.web.ad.controller.vo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.Set;

@Data
public class AdMappingGroupSaveVO {
    private Long id;
    @NotNull
    @Length(max = 50, message = "映射名称最多不能超过50个字符")
    private String name;
    @NotNull
    private Long adGroupId;
    @NotNull
    private Set<Long> sourceStyleIds;
    @NotNull
    private Set<Long> mappingStyleIds;
}

package outfox.ead.youxuan.web.ad.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiOperation;
import outfox.ead.youxuan.entity.UnionFrequencyRule;
import outfox.ead.youxuan.web.ad.controller.vo.*;

import java.util.List;
import java.util.Set;

public interface UnionFrequencyRuleService extends YouxuanService<UnionFrequencyRule> {

    /**
     * 分页查询联合频控规则列表
     *
     * @param queryVO
     * @return
     */
    IPage<UnionFrequencyRulePageVO> getRuleList(UnionFrequencyRuleCriteriaQueryVO queryVO);

    /**
     * 联合频控规则详情
     *
     * @param id
     * @return
     */
    UnionFrequencyRuleByIdVO getRuleDetailById(Long id);

    /**
     * 保存或更新联合频控规则
     *
     * @param saveOrUpdateVo
     * @param userId
     * @return
     */
    Long saveOrUpdate(UnionFrequencyRuleSaveOrUpdateVO saveOrUpdateVo, Long userId);

    /**
     * 逻辑删除联合频控规则
     *
     * @param id
     */
    void deleteById(Long id);

    /**
     * 查询联合频控DO
     *
     * @return 联合频控DO
     */
    List<UnionFrequencyRule> listValid();


    void batchUpdateUnionRulesAndAdPlans (Set<UnionFrequencyRule> unionFrequencyRules, Set<Long> adPlanIds);

}

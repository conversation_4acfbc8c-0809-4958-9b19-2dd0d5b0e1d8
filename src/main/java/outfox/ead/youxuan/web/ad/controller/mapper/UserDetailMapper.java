package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import outfox.ead.youxuan.entity.UserDetail;
import outfox.ead.youxuan.web.ad.controller.bo.BrandKolTemplate;
import outfox.ead.youxuan.web.ad.controller.bo.KolTemplate;
import outfox.ead.youxuan.web.ad.controller.bo.SponsorTemplate;
import outfox.ead.youxuan.web.ad.controller.vo.UserDetailSaveVO;
import outfox.ead.youxuan.web.ad.controller.vo.UserDetailVO;
import outfox.ead.youxuan.web.ad.controller.vo.UserOverviewVO;

@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface UserDetailMapper {
    UserOverviewVO userDetail2UserOverview(UserDetail userDetail);

    UserDetailVO do2Vo(UserDetail byUserId);

    UserDetail saveVo2Do(UserDetailSaveVO userDetailSaveVO);

    UserDetail sponsorTemplate2DO(SponsorTemplate sponsorTemplates);

    UserDetail kolTemplate2DO(KolTemplate kolTemplate);

    UserDetail brandKolTemplate2DO(BrandKolTemplate brandKolTemplate);
}

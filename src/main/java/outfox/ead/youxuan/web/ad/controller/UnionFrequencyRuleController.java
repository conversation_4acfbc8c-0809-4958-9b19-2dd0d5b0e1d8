package outfox.ead.youxuan.web.ad.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.UnionFrequencyRuleService;

import javax.validation.Valid;

@RestController
@AllArgsConstructor
@BaseResponse
@Validated
@RequestMapping("/union_frequency_rule")
@Api(tags = "联合频控")
@AccessControl(roles = {RoleEnum.ADMIN, RoleEnum.AD_OPERATOR})
@Slf4j
public class UnionFrequencyRuleController {

    private UnionFrequencyRuleService unionFrequencyRuleService;

    @GetMapping("/rule_list")
    @ApiOperation("分页查询联合频控规则列表")
    public IPage<UnionFrequencyRulePageVO> getRuleList(UnionFrequencyRuleCriteriaQueryVO queryVO) {
        return unionFrequencyRuleService.getRuleList(queryVO);

    }

    @ApiOperation("联合频控规则详情")
    @GetMapping("/detail")
    public UnionFrequencyRuleByIdVO getRuleDetailById(Long id) {
        return unionFrequencyRuleService.getRuleDetailById(id);
    }

    @ApiOperation("保存或更新联合频控规则")
    @PostMapping("/upsert")
    @LogRecord(
            success = "新建或修改联合频控,参数{{#vo}}",
            type = "unionFrequencyRule",
            bizNo = "saveOrUpdate"
    )
    public Long saveOrUpdate(@RequestBody @Valid UnionFrequencyRuleSaveOrUpdateVO vo) {
        Long userId = SecurityUtil.getUserId();
        return unionFrequencyRuleService.saveOrUpdate(vo, userId);
    }

    @ApiOperation("逻辑删除联合频控规则")
    @PutMapping("/delete")
    @LogRecord(
            success = "新建或修改联合频控,参数{{#id}}",
            type = "unionFrequencyRule",
            bizNo = "deleteRuleByIdList"
    )
    public void deleteRuleById(Long id) {
        unionFrequencyRuleService.deleteById(id);
    }


}

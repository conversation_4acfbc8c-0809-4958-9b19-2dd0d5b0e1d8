package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.util.AreaUtil;
import outfox.ead.youxuan.web.ad.controller.dto.CreativeContent;
import outfox.ead.youxuan.web.ad.controller.dto.ScheduleResourceDetail;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.Constants.*;
import static outfox.ead.youxuan.constants.PromotionStatusEnum.DELIVER_PAUSE;
import static outfox.ead.youxuan.constants.ResourceStatusEnum.DELETED;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class AdPositionScheduleServiceImpl implements AdPositionScheduleService {
    private final AdPlanService adPlanService;
    private final AdGroupService adGroupService;
    private final AdContentService adContentService;
    private final AdContentRelationService adContentRelationService;

    private final MediaService mediaService;
    private final AdPositionService adPositionService;
    private final StyleService styleService;

    private final AdResourceService adResourceService;
    private final AdDeliveryTimeService adDeliveryTimeService;
    private final CustomerService customerService;

    @Override
    public PageVO<AdPositionScheduleVO> getAdPositionSchedule(AdPositionScheduleCriteriaQueryVO queryVO) {
        List<Long> adPositionIds;
        String adPositionIdString = queryVO.getAdPositionIds();
        if (StringUtils.isNotBlank(adPositionIdString)) {
            adPositionIds = Arrays.stream(adPositionIdString.split(COMMA)).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        } else {
            adPositionIds = null;
        }

        Page<AdPosition> page = new LambdaQueryChainWrapper<>(
                adPositionService.getBaseMapper())
                .in(Objects.nonNull(adPositionIds), AdPosition::getId, adPositionIds)
                .ne(AdPosition::getStatus, DELETED.getCode())
                .page(new Page<>(queryVO.getCurrent(), queryVO.getSize()));
        List<AdPosition> adPositions = page.getRecords();
        // 广告位每日最大已售出
        Collection<ScheduleResourceDetail> scheduleResourceDetails = adResourceService.getScheduleResourceDetails(queryVO.getStartDate(),
                queryVO.getEndDate(),
                queryVO.getBillingType(),
                adPositions.stream().map(AdPosition::getId).collect(Collectors.toSet()));

        List<AdPositionScheduleVO> adPositionScheduleVos = new ArrayList<>();
        if (queryVO.getBillingType().equals(CPT)) {
            populateCpt(queryVO.getStartDate(), queryVO.getEndDate(), adPositions, scheduleResourceDetails, adPositionScheduleVos);
        } else {
            populateCpm(queryVO.getStartDate(), queryVO.getEndDate(), adPositions, scheduleResourceDetails, adPositionScheduleVos);
        }

        return new PageVO<>(page.getCurrent(), page.getSize(), adPositionScheduleVos, page.getTotal());
    }

    private void populateCpm(LocalDate startDate,
                             LocalDate endDate,
                             List<AdPosition> adPositions,
                             Collection<ScheduleResourceDetail> scheduleResourceDetails,
                             List<AdPositionScheduleVO> adPositionScheduleVos) {
        Map<Long, String> mediaId2Name = mediaService.listByIds(adPositions.stream().map(AdPosition::getMediaId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(Media::getId, Media::getName));
        for (AdPosition adPosition : adPositions) {
            AdPositionScheduleVO adPositionScheduleVO = new AdPositionScheduleVO();
            adPositionScheduleVO.setAdPositionId(adPosition.getId());
            adPositionScheduleVO.setAdPositionName(mediaId2Name.get(adPosition.getMediaId()) + "_" + adPosition.getName());

            List<ScheduleDetail> scheduleDetailList = new ArrayList<>();
            LocalDate start = startDate;
            while (!start.isAfter(endDate)) {
                ScheduleDetail scheduleDetail = new ScheduleDetail();
                scheduleDetail.setStatus(RESOURCE_SPARE);
                scheduleDetail.setExactDate(start);
                scheduleDetail.setSale(0.0000);

                ScheduleResourceDetail detail = null;
                for (ScheduleResourceDetail scheduleResourceDetail : scheduleResourceDetails) {
                    if (scheduleResourceDetail.getAdPositionId().equals(adPosition.getId())
                            && scheduleResourceDetail.getDate().equals(start)) {
                        if (detail == null) {
                            detail = scheduleResourceDetail;
                        } else {
                            detail.setSale(detail.getSale() + scheduleResourceDetail.getSale());
                        }
                    }
                }

                if (Objects.nonNull(detail)) {
                    scheduleDetail.setSale(BigDecimal.valueOf((float) detail.getSale() / 10000)
                            .setScale(4, RoundingMode.HALF_UP).doubleValue());
                    if (detail.getStatus().equals(DELIVER_PAUSE.getCode())) {
                        scheduleDetail.setStatus(RESOURCE_PAUSE);
                    } else {
                        scheduleDetail.setStatus(RESOURCE_PARTIALLY_SOLD);
                    }
                }
                scheduleDetailList.add(scheduleDetail);
                start = start.plusDays(1);
            }
            adPositionScheduleVO.setDetailList(scheduleDetailList);
            adPositionScheduleVos.add(adPositionScheduleVO);
        }
    }

    private void populateCpt(LocalDate startDate,
                             LocalDate endDate,
                             List<AdPosition> adPositions,
                             Collection<ScheduleResourceDetail> scheduleResourceDetails,
                             List<AdPositionScheduleVO> adPositionScheduleVos) {
        int cities = AreaUtil.getSubCityIds(LEVEL_INLAND).size() + AreaUtil.getSubCityIds(LEVEL_INTERNATION).size();
        Map<Long, String> mediaId2Name = mediaService.listByIds(adPositions.stream().map(AdPosition::getMediaId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(Media::getId, Media::getName));
        for (AdPosition adPosition : adPositions) {
            LocalDate start = startDate;

            AdPositionScheduleVO adPositionScheduleVO = new AdPositionScheduleVO();
            adPositionScheduleVO.setAdPositionId(adPosition.getId());
            adPositionScheduleVO.setAdPositionName(mediaId2Name.get(adPosition.getMediaId()) + "_" + adPosition.getName());
            Integer sumDisplayTimes = adPosition.getDisplayTimes() * 24 * cities;

            List<ScheduleDetail> scheduleDetailList = new ArrayList<>();
            while (!start.isAfter(endDate)) {
                ScheduleDetail scheduleDetail = new ScheduleDetail();
                scheduleDetail.setDisplayTimes(adPosition.getDisplayTimes());
                scheduleDetail.setDisplayStock(sumDisplayTimes);
                scheduleDetail.setStatus(RESOURCE_SPARE);
                scheduleDetail.setExactDate(start);

                ScheduleResourceDetail detail = null;
                for (ScheduleResourceDetail scheduleResourceDetail : scheduleResourceDetails) {
                    if (scheduleResourceDetail.getAdPositionId().equals(adPosition.getId())
                            && scheduleResourceDetail.getDate().equals(start)) {
                        detail = scheduleResourceDetail;
                        break;
                    }
                }

                if (Objects.nonNull(detail)) {
                    int stock = sumDisplayTimes - detail.getWeight();
                    scheduleDetail.setDisplayStock(stock);
                    if (detail.getStatus().equals(DELIVER_PAUSE.getCode())) {
                        scheduleDetail.setStatus(RESOURCE_PAUSE);
                    } else if (stock == 0) {
                        scheduleDetail.setStatus(RESOURCE_SOLD_ALL);
                    } else {
                        scheduleDetail.setStatus(RESOURCE_PARTIALLY_SOLD);
                    }
                }

                double ratio = 0;
                int sale = sumDisplayTimes - scheduleDetail.getDisplayStock();
                if (sale > 0) {
                    ratio = Math.max(0.0001,
                            BigDecimal.valueOf((float) sale / sumDisplayTimes)
                                    .setScale(4, RoundingMode.HALF_UP).doubleValue());
                }
                scheduleDetail.setDisplaySalesRatio(ratio);
                scheduleDetailList.add(scheduleDetail);
                start = start.plusDays(1);
            }
            adPositionScheduleVO.setDetailList(scheduleDetailList);
            adPositionScheduleVos.add(adPositionScheduleVO);
        }
    }


    @Override
    public List<ResourceSalesDetailsVO> getAdResourceSalesDetails(LocalDate date, Long adPositionId, Integer billingType) {
        // 求出 adPlan adGroup adContents customer
        List<AdDeliveryTime> adDeliveryTimeList = adDeliveryTimeService
                .listByTimeInterval(LocalDateTime.of(date, LocalTime.MIN), LocalDateTime.of(date, LocalTime.of(23, 59, 59)));
        Set<Long> adPlanIds = adDeliveryTimeList.stream().map(AdDeliveryTime::getAdPlanId).collect(Collectors.toSet());
        List<AdPlan> adPlans = adPlanService.listNotDeleteByIdsAndBillingType(adPlanIds, billingType);
        adPlanIds = adPlans.stream().map(AdPlan::getId).collect(Collectors.toSet());
        List<AdGroup> adGroups = adGroupService.listNoteDeleteByAdPlanIds(adPlanIds);
        List<Long> adGroupIds = adGroups.stream().map(AdGroup::getId).collect(Collectors.toList());
        List<AdContent> adContents = adContentService.listByAdGroupIds(adGroupIds);
        List<Long> customerIds = adPlans.stream().map(AdPlan::getCustomerId).collect(Collectors.toList());
        Map<Long, Customer> idToCustomer = customerService.listByIds(customerIds).stream().collect(Collectors.toMap(Customer::getId, Function.identity()));

        List<AdContentRelation> adContentRelations = adContentRelationService.listValidByAdGroupIds(adGroupIds);

        // 通过positionId 求出 media adPosition style
        List<Style> styles = styleService.listNotDeletedByAdPositionId(adPositionId);
        Map<Long, Style> idToStyle = styles.stream().collect(Collectors.toMap(Style::getId, Function.identity()));
        AdPosition adPosition = adPositionService.getNotDeleteById(adPositionId);
        Media media = mediaService.getById(adPosition.getMediaId());

        adContentRelations = adContentRelations.stream().filter(a -> styles.stream().map(Style::getId).collect(Collectors.toSet()).contains(a.getStyleId())).collect(Collectors.toList());
        Map<Long, AdGroup> idToAdGroup = adGroups.stream().collect(Collectors.toMap(AdGroup::getId, Function.identity()));
        Map<Long, AdPlan> idToAdPlan = adPlans.stream().collect(Collectors.toMap(AdPlan::getId, Function.identity()));

        // 组合数据
        return getResourceSalesDetailsVos(adContents,
                idToCustomer,
                adContentRelations,
                idToStyle,
                adPosition,
                media,
                idToAdGroup,
                idToAdPlan);
    }

    /**
     * 组装数据
     */
    private List<ResourceSalesDetailsVO> getResourceSalesDetailsVos(List<AdContent> adContents,
                                                                    Map<Long, Customer> idToCustomer,
                                                                    List<AdContentRelation> adContentRelations,
                                                                    Map<Long, Style> idToStyle,
                                                                    AdPosition adPosition,
                                                                    Media media,
                                                                    Map<Long, AdGroup> idToAdGroup,
                                                                    Map<Long, AdPlan> idToAdPlan) {
        List<ResourceSalesDetailsVO> resourceSalesDetailsVos = new ArrayList<>();
        for (AdContentRelation relation : adContentRelations) {
            Style style = idToStyle.get(relation.getStyleId());
            AdGroup adGroup = idToAdGroup.get(relation.getAdGroupId());
            if (!ObjectUtils.allNotNull(style, adGroup)) {
                continue;
            }
            AdPlan adPlan = idToAdPlan.get(adGroup.getAdPlanId());
            ResourceSalesDetailsVO resourceSalesDetailsVO = new ResourceSalesDetailsVO();
            resourceSalesDetailsVO.setAdPlanName(adPlan.getName());
            resourceSalesDetailsVO.setAdDeliverySlot(adPlan.getAdDeliveryInterval());
            resourceSalesDetailsVO.setCustomerName(idToCustomer.get(adPlan.getCustomerId()).getName());
            resourceSalesDetailsVO.setAdGroupName(adGroup.getName());
            resourceSalesDetailsVO.setTime(adGroup.getCreateTime());
            resourceSalesDetailsVO.setDisplayWeight(relation.getDisplayWeight());
            resourceSalesDetailsVO.setStatus(adGroup.getStatus());
            List<CreativeContent> contents = adContents
                    .stream()
                    .filter(c -> c.getAdGroupId().equals(relation.getAdGroupId()))
                    .flatMap(adContent -> adContent.getImageCreativeContents().stream())
                    .collect(Collectors.toList());
            if (!contents.isEmpty()) {
                Collections.sort(contents);
                resourceSalesDetailsVO.setPreviewImage(contents.get(0).getProperties().iterator().next().getUrl());
            }
            String deliveryPosition = String.format("%s_%s_%s", media.getName(), adPosition.getName(), style.getName());
            resourceSalesDetailsVO.setDeliveryPosition(deliveryPosition);
            resourceSalesDetailsVos.add(resourceSalesDetailsVO);
        }
        resourceSalesDetailsVos.sort(Comparator.comparing(ResourceSalesDetailsVO::getTime).reversed());
        return resourceSalesDetailsVos;
    }
}

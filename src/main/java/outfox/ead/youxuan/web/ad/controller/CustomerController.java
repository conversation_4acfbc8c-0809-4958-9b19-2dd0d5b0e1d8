package outfox.ead.youxuan.web.ad.controller;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.entity.Customer;
import outfox.ead.youxuan.web.ad.service.CustomerService;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/1 19:53
 */
@AllArgsConstructor
@BaseResponse
@RequestMapping("/customer")
@Validated
@RestController
@AccessControl(roles = RoleEnum.AD_OPERATOR)
@Api(tags = "客户接口")
public class CustomerController {
    private final CustomerService customerService;

    /**
     * 获得客户列表
     *
     * @return id->name映射列表
     */
    @GetMapping
    public List<Customer> getCustomerList() {
        return customerService.listIdToName();
    }

    /**
     * 客户名称重复判断接口
     *
     * @param name 客户名称
     */
    @GetMapping("/name_repeat")
    public void repeatCustomerName(String name) {
        if (customerService.nameRepeat(name)) {
            throw new RuntimeException("客户名称重复");
        }
    }
}

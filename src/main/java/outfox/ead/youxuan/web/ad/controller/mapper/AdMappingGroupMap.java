package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.AdGroup;
import outfox.ead.youxuan.entity.AdMappingGroup;
import outfox.ead.youxuan.entity.User;
import outfox.ead.youxuan.web.ad.controller.response.AdMappingPageResponse;
import outfox.ead.youxuan.web.ad.controller.vo.AdMappingGroupSaveVO;
import outfox.ead.youxuan.web.ad.controller.vo.UpdateStatusVO;

import java.util.Map;

@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface AdMappingGroupMap {

    @Mapping(expression = "java(id2AdGroup.get(adMapping.getAdGroupId()).getName())", target = "adGroupName")
    @Mapping(expression = "java(id2AdGroup.get(adMapping.getAdGroupId()).getStatus())", target = "adGroupStatus")
    @Mapping(expression = "java(id2Customer.get(adPlanId2CustomerId.get(id2AdGroup.get(adMapping.getAdGroupId()).getAdPlanId())))", target = "customerName")
    @Mapping(expression = "java(String.valueOf(adPlanId2CustomerId.get(id2AdGroup.get(adMapping.getAdGroupId()).getAdPlanId())))", target = "customer")
    @Mapping(expression = "java(id2User.get(adMapping.getCreator()).getUsername())", target = "creatorName")
    @Mapping(expression = "java(id2User.get(adMapping.getModifier()).getUsername())", target = "modifier")
    @Mapping(expression = "java(id2SourceStyleString.get(adMapping.getId()))", target = "sourceStyle")
    @Mapping(expression = "java(id2MappingStyleString.get(adMapping.getId()))", target = "mappingStyle")
    AdMappingPageResponse vo2AdMappingPageResponse(AdMappingGroup adMapping, Map<Long, AdGroup> id2AdGroup, Map<Long, User> id2User, Map<Long, Long> adPlanId2CustomerId, Map<Long, String> id2Customer, Map<Long, String> id2SourceStyleString, Map<Long, String> id2MappingStyleString);

    AdMappingGroup saveVO2DO(AdMappingGroupSaveVO adMappingSaveVO);

    AdMappingGroup updateStatusVO2DO(UpdateStatusVO vo);
}

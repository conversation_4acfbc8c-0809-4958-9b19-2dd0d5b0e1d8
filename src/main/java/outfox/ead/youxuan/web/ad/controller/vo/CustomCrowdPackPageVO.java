package outfox.ead.youxuan.web.ad.controller.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CustomCrowdPackPageVO {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty(value = "0-有效 1-校验中 2-校验失败 3-生成中 4-已失效 5-已删除")
    private Integer status;

    @ApiModelProperty(value = "设备号类型")
    private Integer deviceIdType;

    @ApiModelProperty(value = "创建人")
    private Long creator;

    @ApiModelProperty(value = "失效时间")
    private LocalDateTime expiryTime;

    @ApiModelProperty(value = "有效数量")
    private Integer validCount;

    @ApiModelProperty(value = "校验失败原因")
    private String errMsg;

    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private Long modifier;

    private LocalDateTime lastModTime;
}

package outfox.ead.youxuan.web.ad.controller.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 创意内容具体的样式元素
 *
 * <AUTHOR> <EMAIL>
 * @date 2021/8/26 18:15
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StyleElement {
    @ApiModelProperty("样式元素名称,用于开发者和发布系统前端展示")
    private String name;
    @ApiModelProperty("样式元素key，用于解析")
    private String elementKey;
    @ApiModelProperty("图片比例")
    @Pattern(regexp = "((0|[1-9][0-9]*):(0|[1-9][0-9]*))|自定义")
    private String ratio;
    @ApiModelProperty("文字样式元素元素长度")
    private Integer length;
    @ApiModelProperty("图片样式元素高度")
    private Integer height;
    @ApiModelProperty("图片样式元素宽度")
    private Integer width;
    @ApiModelProperty("媒体格式 mp4,jpg等等")
    private String mimeType;
    @ApiModelProperty("样式元素类型，1:文字;2:图片;3:视频")
    private Integer type;
    @ApiModelProperty("文本内容")
    private String text;
    @ApiModelProperty("图片或视频链接")
    private String url;
    @ApiModelProperty("图片或视频链接数组")
    private List<String> urls;
}

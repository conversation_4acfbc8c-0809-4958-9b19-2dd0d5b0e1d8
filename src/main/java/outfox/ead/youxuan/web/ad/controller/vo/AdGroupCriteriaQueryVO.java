package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.joda.time.LocalDate;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/23 11:47
 */
@Data
public class AdGroupCriteriaQueryVO {
    @ApiModelProperty("推广组名称（模糊查询）、推广组id（精确查询）、计划名称（模糊查询）、计划ID（精确查询)")
    private String name;
    @ApiModelProperty("推广组id")
    private String id;
    @ApiModelProperty("推广组id列表")
    private List<String> ids;
    @ApiModelProperty("推广计划名称")
    private String adPlanName;
    @ApiModelProperty("推广计划id")
    private String adPlanId;
    @Min(value = 0, message = "状态类型错误")
    @Max(value = 6, message = "状态类型错误")
    @ApiModelProperty("状态 0-投放中、1-投放结束、2-即将开启、3-暂停、4-未暂停、5-已删除、6-未删除")
    private Integer status;
    @NotNull
    @ApiModelProperty("页数")
    private Long current;
    @NotNull
    @ApiModelProperty("条数")
    private Long size;
    @ApiModelProperty("开始时间")
    private LocalDate adOpenDate;
    @ApiModelProperty("结束时间")
    private LocalDate adCloseDate;
}

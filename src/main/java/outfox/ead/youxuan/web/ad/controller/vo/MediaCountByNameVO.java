package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/9/3/11:47
 */
@Data
public class MediaCountByNameVO {
    @NotBlank(message = "名字不能为空")
    @ApiModelProperty(value = "媒体名称")
    private String name;
    @ApiModelProperty(value = "媒体id")
    private Long id;
}

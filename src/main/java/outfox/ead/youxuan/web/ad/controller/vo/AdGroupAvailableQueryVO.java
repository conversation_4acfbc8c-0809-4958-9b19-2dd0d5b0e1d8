package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 根据交互类型和广告位查询可用推广组的查询VO
 */
@Data
public class AdGroupAvailableQueryVO {
    @ApiModelProperty("交互类型, 1：摇一摇，2：滑动互动，5：扭一扭, 6:三合一")
    @NotNull(message = "交互类型不能为空")
    private Integer interactionType;

    @ApiModelProperty("广告位ID列表")
    @NotEmpty(message = "广告位ID列表不能为空")
    private List<Long> adPositionIds;

    @ApiModelProperty("推广组ID")
    private String adGroupId;

    @ApiModelProperty(value = "当前页码", example = "1")
    private Long current = 1L;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Long size = 10L;
} 
package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.AdvertisingByKeywordsRule;
import outfox.ead.youxuan.mapper.youxuan.AdvertisingByKeywordsRuleMapper;
import outfox.ead.youxuan.web.ad.service.AdvertisingByKeywordsRuleService;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class AdvertisingByKeywordsRuleServiceImpl
        extends YouxuanServiceImpl<AdvertisingByKeywordsRuleMapper, AdvertisingByKeywordsRule>
        implements AdvertisingByKeywordsRuleService {

    @Override
    public AdvertisingByKeywordsRule getByAdGroupId(long adGroupId) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(AdvertisingByKeywordsRule::getAdGroupId, adGroupId)
                .one();
    }
}

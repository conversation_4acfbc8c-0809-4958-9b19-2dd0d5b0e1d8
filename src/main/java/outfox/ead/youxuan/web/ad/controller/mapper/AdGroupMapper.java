package outfox.ead.youxuan.web.ad.controller.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import outfox.ead.youxuan.entity.AdGroup;
import outfox.ead.youxuan.entity.AdPlan;
import outfox.ead.youxuan.web.ad.controller.vo.AdGroupByIdVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdGroupListVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdGroupSaveOrUpdateVO;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/9/15 19:53
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface AdGroupMapper {
    @Mapping(source = "adGroup.status", target = "status")
    @Mapping(source = "adGroup.status", target = "deliverStatus")
    @Mapping(source = "adGroup.id", target = "id")
    @Mapping(source = "adGroup.name", target = "name")
    @Mapping(source = "adGroup.ageOrientationV2", target = "ageOrientation")
    AdGroupByIdVO doToAdGroupByIdVO(AdGroup adGroup, AdPlan adPlan);

    @Mapping(source = "adGroupVO.ageOrientation", target = "ageOrientationV2")
    AdGroup saveVO2DO(AdGroupSaveOrUpdateVO adGroupVO);


    @Mapping(source = "adGroup.id", target = "id")
    @Mapping(source = "adGroup.name", target = "name")
    @Mapping(source = "adGroup.status", target = "status")
    @Mapping(source = "adPlan.id", target = "adPlanId")
    @Mapping(source = "adPlan.name", target = "adPlanName")
    @Mapping(target = "adDeliverySlot", expression = "java(adPlan.getAdDeliveryInterval())")
    AdGroupListVO DO2ListVO(AdGroup adGroup, AdPlan adPlan, List<String> deliveryPosition, String creative);

    AdGroupSaveOrUpdateVO detailVo2SaveVO(AdGroupByIdVO sourceAdGroup);
}

package outfox.ead.youxuan.web.ad.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.rholder.retry.RetryException;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import in.zapr.druid.druidry.client.DruidClient;
import in.zapr.druid.druidry.query.DruidQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.boot.logging.LogLevel;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.ip.location.mapper.ClassCityMapper;
import outfox.ead.youxuan.constants.PromotionStatusEnum;
import outfox.ead.youxuan.constants.ResourceStatusEnum;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.core.thirdParty.druid.DruidBrandStatQuery;
import outfox.ead.youxuan.core.thirdParty.druid.StatementTimeSeriesStatistic;
import outfox.ead.youxuan.core.thirdParty.druid.StatementsGroupByStatics;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.mapper.youxuan.AdGroupMapper;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.bo.DictVideoPost;
import outfox.ead.youxuan.web.ad.controller.dto.CreativeContent;
import outfox.ead.youxuan.web.ad.controller.dto.CreativeContentProperty;
import outfox.ead.youxuan.web.ad.controller.dto.StyleElement;
import outfox.ead.youxuan.web.ad.controller.mapper.AdContentMapper;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.*;
import outfox.ead.youxuan.web.kol.service.DictService;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.Constants.*;
import static outfox.ead.youxuan.constants.ElementKey.*;
import static outfox.ead.youxuan.constants.PromotionStatusEnum.*;
import static outfox.ead.youxuan.constants.ResponseType.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AdGroupServiceImpl extends YouxuanServiceImpl<AdGroupMapper, AdGroup> implements AdGroupService {
    private final AdPlanService adPlanService;
    private final AdContentService adContentService;
    private final AdContentRelationService adContentRelationService;
    private final AdvertisingByKeywordsRuleService advertisingByKeywordsRuleService;

    @Lazy
    private final AdResourceService adResourceService;
    private final AdDeliveryTimeService adDeliveryTimeService;

    private final MediaService mediaService;
    private final AdPositionService adPositionService;
    private final StyleService styleService;

    private final DruidClient druidClient;

    private final YexPmpDealService yexPmpDealService;
    private final YexDspService yexDspService;
    private final SdkSlotServiceImpl sdkSlotService;

    private final DictService dictService;
    private final CustomCrowdPackRelationService customCrowdPackRelationService;
    private final CustomCrowdPackService customCrowdPackService;
    private final AdContentMapper adContentMapper;

    private final outfox.ead.youxuan.web.ad.controller.mapper.AdGroupMapper adGroupMapper;

    @Override
    public AdGroupByIdVO getAdGroupDetailById(Long id) {
        AdGroup adGroup = this.getById(id);
        if (Objects.isNull(adGroup)) {
            return null;
        }
        Long adPlanId = adGroup.getAdPlanId();
        AdPlan adPlan = adPlanService.getById(adPlanId);
        if (!SecurityUtil.isAdmin()) {
            if (!(adGroup.getCreator().equals(SecurityUtil.getUserId()) && adGroup.getRoleId().equals(SecurityUtil.getCurrentRole().getId()))) {
                throw new CustomException(ACCESS_DENIED);
            }
            if (!(adPlan.getCreator().equals(SecurityUtil.getUserId()) && adPlan.getRoleId().equals(SecurityUtil.getCurrentRole().getId()))) {
                throw new CustomException(ACCESS_DENIED);
            }
        }
        AdGroupByIdVO adGroupVO = adGroupMapper.doToAdGroupByIdVO(adGroup, adPlan);
        List<Long> styleIds = styleService
                .listValidByIds(
                        adContentRelationService
                                .listByAdGroupId(id)
                                .stream()
                                .map(AdContentRelation::getStyleId)
                                .collect(Collectors.toList())
                )
                .stream().map(Style::getId).collect(Collectors.toList());
        adGroupVO.setCarouselInfo(adResourceService.initialCarouselInfo(styleIds, adGroup.getAdPlanId(), id));
        adGroupVO.setCreativeContentContents(adResourceService.initialCreativeContents(styleIds, id, adGroup.getInteractionType()));
        AdvertisingByKeywordsRule advertisingByKeywordsRule = advertisingByKeywordsRuleService.getByAdGroupId(adGroupVO.getId());
        if (Objects.nonNull(advertisingByKeywordsRule)) {
            adGroupVO.setAdvertisingKeywordList(advertisingByKeywordsRule.getKeywordList());
            adGroupVO.setAudioUrls(advertisingByKeywordsRule.getAudioUrls());
        }
        if (adGroup.getStatus().equals(DELIVER_PAUSE.getCode())) {
            adGroupVO.setDeliverStatus(adPlanService.getDeliverStatus(adPlanId));
        }
        if (isAdvertisingToDsp(adPlan)) {
            adGroupVO.setMsg(getMsg(adGroupVO));
        }
        adGroupVO.setCrowdPackIds(
                customCrowdPackService.listByIds(adGroup.getCrowdPackIds())
                        .stream().filter(s -> s.getStatus().equals(CustomCrowdPack.VALID))
                        .map(CustomCrowdPack::getId)
                        .collect(Collectors.toSet()));
        return adGroupVO;
    }

    private String getMsg(AdGroupByIdVO adGroupVO) {
        if (yexDspService.countValidById(adGroupVO.getDspId()) == 0) {
            adGroupVO.setDspId(null);
            return "已选DSP已失效，请重新选择";
        }
        return null;
    }

    @Override
    public PageVO<AdGroupListVO> page(AdGroupCriteriaQueryVO criteriaQuery) {
        LambdaQueryChainWrapper<AdGroup> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .like(Objects.nonNull(criteriaQuery.getName()), AdGroup::getName, criteriaQuery.getName())
                .eq(Objects.nonNull(criteriaQuery.getId()), AdGroup::getId, StringUtils.isNumeric(criteriaQuery.getId()) ? criteriaQuery.getId() : -1)
                .in(Objects.nonNull(criteriaQuery.getIds()), AdGroup::getId, criteriaQuery.getIds())
                .eq(Objects.nonNull(criteriaQuery.getAdPlanId()), AdGroup::getAdPlanId, StringUtils.isNumeric(criteriaQuery.getAdPlanId()) ? criteriaQuery.getAdPlanId() : -1)
                .eq(!SecurityUtil.isAdmin(), AdGroup::getCreator, SecurityUtil.getUserId())
                .eq(!SecurityUtil.isAdmin(), AdGroup::getRoleId, SecurityUtil.getCurrentRole().getId())
                .orderByDesc(AdGroup::getCreateTime);
        if (Objects.nonNull(criteriaQuery.getAdPlanName())) {
            List<Long> adPlanIds = adPlanService.listByName(criteriaQuery.getAdPlanName()).stream().map(AdPlan::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(adPlanIds)) {
                return PageVO.emptyPage(criteriaQuery.getCurrent(), criteriaQuery.getSize());
            }
            wrapper.in(AdGroup::getAdPlanId, adPlanIds);
        }
        Integer status = criteriaQuery.getStatus();
        if (Objects.nonNull(status)) {
            if (status.equals(PromotionStatusEnum.NOT_DELETED.getCode())) {
                wrapper.ne(AdGroup::getStatus, PromotionStatusEnum.DELETED.getCode());
            } else if (status.equals(PromotionStatusEnum.DELIVER_NOT_PAUSE.getCode())) {
                wrapper.ne(AdGroup::getStatus, PromotionStatusEnum.DELIVER_PAUSE.getCode());
            } else {
                wrapper.eq(AdGroup::getStatus, status);
            }
        }
        Page<AdGroup> page = wrapper.page(new Page<>(criteriaQuery.getCurrent(), criteriaQuery.getSize()));
        List<AdGroup> adGroups = page.getRecords();
        List<Long> adGroupIds = adGroups.stream().map(AdGroup::getId).collect(Collectors.toList());
        List<AdContentRelation> adContentRelations = adContentRelationService.listValidByAdGroupIds(adGroupIds);

        List<AdPlan> adPlans = new ArrayList<>(adPlanService.listByIds(adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toList())));
        List<AdContent> adContents = new ArrayList<>(adContentService.listByAdGroupIds(adGroupIds));
        List<Style> styles = new ArrayList<>(styleService.listByIds(adContentRelations.stream().map(AdContentRelation::getStyleId).collect(Collectors.toList())));
        List<AdPosition> adPositions = new ArrayList<>(adPositionService.listByIds(styles.stream().map(Style::getAdPositionId).collect(Collectors.toList())));
        List<Media> medias = new ArrayList<>(mediaService.listByIds(adPositions.stream().map(AdPosition::getMediaId).collect(Collectors.toList())));

        List<AdGroupListVO> adGroupListVos = getAdGroupListVO(medias, adPositions, styles, adPlans, adGroups, adContents, adContentRelations);

        populateDruidData(criteriaQuery, adGroupIds, adGroupListVos);
        return new AdPromotionPageVO<>(criteriaQuery.getCurrent(),
                criteriaQuery.getSize(),
                adGroupListVos,
                page.getTotal(),
                LocalDateTime.now()
        );
    }

    private void populateDruidData(AdGroupCriteriaQueryVO criteriaQuery, List<Long> adGroupIds, List<AdGroupListVO> adGroupListVos) {
        if (Objects.nonNull(criteriaQuery.getAdOpenDate()) && Objects.nonNull(criteriaQuery.getAdCloseDate())) {
            DruidQuery druidQuery = DruidBrandStatQuery.queryBuilder(adGroupIds,
                    criteriaQuery.getAdOpenDate().toDateTime(org.joda.time.LocalTime.MIDNIGHT),
                    criteriaQuery.getAdCloseDate().toDateTime(org.joda.time.LocalTime.MIDNIGHT).plusDays(1),
                    DIMENSION_AD_GROUP);
            try {
                Map<Long, StatementTimeSeriesStatistic.Event> map = druidClient.query(druidQuery, StatementsGroupByStatics.class).stream()
                        .map(StatementsGroupByStatics::getEvent)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(StatementTimeSeriesStatistic.Event::getAdGroupId, Function.identity()));
                for (AdGroupListVO adGroupListVO : adGroupListVos) {
                    StatementTimeSeriesStatistic.Event event = map.get(adGroupListVO.getId());
                    if (Objects.nonNull(event)) {
                        adGroupListVO.setClickNum(event.getClick());
                        adGroupListVO.setImprNum(event.getImpr());
                        adGroupListVO.setClickRate(event.getClickRate());
                    } else {
                        adGroupListVO.setClickNum(0L);
                        adGroupListVO.setImprNum(0L);
                        adGroupListVO.setClickRate(0.0);
                    }
                }
            } catch (Exception e) {
                log.warn("Populate druid data error", e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchUpdateStatusById(List<Long> adGroupIds, Integer operation) {
        List<AdGroup> adGroups = this.listByIds(adGroupIds);
        updateBatchPreCheck(adGroups, operation);
        if (operation.equals(PromotionStatusEnum.DELIVER_PAUSE.getCode()) || operation.equals(PromotionStatusEnum.DELETED.getCode())) {
            for (AdGroup adGroup : adGroups) {
                adGroup.setStatus(operation);
            }
        } else if (operation.equals(PromotionStatusEnum.DELIVER_NOT_PAUSE.getCode())) {
            List<Long> adPlanIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toList());
            Map<Long, AdPlan> idToAdPlan = adPlanService.listNotDeleteByIds(adPlanIds).stream().collect(Collectors.toMap(AdPlan::getId, Function.identity()));
            // 推广计划如果是暂停 不能修改为开启
            adGroups = adGroups.stream().filter(adGroup -> !idToAdPlan.get(adGroup.getAdPlanId()).getStatus().equals(PromotionStatusEnum.DELIVER_PAUSE.getCode())).collect(Collectors.toList());
            for (AdGroup adGroup : adGroups) {
                if (adGroup.getStatus().equals(PromotionStatusEnum.DELIVER_PAUSE.getCode())) {
                    adGroup.setStatus(idToAdPlan.get(adGroup.getAdPlanId()).getStatus());
                }
            }
        }
        this.updateBatchById(adGroups);
        return batchUpdateMessage(adGroups, operation);
    }

    /**
     * 批量修改返回前端信息
     */
    private String batchUpdateMessage(List<AdGroup> adGroups, Integer status) {
        StringBuilder sb = new StringBuilder();
        sb.append(adGroups.size());
        if (status.equals(PromotionStatusEnum.DELIVER_NOT_PAUSE.getCode())) {
            sb.append("个推广组状态已修改为开启");
        } else if (status.equals(PromotionStatusEnum.DELETED.getCode())) {
            sb.append("个推广组状态已修改为删除");
        } else {
            sb.append("个推广组状态已修改为暂停");
        }
        return sb.toString();
    }

    /**
     * 已删除的推广组不能操作
     */
    private void updateBatchPreCheck(List<AdGroup> adGroups, Integer operation) {
        batchCheckGroupOperatePermissions(adGroups);
        // 投放结束和已删除不能修改状态
        adGroups.removeIf(adGroup -> adGroup.getStatus().equals(DELIVER_FINISH.getCode())
                || adGroup.getStatus().equals(DELETED.getCode())
                || adGroup.getStatus().equals(operation));
        if (operation.equals(PromotionStatusEnum.DELETED.getCode())) {
            // 只能删除未投放过的推广计划
            Set<Long> deliveredPlanIds = adDeliveryTimeService.listDeliveredByAdPlanIds(adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toList()));
            adGroups.removeIf(adGroup -> deliveredPlanIds.contains(adGroup.getAdPlanId()));
        } else if (operation.equals(PromotionStatusEnum.DELIVER_NOT_PAUSE.getCode())) {
            // 开启只修改处于暂停状态的推广计划
            adGroups.removeIf(adGroup -> !adGroup.getStatus().equals(PromotionStatusEnum.DELIVER_PAUSE.getCode()));
        } else {
            if (!operation.equals(PromotionStatusEnum.DELIVER_PAUSE.getCode())) {
                throw new CustomException(INVALID_PARAMETERS, "资源状态操作错误");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, timeout = 200)
    @Override
    public void saveOrUpdate(AdGroupSaveOrUpdateVO adGroupVO) throws ExecutionException, RetryException {
        List<Long> styleIds = adGroupVO.getRelations().stream().map(AdContentRelation::getStyleId).collect(Collectors.toList());
        List<Style> validStyles = styleService.listValidByIds(styleIds);

        saveOrUpdatePreCheck(adGroupVO, styleIds, validStyles);
        preDealFullClickClassCities(adGroupVO);

        AdGroup adGroup = saveOrUpdateAdGroup(adGroupVO);

        List<AdContentRelation> relations = adGroupVO.getRelations();
        AdPlan adPlan = adPlanService.getById(adGroupVO.getAdPlanId());
        saveOrUpdateAdContentRelation(relations, adGroup.getId());
        syncDictVideoPost(validStyles, adGroupVO, adGroup.getId(), relations, adPlan);
        saveOrUpdateAdContents(adGroupVO, adGroup);
        saveOrUpdateAdvertisingByKeywordsRule(adGroupVO.getAdvertisingKeywordList(), adGroupVO.getAudioUrls(), adGroup.getId());
        saveYexData(adGroupVO.getDspId(), adGroupVO.getDealId(), adGroup.getDealRemark(), adGroup.getId(), adPlan, validStyles);
        customCrowdPackRelationService.saveOrUpdate(adGroup.getId(), adGroup.getCrowdPackIds());
    }

    private void preDealFullClickClassCities(AdGroupSaveOrUpdateVO adGroupVO) {
        if (adGroupVO.getFullClickIsAllCity() != null && adGroupVO.getFullClickIsAllCity()) {
            adGroupVO.setFullClickClassCities(Collections.singletonList(FULL_CLICK_CLASS_CITIES_IS_ALL_CITY_ID));
        }
        if (!adGroupVO.isFullClickIsOpen()) {
            adGroupVO.setFullClickClassCities(Collections.emptyList());
        }
    }

    /**
     * 词典视频流特殊处理，将词典所需的数据同步
     */
    private void syncDictVideoPost(List<Style> validStyles,
                                   AdGroupSaveOrUpdateVO adGroupVO,
                                   Long adGroupId,
                                   List<AdContentRelation> relations,
                                   AdPlan adPlan) throws ExecutionException, RetryException {
        if (isAdvertisingToDsp(adPlan)) {
            return;
        }
        List<DictVideoPost> newDictVideoPosts = new ArrayList<>();
        List<DictVideoPost> oldDictVideoPosts = new ArrayList<>();
        getOldAndNewDictVideoPosts(validStyles, adGroupVO, adGroupId, relations, newDictVideoPosts, oldDictVideoPosts);
        setUidAndPostId(newDictVideoPosts, oldDictVideoPosts);
        adContentRelationService.saveOrUpdateBatch(getRelations(newDictVideoPosts));
    }

    /**
     * @param newDictVideoPosts 新的变体相关数据
     * @param oldDictVideoPosts 旧的变体相关数据，如果是新增则为空
     */
    private void setUidAndPostId(List<DictVideoPost> newDictVideoPosts, List<DictVideoPost> oldDictVideoPosts) throws ExecutionException, RetryException {
        Map<Long, DictVideoPost> relationId2OldProperty = oldDictVideoPosts
                .stream().filter(a -> Objects.nonNull(a.getRelationId())).collect(Collectors.toMap(DictVideoPost::getRelationId, Function.identity()));
        Map<String, String> urlCache = new HashMap<>();
        for (DictVideoPost dictVideoPost : newDictVideoPosts) {
            dictVideoPost.setUid(dictService.insertOrUpdateUser(dictVideoPost.getIconImage(), dictVideoPost.getAppName(), dictVideoPost.getUid()));
            if (!dictVideoPost.equals(relationId2OldProperty.get(dictVideoPost.getRelationId()))) {
                dictVideoPost.setDictPostId(null);
            }
            dictVideoPost.setDictPostId(dictService.postContent(dictVideoPost, urlCache));
        }
    }

    private void getOldAndNewDictVideoPosts(List<Style> validStyles,
                                            AdGroupSaveOrUpdateVO adGroupVO,
                                            Long adGroupId,
                                            List<AdContentRelation> relations,
                                            List<DictVideoPost> newDictVideoPosts,
                                            List<DictVideoPost> oldDictVideoPosts) {
        List<Style> dictVideoStreamStyles = validStyles.stream().filter(Style::isDictVideoPost).collect(Collectors.toList());
        Map<Integer, AdContentSaveOrUpdateVO> type2Content = adGroupVO.getCreativeContents().stream().collect(Collectors.toMap(AdContentSaveOrUpdateVO::getType, Function.identity()));
        Map<Integer, AdContentSaveOrUpdateVO> type2OldContent = adContentMapper.doListToAdContentSaveOrUpdateVO(adContentService.listValidByAdGroupId(adGroupId)).stream().collect(Collectors.toMap(AdContentSaveOrUpdateVO::getType, Function.identity()));
        Map<Long, AdContentRelation> styleId2Relation = relations.stream().collect(Collectors.toMap(AdContentRelation::getStyleId, Function.identity()));

        dictVideoStreamStyles.forEach(style -> newDictVideoPosts.add(getDictVideoPosts(style, type2Content, adGroupVO.getYouthMode(), styleId2Relation)));
        dictVideoStreamStyles.forEach(style -> oldDictVideoPosts.add(getDictVideoPosts(style, type2OldContent, adGroupVO.getYouthMode(), styleId2Relation)));
    }

    private Collection<AdContentRelation> getRelations(List<DictVideoPost> dictVideoPosts) {
        List<AdContentRelation> relations = new ArrayList<>();
        dictVideoPosts.forEach(dictVideoPost -> {
            AdContentRelation relation = new AdContentRelation();
            relation.setId(dictVideoPost.getRelationId());
            relation.setDictUid(dictVideoPost.getUid());
            relation.setDictPostId(dictVideoPost.getDictPostId());
            dictVideoPost.getHistoryDictPostIds().add(dictVideoPost.getDictPostId());
            relation.setHistoryDictPostIds(dictVideoPost.getHistoryDictPostIds());
            relations.add(relation);
        });
        return relations;
    }

    private DictVideoPost getDictVideoPosts(Style style,
                                            Map<Integer, AdContentSaveOrUpdateVO> type2Content,
                                            Boolean youthMode,
                                            Map<Long, AdContentRelation> styleId2Relation) {
        Integer type = adPositionService.getById(style.getAdPositionId()).getType();
        AdContentSaveOrUpdateVO content = type2Content.getOrDefault(type, new AdContentSaveOrUpdateVO());
        AdContentRelation relation = styleId2Relation.getOrDefault(style.getId(), new AdContentRelation());
        DictVideoPost dictVideoPost = new DictVideoPost();
        dictVideoPost.setYouthVisible(!youthMode || !mediaService.getById(adPositionService.getById(style.getAdPositionId()).getMediaId()).getYouthMode());
        dictVideoPost.setRelationId(relation.getId());
        dictVideoPost.setUid(relation.getDictUid());
        dictVideoPost.setDictPostId(relation.getDictPostId());
        dictVideoPost.setHistoryDictPostIds(CollectionUtils.isEmpty(relation.getHistoryDictPostIds()) ? new HashSet<>() : relation.getHistoryDictPostIds());

        Map<String, StyleElement> key2TextElement = style.getTextStyleContent().stream().collect(Collectors.toMap(StyleElement::getElementKey, Function.identity()));
        Map<String, CreativeContent> key2TextContent = content.getTextCreativeContents().stream().collect(Collectors.toMap(CreativeContent::getElementKey, Function.identity()));
        dictVideoPost.setTitle(getProperty(key2TextElement.get("title"), key2TextContent.get("title")));
        dictVideoPost.setAppName(getProperty(key2TextElement.get("appName"), key2TextContent.get("appName")));
        dictVideoPost.setText(getProperty(key2TextElement.get("text"), key2TextContent.get("text")));

        Map<String, StyleElement> key2PicElement = style.getPicStyleContent().stream().collect(Collectors.toMap(StyleElement::getElementKey, Function.identity()));
        Map<String, CreativeContent> key2ImageContent = content.getImageCreativeContents().stream().collect(Collectors.toMap(CreativeContent::getElementKey, Function.identity()));
        dictVideoPost.setCoverImage(getProperty(key2PicElement.get("coverimage"), key2ImageContent.get("coverimage")));
        StyleElement coverImage = key2PicElement.get("coverimage");
        dictVideoPost.setCoverImageWidth(coverImage.getWidth());
        dictVideoPost.setCoverImageHeight(coverImage.getHeight());

        dictVideoPost.setIconImage(getProperty(key2PicElement.get("iconimage"), key2ImageContent.get("iconimage")));

        Map<String, StyleElement> key2VideoElement = style.getVideoStyleContent().stream().collect(Collectors.toMap(StyleElement::getElementKey, Function.identity()));
        Map<String, CreativeContent> key2VideoContent = content.getVideoCreativeContents().stream().collect(Collectors.toMap(CreativeContent::getElementKey, Function.identity()));
        dictVideoPost.setVideo(getProperty(key2VideoElement.get("video"), key2VideoContent.get("video")));
        return dictVideoPost;
    }

    private String getProperty(StyleElement element, CreativeContent content) {
        if (ObjectUtils.allNotNull(element, content)) {
            for (CreativeContentProperty property : content.getProperties()) {
                if (isMatch(property, element)) {
                    return element.getType().equals(TEXT_ELEMENT) ? property.getText() : property.getUrl();
                }
            }
        }
        return null;
    }

    private boolean isMatch(CreativeContentProperty property, StyleElement element) {
        return Objects.equals(property.getHeight(), element.getHeight())
                && Objects.equals(property.getLength(), element.getLength())
                && Objects.equals(property.getWidth(), element.getWidth())
                && Objects.equals(property.getRatio(), element.getRatio());
    }

    @Override
    public void saveYexData(String dspId, String dealId, String dealRemark, Long id, AdPlan adPlan, List<Style> styles) {
        if (!isAdvertisingToDsp(adPlan)) {
            return;
        }
        List<YexPmpDeal> yexPmpDeals = new ArrayList<>();
        Set<Long> adPositionIds = styles.stream().map(Style::getAdPositionId).collect(Collectors.toSet());
        Set<SdkSlot> sdkSlots = new HashSet<>(sdkSlotService.listSlotsByAdPositionIds(adPositionIds));
        List<AdDeliveryTime> adDeliveryTimes = adPlanService.getAdDeliveryTimes(adPlan.getId(),
                adPlan.getTimeOrientation(),
                adPlan.getAdDeliveryInterval(),
                adPlan.getTimeDest(),
                adPlan.getAdOpenDate());
        String dealName = "youxuan_" + id;
        yexPmpDealService.deleteByDealName(dealName);
        for (SdkSlot slot : sdkSlots) {
            for (AdDeliveryTime adDeliveryTime : adDeliveryTimes) {
                YexPmpDeal yexPmpDeal = new YexPmpDeal();
                yexPmpDeal.setDealId(dealId);
                yexPmpDeal.setDealName(dealName);
                yexPmpDeal.setDealType("preferred_deal");
                yexPmpDeal.setDsps(dspId);
                yexPmpDeal.setSlotId(slot.getSdkSlotUdid());
                yexPmpDeal.setPrice(slot.getBidFloorYex() == null ? 100 : slot.getBidFloorYex());
                yexPmpDeal.setStatus(YEX_VALID);
                yexPmpDeal.setRatio(0);
                yexPmpDeal.setRemark(dealRemark);
                yexPmpDeal.setStartTime(getYexPreStartTime(adDeliveryTime.getStartTime()));
                yexPmpDeal.setEndTime(adDeliveryTime.getEndTime());
                yexPmpDeals.add(yexPmpDeal);
            }
        }
        yexPmpDealService.saveBatch(yexPmpDeals);
    }

    private static final int DAYS_IN_ADVANCE = 7;

    /**
     * 由于预取广告逻辑，将yexPmpDeal的startTime推前 {@link #DAYS_IN_ADVANCE} 天
     *
     * @param startTime 广告投放时间
     * @return 预取广告时间
     */
    private LocalDateTime getYexPreStartTime(LocalDateTime startTime) {
        return startTime.minusDays(DAYS_IN_ADVANCE);
    }


    private boolean isAdvertisingToDsp(AdPlan adPlan) {
        return (adPlan.getBillingType().equals(CPT) && adPlan.getDeliveryType().equals(DELIVERY_PDB))
                || (adPlan.getBillingType().equals(CPM) && adPlan.getDeliveryType().equals(DELIVERY_PD))
                || (adPlan.getBillingType().equals(CPM) && adPlan.getDeliveryType().equals(DELIVERY_PDB));
    }

    /**
     * 更新或者修改前置校验<p>
     * 1.推广组名称不能重复<br>
     * 2.检测资源位状态<br>
     * 3.检测资源是否充足<br>
     * 4.互斥的选项是否同时开启
     * 5.h5小程序合成链接功能是否添加appId字段
     */
    private void saveOrUpdatePreCheck(AdGroupSaveOrUpdateVO adGroupVO, List<Long> styleIds, List<Style> validStyles) {
        nameRepeatCheck(adGroupVO.getName(), adGroupVO.getId(), adGroupVO.getAdPlanId());
        resourceValidCheck(styleIds, validStyles);
        crowdLabelCheck(adGroupVO, styleIds);
        customCrowdPackCheck(adGroupVO);
        AdPlan adPlan = adPlanService.getById(adGroupVO.getAdPlanId());
        if (isNew(adGroupVO.getId())) {
            adGroupVO.setStatus(adPlan.getStatus());
        }
        if (isAdvertisingToDsp(adPlan)) {
            yexCheck(adGroupVO.getDealId(), adGroupVO.getDspId());
        }
        // 如果选择了无定向模式，需要将定向相关值设置为空
        if (adGroupVO.getOrientationMode() == 0) {
            adGroupVO.setAgeOrientation(Collections.emptyList());
            adGroupVO.setGenderOrientation(0);
            adGroupVO.setCrowdLabel(Lists.emptyList());
        }
        if (adPlan.getBillingType().equals(CPT)
                && Objects.equals(BOOT_FIRST_REFRESH_CLOSE, adGroupVO.getBootFirstRefresh())
                && !Objects.equals(GENDER_ORIENTATION_NO, adGroupVO.getGenderOrientation())) {
            throw new CustomException(INVALID_PARAMETERS, "CPT广告不支持开启首屏刷新或性别定向功能");
        }
        if (isUpdate(adGroupVO.getId())) {
            updatePreCheck(adGroupVO, adPlan);
        }
        if (adGroupVO.getH5TransitUrl() && StringUtils.isBlank(adGroupVO.getTransitTargetWechatAppId())) {
            throw new CustomException(INVALID_PARAMETERS, "H5合成小程序链接功能，需要填写目标小程序appId字段");
        }
        if (!adGroupVO.getH5TransitUrl()) {
            adGroupVO.setTransitTargetWechatAppId("");
        }
        List<AdPosition> adPositions = adPositionService
                .listByStyleIds(styleIds);
        // 全链路推广组只能选择开屏和支持全链路的广告位
        if (BooleanUtils.isTrue(adGroupVO.getFullChannel())) {
            List<String> adPositionNames = adPositions
                    .stream()
                    .filter(adPosition -> !adPosition.getType().equals(OPEN_SCREEN) && !adPosition.getFullChannel())
                    .map(AdPosition::getName)
                    .collect(Collectors.toList());
            if (adPositionNames.size() > 0) {
                throw new CustomException(INVALID_PARAMETERS, adPositionNames + "不支持全链路投放");
            }
        }
        if (adPositions.stream().anyMatch(AdPosition::isSupportAdvertisingByKeywords)) {
            if (CollectionUtils.isEmpty(adGroupVO.getAdvertisingKeywordList())) {
                throw new CustomException(INVALID_PARAMETERS, "定向词不能为空");
            }
            if (adGroupVO.getAdvertisingKeywordList().stream().distinct().count() < adGroupVO.getAdvertisingKeywordList().size()) {
                throw new CustomException(INVALID_PARAMETERS, "定向词不可重复");
            }
            if (adPositions.stream().anyMatch(AdPosition::getIsAudio)) {
                if (CollectionUtils.size(adGroupVO.getAdvertisingKeywordList()) != CollectionUtils.size(adGroupVO.getAudioUrls())) {
                    throw new CustomException(INVALID_PARAMETERS, "音频数量需要和关键词数量一致");
                }
            }
        }
        resourceSufficientCheck(adGroupVO, adPlan);
        checkFullClickClassCities(adGroupVO);
    }

    private void checkFullClickClassCities(AdGroupSaveOrUpdateVO adGroupVO) {
        Optional.ofNullable(adGroupVO.getFullClickClassCities())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(cities -> {
                    if (cities.stream().anyMatch(c -> {
                        try {
                            return !ClassCityMapper.getInstance().getClassCityIdToClassCityInfo().containsKey(c);
                        } catch (Exception e) {
                            log.error("error ClassCityMapper.getInstance ", e);
                            return true;
                        }
                    })) {
                        throw new CustomException(INVALID_PARAMETERS, "城市等级不存在");
                    }
                });
    }

    private void customCrowdPackCheck(AdGroupSaveOrUpdateVO adGroupVO) {
        if (customCrowdPackService.listByIds(adGroupVO.getCrowdPackIds())
                .stream().anyMatch(s -> !s.getStatus().equals(CustomCrowdPack.VALID))) {
            throw new CustomException(RESOURCE_FAILURE, "所选的定向人群包已失效，请重新选择");
        }
    }

    private void crowdLabelCheck(AdGroupSaveOrUpdateVO adGroupVO, List<Long> styleIds) {
        if (!CollectionUtils.isEmpty(adGroupVO.getCrowdLabel()) && !mediaService.listByStyleIds(styleIds).stream().allMatch(media -> "有道词典-iOS".equals(media.getName()) || "有道词典-安卓".equals(media.getName()))) {
            throw new CustomException(INVALID_PARAMETERS, "有道词典-ios和有道词典-安卓才能添加人群标签");
        }
    }


    /**
     * 修改校验 <p>
     * 1.投放位置不支持修改 <br>
     * 2.CPT 轮播数不支持修改 <br>
     * 3.CPM 总展示量不得下调 <br>
     * 4.投放中/投放结束 不支持修改定向词列表
     */
    private void updatePreCheck(AdGroupSaveOrUpdateVO adGroupVO, AdPlan adPlan) {
        AdGroup oldGroup = baseMapper.selectById(adGroupVO.getId());
        batchCheckGroupOperatePermissions(Collections.singletonList(oldGroup));
        AdvertisingByKeywordsRule oldRule = advertisingByKeywordsRuleService.getByAdGroupId(adGroupVO.getId());
        List<String> oldKeywordList = Objects.nonNull(oldRule) ? oldRule.getKeywordList() : Collections.emptyList();
        if (isDelivering(oldGroup.getAdPlanId())) {
            Map<Long, Integer> newRelation = adGroupVO.getRelations().stream().collect(Collectors.toMap(AdContentRelation::getStyleId, AdContentRelation::getDisplayWeight));
            Map<Long, Integer> oldRelations = adContentRelationService.listByAdGroupId(oldGroup.getId()).stream().collect(Collectors.toMap(AdContentRelation::getStyleId, AdContentRelation::getDisplayWeight));
            if (!newRelation.keySet().equals(oldRelations.keySet())) {
                throw new CustomException(UPDATE_POSITION_EXCEPTION);
            }
            if (adPlan.getBillingType().equals(CPT)) {
                if (!newRelation.equals(oldRelations)) {
                    throw new CustomException(UPDATE_DISPLAY_WEIGHT_EXCEPTION);
                }
            } else {
                if (adGroupVO.getSumDisplayCount() < oldGroup.getSumDisplayCount()) {
                    throw new CustomException(UPDATE_SUM_DISPLAY_EXCEPTION);
                }
            }
        }

        // 已删除或者投放结束不支持修改日展示量
        if (isDeliverFinish(adGroupVO.getAdPlanId()) || adGroupVO.getStatus().equals(DELETED.getCode())) {
            if (adPlan.getBillingType().equals(CPM) && !Objects.equals(adGroupVO.getDailyDisplayLimit(), oldGroup.getDailyDisplayLimit())) {
                throw new CustomException(UPDATE_DAILY_DISPLAY_EXCEPTION, "日展示量不可修改");
            }
            if (isKeywordListMod(oldKeywordList, adGroupVO.getAdvertisingKeywordList())) {
                throw new CustomException(UPDATE_ADVERTISING_KEYWORDS_LIST_EXCEPTION);
            }
        }
    }

    /**
     * 推广计划是否在投放区间内（包含暂停）
     *
     * @param adPlanId 推广计划id
     * @return 推广计划
     */
    private boolean isDelivering(Long adPlanId) {
        return adPlanService.getDeliverStatus(adPlanId).equals(DELIVERING.getCode());
    }

    private boolean isDeliverFinish(Long adPlanId) {
        return adPlanService.getDeliverStatus(adPlanId).equals(DELIVER_FINISH.getCode());
    }

    private boolean isKeywordListMod(List<String> oldKeywords, List<String> newKeywords) {
        return !ListUtils.isEqualList(ListUtils.emptyIfNull(newKeywords), ListUtils.emptyIfNull(oldKeywords));
    }

    /**
     * 资源数量校验
     */
    private void resourceSufficientCheck(AdGroupSaveOrUpdateVO adGroupVO, AdPlan adPlan) {
        if (adPlan.getBillingType().equals(CPT)) {
            if (!adResourceService.resourcesSufficient(adGroupVO.getRelations(),
                    adPlanService.getAdDeliveryTimes(adPlan.getId(), adPlan.getTimeOrientation(), adPlan.getAdDeliveryInterval(), adPlan.getTimeDest(), adPlan.getAdOpenDate()),
                    adPlan.getRegionalOrientation(),
                    adPlan.getInlandRegionalDest(),
                    adPlan.getInternationalRegionalDest(),
                    null,
                    adGroupVO.getId(),
                    adGroupVO.getFullChannel())) {
                throw new CustomException(RESOURCE_SCARCITY_EXCEPTION, "已选广告位剩余轮播数有变更，请重新录入");
            }
        } else {
            List<AdContentRelation> relations = adGroupVO.getRelations();
            for (AdContentRelation relation : relations) {
                int cpmMaxDisplayWeight = 20;
                if (relation.getDisplayWeight() > cpmMaxDisplayWeight) {
                    throw new CustomException(DISPLAY_TIMES_ILLEGAL_EXCEPTION, "CPM轮播数不能超过20");
                }
            }
        }
    }

    /**
     * 资源有效check
     *
     * @param styleIds    样式id列表
     * @param validStyles 有效样式列表
     */
    private void resourceValidCheck(List<Long> styleIds, List<Style> validStyles) {
        if (validStyles.isEmpty()) {
            throw new CustomException(RESOURCE_FAILURE, "已选的投放位置状态均已失效，请重新选择");
        } else if (validStyles.size() < styleIds.size()) {
            throw new CustomException(RESOURCE_FAILURE, "已选的部分投放位置状态已失效，请重新选择");
        }
    }

    /**
     * 检测yex dealId和dspId的对应关系
     * <p> dealId和dspIp 是多对一的关系</p>
     */
    private void yexCheck(String dealId, String dspId) {
        if (!ObjectUtils.allNotNull(dealId, dspId)) {
            throw new CustomException(INVALID_PARAMETERS, "DSP相关属性没有填写");
        }
        List<YexPmpDeal> yexPmpDeals = yexPmpDealService.listValidByDealId(dealId);
        if (!yexPmpDeals.isEmpty()) {
            YexPmpDeal yexPmpDeal = yexPmpDeals.get(0);
            if (Objects.nonNull(yexPmpDeal) && !yexPmpDeal.getDsps().equals(dspId)) {
                throw new CustomException(DSP_ID_EXCEPTION);
            }
        }
    }

    /**
     * 名字重复校验
     */
    private void nameRepeatCheck(String name, Long id, Long adPlanId) {
        if (nameRepeat(name, id, adPlanId)) {
            throw new RuntimeException("名称重复，请修改");
        }
    }

    @Override
    public Boolean nameRepeat(String name, Long id, Long adPlanId) {
        return baseMapper.nameCount(name, id, adPlanId) > 0;
    }

    @Override
    public List<AdGroup> listValidByAdPlanId(Long adPlanId) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(AdGroup::getAdPlanId, adPlanId)
                .ne(AdGroup::getStatus, PromotionStatusEnum.DELETED.getCode())
                .list();
    }

    @Override
    public List<AdGroup> listNotDeleteByIds(Collection<Long> adGroupIds) {
        if (CollectionUtils.isEmpty(adGroupIds)) {
            return Lists.emptyList();
        }
        return new LambdaQueryChainWrapper<>(baseMapper)
                .in(AdGroup::getId, adGroupIds)
                .ne(AdGroup::getStatus, PromotionStatusEnum.DELETED.getCode())
                .list();
    }

    @Override
    public InitialVO initialize(List<Long> styleIds, Long adPlanId, Long adGroupId, Integer mode, Integer interactionType) {
        InitialVO initialVO = new InitialVO();
        if (ObjectUtils.isEmpty(styleIds)) {
            if (Objects.nonNull(adGroupId)) {
                initialVO.setMsg("所选的投放位置均已失效，请重新选择");
                return initialVO;
            } else {
                throw new CustomException(INVALID_PARAMETERS, "样式ID不能为空");
            }
        }
        initialVO.setAdContents(adResourceService.initialCreativeContents(styleIds, adGroupId, interactionType));
        if (Objects.isNull(mode) || mode.equals(0)) {
            initialVO.setCarouselInfo(adResourceService.initialCarouselInfo(styleIds, adPlanId, adGroupId));
        } else {
            initialVO.setCarouselInfo(adResourceService.initialCarouselInfo(styleIds, adPlanId, null));
        }

        List<Style> styles = styleService.listByIds(styleIds);
        List<AdPosition> adPositions = adPositionService.listByIds(styles.stream().map(Style::getAdPositionId).collect(Collectors.toList()));
        List<Media> medias = mediaService.listByIds(adPositions.stream().map(AdPosition::getMediaId).collect(Collectors.toList()));
        initExtraOptions(initialVO, styles, adPositions, medias);
        if (Objects.nonNull(adGroupId)) {
            AdGroup adGroup = baseMapper.selectById(adGroupId);
            if (Objects.nonNull(adGroup)) {
                initialVO.setBootFirstRefresh(adGroup.getBootFirstRefresh());
                initialVO.setOpenScreenRecycling(adGroup.getOpenScreenRecycle());
                initialVO.setFullClickIsOpen(adGroup.getFullClickIsOpen());
                List<Integer> fullClickClassCities = Optional.ofNullable(adGroup.getFullClickClassCities()).orElseGet(ArrayList::new);
                initialVO.setFullClickIsAllCity(fullClickClassCities.contains(FULL_CLICK_CLASS_CITIES_IS_ALL_CITY_ID));
                initialVO.setFullClickClassCities(fullClickClassCities.contains(FULL_CLICK_CLASS_CITIES_IS_ALL_CITY_ID) ? Collections.emptyList() : fullClickClassCities);
            }
            if (styleService.listByAdGroupId(adGroupId)
                    .stream().anyMatch(s -> !s.getStatus().equals(ResourceStatusEnum.VALID.getCode()))) {
                initialVO.setMsg("所选的部分投放位置已失效，请重新选择");
            }
            if (customCrowdPackService.listByIds(adGroup.getCrowdPackIds())
                    .stream().anyMatch(s -> !s.getStatus().equals(0))) {
                initialVO.setMsg("所选的定向人群包已失效，请重新选择");
            }
        }
        AdPlan adPlan = adPlanService.getById(adPlanId);
        if (isAdvertisingToDsp(adPlan)) {
            initialVO.setDspList(yexDspService.listId2DspName(isMultipleMainImage(styles)));
        }
        return initialVO;
    }

    private final Set<Long> SUPPORT_ADVERTISING_BY_CROWD_MEDIA_IDS = ImmutableSet.of(300001L, 300002L);

    private void initExtraOptions(InitialVO initialVO, List<Style> styles, List<AdPosition> adPositions, List<Media> medias) {
        initialVO.setSupportBootFirstRefresh(styles
                .stream()
                .anyMatch(style -> style.getBootFirstRefresh().equals(SUPPORT)) ? SUPPORT : NOT_SUPPORT);
        initialVO.setSupportOpenScreenRecycling(styles
                .stream()
                .anyMatch(style -> style.getOpenScreenRecycling().equals(SUPPORT)) ? SUPPORT : NOT_SUPPORT);
        initialVO.setSupportAdvertisingByKeywords(adPositions.stream().anyMatch(AdPosition::isSupportAdvertisingByKeywords));
        initialVO.setSupportAdvertisingByCrowd(medias.stream().allMatch(media -> SUPPORT_ADVERTISING_BY_CROWD_MEDIA_IDS.contains(media.getId())));
        initialVO.setIsNeedYouthMode(medias.stream().anyMatch(Media::getYouthMode));
        initialVO.setSupportShakable(styles.stream().anyMatch(Style::getShakable));
        initialVO.setSlideInteract(styles.stream().allMatch(Style::getSlideInteract));
        initialVO.setDoubleLink(styles.stream().anyMatch(Style::getDoubleLink));
        initialVO.setThreeLink(styles.stream().anyMatch(Style::getThreeLink));
        initialVO.setSupportTwist(styles.stream().anyMatch(Style::getHasTwist));
        initialVO.setIsAudio(adPositions.stream().anyMatch(AdPosition::getIsAudio));
        initialVO.setSupportFullClick(styles.stream().anyMatch(Style::getFullClick));
        initialVO.setSupportThreeInOne(styles.stream().anyMatch(Style::getThreeInOne));
        Map<Long, List<AdPosition>> mediaId2Positions = adPositions.stream().collect(Collectors.groupingBy(AdPosition::getMediaId));
        for (List<AdPosition> positions : mediaId2Positions.values()) {
            // 同一个app下，同时选中了开屏和支持全链路投放广告位时，则支持全链路投放设置
            if (positions.stream().anyMatch(p -> p.getType().equals(1)) && positions.stream().anyMatch(AdPosition::getFullChannel)) {
                initialVO.setSupportFullChannel(true);
                break;
            }
        }
    }

    private final Set<String> MAIN_IMAGE_KEYS = ImmutableSet.of(
            MAIN_IMAGE.getValue(),
            MAIN_IMAGE1.getValue(),
            MAIN_IMAGE2.getValue(),
            MAIN_IMAGE3.getValue(),
            MAIN_IMAGE4.getValue()
    );

    private Boolean isMultipleMainImage(List<Style> styles) {
        return styles.stream().anyMatch(
                style -> style.getPicStyleContent()
                        .stream()
                        .map(StyleElement::getElementKey)
                        .distinct()
                        .filter(MAIN_IMAGE_KEYS::contains)
                        .count() > 1
        );
    }

    @Override
    public List<AdGroup> listNoteDeleteByAdPlanId(Long id) {
        return baseMapper.listNoteDeleteByAdPlanId(id);
    }

    @Override
    public void updateValidStatusByAdPlanId(Long id, Integer status) {
        baseMapper.updateValidStatusByAdPlanId(id, status);
    }

    @Override
    public long countNotDeleteByAdPlanId(Long id) {
        return baseMapper.countNotDeleteByAdPlanId(id);
    }


    @Override
    public List<AdGroup> listNoteDeleteByAdPlanIds(Collection<Long> adPlanIds) {
        return listNotDeleteByAdPlanIdsExcludeAdGroup(adPlanIds, null);
    }

    @Override
    public List<AdGroup> listNotDeleteByAdPlanIdsExcludeAdGroup(Collection<Long> adPlanIds, Long adGroupId) {
        if (Objects.isNull(adPlanIds) || adPlanIds.isEmpty()) {
            return Lists.emptyList();
        }
        return new LambdaQueryChainWrapper<>(baseMapper)
                .in(AdGroup::getAdPlanId, adPlanIds)
                .ne(AdGroup::getStatus, PromotionStatusEnum.DELETED.getCode())
                .ne(Objects.nonNull(adGroupId), AdGroup::getId, adGroupId)
                .list();
    }

    @Override
    public List<AdGroup> listNotDeletedAdGroupsLikeNameOrInAdPlanIds(String name, Set<Long> adPlanIds) {
        return baseMapper.listNotDeletedAdGroupsLikeNameOrInAdPlanIds(name, adPlanIds);
    }

    /**
     * 通过推广管理数据以及资源管理数据进行拼接，填充adGroupListVos
     *
     * @param medias             媒体
     * @param adPositions        广告位
     * @param styles             样式
     * @param adPlans            推广计划
     * @param adGroups           推广组
     * @param adContents         推广内容
     * @param adContentRelations 关系
     * @return List
     */
    private List<AdGroupListVO> getAdGroupListVO(List<Media> medias,
                                                 List<AdPosition> adPositions,
                                                 List<Style> styles,
                                                 List<AdPlan> adPlans,
                                                 List<AdGroup> adGroups,
                                                 List<AdContent> adContents,
                                                 List<AdContentRelation> adContentRelations) {
        Map<Long, Media> idToMedia = medias.stream().collect(Collectors.toMap(Media::getId, Function.identity()));
        Map<Long, AdPosition> idToAdPosition = adPositions.stream().collect(Collectors.toMap(AdPosition::getId, Function.identity()));
        Map<Long, Style> idToStyle = styles.stream().collect(Collectors.toMap(Style::getId, Function.identity()));

        Map<Long, AdPlan> idToAdPlan = adPlans.stream().collect(Collectors.toMap(AdPlan::getId, Function.identity()));

        Map<Long, List<AdContent>> adGroupIdToAdContents = new HashMap<>();
        adContents.forEach(adContent -> {
                    List<AdContent> adContentList = adGroupIdToAdContents.getOrDefault(adContent.getAdGroupId(), new ArrayList<>());
                    adContentList.add(adContent);
                    adGroupIdToAdContents.put(adContent.getAdGroupId(), adContentList);
                }
        );

        List<AdGroupListVO> res = new ArrayList<>();
        adGroups.forEach(adGroup -> res.add(adGroupMapper.DO2ListVO(adGroup,
                idToAdPlan.get(adGroup.getAdPlanId()),
                getDeliveryPositions(adContentRelations, idToMedia, idToAdPosition, idToStyle, adGroup),
                getCreativeUrl(adGroupIdToAdContents.get(adGroup.getId())))));
        return res;
    }

    /**
     * video 和 videoUrl存在覆盖关系
     */
    private final Map<String, String> URL_2_VIDEO = ImmutableMap.of(
            VIDEO_URL.getValue(), VIDEO.getValue(),
            VIDEO_URL1.getValue(), VIDEO1.getValue(),
            VIDEO_URL2.getValue(), VIDEO2.getValue(),
            VIDEO_URL3.getValue(), VIDEO3.getValue()
    );

    private List<String> getDeliveryPositions(List<AdContentRelation> adContentRelations, Map<Long, Media> idToMedia, Map<Long, AdPosition> idToAdPosition, Map<Long, Style> idToStyle, AdGroup adGroup) {
        List<String> deliveryPositions = new ArrayList<>();
        for (AdContentRelation adContentRelation : adContentRelations) {
            if (adContentRelation.getAdGroupId().equals(adGroup.getId())) {
                Style style = idToStyle.get(adContentRelation.getStyleId());
                if (Objects.isNull(style)) {
                    continue;
                }
                AdPosition adPosition = idToAdPosition.get(style.getAdPositionId());
                Media media = idToMedia.get(adPosition.getMediaId());
                String deliveryPosition = String.format("%s_%s_%s(购买%d轮)", media.getName(), adPosition.getName(), style.getName(), adContentRelation.getDisplayWeight());
                deliveryPositions.add(deliveryPosition);
            }
        }
        return deliveryPositions;
    }


    /**
     * 保存或修改推广组
     *
     * @param adGroupVO 推广组相关信息
     * @return 推广组信息
     */
    private AdGroup saveOrUpdateAdGroup(AdGroupSaveOrUpdateVO adGroupVO) {
        AdGroup adGroup = adGroupMapper.saveVO2DO(adGroupVO);
        if (isSave(adGroup)) {
            save(adGroup);
        } else {
            new LambdaUpdateChainWrapper<>(baseMapper)
                    .eq(AdGroup::getId, adGroup.getId())
                    .set(AdGroup::getLandingPageLink1, adGroup.getLandingPageLink1())
                    .set(AdGroup::getDeeplinkUrl1, adGroup.getDeeplinkUrl1())
                    .set(AdGroup::getWechatOriginalId1, adGroup.getWechatOriginalId1())
                    .set(AdGroup::getWechatPath1, adGroup.getWechatPath1())
                    .set(AdGroup::getLandingPageLink2, adGroup.getLandingPageLink2())
                    .set(AdGroup::getDeeplinkUrl2, adGroup.getDeeplinkUrl2())
                    .set(AdGroup::getWechatOriginalId2, adGroup.getWechatOriginalId2())
                    .set(AdGroup::getWechatPath2, adGroup.getWechatPath2())
                    .set(AdGroup::getCpmPrice, adGroup.getCpmPrice())
                    .set(AdGroup::getFullClickIsOpen, adGroup.getFullClickIsOpen())
                    .set(AdGroup::getFullClickClassCities, JSON.toJSONString(adGroup.getFullClickClassCities()))
                    .update(adGroup);
        }
        return adGroup;
    }

    private boolean isSave(AdGroup adGroup) {
        return Objects.isNull(adGroup.getId());
    }

    /**
     * 保存或修改推广内容
     *
     * @param adGroupVO 推广组相关信息
     * @param adGroup   推广组信息
     */
    private void saveOrUpdateAdContents(AdGroupSaveOrUpdateVO adGroupVO, AdGroup adGroup) {
        List<AdContent> adContents = getAdContents(adGroupVO, adGroup.getId());
        List<AdContent> oldAdContents = adContentService.listValidByAdGroupId(adGroup.getId());
        Map<Integer, AdContent> type2OldContent = oldAdContents
                .stream()
                .collect(Collectors.toMap(AdContent::getType, Function.identity()));
        adContents.forEach(c -> type2OldContent.computeIfPresent(c.getType(), (k, v) -> {
            c.setId(v.getId());
            c.setStatus(0);
            c.setCreator(v.getCreator());
            return v;
        }));
        adContentService.removeByAdGroupId(adGroup.getId());
        adContentService.saveOrUpdateBatch(adContents);
    }

    private final Set<String> VIDEO_KEYS = ImmutableSet.of(VIDEO.getValue(), VIDEO1.getValue(), VIDEO2.getValue(), VIDEO3.getValue());

    /**
     * 获取创意内容数据
     *
     * @param adGroupVO vo
     * @param id        推广组id
     * @return 创意内容列表
     */
    private List<AdContent> getAdContents(AdGroupSaveOrUpdateVO adGroupVO, Long id) {
        List<AdContent> adContents = new ArrayList<>();
        List<AdContentSaveOrUpdateVO> creativeContents = adGroupVO.getCreativeContents();
        if (ObjectUtils.isEmpty(creativeContents)) {
            return adContents;
        }
        for (AdContentSaveOrUpdateVO creativeContent : creativeContents) {
            AdContent adContent = adContentMapper.saveOrUpdateVoToDo(creativeContent);
            adContent.setAdGroupId(id);
            adContent.setStatus(0);
            for (CreativeContent imageCreativeContent : adContent.getImageCreativeContents()) {
                for (CreativeContentProperty property : imageCreativeContent.getProperties()) {
                    try {
                        String url = property.getUrl();
                        if (StringUtils.isNotBlank(url)) {
                            property.setMimeType(getAndValidMimeType(url));
                        } else {
                            property.getUrls().forEach(this::getAndValidMimeType);
                        }
                    } catch (Exception e) {
                        log.error("invalid image type,groupId:{},adContent:{}, property:{}", id, adContent, property, e);
                        throw new CustomException(INVALID_PARAMETERS, "图片链接格式错误", LogLevel.ERROR);
                    }
                }
            }

            Map<String, Map<CreativeContentProperty, String>> key2Property2Url = new HashMap<>();
            for (CreativeContent videoCreativeContent : adContent.getVideoCreativeContents()) {
                if (isVideoKey(videoCreativeContent)) {
                    key2Property2Url.put(videoCreativeContent.getElementKey(), videoCreativeContent.getProperties().stream().collect(Collectors.toMap(Function.identity(), CreativeContentProperty::getUrl)));
                }
            }
            fillVideoUrl(adContent, key2Property2Url);
            adContents.add(adContent);
        }
        return adContents;
    }

    private final Set<String> VIDEO_URL_KEYS = ImmutableSet.of(VIDEO_URL.getValue(), VIDEO_URL1.getValue(), VIDEO_URL2.getValue(), VIDEO_URL3.getValue());

    private boolean isVideoKey(CreativeContent videoCreativeContent) {
        return VIDEO_KEYS.contains(videoCreativeContent.getElementKey());
    }

    /**
     * 当一个创意内容中有相同尺寸的video和videoUrl，用户只需要上传video，
     * 而不需要填写videoUrl，所以需要查看是否有相同尺寸的videoUrl和video元素，
     * 如果有，需要将video的url赋值给videoUrl
     */
    private void fillVideoUrl(AdContent adContent, Map<String, Map<CreativeContentProperty, String>> key2Property2Url) {
        for (CreativeContent videoCreativeContent : adContent.getVideoCreativeContents()) {
            if (isVideoUrlKey(videoCreativeContent)) {
                for (CreativeContentProperty property : videoCreativeContent.getProperties()) {
                    Optional.ofNullable(key2Property2Url)
                            .map(map -> map.get(URL_2_VIDEO.get(videoCreativeContent.getElementKey())))
                            .map(property2Url -> property2Url.get(property))
                            .ifPresent(property::setUrl);

                    if (Objects.isNull(property.getUrl())) {
                        throw new CustomException(INVALID_PARAMETERS, "videoUrl参数为空");
                    }
                }
            }
        }
    }

    /**
     * 根据图片元素排序，取第一个元素中的url或urls首图作为创意返回
     *
     * @param adContentList
     * @return
     */
    public String getCreativeUrl(List<AdContent> adContentList) {
        if (CollectionUtils.isNotEmpty(adContentList)) {
            List<CreativeContent> contents = adContentList
                    .stream()
                    .flatMap(adContent -> adContent.getImageCreativeContents().stream())
                    .collect(Collectors.toList());
            if (!contents.isEmpty()) {
                Collections.sort(contents);
                CreativeContent creativeContent = contents.get(0);
                CreativeContentProperty property = creativeContent.getProperties().iterator().next();
                return CollectionUtils.isNotEmpty(property.getUrls()) ? property.getUrls().get(0) : property.getUrl();
            }
        }
        return null;
    }

    private boolean isVideoUrlKey(CreativeContent videoCreativeContent) {
        return VIDEO_URL_KEYS.contains(videoCreativeContent.getElementKey());
    }

    private final Set<String> SUPPORT_MIME_TYPE = ImmutableSet.of("jpeg", "jpg", "gif", "png");

    private String getAndValidMimeType(String url) {
        String mimeType = StringUtils.lowerCase(url.substring(url.lastIndexOf('=') + 1));
        if (SUPPORT_MIME_TYPE.contains(mimeType)) {
            return mimeType;
        }
        throw new CustomException(INVALID_PARAMETERS, "图片格式不符合要求");
    }

    /**
     * 保存联系表，由于不好更新，选择采用删除插入代替更新
     *
     * @param adGroupId 推广组id
     */
    private void saveOrUpdateAdContentRelation(List<AdContentRelation> relations, Long adGroupId) {
        relations.forEach(relation -> relation.setAdGroupId(adGroupId));
        List<AdContentRelation> oldRelations = adContentRelationService.listByAdGroupId(adGroupId);
        Map<Long, AdContentRelation> styleId2OldRelation = oldRelations
                .stream().collect(Collectors.toMap((AdContentRelation::getStyleId), Function.identity()));
        relations.forEach(r -> {
            AdContentRelation oldRelation = styleId2OldRelation
                    .getOrDefault(r.getStyleId(), new AdContentRelation());
            r.setId(oldRelation.getId());
            r.setDictUid(oldRelation.getDictUid());
            r.setDictPostId(oldRelation.getDictPostId());
            r.setHistoryDictPostIds(oldRelation.getHistoryDictPostIds());
            r.setStatus(0);
        });
        if (!relations.equals(oldRelations)) {
            adContentRelationService.removeByAdGroupId(adGroupId);
        }
        adContentRelationService.saveOrUpdateBatch(relations);
    }

    private void saveOrUpdateAdvertisingByKeywordsRule(List<String> advertisingKeywordList, List<String> audioUrls, Long adGroupId) {
        AdvertisingByKeywordsRule advertisingByKeywordsRule = advertisingByKeywordsRuleService.getByAdGroupId(adGroupId);
        if (Objects.isNull(advertisingByKeywordsRule)) {
            advertisingByKeywordsRule = new AdvertisingByKeywordsRule();
            advertisingByKeywordsRule.setAdGroupId(adGroupId);
            advertisingByKeywordsRule.setKeywordList(ListUtils.emptyIfNull(advertisingKeywordList));
            advertisingByKeywordsRule.setAudioUrls(ListUtils.emptyIfNull(audioUrls));
        } else {
            if (isKeywordListMod(advertisingByKeywordsRule.getKeywordList(), advertisingKeywordList)) {
                advertisingByKeywordsRule.setKeywordList(advertisingKeywordList);
            } else {
                log.warn("AdvertisingByKeywordsRule is not modified. record: {}, vo: {}.", advertisingByKeywordsRule.getKeywordList(), advertisingKeywordList);
                return;
            }
            advertisingByKeywordsRule.setAudioUrls(audioUrls);
        }
        advertisingByKeywordsRuleService.saveOrUpdate(advertisingByKeywordsRule);
    }

    @Override
    public Collection<AdGroup> listByName(String adGroupName) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .like(AdGroup::getName, adGroupName)
                .list();
    }

    @Override
    public String updateTestDirectStatusById(long adGroupId, boolean testDirect) {
        AdGroup adGroup = this.getById(adGroupId);
        updateDirectTimePreCheck(adGroup);
        AdPlan adPlan = adPlanService.getById(adGroup.getAdPlanId());
        LocalDateTime firstDeliverTime = getFirstDeliverTime(adPlan);
        if (testDirect) {
            LocalDateTime directStartTime = LocalDateTime.now().isBefore(firstDeliverTime) ? firstDeliverTime : LocalDateTime.now();
            adGroup.setDirectStartTime(directStartTime);
            adGroup.setDirectEndTime(directStartTime.plusHours(1L));
        }
        adGroup.setTestDirect(testDirect);
        this.updateById(adGroup);
        return "推广组直达时间已更新";
    }

    /**
     * 已删除和投放结束的推广组不能修改测试直达状态
     */
    private void updateDirectTimePreCheck(AdGroup adGroup) {
        batchCheckGroupOperatePermissions(Collections.singletonList(adGroup));
        if (adGroup.getStatus().equals(DELIVER_FINISH.getCode()) || adGroup.getStatus().equals(DELETED.getCode())) {
            throw new CustomException(INVALID_PARAMETERS, "资源状态操作错误");
        }
    }

    /**
     * 获取推广计划中第一次开始投放的时间
     * @param adPlan
     * @return
     */
    private LocalDateTime getFirstDeliverTime(AdPlan adPlan) {
        List<AdDeliveryTime> adDeliveryTimes = adPlanService.getAdDeliveryTimes(adPlan.getId(),
                adPlan.getTimeOrientation(),
                adPlan.getAdDeliveryInterval(),
                adPlan.getTimeDest(),
                adPlan.getAdOpenDate());
        return adDeliveryTimes.stream().map(AdDeliveryTime::getStartTime).sorted().findFirst().orElse(LocalDateTime.now());
    }

    /**
     * 校验批量操作中，是否有推广组无操作权限
     * @param adGroupList
     */
    private void batchCheckGroupOperatePermissions(List<AdGroup> adGroupList) {
        if (!SecurityUtil.isAdmin()) {
            List<AdPlan> adPlans = adPlanService.listByIds(adGroupList.stream().map(AdGroup::getAdPlanId).collect(Collectors.toList()));
            // 若用户非此推广计划的创建者，且此角色对此计划也无权限，则返回权限错误
            if (adPlans.stream().anyMatch(plan -> !plan.getCreator().equals(SecurityUtil.getUserId()) || !plan.getRoleId().equals(SecurityUtil.getCurrentRole().getId()))) {
                throw new CustomException(ACCESS_DENIED);
            }
        }
    }

    private boolean isUpdate(Long id) {
        return Objects.nonNull(id);
    }

    private boolean isNew(Long id) {
        return Objects.isNull(id);
    }
}

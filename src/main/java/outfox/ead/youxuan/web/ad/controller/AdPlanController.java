package outfox.ead.youxuan.web.ad.controller;

import com.github.rholder.retry.RetryException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.AccessControl;
import outfox.ead.youxuan.core.annotation.BaseResponse;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.AdPlanService;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.concurrent.ExecutionException;

import static outfox.ead.youxuan.constants.ResponseType.NAME_REPEATED_EXCEPTION;

/**
 * <AUTHOR>  <EMAIL>
 * @version 1.0
 * @date 2021/8/18 20:59
 */
@RestController
@BaseResponse
@AllArgsConstructor
@Validated
@Api(tags = "推广计划")
@RequestMapping("/ad_plan")
@AccessControl(roles = RoleEnum.AD_OPERATOR)
public class AdPlanController {
    private final AdPlanService adPlanService;

    /**
     * 通过id查询推广计划，用于复制以及编辑操作
     *
     * @param id 推广计划主键
     * @return 推广计划详细信息
     */
    @ApiOperation("根据id获取详情用于修改")
    @GetMapping("/{id}")
    public AdPlanByIdVO getAdPlan(@PathVariable("id")
                                  @NotNull(message = "id不能为空") Long id) {
        return adPlanService.getAdPlanVoById(id);
    }

    /**
     * 条件分页查询推广计划
     *
     * @param adPlan 支持id精确查询，推广计划名称模糊查询，客户名称模糊查询，投放时间范围筛选，多状态查询，多推广标查询，多计费方式查询，多投放方式查询
     * @return 推广计划列表信息
     */
    @GetMapping
    @ApiOperation("分页列表查询")
    public PageVO<AdPlanListVO> getAdPlanPageList(AdPlanCriteriaQueryVO adPlan) {
        return adPlanService.pageList(adPlan);
    }

    /**
     * 插入/修改推广计划
     *
     * @param adPlan 推广计划
     */
    @ApiOperation("保存修改推广计划")
    @PostMapping
    public Long saveOrUpdateAdPlan(@RequestBody @Valid AdPlanSaveOrUpdateVO adPlan) {
        return adPlanService.saveOrUpdate(adPlan);
    }

    /**
     * 批量修改状态
     */
    @ApiOperation("批量修改状态")
    @PutMapping("/batch_update_status")
    public String batchUpdateStatus(@RequestBody @Valid BatchUpdateStatusVO batchUpdateStatusVO) {
        return adPlanService.batchUpdateStatusById(batchUpdateStatusVO.getIds(), batchUpdateStatusVO.getStatus());
    }

    /**
     * 校验推广计划名字是否重复
     *
     * @param name 推广计划名字
     */
    @ApiOperation("名字重复校验")
    @GetMapping("/name_repeat")
    public void repeatAdPlanName(@NotBlank @ApiParam("推广计划名字") String name, @RequestParam(required = false) Long id) {
        if (adPlanService.nameRepeat(name, id)) {
            throw new CustomException(NAME_REPEATED_EXCEPTION, "推广计划名字重复");
        }
    }

    @GetMapping("/id_to_name")
    public List<IdToNameVO> idToNameList(@RequestParam String name) {
        return adPlanService.listIdToName(name);
    }

    /**
     * 该接口只能找超管账户进行使用
     */
    @ApiOperation("excel批量建广告")
    @PostMapping("batch")
    public void batchCreateAdPlanAndGroup(@RequestParam Long sourceAdGroupId, @RequestPart("file") MultipartFile file) throws ExecutionException, RetryException {
        adPlanService.batchCreateAdPlanAndAdGroup(sourceAdGroupId, file);
    }
}

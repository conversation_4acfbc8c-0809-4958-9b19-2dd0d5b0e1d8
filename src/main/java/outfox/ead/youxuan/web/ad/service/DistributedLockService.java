
package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.core.annotation.DistributedLock;

import java.util.concurrent.TimeUnit;

/**
* <AUTHOR>
* @description 针对表【DistributedLock】的数据库操作Service
* @createDate 2022-07-19 17:31:56
*/
public interface DistributedLockService {

    /**
     * 可重入互斥锁 加锁
     * @param lockKey -
     */
    void mutexLock(String lockKey);
    /**
     * 可重入互斥锁 加锁
     * @param lockKey -
     * @param time 获取锁等待最大时间
     * @param timeUnit 时间单位
     */
    void mutexLock(String lockKey, Integer time, TimeUnit timeUnit);

    /**
     * 解锁
     * @param lockKey -
     */
    void unlock(String lockKey);

    /**
     * 读写锁加锁
     * @param lockKey -
     * @param lockType {@link DistributedLock}
     */
    void readWriteLock(String lockKey, Integer lockType);

    /**
     * lock
     * @param lockKey -
     * @param time -
     * @param timeUnit -
     * @param lockType {@link DistributedLock}
     */
    void readWriteLock(String lockKey, Integer time, TimeUnit timeUnit, Integer lockType);

    /**
     * unlock
     * @param lockKey -
     * @param lockType - {@link DistributedLock}
     */
    void readWriteUnLock(String lockKey, Integer lockType);
}

package outfox.ead.youxuan.web.ad.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import outfox.ead.youxuan.web.ad.controller.vo.AdSensitivityCreateVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdSensitivityDetailVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdSensitivityQueryVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdSensitivityUpdateVO;
import outfox.ead.youxuan.web.ad.controller.vo.AdSensitivitySupportPositionVO;

import java.util.List;

/**
 * 开屏灵敏度配置 Service 接口
 *
 * <AUTHOR>
 * @create 2024-03-21
 **/
public interface AdSensitivityService {
    
    /**
     * 新增开屏灵敏度配置
     *
     * @param adSensitivityCreateVO 创建参数
     */
    void saveAdSensitivity(AdSensitivityCreateVO adSensitivityCreateVO);

    /**
     * 删除开屏灵敏度配置
     *
     * @param ids 配置ID列表
     */
    void deleteAdSensitivity(List<Long> ids);

    /**
     * 修改开屏灵敏度配置
     *
     * @param adSensitivityUpdateVO 更新参数
     */
    void updateAdSensitivity(AdSensitivityUpdateVO adSensitivityUpdateVO);

    /**
     * 查询开屏灵敏度配置详情
     *
     * @param id 配置ID
     * @return 配置详情
     */
    AdSensitivityDetailVO getAdSensitivityDetail(Long id);

    /**
     * 分页查询开屏灵敏度配置
     *
     * @param adSensitivityQueryVO 查询参数
     * @return 分页结果
     */
    IPage<AdSensitivityDetailVO> getAdSensitivityList(AdSensitivityQueryVO adSensitivityQueryVO);

    /**
     * 获取广告位列表
     *
     * @return 广告位列表
     */
    List<AdSensitivitySupportPositionVO> getAdSensitivitySupportPositions();
} 
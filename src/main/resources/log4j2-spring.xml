<?xml version="1.0" encoding="UTF-8"?>
<!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出-->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数-->
<configuration status="WARN" monitorInterval="30">
    <!-- 配置日志文件输出目录，此配置将日志输出到tomcat根目录下的指定文件夹 -->
    <properties>
        <property name="LOG_HOME">logs</property>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} %t %-5level %logger{80} %L - %msg%n</Property>
        <Property name="LOG_CONSOLE_PATTERN">
            [%d] %highlight{%-5level}{ERROR=Bright RED, WARN=Bright Yellow, INFO=Bright Green, DEBUG=Bright Cyan, TRACE=Bright White} [%t] %style{---}{bright,cyan} %c{1.}.%M:%L %style{-}{Bright,cyan}%m%n
        </Property>
    </properties>
    <!--先定义所有的appender-->
    <appenders>
        <!-- 优先级从高到低分别是 OFF、FATAL、ERROR、WARN、INFO、DEBUG、ALL -->
        <!-- 单词解释： Match：匹配 DENY：拒绝 Mismatch：不匹配 ACCEPT：接受 -->
        <!-- DENY，日志将立即被抛弃不再经过其他过滤器； NEUTRAL，有序列表里的下个过滤器过接着处理日志； ACCEPT，日志会被立即处理，不再经过剩余过滤器。 -->
        <!--输出日志的格式
         %d{yyyy-MM-dd HH:mm:ss, SSS} : 日志生产时间
         %p : 日志输出格式
         %c : logger的名称
         %m : 日志内容，即 logger.info("message")
         %n : 换行符
         %C : Java类名
         %L : 日志输出所在行数
         %M : 日志输出所在方法名
         hostName : 本地机器名
         hostAddress : 本地ip地址 -->
        <!--这个输出控制台的配置-->
        <console name="Console" target="SYSTEM_OUT">
            <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            <!--输出日志的格式-->
            <PatternLayout pattern="${LOG_CONSOLE_PATTERN}"/>
        </console>
        <!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
        <!-- TRACE级别日志 ; 设置日志格式并配置日志压缩格式，压缩文件独立放在一个文件夹内， 日期格式不能为冒号，否则无法生成，因为文件名不允许有冒号，此appender只输出trace级别的数据到trace.log -->
        <!--        <RollingFile name="RollingFileTrace" immediateFlush="true" fileName="${LOG_HOME}/trace.log"-->
        <!--                     filePattern="${LOG_HOME}/trace_%d{yyyy-MM-dd-HH}-%i.log.zip">-->
        <!--            <ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>-->
        <!--            <PatternLayout pattern="${LOG_PATTERN}"/>-->
        <!--            <Policies>-->
        <!--                <TimeBasedTriggeringPolicy interval="24" modulate="true"/>-->
        <!--                <SizeBasedTriggeringPolicy size="100 MB"/>-->
        <!--            </Policies>-->
        <!--            &lt;!&ndash; DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件，这里设置了20 &ndash;&gt;-->
        <!--            <DefaultRolloverStrategy max="20">-->
        <!--                &lt;!&ndash;这里的age必须和filePattern协调, 后者是精确到HH, 这里就要写成xH, xd就不起作用-->
        <!--                    另外, 数字最好>2, 否则可能造成删除的时候, 最近的文件还处于被占用状态,导致删除不成功!&ndash;&gt;-->
        <!--                <Delete basePath="${LOG_HOME}" maxDepth="2">-->
        <!--                    <IfFileName glob="trace_*.zip"/>-->
        <!--                    &lt;!&ndash; 保存时间与filePattern相同即可 &ndash;&gt;-->
        <!--                    &lt;!&ndash; 如果filePattern为：yyyy-MM-dd-HH:mm:ss, age也可以为5s,表示日志存活时间为5s &ndash;&gt;-->
        <!--                    <IfLastModified age="168H"/>-->
        <!--                </Delete>-->
        <!--            </DefaultRolloverStrategy>-->
        <!--        </RollingFile>-->

        <!--        <RollingFile name="RollingFileDebug" immediateFlush="true" fileName="${LOG_HOME}/debug.log"-->
        <!--                     filePattern="${LOG_HOME}/debug_%d{yyyy-MM-dd-HH}-%i.log.zip">-->
        <!--            <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>-->
        <!--            <PatternLayout pattern="${LOG_PATTERN}"/>-->
        <!--            <Policies>-->
        <!--                <TimeBasedTriggeringPolicy interval="24" modulate="true"/>-->
        <!--                <SizeBasedTriggeringPolicy size="100 MB"/>-->
        <!--            </Policies>-->
        <!--            <DefaultRolloverStrategy max="20">-->
        <!--                <Delete basePath="${LOG_HOME}" maxDepth="2">-->
        <!--                    <IfFileName glob="debug_*.zip"/>-->
        <!--                    <IfLastModified age="168H"/>-->
        <!--                </Delete>-->
        <!--            </DefaultRolloverStrategy>-->
        <!--        </RollingFile>-->

        <!-- info日志配置 -->
        <RollingFile name="RollingFileInfo" immediateFlush="true" fileName="${LOG_HOME}/info.log"
                     filePattern="${LOG_HOME}/info_%d{yyyy-MM-dd-HH}-%i.log.zip">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="24" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="20">
                <Delete basePath="${LOG_HOME}" maxDepth="2">
                    <IfFileName glob="info_*.zip">
                        <IfAccumulatedFileSize exceeds="1 GB"/>
                        <IfAccumulatedFileCount exceeds="7"/>
                    </IfFileName>
                    <IfLastModified age="168H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!-- warn日志配置 -->
        <RollingFile name="RollingFileWarn" immediateFlush="true" fileName="${LOG_HOME}/warn.log"
                     filePattern="${LOG_HOME}/warn_%d{yyyy-MM-dd-HH}-%i.log.zip">
            <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="24" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="20">
                <Delete basePath="${LOG_HOME}" maxDepth="2">
                    <IfFileName glob="warn_*.zip">
                        <IfAccumulatedFileSize exceeds="1 GB"/>
                        <IfAccumulatedFileCount exceeds="7"/>
                    </IfFileName>
                    <IfLastModified age="168H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!-- error日志配置 -->
        <RollingFile name="RollingFileError" immediateFlush="true" fileName="${LOG_HOME}/error.log"
                     filePattern="${LOG_HOME}/error_%d{yyyy-MM-dd-HH}-%i.log.zip">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="24" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="20">
                <Delete basePath="${LOG_HOME}" maxDepth="2">
                    <IfFileName glob="error_*.zip">
                        <IfAccumulatedFileSize exceeds="1 GB"/>
                        <IfAccumulatedFileCount exceeds="7"/>
                    </IfFileName>
                    <IfLastModified age="168H"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </appenders>
    <!--然后定义logger，只有定义了logger并引入的appender，appender才会生效-->
    <loggers>
        <root level="INFO">
            <!--            <appender-ref ref="Console"/>-->
            <!--            <appender-ref ref="RollingFileDebug"/>-->
            <!--            <appender-ref ref="RollingFileTrace"/>-->
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
        </root>
    </loggers>
</configuration>

-- noinspection SqlDialectInspectionForFile

SET NAMES utf8mb4;
SET
    FOREIGN_KEY_CHECKS = 0;

DROP
    DATABASE IF EXISTS `youxuan`;
CREATE
    DATABASE youxuan;
use
    youxuan;
-- ----------------------------
-- Table structure for AdContent
-- ----------------------------
DROP TABLE IF EXISTS `AdContent`;
CREATE TABLE `AdContent`
(
    `ID`                      bigint(20)                                                     NOT NULL AUTO_INCREMENT,
    `AD_GROUP_ID`             bigint(20)                                                     NOT NULL COMMENT '推广组主键',
    `TYPE`                    tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '创意内容类型',
    `TEXT_CREATIVE_CONTENTS`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL,
    `IMAGE_CREATIVE_CONTENTS` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          NULL,
    `VIDEO_CREATIVE_CONTENTS` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          NULL,
    `SHOW_TIME`               int(11)                                                        NULL     DEFAULT NULL COMMENT '展示时长',
    `TEST_TAG`                tinyint(4)                                                     NULL     DEFAULT 0 COMMENT '测试字段 0-不是 1-是',
    `CREATE_TIME`             timestamp                                                      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `LAST_MOD_TIME`           timestamp                                                      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `CREATOR`                 bigint(20)                                                     NOT NULL COMMENT '创建人',
    `MODIFIER`                bigint(20)                                                     NOT NULL COMMENT '修改人',
    `STATUS`                  tinyint                                                                 default 0 not null comment '0-有效，1-删除',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 300000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for AdContentRelation
-- ----------------------------
DROP TABLE IF EXISTS `AdContentRelation`;
CREATE TABLE `AdContentRelation`
(
    `ID`                  bigint(20)   NOT NULL AUTO_INCREMENT,
    `AD_GROUP_ID`         bigint(20)   NOT NULL DEFAULT 0 COMMENT '推广组id',
    `STYLE_ID`            bigint(20)   NOT NULL DEFAULT 0 COMMENT '样式id',
    `DISPLAY_WEIGHT`      int(11)      NULL     DEFAULT NULL COMMENT '购买轮播数',
    `STATUS`              tinyint               default 0 not null comment '0-有效，1-删除',
    DICT_UID              varchar(255) null comment '词典虚拟账户id',
    DICT_POST_ID          varchar(255) null comment '词典内容id',
    HISTORY_DICT_POST_IDS text         null comment '词典内容历史id',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 300000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for AdDeliveryTime
-- ----------------------------
DROP TABLE IF EXISTS `AdDeliveryTime`;
CREATE TABLE `AdDeliveryTime`
(
    `ID`         bigint(20) NOT NULL AUTO_INCREMENT,
    `AD_PLAN_ID` bigint(20) NOT NULL DEFAULT 0,
    `START_TIME` timestamp  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '开始时间',
    `END_TIME`   timestamp  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结束时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for AdGroup
-- ----------------------------
DROP TABLE IF EXISTS `AdGroup`;
CREATE TABLE `AdGroup`
(
    `ID`                  bigint(20)                                                     NOT NULL AUTO_INCREMENT,
    `AD_PLAN_ID`          bigint(20)                                                     NOT NULL COMMENT '推广计划主键',
    `NAME`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '推广组名称',
    `STATUS`              tinyint(4)                                                     NOT NULL COMMENT '状态，0-投放中、1-投放结束、2-即将开启、3-暂停、4-未暂停、5-已删除、6-未删除',
    `CPM_PRICE`           int(11)                                                        NULL     DEFAULT NULL COMMENT 'cpm 出价',
    `SUM_DISPLAY_COUNT`   int(11)                                                        NULL     DEFAULT NULL COMMENT '总展示量',
    `DAILY_DISPLAY_LIMIT` int(11)                                                        NULL     DEFAULT NULL COMMENT '日展示量上限',
    `DELIVERY_SPEED`      tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '投放速度,0代表快速，1代表均匀',
    `DEAL_ID`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '订单id',
    `DEAL_REMARK`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '订单备注',
    `DSP_ID`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT 'dsp id',
    `BASE_PADDING`        tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '打底广告，0不填充，1填充',
    `BOOT_FIRST_REFRESH`  tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '开机首刷，0不开启，1开启',
    `OPEN_SCREEN_RECYCLE` tinyint(4)                                                     NULL     DEFAULT NULL COMMENT '开屏回收，0不开启，1开启',
    `BRAND_CLK_TYPE`      int(11)                                                        NULL     DEFAULT NULL,
    `LANDING_PAGE_LINK`   varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '落地页链接',
    `DEEPLINK_URL`        varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '深链接',
    `WECHAT_APP_ID`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '小程序APPID',
    `WECHAT_ORIGINAL_ID`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '小程序原始id',
    `WECHAT_PATH`         TEXT COMMENT '目标路径',
    `EXPO_DETE_LINK`      varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '曝光监测链接',
    `CLICK_DETE_LINK`     varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '点击监测链接',
    `CREATE_TIME`         timestamp                                                      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `LAST_MOD_TIME`       timestamp                                                      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `CREATOR`             bigint(20)                                                     NOT NULL COMMENT '创建人',
    `MODIFIER`            bigint(20)                                                     NOT NULL COMMENT '修改人',
    `YOUTH_MODE`          tinyint(4)                                                              default 0 null comment '青少年模式，0-否，1-是',
    GENDER_ORIENTATION    tinyint                                                                 default 0 null comment '性别定向 0-无 1-男 2-女',
    `ROLE_ID`             bigint                                                         not null comment '角色id',
    MUST_DEVICE_ID        tinyint(1)                                                              default 0 null comment '无设备号过滤 0-false 1-true',
    SHAKABLE              tinyint(1)                                                              default 0 null comment '是否支持摇一摇 0-否 1-是',
    ORIENTATION_MODE      int                                                                     default 0 not null comment '定向模式 0-无 1-扩展人群定向 2-精准人群定向 ',

    /**
      补充年龄定向需求新增字段
     */
    AGE_ORIENTATION      tinyint(8)                                                     null comment '年龄定向 bitmap 0 0-18 1 19-24 2 25-29 3 30-39 4 40-49 5 50以上',
    CROWD_LABEL          text                                                           null comment '人群标签',
    SLIDE_INTERACT       tinyint                                                                 default 0 not null comment '滑动交互 0-否，1-是',
    LANDING_PAGE_LINK1   text                                                           null comment '落地页链接1',
    DEEPLINK_URL1        text                                                           null comment '深链接1',
    WECHAT_ORIGINAL_ID1  varchar(255)                                                   null comment '小程序原始id1',
    WECHAT_PATH1         text                                                           null comment '目标路径1',
    LANDING_PAGE_LINK2   text                                                           null comment '落地页链接2',
    DEEPLINK_URL2        text                                                           null comment '深链接2',
    WECHAT_ORIGINAL_ID2  varchar(255)                                                   null comment '小程序原始id2',
    WECHAT_PATH2         text                                                           null comment '目标路径2',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 300000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for AdPlan
-- ----------------------------
DROP TABLE IF EXISTS `AdPlan`;
CREATE TABLE `AdPlan`
(
    `ID`                          bigint(20)                                                     NOT NULL AUTO_INCREMENT COMMENT '推广计划主键',
    `PROMOTION_TARGET`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '推广目标',
    `NAME`                        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '推广计划名称',
    `CUSTOMER_ID`                 bigint(20)                                                     NOT NULL COMMENT '客户ID',
    `BILLING_TYPE`                tinyint(4)                                                     NOT NULL COMMENT '计费模式',
    `DELIVERY_TYPE`               tinyint(4)                                                     NOT NULL COMMENT '投放类型',
    `DATE_CONTINUOUS`             tinyint(4)                                                     NOT NULL COMMENT '投放日期是否连续 0-连续 1-不连续',
    `AD_DELIVERY_DATE`            varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '投放时间',
    `AD_OPEN_DATE`                timestamp                                                      NULL     DEFAULT '0000-00-00 00:00:00' COMMENT '投放开始时间',
    `AD_CLOSE_DATE`               timestamp                                                      NULL     DEFAULT '0000-00-00 00:00:00' COMMENT '投放结束时间',
    `TIME_ORIENTATION`            tinyint(4)                                                     NOT NULL COMMENT '投放时段 0-不限 1-指定时段',
    `TIME_DEST`                   varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '投放时段',
    `REGIONAL_ORIENTATION`        tinyint(4)                                                     NOT NULL COMMENT '地域定向 0-不限 1-按区域',
    `INLAND_REGIONAL_DEST`        varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '国内投放目标地址',
    `INTERNATIONAL_REGIONAL_DEST` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '国际投放目标地址',
    `STATUS`                      tinyint(4)                                                     NOT NULL DEFAULT -1 COMMENT '计划状态，0-投放中、1-投放结束、2-即将开启、3-暂停、4-未暂停、5-已删除、6-未删除',
    `CREATE_TIME`                 timestamp                                                      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `LAST_MOD_TIME`               timestamp                                                      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `CREATOR`                     bigint(20)                                                     NOT NULL COMMENT '创建人',
    `MODIFIER`                    bigint(20)                                                     NOT NULL COMMENT '修改人',
    `ROLE_ID`                     bigint                                                         not null comment '角色id',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 300000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for AdPosition
-- ----------------------------
DROP TABLE IF EXISTS `AdPosition`;
CREATE TABLE `AdPosition`
(
    `ID`                            bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '广告位主键',
    `MEDIA_ID`                      bigint(20)                                                    NOT NULL COMMENT '媒体ID',
    `NAME`                          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '广告位名称',
    `TYPE`                          tinyint(4)                                                    NOT NULL COMMENT '广告位类型 0-信息流 1-开屏 2-插屏 3-焦点图 4-激励视频 5-横幅 6-自定义',
    `DISPLAY_TIMES`                 tinyint(4)                                                    NOT NULL DEFAULT 0 COMMENT '支持轮播图片数量',
    `PROMOTION_TYPE`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '推广标类型 1-落地页推广 2-应用直达 3-小程序推广',
    `OPEN_SCREEN_RECYCLING`         tinyint(4)                                                    NOT NULL DEFAULT 0 COMMENT '开屏回收 0-不支持 1-支持',
    `BOOT_FIRST_REFRESH`            tinyint(4)                                                    NOT NULL DEFAULT 0 COMMENT '开机首刷 0-不支持 1-支持',
    `RECEIVE_OPEN_SCREEN_RECYCLING` tinyint(4)                                                    NOT NULL default 0 COMMENT '是否接受开屏广告 0-否 1-是',
    SUPPORT_ADVERTISING_BY_KEYWORDS tinyint(4)                                                    NOT NULL default 0 COMMENT '是否支持查词定向 0-不支持 1-支持',
    `CREATE_TIME`                   timestamp                                                     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `LAST_MOD_TIME`                 timestamp                                                     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `CREATOR`                       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
    `MODIFIER`                      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '修改人',
    `STATUS`                        int(11)                                                       NOT NULL COMMENT '状态,0-有效，1-暂停,2-已删除',
    HAS_SHAKABLE            tinyint(1) default 0                     null comment '是否支持摇一摇 0-否 1-是',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 300000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for BookingInfo
-- ----------------------------
DROP TABLE IF EXISTS `BookingInfo`;
CREATE TABLE `BookingInfo`
(
    `ID`           bigint(20)                                                     NOT NULL AUTO_INCREMENT,
    `COMPANY_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '企业名称',
    `NAME`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '名字',
    `PHONE_NUM`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '手机号',
    `CITY`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '地区',
    `INDUSTRY`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '行业',
    `DESCRIPTION`  varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Customer
-- ----------------------------
DROP TABLE IF EXISTS `Customer`;
CREATE TABLE `Customer`
(
    `ID`            bigint(20)                                                    NOT NULL,
    `NAME`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `CREATE_TIME`   timestamp                                                     NOT NULL DEFAULT '0000-00-00 00:00:00',
    `LAST_MOD_TIME` timestamp                                                     NOT NULL DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Element
-- ----------------------------
DROP TABLE IF EXISTS `Element`;
CREATE TABLE `Element`
(
    `ID`            bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '样式元素主键，自增',
    `NAME`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '样式元素名称,用于开发者和发布系统前端展示',
    `ELEMENT_KEY`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL COMMENT '样式元素key，用于解析',
    `LENGTH`        int(11)                                                       NULL     DEFAULT NULL COMMENT '文字样式元素元素长度',
    `HEIGHT`        int(11)                                                       NULL     DEFAULT NULL COMMENT '图片样式元素高度',
    `WIDTH`         int(11)                                                       NULL     DEFAULT NULL COMMENT '图片样式元素宽度',
    `RATIO`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '图片比例',
    `MIME_TYPE`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '媒体格式',
    `TYPE`          tinyint(4)                                                    NOT NULL COMMENT '样式元素类型，1:文字;2:图片;3:视频',
    `STATUS`        int(11)                                                       NOT NULL COMMENT '样式元素状态，0：无效（可扩展）1：有效',
    `CREATE_TIME`   timestamp                                                     NOT NULL DEFAULT '0000-00-00 00:00:00',
    `LAST_MOD_TIME` timestamp                                                     NOT NULL DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 300000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Media
-- ----------------------------
DROP TABLE IF EXISTS `Media`;
CREATE TABLE `Media`
(
    `ID`            bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '媒体主键',
    `NAME`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '媒体名称',
    `OS_TYPE`       int(11)                                                       NOT NULL COMMENT '平台',
    `NOTE`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '备注',
    `STATUS`        int(11)                                                       NOT NULL COMMENT '状态,0-有效，1-暂停，2-已删除',
    `CREATE_TIME`   timestamp                                                     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `LAST_MOD_TIME` timestamp                                                     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `CREATOR`       bigint(20)                                                    NOT NULL COMMENT '创建人',
    `MODIFIER`      bigint(20)                                                    NOT NULL COMMENT '修改人',
    `YOUTH_MODE`    tinyint(4)                                                             default 0 null comment '青少年模式，0-否，1-是',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 300000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Style
-- ----------------------------
DROP TABLE IF EXISTS `Style`;
CREATE TABLE `Style`
(
    `ID`                            bigint(20)                                                     NOT NULL AUTO_INCREMENT COMMENT '样式主键',
    `AD_POSITION_ID`                bigint(20)                                                     NOT NULL COMMENT '广告位ID',
    `NAME`                          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '样式名称',
    `MIN_VERSION_SUPPORTED`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '可兼容的最低版本号',
    `SHOW_TIME`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '自定义展示时间，单位s',
    `TEXT_STYLE_CONTENT`            varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文本样式元素',
    `PIC_STYLE_CONTENT`             varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片样式元素',
    `VIDEO_STYLE_CONTENT`           varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '视频样式元素',
    `STATUS`                        tinyint(4)                                                     NOT NULL COMMENT '状态,0-有效，1-暂停，2-已删除',
    `CREATE_TIME`                   timestamp                                                      NOT NULL DEFAULT '0000-00-00 00:00:00',
    `LAST_MOD_TIME`                 timestamp                                                      NOT NULL DEFAULT '0000-00-00 00:00:00',
    `CREATOR`                       bigint(20)                                                     NOT NULL COMMENT '创建人',
    `MODIFIER`                      bigint(20)                                                     NOT NULL COMMENT '修改人',
    `STYLE_TYPE`                    tinyint(4)                                                     NULL DEFAULT 0 COMMENT '样式类型，0-无，1，原生开屏样式，2，摇一摇样式',
    `FULL_SCREEN`                   tinyint    default 0                                           null comment '是否全屏，0-否，1-是',
    `OPEN_SCREEN_RECYCLING`         tinyint    default 0                                           null comment '开屏回收 0-不支持 1-支持',
    `BOOT_FIRST_REFRESH`            tinyint    default 0                                           null comment '开机首刷 0-不支持 1-支持',
    `RECEIVE_OPEN_SCREEN_RECYCLING` tinyint    default 0                                           null comment '是否接受开屏广告 0-否 1-是',
    DICT_VIDEO_POST                 tinyint(1) default 0                                           null comment '词典视屏流',
    SHAKABLE                        tinyint(1) default 0                                           null comment '是否开启摇一摇 0-否 1-是',
    SLIDE_INTERACT                  tinyint    default 0                                           not null comment '支持滑动交互 0-否，1-是',
    DOUBLE_LINK                     tinyint    default 0                                           not null comment '支持双link 0-否，1-是',
    THREE_LINK                      tinyint    default 0                                           not null comment '支持三link 0-否，1-是',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 300000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for User
-- ----------------------------
DROP TABLE IF EXISTS `User`;
CREATE TABLE `User`
(
    `ID`            bigint(20)                                                    NOT NULL AUTO_INCREMENT,
    `USERNAME`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `DICT_UID`      varchar(255)                                                  null comment '词典uid',
    `STATUS`        tinyint                                                                default 1 not null,
    `CREATE_TIME`   timestamp                                                     NOT NULL DEFAULT '0000-00-00 00:00:00',
    `LAST_MOD_TIME` timestamp                                                     NOT NULL DEFAULT '0000-00-00 00:00:00',
    constraint User_DICT_UID_uindex
        unique (DICT_UID),
    constraint User_USERNAME_uindex
        unique (USERNAME),
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 300000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

create table Role
(
    ID            bigint auto_increment
        primary key,
    NAME          varchar(255)                        null comment '角色名称',
    ROLE_KEY      varchar(255)                        not null comment '角色唯一标识',
    STATUS        int       default 0                 not null comment '状态 0-正常，1-暂停，2-删除',
    CREATOR       bigint                              not null comment '创建人',
    MODIFIER      bigint                              not null comment '修改人',
    CREATE_TIME   timestamp default CURRENT_TIMESTAMP not null,
    LAST_MOD_TIME timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    constraint Role_ROLE_KEY_uindex
        unique (ROLE_KEY)
) comment '角色表';

create table UserRoleRelation
(
    ID            bigint auto_increment
        primary key,
    USER_ID       bigint                              not null comment '账户id',
    ROLE_ID       bigint                              not null comment '角色id',
    STATUS        int       default 0                 not null comment '状态 0-正常，1-暂停，2-删除',
    CREATOR       bigint                              not null comment '创建人',
    MODIFIER      bigint                              not null comment '修改人',
    CREATE_TIME   timestamp default CURRENT_TIMESTAMP not null,
    LAST_MOD_TIME timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
) comment '用户角色绑定表';

create table UserDetail
(
    ID                   bigint auto_increment
        primary key,
    AVATAR               text                                 null comment '头像',
    USER_ID              bigint                               null comment '账户id',
    NICKNAME             varchar(255)                         null comment '昵称',
    NAME                 varchar(20)                          null comment '联系人姓名',
    PHONE                varchar(11)                          null comment '联系人电话',
    EMAIL                varchar(50)                          null comment '联系人邮箱',
    COMPANY_NAME         varchar(50)                          null comment '公司名称',
    VERIFIED             tinyint    default 0                 null comment '广告主和机构创作者是否资质认证通过 0-否 1-是',
    ROLE_ID              int                                  null comment '角色',
    INDUSTRY             int                                  null comment '行业信息',
    STAGE                int        default 0                 null comment '表示角色信息填充处在第几阶段',
    STATUS               int        default 0                 not null comment '状态 0-正常，1-暂停，2-删除',
    TASK_PERMISSION      tinyint(1)                           not null default 0 comment '指派任务权限 0-否 1-是',
    POST_TASK_PERMISSION tinyint(1) default 0                 not null comment '投稿任务权限 0-否 1-是',
    CREATOR              bigint                               not null comment '创建人',
    MODIFIER             bigint                               not null comment '修改人',
    CREATE_TIME          timestamp  default CURRENT_TIMESTAMP not null,
    LAST_MOD_TIME        timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
) comment '用户详细信息';

-- ----------------------------
-- Table structure for AdvertisingByKeywordsRule
-- ----------------------------
DROP TABLE IF EXISTS `AdvertisingByKeywordsRule`;
CREATE TABLE `AdvertisingByKeywordsRule`
(
    `ID`            bigint(20)                              NOT NULL AUTO_INCREMENT comment '主键',
    `AD_GROUP_ID`   bigint(20)                              NOT NULL comment '广告组ID',
    `KEYWORD_LIST`  text                                    NOT NULL comment '定向词汇列表',
    `CREATE_TIME`   timestamp default '0000-00-00 00:00:00' NOT NULL comment '创建时间',
    `LAST_MOD_TIME` timestamp default '0000-00-00 00:00:00' NOT NULL comment '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 300000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

create table UserRelation
(
    ID            bigint auto_increment
        primary key,
    USER_ID       bigint                              not null comment '账户id',
    BOUND_USER_ID bigint                              not null comment '被绑定账户id',
    STATUS        int       default 0                 not null comment '状态 0-正常，1-暂停，2-删除',
    CREATOR       bigint                              not null comment '创建人',
    MODIFIER      bigint                              not null comment '修改人',
    CREATE_TIME   timestamp default CURRENT_TIMESTAMP not null,
    LAST_MOD_TIME timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
) comment '用户单向绑定表';

create table AppAccount
(
    ID                        bigint auto_increment
        primary key,
    APP_USER_ID               varchar(200)                         not null comment '媒体账户id',
    PLATFORM_ID               bigint                               not null comment '媒体平台id',
    NAME                      varchar(255)                         not null comment '媒体账户昵称',
    AVATAR                    text                                 null comment '媒体账户头像',
    GENDER                    tinyint unsigned                     null comment '性别，0-男 1-女',
    FANS_NUM                  bigint                               null comment '粉丝数',
    AREA                      int                                  null comment '地区',
    USER_ID                   bigint                               not null comment '用户id',
    IN_SERVICE                tinyint(1) default 0                 not null comment '是否在接单：0-否 1-是',
    PRODUCT_WINDOW_PERMISSION tinyint(1) default 0                 null comment '商品橱窗权限',
    STATUS                    int        default 0                 not null comment '状态 0-正常，1-暂停，2-删除',
    CREATOR                   bigint                               not null comment '创建人',
    MODIFIER                  bigint                               not null comment '修改人',
    CREATE_TIME               timestamp  default CURRENT_TIMESTAMP not null,
    LAST_MOD_TIME             timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    MCN                       varchar(255)                         null comment '所属mcn',
    KOL_OPERATOR_USER_ID      bigint                               null comment '所属运营用户id',
    HOMEPAGE_LINK             text                                 null comment '主页链接',
    NOTIFY_SCHEMA_UPGRADE     tinyint    default 1                 not null comment '提示样式迁移（目前只有词典需要用这个字段）？0-不提示，1-提示'
) comment '媒体账户';

create table Platform
(
    ID            bigint auto_increment
        primary key,
    NAME          varchar(32)                         not null,
    ICON          text                                null comment '媒体logo',
    BIND_TYPE     tinyint   default 0                 not null comment '绑定类型 0-不验证 1-需要验证',
    VERIFY_URL    varchar(255)                        null comment '验证URL',
    USER_INFO_URL varchar(255)                        null,
    CREATOR       bigint                              not null,
    CREATE_TIME   timestamp default CURRENT_TIMESTAMP not null,
    MODIFIER      bigint                              not null,
    LAST_MOD_TIME timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
) comment '平台信息';

create table ContentTag
(
    ID            bigint auto_increment comment '主键'
        primary key,
    NAME          varchar(16)                         not null comment '标签名称',
    TYPE          tinyint   default 0                 not null comment '标签类型：0-内容标签 1-名师标签',
    STATUS        tinyint   default 0                 not null comment '0-有效 1-无效',
    CREATOR       bigint                              not null,
    CREATE_TIME   timestamp default CURRENT_TIMESTAMP not null,
    MODIFIER      bigint                              not null,
    LAST_MOD_TIME timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
) comment '达人内容标签';

create table KolContentTagRelation
(
    ID             bigint auto_increment
        primary key,
    APP_ACCOUNT_ID bigint                              not null comment '达人ID',
    CONTENT_TAG_ID bigint                              not null comment '内容标签ID',
    CREATOR        bigint                              not null,
    CREATE_TIME    timestamp default CURRENT_TIMESTAMP not null
) comment '达人与内容标签绑定关系';

create table PlatformContent
(
    ID               bigint auto_increment
        primary key,
    PLATFORM_TASK_ID bigint                              not null comment '平台任务ID',
    NAME             varchar(64)                         not null comment '内容类型名字',
    CREATOR          bigint                              not null comment '创建人',
    CREATE_TIME      timestamp default CURRENT_TIMESTAMP not null,
    MODIFIER         bigint                              not null comment '修改人',
    LAST_MOD_TIME    timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
);

create table PlatformTask
(
    ID            bigint auto_increment
        primary key,
    NAME          varchar(64)                         not null comment '任务类型',
    PLATFORM_ID   bigint                              not null comment '平台ID',
    CREATOR       bigint                              not null comment '创建人',
    CREATE_TIME   timestamp default CURRENT_TIMESTAMP not null,
    MODIFIER      bigint                              not null comment '修改人',
    LAST_MOD_TIME timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
);

create table KolPlatformContentRelation
(
    ID                  bigint auto_increment
        primary key,
    APP_ACCOUNT_ID      bigint                              not null comment '达人ID',
    PLATFORM_CONTENT_ID bigint                              not null comment '平台内容类型ID',
    PRICE               bigint                              not null comment '服务报价，以分为单位',
    CREATOR             bigint                              not null,
    CREATE_TIME         timestamp default CURRENT_TIMESTAMP not null,
    MODIFIER            bigint                              not null,
    LAST_MOD_TIME       timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
) comment '达人与平台内容类型绑定关系';

create table ConvModule
(
    ID                                  bigint auto_increment
        primary key,
    ENABLED                             tinyint(1) default 0                 not null comment '启用？0-关闭 1-启用',
    TYPE                                tinyint                              not null comment '0-官方网站；1-应用下载；2-联系电话；3-推广活动',
    APP_ACCOUNT_ID                      bigint                               not null comment '媒体账户ID',
    DISPLAY_NAME                        varchar(32)                          not null comment '显示名称',
    URL                                 text                                 null comment '官方网站和推广活动的链接地址',
    ANDROID_PACKAGE_NAME                varchar(255)                         null comment '安卓应用包名',
    IOS_DOWNLOAD_URL                    text                                 null comment 'iOS下载地址',
    PHONE                               varchar(64)                          null comment '电话；需要支持400或区号开头的电话',
    ENABLED_WECHAT_MICRO_PROGRAM_SWITCH tinyint(1)                           null comment '跳转微信小程序：0-关闭 1-开启',
    WECHAT_MICRO_PROGRAM_RAW_ID         varchar(255)                         null comment '微信小程序原始ID',
    WECHAT_MICRO_PROGRAM_PATH           varchar(255)                         null comment '微信小程序路径',
    CREATOR                             bigint                               not null comment '创建人',
    CREATE_TIME                         timestamp  default CURRENT_TIMESTAMP not null,
    MODIFIER                            bigint                               not null comment '修改人',
    LAST_MOD_TIME                       timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '主页转化工具';

create table PreTaskOrder
(
    ID                  bigint auto_increment
        primary key,
    APP_ACCOUNT_ID      bigint                              null comment '达人ID',
    PLATFORM_CONTENT_ID bigint                              null comment '推广内容类型ID',
    KOL_NAME            varchar(255)                        not null comment '加入已选达人列表时的媒体账户昵称',
    KOL_AVATAR          text                                null comment '加入已选达人列表时的媒体账户头像',
    COMMENT             text                                null comment '备注（仅运营可见）',
    PRICE               bigint                              null comment '加入已选达人列表时的价格，以人民币分为单位',
    CREATOR             bigint                              not null comment '创建人',
    CREATE_TIME         timestamp default CURRENT_TIMESTAMP not null,
    LAST_MOD_TIME       timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    DELETED             tinyint   default 0                 not null comment '0-未删除 1-删掉了'
) comment '已选达人表';

create table TaskOrder
(
    ID                     bigint auto_increment
        primary key,
    PARENT_ORDER_ID        bigint                              null comment '父订单ID',
    PLATFORM_CONTENT_ID    bigint                              null comment '推广内容类型ID',
    APP_ACCOUNT_ID         bigint                              null comment '达人ID',
    COMMENT                text                                null comment '备注（仅运营可见）',
    PRICE                  bigint                              null comment '价格，以人民币分为单位',
    STATUS                 tinyint                             null comment '状态;0待接收、1待付款、2进行中、3已完成、4已取消、5待开启、6已下架、7已结束',
    TYPE                   tinyint   default 1                 not null comment '任务类型；1-指派任务，2-投稿任务',
    NAME                   varchar(32)                         null comment '任务名称',
    SHOWCASE_COMPONENT_ID  bigint                              null comment '橱窗组件ID',
    BEGIN_DATE             date                                null comment '推广开始日期',
    END_DATE               date                                null comment '推广结束日期',
    POST_LIMIT             bigint                              null comment '投稿上限；0为无上限，其他值为上限',
    BILLING_TYPE           tinyint                             null comment '计费类型；0-其他，1-CPM，2-CPC，3-CPA，4-CPS',
    COMMISSION_PRICE       bigint                              null comment '佣金单价，以人民币分为单位',
    SETTLEMENT_INTERVAL    tinyint                             null comment '结算周期;0-其他，1-月结，2-周结，3-日结',
    ASSESSMENT_DESCRIPTION text                                null comment '考核说明',
    CREATOR                bigint                              not null comment '创建人',
    CREATE_TIME            timestamp default CURRENT_TIMESTAMP not null,
    MODIFIER               bigint                              not null comment '修改人',
    LAST_MOD_TIME          timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    WEIGHT                 int                                 null comment '权重',
    AUTO_SHOWCASE          tinyint(1) default 0                null comment '自动挂窗'
)
    comment '任务订单表';

create table ShowcaseComponent
(
    ID                      bigint auto_increment
        primary key,
    COMPONENT_ID            varchar(32)                         not null comment '组件ID，为10位大小写字母组成的字符串',
    APP_ACCOUNT_ID            bigint                              null comment 'AppAccountID',
    SOURCE_ID                 bigint                              null comment '来源橱窗组件ID，广告主创建的组件需要同步到绑定了的品牌号上。',
    NAME                      varchar(64)                         not null comment '组件名称',
    PROMOTION_TYPE            tinyint                             not null comment '推广类型；1-品牌宣传，2-商品售卖，3-线索留资，4-应用推广',
    CATEGORY                  int                                 not null comment '推广品类，复用行业信息类目',
    SWITCH_TYPE               tinyint                             not null comment '跳转类型；1-落地页，2-应用直达，3-微信小程序，4-应用商店',
    DEEP_LINK                 text                                null comment 'deeplink链接',
    LANDING_PAGE_URL          text                                null comment '落地页跳转链接',
    BACKUP_LANDING_PAGE_URL   text                                null comment '备用落地页链接',
    APPEND_OUT_VENDOR         tinyint                             null comment '是否自动为跳转链接添加来源参数配置项',
    MICRO_PROGRAM_ID          varchar(128)                        null comment '微信小程序原始ID',
    MICRO_PROGRAM_PATH        text                                null comment '微信小程序目标页面路径',
    APP_PLATFORM              tinyint                             null comment '应用平台；0-不限，1-Android，2-iOS',
    IOS_APP_ID                text                                null comment 'iOS应用ID',
    SCHEMA_ID                 bigint                              not null comment '样式ID',
    LEAD_TEXT                 varchar(64)                         null comment '引导文案',
    ITEM_PRICE                bigint                              null comment '商品价格',
    STRIKE_THROUGH_PRICE      bigint                              null comment '划线价',
    BUTTON_TEXT               varchar(32)                         null comment '按钮文案',
    PROMOTE_IMAGE_URL         text                                null comment '推广图片的URL',
    PROMOTE_TITLE             varchar(100)                        null comment '推广标题',
    PROMOTE_TEXT              varchar(512)                        null comment '推广文案',
    APP_NAME                  varchar(100)                        null comment '应用名称',
    AUDITED_CONTENT           text                                null comment '已经审核通过的组件信息，以JSON格式存储。',
    AUDIT_DATETIME            timestamp                           null comment '审核时间',
    ITEM_NAME                 varchar(100)                        null comment '商品名称',
    MARK_AS_AD                tinyint   default 1                 not null comment '是否标记为广告；0-否，1是',
    STATUS                    tinyint   default 1                 not null comment '状态；1-审核中（待审核），2-已上架（已通过），3-已下架，4-未通过',
    AUDIT_STATUS              tinyint   default 1                 not null comment '状态；1-审核中（待审核），2-已上架（已通过），3-已下架，4-未通过',
    DELETED                   tinyint   default 0                 not null comment '已删除？0-未删除，1-删掉了',
    CREATOR                   bigint                              not null comment '创建人',
    CREATE_TIME               timestamp default CURRENT_TIMESTAMP not null,
    MODIFIER                  bigint                              not null comment '修改人',
    LAST_MOD_TIME             timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    ANDROID_PACKAGE_NAME      text                                null comment '安卓应用包名',
    AUDITED_PROMOTE_TITLE     varchar(32)                         null comment '审核通过的推广标题；品牌宣传：引导文案；商品售卖：商品名称；线索留资：推广标题；应用推广：应用名称；',
    ORIGINAL_CATEGORY_INFO    text                                null comment '原始品类信息',
    SKU_ID                    bigint                              null comment '京东商品唯一标识',
    ORIGINAL_COMMISSION_PRICE text                                null comment '原始佣金信息',
    constraint ShowcaseComponent_SKU_ID_uindex
        unique (SKU_ID),
    constraint showcaseComponent_COMPONENT_ID_uindex
        unique (COMPONENT_ID)
)
    comment '橱窗组件';


create table ShowcaseComponentWhiteList
(
    ID             bigint auto_increment
        primary key,
    USER_ID     bigint                              not null,
    CREATOR        bigint                              not null comment '创建人',
    CREATE_TIME    timestamp default CURRENT_TIMESTAMP not null,
    MODIFIER       bigint                              not null comment '修改人',
    LAST_MOD_TIME  timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    constraint showcaseComponentWhiteList_USER_ID_uindex
        unique (USER_ID)
)
    comment '橱窗组件特殊功能白名单';

create table ShowcaseComponentSchema
(
    ID             bigint auto_increment
        primary key,
    NAME           varchar(32)                         not null comment '样式名称',
    PROMOTION_TYPE tinyint                             not null comment '推广类型；1-品牌宣传，2-商品售卖，3-线索留资，4-应用推广',
    STATUS         tinyint   default 1                 not null comment '状态；0-可选，1-不可选',
    CREATOR        bigint                              not null comment '创建人',
    CREATE_TIME    timestamp default CURRENT_TIMESTAMP not null,
    MODIFIER       bigint                              not null comment '修改人',
    LAST_MOD_TIME  timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '橱窗组件样式';


-- ----------------------------
-- Table structure for UnionFrequencyRule
-- ----------------------------

CREATE TABLE `UnionFrequencyRule`
(
    `ID`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '联合频控主键',
    `NAME`            varchar(255) NOT NULL COMMENT '联合频控名称',
    `FREQUENCY_TYPE`  int(5) NOT NULL DEFAULT '0' COMMENT '频控类型:0-不频控 1-按天频控, 2-按自然周频控, 3-按投放周期频控',
    `FREQUENCY_LIMIT` int(10) NOT NULL DEFAULT '0' COMMENT '展示频次限制数，即在一个频控周期内，一个用户最多可以看到关联的推广系列广告的次数',
    `STATUS`          tinyint(4) NOT NULL COMMENT '联合频控状态:1-生效中, 2-已失效, 3-已删除',
    `CREATOR`         bigint(20) NOT NULL COMMENT '创建人',
    `CREATE_TIME`     timestamp    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `END_TIME`        timestamp    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '联合频控失效时间(即关联的广告计划中最晚的结束时间点)',
    `MODIFIER`        bigint(20) NOT NULL COMMENT '更新人',
    `LAST_MOD_TIME`   timestamp NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `AD_PLAN_ID_LIST` text         NOT NULL COMMENT '关联的广告计划id集合',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC


-- ----------------------------
-- Table structure for AdPlan
-- ----------------------------

ALTER TABLE AdPlan
    ADD VALID_UNION_FREQUENCY_RULE_ID bigint(20) DEFAULT null comment '关联生效中的联合频控id',
    ADD INDEX idx_valid_union_frequency_rule (VALID_UNION_FREQUENCY_RULE_ID);


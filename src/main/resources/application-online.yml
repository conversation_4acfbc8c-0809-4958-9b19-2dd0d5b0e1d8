spring:
  datasource:
    youxuan:
      url: ******************************************************************************************************************************************************************************************************************************
      username: youxuan_rw
      password: knhqmL44Xd2AyEIcui56
      driver-class-name: com.mysql.cj.jdbc.Driver
    yex:
      url: ***************************************************************************************************************************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
      driver-class-name: com.mysql.cj.jdbc.Driver
    eadb1:
      url: *****************************************************************************************************************************************************************************************************************************
      username: ead_read
      password: ea89,d24
      driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    database: 0
    host: 7d770a.redis.baichuan.ynode.cn
    port: 6379
    password: yVLsVlnj
  data:
    mongodb:
      uri: mongodb://youxuan_rw:<EMAIL>:27018,zj042.corp.yodao.com:27018,zj043.corp.yodao.com:27018/youxuan

youxuan:
  baseUrl: https://youxuan-backend.youdao.com/v1.0.0/
  web: https://youxuan.youdao.com/
  reporter:
    enable: true
    type: graphite
    graphiteHost: quipu-graphite.inner.youdao.com
    graphitePort: 2003

file:
  template: /mfs_ead/youxuan/static/fileTemplate/

zookeeper:
  uri: zk1.corp.yodao.com:2181,zk2.corp.yodao.com:2181,zk3.corp.yodao.com:2181
  basepath: "/ead/youxuan"

dict:
  community:
    video:
      baseUrl: https://wow.youdao.com/
    admin:
      baseUrl: http://dict-community-admin.inner.youdao.com/
    analyzer:
      baseUrl: https://wow-analyzer.youdao.com/
  profile:
    baseUrl: http://dict.youdao.com/profile/
  live:
    baseUrl: https://dict-live.youdao.com/server/


# 关闭线上mybatis sql打印
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

# 禁用swagger
springfox:
  documentation:
    enabled: false

expired: 86400

xxl-job-core:
  admin:
    addresses: https://ead-xxl-job-admin.inner.youdao.com/xxl-job-admin

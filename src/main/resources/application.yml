spring:
  datasource:
    youxuan:
      url: ****************************************************************************************************************************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
      driver-class-name: com.mysql.cj.jdbc.Driver
    yex:
      url: ************************************************************************************************************************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
      driver-class-name: com.mysql.cj.jdbc.Driver
    eadb1:
      url: **************************************************************************************************************************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
      driver-class-name: com.mysql.cj.jdbc.Driver
  jta:
    enabled: true
    atomikos:
      properties:
        log-base-name: youxuan
        log-base-dir: ./logs
        default-jta-timeout: 30000

  profiles:
    active: dev
  application:
    name: youxuan
  output:
    ansi:
      enabled: detect
  mvc:
    throw-exception-if-no-handler-found: true
  resources:
    add-mappings: false
  messages:
    encoding: UTF-8
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB
      resolve-lazily: true
  freemarker:
    prefer-file-system-access: false
  redis:
    timeout: 20000
    lettuce:
      pool:
        max-idle: 8
        max-active: 8
        min-idle: 0
    database: 10
    host: 4ff6b8.redis.baichuan.ynode.cn
    port: 6379
    password: 8XSaytOn
  central_dogma:
    hosts: centraldogma1.corp.youdao.com,centraldogma2.corp.youdao.com,centraldogma3.corp.youdao.com
    port: 80

server:
  name: youxuan
  tomcat:
    basedir: access-log
    uri-encoding: UTF-8
    accesslog:
      enabled: true
      pattern: '%h %l %u %t "%r" %s %b "%{Referer}i" "%{X-Forwarded-For}i" "%{User-Agent}i" %D ms'
      prefix: access
      suffix: .log
      max-days: 15
    connection-timeout: 10s
    max-http-header-size: 8KB
    accept-count: 100
    max-connections: 8192
    max-http-form-post-size: 2MB
    threads:
      max: 200
      min-sapre: 10
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
    context-path: /${version}
  error:
    path: /error
    whitelabel:
      enabled: false

management:
  endpoint:
    health:
      show-details: always
      cache:
        time-to-live: 10s
  endpoints:
    web:
      base-path: /

druid:
  address: http://eadata-druid-broker.corp.youdao.com/druid/v2
file:
  mfs: /mfs_ead/eadop/adbrand
  midUrl: /youxuan/${spring.profiles.active}
  template: /mfs_ead/youxuan/static/fileTemplate/test/

app:
  version: @project.version@
  wiki:
    url: https://confluence.inner.youdao.com/pages/viewpage.action?pageId=194524743

version: v1.0.0
mybatis-plus:
  mapper-locations: classpath:mapper/youxuan/*.xml,classpath:mapper/yex/*.xml,classpath:mapper/eadb1/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
  config: classpath:log4j2-spring.xml

# swagger
springfox:
  documentation:
    enabled: true

dict:
  mp:
    ios: wx5c6bfdfc3d281fa9
    android: wxbb793d473ac371b1
  login:
    from: youxuan
    baseUrl: https://dict.youdao.com/login/acc/
    key: 3248576B7438474C78472B4D6C314575
  community:
    analyzer:
      baseUrl: http://dict-community-analyzer-api-test.inner.youdao.com/
    video:
      baseUrl: http://dict-community-video.inner.youdao.com/
    admin:
      baseUrl: http://dict-community-admin-video.inner.youdao.com/
  profile:
    baseUrl: http://dict-test.youdao.com/profile/
  live:
    baseUrl: https://dict-live-test.youdao.com/server/

token:
  generate: true

smsNotify:
  enable: false
  url: http://sms.corp.yodao.com:8080/sms?group=ead_youxuan&message=%s

intranet: true

zookeeper:
  uri: ad1-test-01.zookeeper.yodao.cn:2181,ad1-test-02.zookeeper.yodao.cn:2181,ad1-test-03.zookeeper.yodao.cn:2181
  leader:
    path: ${zookeeper.basepath}/leader
  lock:
    path: ${zookeeper.basepath}/distributedLock

expired: 300
centralDogma:
  project: youxuan-${spring.profiles.active}
youxuan:
  reporter:
    enable: true
    prefix: ead.youxuan.${spring.profiles.active}
cache:
  prefix: 'youxuan_${spring.profiles.active}::'


xxl-job-core:
  admin:
    addresses: https://ead-xxl-job-admin-test.inner.youdao.com/xxl-job-admin
  executor:
    app-name: youxuan-${spring.profiles.active}

custom-audience-package-hdfs-path: hdfs://eadata-hdfs/user/youxuan/CustomCrowdPack/${spring.profiles.active}/
ead-hdfs-site-conf: /mfs_ead/eadata/online/hadoop-conf/hdfs-site.xml
ead-hdfs-core-conf: /mfs_ead/eadata/online/hadoop-conf/core-site.xml

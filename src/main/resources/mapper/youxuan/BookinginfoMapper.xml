<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.BookingInfoMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.BookingInfo">
            <id property="id" column="ID" jdbcType="BIGINT"/>
            <result property="companyName" column="COMPANY_NAME" jdbcType="VARCHAR"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="phoneNum" column="PHONE_NUM" jdbcType="VARCHAR"/>
            <result property="city" column="CITY" jdbcType="VARCHAR"/>
            <result property="industry" column="INDUSTRY" jdbcType="VARCHAR"/>
            <result property="description" column="DESCRIPTION" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,COMPANY_NAME,NAME,
        PHONE_NUM,CITY,INDUSTRY,
        DESCRIPTION
    </sql>
</mapper>

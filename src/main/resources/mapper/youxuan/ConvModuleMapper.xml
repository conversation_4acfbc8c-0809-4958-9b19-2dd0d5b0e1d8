<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.ConvModuleMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.ConvModule">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="enabled" column="ENABLED" jdbcType="TINYINT"/>
        <result property="type" column="TYPE" jdbcType="TINYINT"/>
        <result property="displayName" column="DISPLAY_NAME" jdbcType="VARCHAR"/>
        <result property="appAccountId" column="APP_ACCOUNT_ID" jdbcType="BIGINT"/>
        <result property="url" column="URL" jdbcType="VARCHAR"/>
        <result property="androidPackageName" column="ANDROID_PACKAGE_NAME" jdbcType="VARCHAR"/>
        <result property="iosDownloadUrl" column="IOS_DOWNLOAD_URL" jdbcType="VARCHAR"/>
        <result property="phone" column="PHONE" jdbcType="VARCHAR"/>
        <result property="enabledWechatMicroProgramSwitch" column="ENABLED_WECHAT_MICRO_PROGRAM_SWITCH" jdbcType="TINYINT"/>
        <result property="wechatMicroProgramRawId" column="WECHAT_MICRO_PROGRAM_RAW_ID" jdbcType="VARCHAR"/>
        <result property="wechatMicroProgramPath" column="WECHAT_MICRO_PROGRAM_PATH" jdbcType="VARCHAR"/>
        <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ENABLED,TYPE,
        DISPLAY_NAME,URL,ANDROID_PACKAGE_NAME,
        IOS_DOWNLOAD_URL,PHONE,ENABLED_WECHAT_MICRO_PROGRAM_SWITCH,
        WECHAT_MICRO_PROGRAM_RAW_ID,WECHAT_MICRO_PROGRAM_PATH,CREATOR,
        CREATE_TIME,MODIFIER,LAST_MOD_TIME
    </sql>
</mapper>

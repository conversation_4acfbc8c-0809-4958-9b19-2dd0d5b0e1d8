<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.AdMappingMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.AdMapping">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="adGroupId" column="AD_GROUP_ID" jdbcType="BIGINT"/>
        <result property="sourceStyleId" column="SOURCE_STYLE_ID" jdbcType="BIGINT"/>
        <result property="mappingStyleId" column="MAPPING_STYLE_ID" jdbcType="BIGINT"/>
        <result property="status" column="STATUS" jdbcType="TINYINT"/>
        <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
        <result property="roleId" column="ROLE_ID" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,AdGroupId,
        SourceStyleId,MappingStyleId,STATUS,
        CREATOR,CREATE_TIME,MODIFIER,
        LAST_MOD_TIME
    </sql>
</mapper>

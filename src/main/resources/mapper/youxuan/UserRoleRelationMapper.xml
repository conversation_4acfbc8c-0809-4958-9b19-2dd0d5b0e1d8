<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.UserRoleRelationMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.UserRoleRelation">
            <id property="id" column="ID" jdbcType="BIGINT"/>
            <result property="userId" column="USER_ID" jdbcType="BIGINT"/>
            <result property="roleId" column="ROLE_ID" jdbcType="BIGINT"/>
            <result property="status" column="STATUS" jdbcType="INTEGER"/>
            <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
            <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,USER_ID,ROLE_ID,
        STATUS,CREATOR,MODIFIER,
        CREATE_TIME,LAST_MOD_TIME
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.AppPackageMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.AppPackage">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="androidPkgName" column="ANDROID_PACKAGE_NAME" jdbcType="INTEGER"/>
        <result property="iosPkgName" column="IOS_PACKAGE_NAME" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,NAME,ANDROID_PACKAGE_NAME,IOS_PACKAGE_NAME,STATUS
    </sql>
</mapper>

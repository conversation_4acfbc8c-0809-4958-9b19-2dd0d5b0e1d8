<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.KolContentTagRelationMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.KolContentTagRelation">
            <id property="id" column="ID" jdbcType="BIGINT"/>
            <result property="appAccountId" column="APP_ACCOUNT_ID" jdbcType="BIGINT"/>
            <result property="contentTagId" column="CONTENT_TAG_ID" jdbcType="BIGINT"/>
            <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,APP_USER_ID,CONTENT_TAG_ID,
        CREATOR,CREATE_TIME
    </sql>

    <select id="getUsageCountByContentTagIds" resultType="outfox.ead.youxuan.web.kol.controller.dto.ContentTagUsageCountDTO">
        SELECT CONTENT_TAG_ID as contentTagId, COUNT(*) as usageCount
        FROM KolContentTagRelation
        WHERE CONTENT_TAG_ID IN
        <foreach collection="contentTagIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY CONTENT_TAG_ID
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.PlatformMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.Platform">
            <id property="id" column="ID" jdbcType="BIGINT"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="icon" column="ICON" jdbcType="VARCHAR"/>
            <result property="bindType" column="BIND_TYPE" jdbcType="TINYINT"/>
            <result property="verifyUrl" column="VERIFY_URL" jdbcType="VARCHAR"/>
            <result property="userInfoUrl" column="USER_INFO_URL" jdbcType="VARCHAR"/>
            <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
            <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,ICON,BIND_TYPE,
        VERIFY_URL,USER_INFO_URL,CREATOR,
        CREATE_TIME,MODIFIER,LAST_MOD_TIME
    </sql>
    <select id="getByPlatformContentId" resultType="outfox.ead.youxuan.entity.Platform">
        SELECT p.*
        FROM Platform p
                 LEFT JOIN PlatformTask pt ON p.ID = pt.PLATFORM_ID
                 LEFT JOIN PlatformContent pc ON pt.ID = pc.PLATFORM_TASK_ID
        WHERE pc.ID = #{platformContentId}
    </select>
</mapper>

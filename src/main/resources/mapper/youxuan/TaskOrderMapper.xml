<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.TaskOrderMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.TaskOrder">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="parentOrderId" column="PARENT_ORDER_ID" jdbcType="BIGINT"/>
        <result property="platformContentId" column="PLATFORM_CONTENT_ID" jdbcType="BIGINT"/>
        <result property="appAccountId" column="APP_ACCOUNT_ID" jdbcType="BIGINT"/>
        <result property="comment" column="COMMENT" jdbcType="VARCHAR"/>
        <result property="price" column="PRICE" jdbcType="BIGINT"/>
        <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
        <result property="status" column="STATUS" jdbcType="TINYINT"/>
        <result property="postLimit" column="POST_LIMIT" jdbcType="BIGINT"/>
        <result property="postRemain" column="POST_REMAIN" jdbcType="BIGINT"/>
        <result property="showcaseComponentId" column="SHOWCASE_COMPONENT_ID" jdbcType="BIGINT"/>
        <result property="billingType" column="BILLING_TYPE" jdbcType="TINYINT"/>
        <result property="type" column="TYPE" jdbcType="TINYINT"/>
        <result property="commissionPrice" column="COMMISSION_PRICE" jdbcType="BIGINT"/>
        <result property="endDate" column="END_DATE" jdbcType="DATE"/>
        <result property="postRemain" column="POST_REMAIN" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,PARENT_ORDER_ID,PLATFORM_CONTENT_ID,
        APP_ACCOUNT_ID,COMMENT,PRICE,
        CREATOR,CREATE_TIME,MODIFIER,
        LAST_MOD_TIME,STATUS,POST_REMAIN
    </sql>
    <select id="pagePostTaskCanPost" resultType="outfox.ead.youxuan.entity.TaskOrder">
        select *
        from TaskOrder
        where status = 2 AND type = 2
        <if test="id!=null and id!=''">
            AND id = #{id}
        </if>
        <if test="orderId!=null">
            AND order_id = #{orderId}
        </if>
        <if test="showcaseComponentIdList!=null">
            AND SHOWCASE_COMPONENT_ID in
            <foreach collection="showcaseComponentIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null == onlyParticipated or false == onlyParticipated">
            AND (
            (POST_LIMIT = 0 or POST_REMAIN > 0)
            <if test="participatedPostTaskIds.size() > 0">
                OR
                (id IN
                <foreach collection="participatedPostTaskIds" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>)
            </if>
            )
        </if>
        <if test="true == onlyParticipated and participatedPostTaskIds.size() > 0">
            AND (id IN
            <foreach collection="participatedPostTaskIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>)
        </if>

        <if test="billingType!=null">
            AND billing_type = #{billingType}
        </if>

        <if test="order!=null and order==1">
            order by COMMISSION_PRICE asc
        </if>
        <if test="order!=null and order==-1">
            order by COMMISSION_PRICE desc
        </if>
        <if test="order!=null and order==2">
            order by END_DATE asc
        </if>
        <if test="order!=null and order==-2">
            order by END_DATE desc
        </if>
        <if test="order!=null and order==3">
            order by CREATE_TIME asc
        </if>
        <if test="order!=null and order==-3">
            order by CREATE_TIME desc
        </if>
        <if test="order!=null and order==4">
            order by -POST_REMAIN desc
        </if>
        <if test="order!=null and order==-4">
            order by -POST_REMAIN asc
        </if>
        <if test="order!=null">
            ,create_time desc,id desc
        </if>
        <if test="order==null">
            order by id desc
        </if>
    </select>

    <select id="pagePostTask" resultType="outfox.ead.youxuan.entity.TaskOrder">
        select *
        from TaskOrder
        where 1=1
        <if test="showcaseComponentIdList!=null">
            <if test="type==2">AND SHOWCASE_COMPONENT_ID in
                <foreach collection="showcaseComponentIdList" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="type==3">AND parent_order_id IN (select id from TaskOrder where SHOWCASE_COMPONENT_ID IN
                <foreach collection="showcaseComponentIdList" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>)
            </if>
        </if>
        <if test="ids!=null">
            AND id in
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderIds!=null">
            AND order_id in
            <foreach collection="orderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="name!=null">
            AND name like CONCAT(CONCAT('%', #{name}), '%')
        </if>
        <if test="status!=null">
            AND status = #{status}
        </if>
        <if test="billingType!=null">
            <if test="type==2">AND billing_type = #{billingType}</if>
            <if test="type==3">AND parent_order_id in (select id from TaskOrder where billing_type = #{billingType})</if>
        </if>
        <if test="userId!=null">
            AND creator = #{userId}
        </if>
        <if test="type!=null">
            AND type = #{type}
        </if>
        <choose>
            <when test="1 == order">
                order by COMMISSION_PRICE asc
            </when>
            <when test="-1 == order">
                order by COMMISSION_PRICE desc
            </when>
            <when test="2 == order">
                order by END_DATE asc
            </when>
            <when test="-2 == order">
                order by END_DATE desc
            </when>
            <when test="3 == order">
                order by CREATE_TIME asc
            </when>
            <when test="-3 == order">
                order by CREATE_TIME desc
            </when>
            <when test="4 == order">
                order by -POST_REMAIN desc
            </when>
            <when test="-4 == order">
                order by -POST_REMAIN asc
            </when>
            <otherwise>
                order by id desc
            </otherwise>
        </choose>
        <if test="order!=null">
            ,create_time desc,id desc
        </if>
    </select>
</mapper>

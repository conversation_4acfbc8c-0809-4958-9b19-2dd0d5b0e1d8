<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.AppAccountMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.AppAccount">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="appUserId" column="APP_USER_ID" jdbcType="VARCHAR"/>
        <result property="platformId" column="PLATFORM_ID" jdbcType="BIGINT"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="avatar" column="AVATAR" jdbcType="VARCHAR"/>
        <result property="gender" column="SEX" jdbcType="TINYINT"/>
        <result property="fansNum" column="FANS_NUM" jdbcType="BIGINT"/>
        <result property="status" column="STATUS" jdbcType="INTEGER"/>
        <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
        <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
        <result property="userId" column="USER_ID" jdbcType="BIGINT"/>
        <result property="area" column="AREA" javaType="INTEGER"/>
        <result property="inService" column="IN_SERVICE" jdbcType="TINYINT"/>
        <result property="productWindowPermission" column="PRODUCT_WINDOW_PERMISSION" jdbcType="TINYINT"/>
        <result property="mcn" column="MCN" jdbcType="VARCHAR"/>
        <result property="kolOperatorUserId" column="KOL_OPERATOR_USER_ID" jdbcType="BIGINT"/>
        <result property="homepageLink" column="HOMEPAGE_LINK" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,APP_USER_ID,PLATFORM_ID,
        NAME,AVATAR,GENDER,
        FANS_NUM,STATUS,CREATOR,
        MODIFIER,CREATE_TIME,LAST_MOD_TIME,
        USER_ID,AREA,IN_SERVICE,PRODUCT_WINDOW_PERMISSION,
        MCN,KOL_OPERATOR_USER_ID,HOMEPAGE_LINK
    </sql>
    <!--  只有开启了接单的KOL才能被显示到达人广场/查询出的KOL必须存在至少一项报价  -->
    <select id="listByConditions" resultType="outfox.ead.youxuan.entity.AppAccount">
        SELECT ap.*
        FROM AppAccount ap
        LEFT JOIN KolPlatformContentRelation kpcr ON ap.ID = kpcr.APP_ACCOUNT_ID
        <if test="contentTagIds != null and contentTagIds.size() > 0">
            LEFT JOIN KolContentTagRelation kctr ON ap.ID = kctr.APP_ACCOUNT_ID
        </if>
        WHERE ap.IN_SERVICE = 1
        AND ap.STATUS = 0
        AND kpcr.PRICE >= 100
        <if test="platformId != null">
            AND PLATFORM_ID = #{platformId}
        </if>
        <if test="fansNumMin != null">
            AND FANS_NUM >= #{fansNumMin}
        </if>
        <if test="fansNumMax != null">
            <![CDATA[AND FANS_NUM <= #{fansNumMax}]]>
        </if>
        <if test="gender != null">
            AND GENDER = #{gender}
        </if>
        <if test="nickName != null and nickName != ''">
            <bind name="fuzzyNickName" value="'%' + nickName + '%'"></bind>
            AND NAME like #{fuzzyNickName}
        </if>
        <if test="contentTagIds != null and contentTagIds.size() > 0">
            AND kctr.CONTENT_TAG_ID in
            <foreach collection="contentTagIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="famousTeacherTagIds != null and famousTeacherTagIds.size() > 0">
            AND ap.ID IN (SELECT APP_ACCOUNT_ID FROM KolContentTagRelation WHERE CONTENT_TAG_ID IN
            <foreach collection="famousTeacherTagIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
                )
        </if>
        <if test="priceMin != null">
            AND kpcr.price >= #{priceMin}
        </if>
        <if test="priceMax != null">
            <![CDATA[AND kpcr.price <= #{priceMax}]]>
        </if>
        GROUP BY ap.ID
        <if test="fansOrder != null or priceOrder != null">
            ORDER BY
        </if>
        <choose>
            <when test="fansOrder == 0">
                MAX(ap.FANS_NUM) DESC
            </when>
            <when test="fansOrder == 1">
                MIN(ap.FANS_NUM) ASC
            </when>
        </choose>
        <if test="fansOrder != null and priceOrder != null">
            ,
        </if>
        <choose>
            <when test="priceOrder == 0">
                MAX(kpcr.PRICE) DESC
            </when>
            <when test="priceOrder == 1">
                MIN(kpcr.PRICE) ASC
            </when>
        </choose>
        <choose>
            <when test="priceOrder != null and fansOrder == null">
                , MAX(ap.FANS_NUM) DESC
            </when>
        </choose>
    </select>
    <select id="getBindByUserIdAndPlatformName" resultType="outfox.ead.youxuan.entity.AppAccount">
        SELECT ap.*
        FROM AppAccount ap
                 left join Platform p
                           on ap.PLATFORM_ID = p.ID
        where p.NAME = #{platformName}
          AND ap.USER_ID = #{userId}
          AND ap.status = 0;
    </select>


    <select id="getBindByPlatformNameAndAppUserId" resultType="outfox.ead.youxuan.entity.AppAccount">
        SELECT ap.*
        FROM AppAccount ap
                 left join Platform p
                           on ap.PLATFORM_ID = p.ID
        where p.NAME = #{platformName}
          AND ap.APP_USER_ID = #{uid}
          AND ap.status = 0;
    </select>

    <select id="getBindByUserIdListAndPlatformName" resultType="outfox.ead.youxuan.entity.AppAccount">
        SELECT ap.*
        FROM AppAccount ap
                 left join Platform p
                           on ap.PLATFORM_ID = p.ID
        where p.NAME = #{platformName}
          AND ap.USER_ID in
        <foreach collection="userIdList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
          AND ap.status = 0;
    </select>


</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.AdGroupMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.AdGroup">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="adPlanId" column="AD_PLAN_ID" jdbcType="BIGINT"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="TINYINT"/>
        <result property="cpmPrice" column="CPM_PRICE" jdbcType="INTEGER"/>
        <result property="sumDisplayCount" column="SUM_DISPLAY_COUNT" jdbcType="INTEGER"/>
        <result property="dailyDisplayLimit" column="DAILY_DISPLAY_LIMIT" jdbcType="INTEGER"/>
        <result property="deliverySpeed" column="DELIVERY_SPEED" jdbcType="TINYINT"/>
        <result property="bootFirstRefresh" column="BOOT_FIRST_REFRESH" jdbcType="TINYINT"/>
        <result property="openScreenRecycle" column="OPEN_SCREEN_RECYCLE" jdbcType="TINYINT"/>

        <result property="brandClkType" column="BRAND_CLK_TYPE" jdbcType="INTEGER"/>
        <result property="landingPageLink" column="LANDING_PAGE_LINK" jdbcType="VARCHAR"/>
        <result property="deeplinkUrl" column="DEEPLINK_URL" jdbcType="VARCHAR"/>
        <result property="wechatAppId" column="WECHAT_APP_ID" jdbcType="VARCHAR"/>
        <result property="wechatOriginalId" column="WECHAT_ORIGINAL_ID" jdbcType="VARCHAR"/>
        <result property="wechatPath" column="WECHAT_PATH" jdbcType="VARCHAR"/>
        <result property="landingPageLink1" column="LANDING_PAGE_LINK1" jdbcType="VARCHAR"/>
        <result property="deeplinkUrl1" column="DEEPLINK_URL1" jdbcType="VARCHAR"/>
        <result property="wechatOriginalId1" column="WECHAT_ORIGINAL_ID1" jdbcType="VARCHAR"/>
        <result property="wechatPath1" column="WECHAT_PATH1" jdbcType="VARCHAR"/>
        <result property="landingPageLink2" column="LANDING_PAGE_LINK2" jdbcType="VARCHAR"/>
        <result property="deeplinkUrl2" column="DEEPLINK_URL2" jdbcType="VARCHAR"/>
        <result property="wechatOriginalId2" column="WECHAT_ORIGINAL_ID2" jdbcType="VARCHAR"/>
        <result property="wechatPath2" column="WECHAT_PATH2" jdbcType="VARCHAR"/>

        <result property="dealId" column="DEAL_ID" jdbcType="VARCHAR"/>
        <result property="dealRemark" column="DEAL_REMARK" jdbcType="VARCHAR"/>
        <result property="dspId" column="DSP_ID" jdbcType="VARCHAR"/>
        <result property="basePadding" column="BASE_PADDING" jdbcType="TINYINT"/>
        <result property="expoDeteLink" column="EXPO_DETE_LINK" jdbcType="VARCHAR"/>
        <result property="clickDeteLink" column="CLICK_DETE_LINK" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
        <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
        <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
        <result property="youthMode" column="YOUTH_MODE" jdbcType="TINYINT"/>
        <result property="genderOrientation" column="GENDER_ORIENTATION" jdbcType="TINYINT"/>
        <result property="mustDeviceId" column="MUST_DEVICE_ID" jdbcType="TINYINT"/>
        <result property="roleId" column="ROLE_ID" jdbcType="BIGINT"/>
        <result property="ageOrientationV2" column="AGE_ORIENTATION_V2" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="crowdLabel" column="CROWD_LABEL" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="replaceMacro" column="REPLACE_MACRO" jdbcType="TINYINT"/>
        <result property="orientationMode" column="ORIENTATION_MODE" jdbcType="INTEGER"/>
        <result property="expansionPromotion" column="EXPANSION_PROMOTION" jdbcType="TINYINT"/>
        <result property="interactionType" column="INTERACTION_TYPE" jdbcType="TINYINT"/>
        <result property="appInstalledOrientation" column="APP_INSTALLED_ORIENTATION"/>
        <result property="installedAppPackageIds" column="INSTALLED_APP_PACKAGE_IDS" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="wechatOrientation" column="WECHAT_ORIENTATION"/>
        <result property="testDirect" column="TEST_DIRECT"/>
        <result property="directStartTime" column="DIRECT_START_TIME"/>
        <result property="directEndTime" column="DIRECT_END_TIME"/>
        <result property="openCustomCrowdPack" column="OPEN_CUSTOM_CROWD_PACK"/>
        <result property="includeCrowdPack" column="INCLUDE_CROWD_PACK"/>
        <result property="crowdPackIds" column="CROWD_PACK_IDS"
                typeHandler="outfox.ead.youxuan.core.handler.mybatis.LongSetTypeHandler"/>
        <result property="h5TransitUrl" column="H5_TRANSIT_URL"/>
        <result property="dpTransitUrl" column="DP_TRANSIT_URL"/>
        <result property="transitTargetWechatAppId" column="TRANSIT_TARGET_WECHAT_APP_ID"/>
        <result property="cpmCostType" column="CPM_COST_TYPE"/>
        <result property="cpmAccelerateRatio" column="CPM_ACCELERATE_RATIO"/>
        <result property="allowCaid" column="ALLOW_CAID"/>

    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,AD_PLAN_ID,NAME,
        STATUS,CPM_PRICE,SUM_DISPLAY_COUNT,
        DAILY_DISPLAY_LIMIT,DELIVERY_SPEED,BOOT_FIRST_REFRESH,OPEN_SCREEN_RECYCLE,
        BRAND_CLK_TYPE,LANDING_PAGE_LINK,DEEPLINK_URL,WECHAT_APP_ID,WECHAT_ORIGINAL_ID,WECHAT_PATH,DEAL_ID,
        LANDING_PAGE_LINK_1,DEEPLINK_URL_1,WECHAT_ORIGINAL_ID_1,WECHAT_PATH_1,
        LANDING_PAGE_LINK_2,DEEPLINK_URL_2,WECHAT_ORIGINAL_ID_2,WECHAT_PATH_2,
        DSP_ID,DEAL_REMARK,BASE_PADDING,
        EXPO_DETE_LINK,CLICK_DETE_LINK,CREATE_TIME,
        LAST_MOD_TIME,CREATOR,MODIFIER,YOUTH_MODE,GENDER_ORIENTATION,MUST_DEVICE_ID,ROLE_ID,
        AGE_ORIENTATION_V2,CROWD_LABEL,REPLACE_MACRO,ORIENTATION_MODE,EXPANSION_PROMOTION,INTERACTION_TYPE,
        APP_INSTALLED_ORIENTATION,INSTALLED_APP_PACKAGE_IDS,TEST_DIRECT,DIRECT_START_TIME,DIRECT_END_TIME,
        H5_TRANSIT_URL,DP_TRANSIT_URL,TRANSIT_TARGET_WECHAT_APP_ID,CPM_COST_TYPE,CPM_ACCELERATE_RATIO,ALLOW_CAID
    </sql>

</mapper>

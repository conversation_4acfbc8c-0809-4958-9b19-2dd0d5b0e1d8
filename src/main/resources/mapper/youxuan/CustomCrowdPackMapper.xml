<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.CustomCrowdPackMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.CustomCrowdPack">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="filePath" column="FILE_PATH" jdbcType="VARCHAR"/>
        <result property="errMsg" column="ERR_MSG" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="TINYINT"/>
        <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
        <result property="deviceIdType" column="DEVICE_ID_TYPE" jdbcType="INTEGER"/>
        <result property="expiryTime" column="EXPIRY_TIME" jdbcType="TIMESTAMP"/>
        <result property="validCount" column="VALID_COUNT" jdbcType="INTEGER"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
        <result property="creatorRole" column="CREATOR_ROLE" jdbcType="BIGINT"/>
        <result property="modifierRole" column="MODIFIER_ROLE" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,STATUS,
        CREATOR,EXPIRY_TIME,VALID_COUNT,
        CREATE_TIME,MODIFIER,LAST_MOD_TIME,
        CREATOR_ROLE,MODIFIER_ROLE
    </sql>
</mapper>

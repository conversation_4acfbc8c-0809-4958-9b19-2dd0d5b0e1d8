<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.RoleMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.Role">
            <id property="id" column="ID" jdbcType="BIGINT"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="roleKey" column="ROLE_KEY" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="INTEGER"/>
            <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
            <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,ROLE_KEY,
        STATUS,CREATOR,MODIFIER,
        CREATE_TIME,LAST_MOD_TIME
    </sql>
    <select id="listByUserId" resultType="outfox.ead.youxuan.entity.Role">
        SELECT
        r.ID, r.NAME, r.ROLE_KEY
        FROM Role r,UserRoleRelation ur
        WHERE ur.USER_ID=#{userId} AND ur.ROLE_ID=r.id AND ur.STATUS=0
    </select>
</mapper>

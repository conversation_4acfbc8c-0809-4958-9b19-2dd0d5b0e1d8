<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.AdDeliveryTimeMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.AdDeliveryTime">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="adPlanId" column="AD_PLAN_ID" jdbcType="BIGINT"/>
        <result property="startTime" column="START_TIME" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="END_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,AD_PLAN_ID,START_TIME,
        END_TIME
    </sql>
    <select id="listByTimeIntervalExcludeAdPlan" resultType="outfox.ead.youxuan.entity.AdDeliveryTime">
        select AD_PLAN_ID,START_TIME,END_TIME FROM AdDeliveryTime
        WHERE
        <if test="excludeAdPlanId!=null">AD_PLAN_ID != #{excludeAdPlanId} AND (</if>
        <foreach collection="adDeliveryTimes" index="index" item="item">
            <if test="index!=0">OR</if>
            (END_TIME &gt;= #{item.startTime,jdbcType=DATETIMEOFFSET} AND START_TIME &lt;= #{item.endTime,jdbcType=DATETIMEOFFSET})
        </foreach>
        <if test="excludeAdPlanId!=null">)</if>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.CustomCrowdPackRelationMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.CustomCrowdPackRelation">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="adGroupId" column="AD_GROUP_ID" jdbcType="BIGINT"/>
        <result property="customCrowdPackId" column="CUSTOM_CROWD_PACK_ID" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,AD_GROUP_ID,CUSTOM_CROWD_PACK_ID
    </sql>
    <select id="existValidRelation" resultType="java.lang.Boolean">
        select count(*) > 0
        from CustomCrowdPackRelation c
                 left join AdGroup g on c.AD_GROUP_ID = g.ID
        where g.STATUS in (0, 2, 3)
          and c.CUSTOM_CROWD_PACK_ID = #{id}
    </select>
</mapper>

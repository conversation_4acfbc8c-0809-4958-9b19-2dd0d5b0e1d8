<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.AdPositionMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.AdPosition">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="mediaId" column="MEDIA_ID" jdbcType="BIGINT"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="type" column="TYPE" jdbcType="TINYINT"/>
        <result property="displayTimes" column="DISPLAY_TIMES" jdbcType="TINYINT"/>
        <result property="promotionType" column="PROMOTION_TYPE" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="openScreenRecycling" column="OPEN_SCREEN_RECYCLING" jdbcType="TINYINT"/>
        <result property="bootFirstRefresh" column="BOOT_FIRST_REFRESH" jdbcType="TINYINT"/>
        <result property="receiveOpenScreenRecycling" column="RECEIVE_OPEN_SCREEN_RECYCLING" jdbcType="TINYINT"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
        <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
        <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
        <result property="status" column="STATUS" jdbcType="TINYINT"/>
        <result property="hasShakable" column="HAS_SHAKABLE" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,MEDIA_ID,NAME,
        TYPE,DISPLAY_TYPE,DISPLAY_TIMES,
        PROMOTION_TYPE,OPEN_SCREEN_RECYCLING,BOOT_FIRST_REFRESH,
        RECEIVE_OPEN_SCREEN_RECYCLING,CREATE_TIME,LAST_MOD_TIME,
        CREATOR,MODIFIER,STATUS,HAS_SHAKABLE
    </sql>
    <select id="listByStyleIds" resultType="outfox.ead.youxuan.entity.AdPosition">
        select
        a.*
        from AdPosition a
        join Style s on a.ID=s.AD_POSITION_ID
        where s.id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>

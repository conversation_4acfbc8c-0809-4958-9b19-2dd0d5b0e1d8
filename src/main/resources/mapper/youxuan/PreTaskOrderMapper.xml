<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.PreTaskOrderMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.PreTaskOrder">
            <id property="id" column="ID" jdbcType="BIGINT"/>
            <result property="appAccountId" column="APP_ACCOUNT_ID" jdbcType="BIGINT"/>
            <result property="platformContentId" column="PLATFORM_CONTENT_ID" jdbcType="BIGINT"/>
            <result property="kolName" column="KOL_NAME" jdbcType="VARCHAR"/>
            <result property="kolAvatar" column="KOL_AVATAR" jdbcType="VARCHAR"/>
            <result property="comment" column="COMMENT" jdbcType="VARCHAR"/>
            <result property="price" column="PRICE" jdbcType="BIGINT"/>
            <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="DELETED" jdbcType="TINYINT"/>
            <result property="showcaseComponentId" column="SHOWCASE_COMPONENT_ID" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,APP_ACCOUNT_ID,PLATFORM_CONTENT_ID,
        KOL_NAME,KOL_AVATAR,COMMENT,
        PRICE,CREATOR,CREATE_TIME,
        LAST_MOD_TIME,DELETED,SHOWCASE_COMPONENT_ID
    </sql>
</mapper>

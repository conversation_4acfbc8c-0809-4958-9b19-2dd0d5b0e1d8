<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.StyleMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.Style">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="adPositionId" column="AD_POSITION_ID" jdbcType="BIGINT"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="minVersionSupported" column="MIN_VERSION_SUPPORTED" jdbcType="VARCHAR"/>
        <result property="showTime" column="SHOW_TIME" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="textStyleContent" column="TEXT_STYLE_CONTENT" jdbcType="VARCHAR"
                typeHandler="outfox.ead.youxuan.core.handler.mybatis.StyleElementTypeHandler"/>
        <result property="picStyleContent" column="PIC_STYLE_CONTENT" jdbcType="VARCHAR"
                typeHandler="outfox.ead.youxuan.core.handler.mybatis.StyleElementTypeHandler"/>
        <result property="videoStyleContent" column="VIDEO_STYLE_CONTENT" jdbcType="VARCHAR"
                typeHandler="outfox.ead.youxuan.core.handler.mybatis.StyleElementTypeHandler"/>
        <result property="status" column="STATUS" jdbcType="TINYINT"/>
        <result property="styleType" column="STYLE_TYPE" jdbcType="TINYINT"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
        <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
        <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
        <result property="fullScreen" column="FULL_SCREEN" jdbcType="TINYINT"/>
        <result property="openScreenRecycling" column="OPEN_SCREEN_RECYCLING" jdbcType="TINYINT"/>
        <result property="bootFirstRefresh" column="BOOT_FIRST_REFRESH" jdbcType="TINYINT"/>
        <result property="receiveOpenScreenRecycling" column="RECEIVE_OPEN_SCREEN_RECYCLING" jdbcType="TINYINT"/>
        <result property="dictVideoPost" column="DICT_VIDEO_POST" jdbcType="TINYINT"/>
        <result property="shakable" column="SHAKABLE" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,AD_POSITION_ID,NAME,
        MIN_VERSION_SUPPORTED,SHOW_TYPE,SHOW_TIME,
        TEXT_STYLE_CONTENT,PIC_STYLE_CONTENT,VIDEO_STYLE_CONTENT,STATUS,CREATE_TIME,NATIVE_OPEN_SCREEN_TYPE,
        LAST_MOD_TIME,CREATOR,MODIFIER,OPEN_SCREEN_RECYCLING,BOOT_FIRST_REFRESH,RECEIVE_OPEN_SCREEN_RECYCLING,FULL_SCREEN,DICT_VIDEO_POST
    </sql>
    <select id="listByAdGroupId" resultType="outfox.ead.youxuan.entity.Style">
        SELECT S.STATUS
        FROM Style S
                 LEFT JOIN AdContentRelation A
                           ON S.ID = A.STYLE_ID
        WHERE S.STATUS != 0
          AND A.AD_GROUP_ID = #{adGroupId}
    </select>

    <select id="listByAdGroupIds" resultType="outfox.ead.youxuan.entity.Style">
        SELECT S.STATUS
        FROM Style S
        LEFT JOIN AdContentRelation A
        ON S.ID = A.STYLE_ID
        WHERE A.AD_GROUP_ID IN
        <foreach collection="collection" item="adGroupId" open="(" close=")" separator=",">
            #{adGroupId}
        </foreach>
    </select>

    <select id="pageStyle" resultType="outfox.ead.youxuan.web.ad.controller.vo.StyleListVO">
        SELECT S.ID, S.NAME, S.AD_POSITION_ID, S.STATUS, P.NAME AS AD_POSITION_NAME, P.MEDIA_ID, M.NAME AS MEDIA_NAME
        FROM Style S
        LEFT JOIN AdPosition P ON S.AD_POSITION_ID = P.ID
        LEFT JOIN Media M ON M.ID = P.MEDIA_ID
        WHERE 1=1
        <if test="styleVO.id!=null">
            and S.ID=#{styleVO.id}
        </if>
        <if test="styleVO.name!=null">
            and S.NAME like CONCAT(CONCAT('%', #{styleVO.name}), '%')
        </if>
        <if test="styleVO.status!=null and styleVO.status==3">
            and S.status!=2
        </if>
        <if test="styleVO.status!=null and styleVO.status!=3">
            and S.status=#{styleVO.status}
        </if>
        <if test="styleVO.adPositionId!=null">
            and S.AD_POSITION_ID=#{styleVO.adPositionId}
        </if>
        <if test="styleVO.adPositionName!=null">
            and P.NAME like CONCAT(CONCAT('%', #{styleVO.adPositionName}), '%')
        </if>
        <if test="styleVO.mediaId!=null">
            and P.MEDIA_ID=#{styleVO.mediaId}
        </if>
        <if test="styleVO.mediaName!=null">
            and M.NAME like CONCAT(CONCAT('%', #{styleVO.mediaName}), '%')
        </if>
        ORDER BY S.CREATE_TIME DESC
    </select>
</mapper>

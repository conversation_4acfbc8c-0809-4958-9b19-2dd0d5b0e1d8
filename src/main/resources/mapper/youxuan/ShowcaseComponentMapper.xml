<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.ShowcaseComponentMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.ShowcaseComponent">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="componentId" column="COMPONENT_ID" jdbcType="VARCHAR"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="promotionType" column="PROMOTION_TYPE" jdbcType="TINYINT"/>
        <result property="category" column="CATEGORY" jdbcType="TINYINT"/>
        <result property="switchType" column="SWITCH_TYPE" jdbcType="TINYINT"/>
        <result property="deepLink" column="DEEP_LINK" jdbcType="VARCHAR"/>
        <result property="backupLandingPageUrl" column="BACKUP_LANDING_PAGE_URL" jdbcType="VARCHAR"/>
        <result property="appendOutVendor" column="APPEND_OUT_VENDOR" jdbcType="TINYINT"/>
        <result property="microProgramId" column="MICRO_PROGRAM_ID" jdbcType="VARCHAR"/>
        <result property="microProgramPath" column="MICRO_PROGRAM_PATH" jdbcType="VARCHAR"/>
        <result property="appPlatform" column="APP_PLATFORM" jdbcType="TINYINT"/>
        <result property="androidPackageName" column="ANDROID_PACKAGE_NAME" jdbcType="VARCHAR"/>
        <result property="iosAppId" column="IOS_APP_ID" jdbcType="VARCHAR"/>
        <result property="leadText" column="LEAD_TEXT" jdbcType="VARCHAR"/>
        <result property="itemName" column="ITEM_NAME" jdbcType="VARCHAR"/>
        <result property="itemPrice" column="ITEM_PRICE" jdbcType="BIGINT"/>
        <result property="strikeThroughPrice" column="UNDERLINE_PRICE" jdbcType="INTEGER"/>
        <result property="buttonText" column="BUTTON_TEXT" jdbcType="VARCHAR"/>
        <result property="promoteImageUrl" column="PROMOTE_IMAGE_URL" jdbcType="VARCHAR"/>
        <result property="promoteTitle" column="PROMOTE_TITLE" jdbcType="VARCHAR"/>
        <result property="promoteText" column="PROMOTE_TEXT" jdbcType="VARCHAR"/>
        <result property="appName" column="APP_NAME" jdbcType="VARCHAR"/>
        <result property="auditedContent" column="AUDITED_CONTENT" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="auditDatetime" column="AUDIT_DATETIME" jdbcType="TIMESTAMP"/>
        <result property="status" column="STATUS" jdbcType="TINYINT"/>
        <result property="deleted" column="DELETED" jdbcType="TINYINT"/>
        <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
        <result property="auditor" column="AUDITOR" jdbcType="BIGINT"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
        <result property="auditStatus" column="AUDIT_STATUS" jdbcType="TINYINT"/>
        <result property="originalCategoryInfo" column="ORIGINAL_CATEGORY_INFO" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,COMPONENT_ID,NAME,
        PROMOTION_TYPE,CATEGORY,SWITCH_TYPE,
        DEEP_LINK,BACKUP_LANDING_PAGE,APPEND_OUT_VENDOR,
        MICRO_PROGRAM_ID,MICRO_PROGRAM_PATH,APP_PLATFORM,
        ANDROID_PACKAGE_NAME,IOS_APP_ID,LEAD_TEXT,
        ITEM_NAME,ITEM_PRICE,UNDERLINE_PRICE,
        BUTTON_TEXT,PROMOTE_IMAGE_URL,PROMOTE_TITLE,
        PROMOTE_TEXT,APP_NAME,AUDITED_CONTENT,
        AUDIT_DATETIME,STATUS,DELETED,ORIGINAL_CATEGORY_INFO
        CREATOR,CREATE_TIME,MODIFIER,
        LAST_MOD_TIME
    </sql>
    <select id="pageAudit" resultType="outfox.ead.youxuan.web.kol.controller.vo.ShowcaseComponentAuditListVO">
        select
        s.ID,s.COMPONENT_ID,s.APP_ACCOUNT_ID,s.NAME,s.PROMOTION_TYPE,s.CATEGORY,s.SWITCH_TYPE,s.LANDING_PAGE_URL,s.LEAD_TEXT,s.ITEM_NAME,s.ITEM_PRICE,s.BUTTON_TEXT,s.PROMOTE_IMAGE_URL,s.MARK_AS_AD,s.STATUS,s.AUDIT_STATUS,s.DELETED,s.CREATOR,s.CREATE_TIME,s.LAST_MOD_TIME,s.SCHEMA_ID,s.AUDIT_DATETIME,
        r.role_key,
        ud.USER_ID,ud.NICKNAME,
        u.USERNAME as auditor
        from ShowcaseComponent s
        left join Role r on r.id = s.role_id
        left join UserDetail ud on ud.USER_ID = s.CREATOR and ud.ROLE_ID=s.ROLE_ID
        left join User u on u.ID = s.AUDITOR
        where 1=1
        <if test="auditPageQuery.id != null">and s.id=#{auditPageQuery.id}</if>
        <if test="auditPageQuery.componentId != null">and s.component_id=#{auditPageQuery.componentId}</if>
        <if test="auditPageQuery.name != null">and s.name like "%"#{auditPageQuery.name}"%"</if>
        <if test="auditPageQuery.auditStatus != null">and s.AUDIT_STATUS=#{auditPageQuery.auditStatus}</if>
        <if test="auditPageQuery.deleted != null">and s.deleted=#{auditPageQuery.deleted}</if>
        <if test="auditPageQuery.userId != null">and s.creator=#{auditPageQuery.userId}</if>
        <if test="auditPageQuery.roleKey != null">and r.role_key=#{auditPageQuery.roleKey}</if>
        <if test="auditPageQuery.nickname != null">and ud.nickname like "%"#{auditPageQuery.nickname}"%"</if>
        order by s.create_time desc
    </select>
</mapper>

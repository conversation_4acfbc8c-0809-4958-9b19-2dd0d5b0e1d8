<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.MediaMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.Media">
            <id property="id" column="ID" jdbcType="BIGINT"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="osType" column="OS_TYPE" jdbcType="INTEGER"/>
            <result property="note" column="NOTE" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="TINYINT"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
            <result property="creator" column="CREATOR" jdbcType="BIGINT"/>
            <result property="modifier" column="MODIFIER" jdbcType="BIGINT"/>
            <result property="youthMode" column="YOUTH_MODE" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NAME,OS_TYPE,
        NOTE,STATUS,CREATE_TIME,
        LAST_MOD_TIME,CREATOR,MODIFIER,YOUTH_MODE
    </sql>
    <select id="listByStyleIds" resultType="outfox.ead.youxuan.entity.Media">
        select m.ID,m.NAME,m.OS_TYPE
        from Media m left join AdPosition ap on m.ID = ap.MEDIA_ID
        left join Style s on ap.ID = s.AD_POSITION_ID
        where s.ID in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getOsByStyleId" resultType="java.lang.Integer">
        select m.OS_TYPE
        from Media m
                 left join AdPosition ap on m.ID = ap.MEDIA_ID
                 left join Style s on ap.ID = s.AD_POSITION_ID
        where s.ID = #{styleId}
    </select>
</mapper>

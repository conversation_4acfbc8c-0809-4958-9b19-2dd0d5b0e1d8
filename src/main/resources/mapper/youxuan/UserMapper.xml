<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.UserMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.User">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="username" column="USERNAME" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="TINYINT"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,USERNAME,STATUS,
        CREATE_TIME,LAST_MOD_TIME
    </sql>
    <select id="lockUser" resultType="java.lang.Long">
        select id from User where
        <foreach collection="userIds" index="index" item="item">
            <if test="index!=0">OR</if>
            User.ID=#{item}
        </foreach>
        for update
    </select>
    <select id="listByRoleKey" resultType="outfox.ead.youxuan.entity.User">
        select u.id, u.username
        from User u
                 left join UserRoleRelation urr on u.ID = urr.USER_ID
                 left join Role r on urr.ROLE_ID = r.ID
        where r.ROLE_KEY = #{roleKey}
    </select>
</mapper>

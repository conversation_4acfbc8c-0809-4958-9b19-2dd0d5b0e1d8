<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.AdContentRelationMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.AdContentRelation">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="adGroupId" column="AD_GROUP_ID" jdbcType="BIGINT"/>
        <result property="styleId" column="STYLE_ID" jdbcType="BIGINT"/>
        <result property="displayWeight" column="DISPLAY_WEIGHT" jdbcType="INTEGER"/>
        <result property="status" column="STATUS" jdbcType="INTEGER"/>
        <result property="dictUid" column="DICT_UID" jdbcType="VARCHAR"/>
        <result property="dictPostId" column="DICT_POST_ID" jdbcType="VARCHAR"/>
        <result property="historyDictPostIds" column="HISTORY_DICT_POST_IDS" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" jdbcType="VARCHAR"/>

    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,AD_GROUP_ID,STYLE_ID,
        DISPLAY_WEIGHT,status,
        DICT_UID,DICT_POST_ID,HISTORY_DICT_POST_IDS
    </sql>
    <select id="listValidByAdGroupIds" resultType="outfox.ead.youxuan.entity.AdContentRelation">
        select R.ID,R.AD_GROUP_ID,R.STYLE_ID,R.DISPLAY_WEIGHT
        from (AdContentRelation R LEFT JOIN Style S ON R.STYLE_ID = S.ID)
        LEFT JOIN AdGroup G ON R.AD_GROUP_ID=G.ID
        WHERE S.`STATUS` != 2 AND G.`STATUS` != 5 AND R.STATUS = 0 AND R.AD_GROUP_ID IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="listValidByStyleIdsAndFilterByAdGroupId"
            resultType="outfox.ead.youxuan.entity.AdContentRelation">
        select R.ID,R.AD_GROUP_ID,R.STYLE_ID,R.DISPLAY_WEIGHT
        from (AdContentRelation R LEFT JOIN Style S ON R.STYLE_ID = S.ID)
        LEFT JOIN AdGroup G ON R.AD_GROUP_ID=G.ID
        WHERE S.`STATUS` != 2 AND G.`STATUS` != 5 AND R.STATUS=0 AND R.STYLE_ID IN
        <foreach collection="styleIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="adGroupId!=null">
            AND R.AD_GROUP_ID != #{adGroupId}
        </if>
    </select>
</mapper>

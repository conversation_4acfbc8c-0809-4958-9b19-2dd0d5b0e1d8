<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.AdvertisingByKeywordsRuleMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.AdvertisingByKeywordsRule">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="adGroupId" column="AD_GROUP_ID" jdbcType="BIGINT"/>
        <result property="keywordList" column="KEYWORD_LIST" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, AD_GROUP_ID, KEYWORD_LIST,
        CREATE_TIME, LAST_MOD_TIME
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.youxuan.ElementMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.Element">
        <id property="id" column="ID" jdbcType="BIGINT"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="elementKey" column="ELEMENT_KEY" jdbcType="VARCHAR"/>
        <result property="ratio" column="RATIO" jdbcType="VARCHAR"/>
        <result property="length" column="LENGTH" jdbcType="INTEGER"/>
        <result property="height" column="HEIGHT" jdbcType="INTEGER"/>
        <result property="width" column="WIDTH" jdbcType="INTEGER"/>
        <result property="ratio" column="RATIO" jdbcType="VARCHAR"/>
        <result property="mimeType" column="MIME_TYPE" jdbcType="VARCHAR"/>
        <result property="type" column="TYPE" jdbcType="TINYINT"/>
        <result property="status" column="STATUS" jdbcType="INTEGER"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID
        ,NAME,ELEMENT_KEY,LENGTH,HEIGHT,
        WIDTH,RATIO,MIME_TYPE,
        TYPE,STATUS,CREATE_TIME,
        LAST_MOD_TIME
    </sql>
</mapper>

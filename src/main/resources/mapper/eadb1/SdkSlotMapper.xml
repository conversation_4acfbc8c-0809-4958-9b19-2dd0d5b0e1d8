<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="outfox.ead.youxuan.mapper.eadb1.SdkSlotMapper">

    <resultMap id="BaseResultMap" type="outfox.ead.youxuan.entity.SdkSlot">
        <id property="sdkSlotId" column="SDK_SLOT_ID" jdbcType="BIGINT"/>
        <result property="sdkSlotUdid" column="SDK_SLOT_UDID" jdbcType="VARCHAR"/>
        <result property="sdkAppId" column="SDK_APP_ID" jdbcType="BIGINT"/>
        <result property="sdkSlotName" column="SDK_SLOT_NAME" jdbcType="VARCHAR"/>
        <result property="sdkSlotDescription" column="SDK_SLOT_DESCRIPTION" jdbcType="VARCHAR"/>
        <result property="type" column="TYPE" jdbcType="INTEGER"/>
        <result property="sdkType" column="SDK_TYPE" jdbcType="INTEGER"/>
        <result property="sdkStatus" column="SDK_STATUS" jdbcType="INTEGER"/>
        <result property="sdkOsType" column="SDK_OS_TYPE" jdbcType="INTEGER"/>
        <result property="sdkImageRules" column="SDK_IMAGE_RULES" jdbcType="VARCHAR"/>
        <result property="sdkTextRules" column="SDK_TEXT_RULES" jdbcType="VARCHAR"/>
        <result property="sdkIconHeight" column="SDK_ICON_HEIGHT" jdbcType="INTEGER"/>
        <result property="sdkIconWidth" column="SDK_ICON_WIDTH" jdbcType="INTEGER"/>
        <result property="sdkMainimageHeight" column="SDK_MAINIMAGE_HEIGHT" jdbcType="INTEGER"/>
        <result property="sdkMainimageWidth" column="SDK_MAINIMAGE_WIDTH" jdbcType="INTEGER"/>
        <result property="sdkTitleLength" column="SDK_TITLE_LENGTH" jdbcType="INTEGER"/>
        <result property="sdkSummaryLength" column="SDK_SUMMARY_LENGTH" jdbcType="INTEGER"/>
        <result property="sdkDescriptionLength" column="SDK_DESCRIPTION_LENGTH" jdbcType="INTEGER"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>
        <result property="lastModTime" column="LAST_MOD_TIME" jdbcType="BIGINT"/>
        <result property="sdkResultImage" column="SDK_RESULT_IMAGE" jdbcType="VARCHAR"/>
        <result property="sdkPackage" column="SDK_PACKAGE" jdbcType="VARCHAR"/>
        <result property="sdkOpStatus" column="SDK_OP_STATUS" jdbcType="INTEGER"/>
        <result property="sdkSlotPosition" column="SDK_SLOT_POSITION" jdbcType="VARCHAR"/>
        <result property="sdkSlotRepeat" column="SDK_SLOT_REPEAT" jdbcType="INTEGER"/>
        <result property="sdkSlotDesc" column="SDK_SLOT_DESC" jdbcType="VARCHAR"/>
        <result property="brandAdSeq" column="BRAND_AD_SEQ" jdbcType="VARCHAR"/>
        <result property="schemaIds" column="SCHEMA_IDS" jdbcType="VARCHAR"/>
        <result property="showConfirmDialogType" column="SHOW_CONFIRM_DIALOG_TYPE" jdbcType="INTEGER"/>
        <result property="filterSize" column="FILTER_SIZE" jdbcType="INTEGER"/>
        <result property="adProviderRatio" column="AD_PROVIDER_RATIO" jdbcType="INTEGER"/>
        <result property="sdkSlotCallbackUrl" column="SDK_SLOT_CALLBACK_URL" jdbcType="VARCHAR"/>
        <result property="callbackUrlSecretKey" column="CALLBACK_URL_SECRET_KEY" jdbcType="VARCHAR"/>
        <result property="videoPrefetch" column="VIDEO_PREFETCH" jdbcType="INTEGER"/>
        <result property="sdkSlotStyle" column="SDK_SLOT_STYLE" jdbcType="INTEGER"/>
        <result property="requirements" column="REQUIREMENTS" jdbcType="VARCHAR"/>
        <result property="isDeeplink" column="IS_DEEPLINK" jdbcType="INTEGER"/>
        <result property="bidFloor" column="BID_FLOOR" jdbcType="BIGINT"/>
        <result property="bidFloorYex" column="BID_FLOOR_YEX" jdbcType="BIGINT"/>
        <result property="entryMethod" column="ENTRY_METHOD" jdbcType="INTEGER"/>
        <result property="tfc" column="TFC" jdbcType="INTEGER"/>
        <result property="isNominateFromAllBrandPosition" column="IS_NOMINATE_FROM_ALL_BRAND_POSITION"
                jdbcType="TINYINT"/>
        <result property="isOnlyNominateBrand" column="IS_ONLY_NOMINATE_BRAND" jdbcType="TINYINT"/>
        <result property="carouselNum" column="CAROUSEL_NUM" jdbcType="VARCHAR"/>
        <result property="brandAdSlotMap" column="BRAND_AD_SLOT_MAP" jdbcType="VARCHAR"/>
        <result property="minCpc" column="MIN_CPC" jdbcType="INTEGER"/>
        <result property="maxCpc" column="MAX_CPC" jdbcType="INTEGER"/>
        <result property="blockSponsorIds" column="BLOCK_SPONSOR_IDS" jdbcType="VARCHAR"/>
        <result property="blockCategoryIds" column="BLOCK_CATEGORY_IDS" jdbcType="VARCHAR"/>
        <result property="isOpenSmoothConsumption" column="IS_OPEN_SMOOTH_CONSUMPTION" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        SDK_SLOT_ID
        ,SDK_SLOT_UDID,SDK_APP_ID,
        SDK_SLOT_NAME,SDK_SLOT_DESCRIPTION,TYPE,
        SDK_TYPE,SDK_STATUS,SDK_OS_TYPE,
        SDK_IMAGE_RULES,SDK_TEXT_RULES,SDK_ICON_HEIGHT,
        SDK_ICON_WIDTH,SDK_MAINIMAGE_HEIGHT,SDK_MAINIMAGE_WIDTH,
        SDK_TITLE_LENGTH,SDK_SUMMARY_LENGTH,SDK_DESCRIPTION_LENGTH,
        CREATE_TIME,LAST_MOD_TIME,SDK_RESULT_IMAGE,
        SDK_PACKAGE,SDK_OP_STATUS,SDK_SLOT_POSITION,
        SDK_SLOT_REPEAT,SDK_SLOT_DESC,BRAND_AD_SEQ,
        SCHEMA_IDS,SHOW_CONFIRM_DIALOG_TYPE,FILTER_SIZE,
        AD_PROVIDER_RATIO,SDK_SLOT_CALLBACK_URL,CALLBACK_URL_SECRET_KEY,
        VIDEO_PREFETCH,SDK_SLOT_STYLE,REQUIREMENTS,
        IS_DEEPLINK,BID_FLOOR,BID_FLOOR_YEX,
        ENTRY_METHOD,TFC,IS_NOMINATE_FROM_ALL_BRAND_POSITION,
        IS_ONLY_NOMINATE_BRAND,CAROUSEL_NUM,BRAND_AD_SLOT_MAP,
        MIN_CPC,MAX_CPC,BLOCK_SPONSOR_IDS,
        BLOCK_CATEGORY_IDS,IS_OPEN_SMOOTH_CONSUMPTION
    </sql>
    <select id="listByAdPositionIds" resultType="outfox.ead.youxuan.entity.SdkSlot">
        SELECT SDK_SLOT_UDID,BID_FLOOR_YEX FROM SdkSlot
        where
        <foreach collection="adPositionIds" index="index" item="item">
            <if test="index!=0">OR</if>
            BRAND_AD_SLOT_MAP Like CONCAT(CONCAT('%', #{item}), '%')
        </foreach> AND SDK_STATUS=0
    </select>
</mapper>

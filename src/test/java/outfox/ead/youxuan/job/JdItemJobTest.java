package outfox.ead.youxuan.job;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.LocalTime;

class JdItemJobTest {
    @Test
    public void testIsIn1150() {
        Assertions.assertTrue(JdItemJob.is2350(LocalTime.of(23, 50)));
        Assertions.assertTrue(JdItemJob.is2350(LocalTime.of(23, 50, 1)));
        Assertions.assertTrue(JdItemJob.is2350(LocalTime.of(23, 50, 59)));
        Assertions.assertFalse(JdItemJob.is2350(LocalTime.of(23, 49)));
        Assertions.assertFalse(JdItemJob.is2350(LocalTime.of(23, 49, 59)));
        Assertions.assertFalse(JdItemJob.is2350(LocalTime.of(23, 51)));
    }
}

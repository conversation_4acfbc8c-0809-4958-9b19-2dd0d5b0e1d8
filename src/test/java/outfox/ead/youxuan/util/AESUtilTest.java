package outfox.ead.youxuan.util;

import org.junit.jupiter.api.Test;

class AESUtilTest {
    @Test
    public void encrypt() throws Exception {
        String key = "3248576B7438474C78472B4D6C314575";
        String uid = "<EMAIL>";
        assert AESUtil.getDecryptResultNoResultStr(key, AESUtil.getEncryptResultNoResultStr(key, uid)).equals(uid);
    }

    @Test
    public void decrypt() throws Exception {
        String key = "3248576B7438474C78472B4D6C314575";
        String uid = "C3A24CB98780EDC83FBBFE7BC4E1F4FEB8EAE08515B991FF7951F452CF5D6F4A595DEA81611BFF536D44E93A35BA5BE8";
        System.out.println(AESUtil.getDecryptResultNoResultStr(key, uid));
    }

}

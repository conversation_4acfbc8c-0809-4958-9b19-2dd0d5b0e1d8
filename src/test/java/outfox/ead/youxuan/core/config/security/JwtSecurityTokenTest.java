package outfox.ead.youxuan.core.config.security;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.web.ad.controller.bo.LoginUser;

import java.util.HashSet;

class JwtSecurityTokenTest {
    @Test
    public void test() throws JsonProcessingException {
        String token = "eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************************************************.CAUfvvjS6vJCCmG2yyRYA0gg6IDKVxofgbmBgG7Wb08PVUH_BjIgprGQYf25_HeyV0X1E_DcyMWyoFbYZyxRIg";
        System.out.println(JwtSecurityToken.of(token, LoginUser.class));
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        LoginUser loginUser = new LoginUser();
        loginUser.setUsername("aaa");
        loginUser.setId(1L);
        HashSet<Role> roles = new HashSet<>();
        Role role = new Role();
        role.setRoleKey("roleKey");
        roles.add(role);
        loginUser.setRoles(roles);
        objectMapper.readValue(objectMapper.writeValueAsString(loginUser), LoginUser.class);
    }
}

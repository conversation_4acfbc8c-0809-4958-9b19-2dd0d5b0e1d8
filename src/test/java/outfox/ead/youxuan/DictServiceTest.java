package outfox.ead.youxuan;

import com.github.rholder.retry.RetryException;
import org.junit.Ignore;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import outfox.ead.youxuan.web.ad.controller.bo.DictVideoPost;
import outfox.ead.youxuan.web.kol.service.DictService;

import java.util.HashMap;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2022年03月11日 4:32 下午
 */
@SpringBootTest
@ActiveProfiles("local")
@Ignore
public class DictServiceTest {
    @Autowired
    private DictService dictService;

    @Test
    void insertOrUpdateUser() {
        System.out.println(dictService.insertOrUpdateUser("https://oimagea4.ydstatic.com/image?id=7681021139071134118&product=adpublish&format=PNG",
                "nickname", null));
    }

    @Test
    void postContent() throws ExecutionException, RetryException {
        DictVideoPost property = new DictVideoPost();

        property.setTitle("测试");
        property.setText("测试");
        property.setCoverImage("http://oimageb2.ydstatic.com/image?id=-3540046337377668153&product=adpublish&format=PNG");
        property.setVideo("https://adpublish.ydstatic.com/youxuan/dev/video/-727070350.mp4");
        property.setAppName("哈哈哈_测就完了");
        property.setIconImage("http://oimageb7.ydstatic.com/image?id=-7897755453684411633&product=adpublish&format=PNG");
        property.setUid("5bef7a6c-fc99-459c9144-82672bec038b");
        property.setYouthVisible(true);
        String postId = dictService.postContent(property, new HashMap<>());
        System.out.println(postId);
        Assertions.assertNotNull(postId);
        Assertions.assertNotEquals(postId,"");
    }
}

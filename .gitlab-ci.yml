# 依赖镜像
image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci

services:
  - name: harbor-registry.inner.youdao.com/devops/docker:20-dind
    entrypoint: [ "dockerd-entrypoint.sh", "--tls=false" ]
    alias: docker

variables:
  DOCKER_IMAGE_DEV_NAME: '$CI_HARBOR_REGISTRY/ead-test/youxuan-dev:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'
  DOCKER_IMAGE_TEST_NAME: '$CI_HARBOR_REGISTRY/ead-test/youxuan-test:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'
  DOCKER_IMAGE_SANDBOX_NAME: '$CI_HARBOR_REGISTRY/ead-test/youxuan-sandbox:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'
  DOCKER_IMAGE_TEMP_NAME: '$CI_HARBOR_REGISTRY/ead-test/youxuan-temp:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA-$CI_PIPELINE_ID'
  DOCKER_IMAGE_AUTO_NAME: '$CI_HARBOR_REGISTRY/ead-test/youxuan-auto:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'
  DOCKER_IMAGE_ONLINE_NAME: '$CI_HARBOR_REGISTRY/ead/youxuan-online:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  RANCHER_URL: "https://rancher-new.inner.youdao.com"

before_script:
  - echo "======== before script ========"

after_script:
  - echo "======== after script ========"
  
.docker_build_tpl: &docker_build_def
  script:
    - docker logout $CI_HARBOR_REGISTRY
    - echo $CI_HARBOR_TOKEN | docker login -u $CI_HARBOR_USER --password-stdin $CI_HARBOR_REGISTRY
    - eval echo ${DOCKER_IMAGE_NAME} $ENV
    - eval docker build --build-arg ENV=${ENV} -t $DOCKER_IMAGE_NAME .
    - eval docker push $DOCKER_IMAGE_NAME
    - echo "finish docker-build stage"

.docker_build_online_tpl: &docker_build_online_def
  script:
    - docker logout $CI_HARBOR_REGISTRY
    - echo $CI_HARBOR_TOKEN | docker login -u $CI_HARBOR_USER --password-stdin $CI_HARBOR_REGISTRY
    - echo $DOCKER_IMAGE_ONLINE_NAME
    - ENV="online"
    - docker build --build-arg ENV=${ENV} -t $DOCKER_IMAGE_ONLINE_NAME .
    - docker push $DOCKER_IMAGE_ONLINE_NAME
    - echo "finish docker-build stage"

.deploy_tpl: &deploy_def
  script:
    - eval ydci deploy set-image $DOCKER_IMAGE_NAME -c ${RANCHER_CLUSTER} -p ${RANCHER_PROJECT_NAME} -n ${RANCHER_NAMESPACE} -w ${RANCHER_WORKLOAD}
    - WEB_URL="${RANCHER_URL}/dashboard/c/${RANCHER_PROJECT}/explorer/service/${RANCHER_NAMESPACE}/${RANCHER_WORKLOAD}#pods"
    - MSG="开始部署 ${RANCHER_NAMESPACE}/${RANCHER_WORKLOAD} ${WEB_URL}"
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA By = $GITLAB_USER_EMAIL $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - echo $FULL_MSG
    - ydci notify popo -t $GITLAB_USER_EMAIL -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"    
    
.deploy_online_tpl: &deploy_online_def
  script:
    - ydci deploy set-image $DOCKER_IMAGE_ONLINE_NAME -c ${RANCHER_CLUSTER} -p ${RANCHER_PROJECT_NAME} -n ${RANCHER_NAMESPACE} -w ${RANCHER_WORKLOAD}
    - WEB_URL="${RANCHER_URL}/dashboard/c/${RANCHER_PROJECT}/explorer/service/${RANCHER_NAMESPACE}/${RANCHER_WORKLOAD}#pods"
    - MSG="开始部署优选后台线上环境 ${RANCHER_NAMESPACE}/${RANCHER_WORKLOAD} ${WEB_URL}"
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA By = $GITLAB_USER_EMAIL $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - POPOID=1446877
    - ydci notify group -t $POPOID  -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"

.notify_tpl: &notify_def
  script:
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - POPOID=1446877
    - ydci notify group -t $POPOID  -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"
    
.notify_popo_tpl: &notify_popo_def
  script:
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA By = $GITLAB_USER_EMAIL $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - echo $FULL_MSG
    - POPOID=1223805
    - ydci notify popo -t $GITLAB_USER_EMAIL -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"   
    - ydci notify group -t $POPOID  -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"
    
# 定义阶段 stages
stages:
  - build
  - docker-build
  - deploy
  - check
  - api-test
  - notify

# 定义 job build 项目
job:build:
  image: harbor-registry.inner.youdao.com/devops/openjdk-for-ead:20200113
  tags:
    - k8s
  stage: build
  script:
    - echo "start build stage"
    - mvn clean $MAVEN_OPTS package -DskipTests
    - echo "finish build stage"
  cache:
    key: youxuan
    paths:
      - .m2/repository
  artifacts:
    paths:
      - target/
  except:
    - api
  allow_failure: false

# 定义 job - 一键拉起环境 build 项目
job:build-temp:
  image: harbor-registry.inner.youdao.com/devops/openjdk-for-ead:20200113
  tags:
    - k8s
  stage: build
  script:
    - echo "start build stage"
    - sed -i 's/************************************:\/\/'${IPPORT_DATABASE}'\/'${DATABASE}'?/' src/main/resources/application.yml
    - sed -i 's/youxuan-dev\/leader/youxuan-temp\/leader-'${WORKLOAD_BACKEND}'/' src/main/resources/application-dev.yml
    - sed -i 's!youxuan-dev.inner.youdao.com!'${VHOST_FRONTEND}'!' src/main/resources/application-dev.yml
    - mvn clean $MAVEN_OPTS package -DskipTests
    - echo "finish build stage"
  only:
    variables:
      - $WORKLOAD_BACKEND
  cache:
    key: youxuan
    paths:
      - .m2/repository
  artifacts:
    paths:
      - target/
  allow_failure: false

# 定义 job - api test环境 build 项目
job:build-apiTest:
  image: harbor-registry.inner.youdao.com/devops/openjdk-for-ead:20200113
  tags:
    - k8s
  stage: build
  script:
    - echo "start build stage"
    - sed -i 's/********************************************************************?/' src/main/resources/application.yml
    - sed -i 's/youxuan-dev\/leader/youxuan-auto\/leader/' src/main/resources/application-dev.yml
    - mvn clean $MAVEN_OPTS package -DskipTests
    - echo "finish build stage"
  only:
    - master
    - add-apiTest
  except:
    - triggers
    - api
  cache:
    key: youxuan
    paths:
      - .m2/repository
  artifacts:
    paths:
      - target/
  allow_failure: false
  
job:build-online:
  image: harbor-registry.inner.youdao.com/devops/openjdk-for-ead:20200113
  tags:
    - k8s
  stage: build
  script:
    - echo "start build stage"
    - mvn clean $MAVEN_OPTS package -DskipTests
    - echo "finish build stage"
  cache:
    key: youxuan
    paths:
      - .m2/repository
  artifacts:
    paths:
      - target/
  only:
    variables:
      - $RUN_NIGHTLY_ONLINE
  allow_failure: false

# 定义 job，构建镜像推送到测试远程
job:docker-build-dev:
  tags:
    - k8s
  stage: docker-build
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_DEV_NAME
  <<: *docker_build_def
  only:
    - dev
  except:
    - master
    - sandbox
    - test
    - api
  environment:
    name: dev
  dependencies:
    - job:build
  allow_failure: false

# 定义 job，构建镜像推送到测试远程
job:docker-build-test:
  tags:
    - k8s
  stage: docker-build
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_TEST_NAME
    ENV: 'test'
  script:
  <<: *docker_build_def
  only:
    - test
  except:
    - api
  environment:
    name: test
  dependencies:
    - job:build

# 定义 job，构建沙箱镜像推送到测试远程
job:docker-build-sandbox:
  tags:
    - k8s
  stage: docker-build
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_SANDBOX_NAME
    ENV: 'sandbox'
  <<: *docker_build_def
  only:
    - master
    - sandbox
  except:
    - api
  environment:
    name: sandbox
  dependencies:
    - job:build

# 定义 job，构建一键拉起镜像推送到测试远程
job:docker-build-temp:
  tags:
    - k8s
  stage: docker-build
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_TEMP_NAME
  script:
  <<: *docker_build_def
  only:
    variables:
      - $WORKLOAD_BACKEND
  dependencies:
    - job:build-temp

# 定义 job，构建api test起镜像推送到测试远程
job:docker-build-apiTest:
  tags:
    - k8s
  stage: docker-build
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_AUTO_NAME
  script:
  <<: *docker_build_def
  only:
    - master
    - add-apiTest
  except:
    - triggers
    - api
  dependencies:
    - job:build-apiTest
    
# 定义 job，构建镜像推送到线上远程
job:docker-build-online:
  tags:
    - k8s
  stage: docker-build
  <<: *docker_build_online_def
  only:
    variables:
      - $RUN_NIGHTLY_ONLINE
  dependencies:
    - job:build-online

# 定义 job，开发环境部署
job:deploy-dev:
  stage: deploy
  tags:
    - k8s
  variables:
    RANCHER_CLUSTER: 'k8s-dev-common1'
    RANCHER_PROJECT: 'c-m-nh6klrxk'
    RANCHER_PROJECT_NAME: 'ad'
    RANCHER_NAMESPACE: 'ad'
    RANCHER_WORKLOAD: 'youxuan-dev'
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_DEV_NAME
  script:
  <<: *deploy_def
  only:
    - dev
  except:
    - master
    - sandbox
    - test
    - api
  environment:
    name: dev
    url: http://youxuan-server-dev.inner.youdao.com/v1.0.0/swagger-ui/
  dependencies:
    - job:docker-build-dev

# 定义 job，测试环境部署
job:deploy-test:
  stage: deploy
  tags:
    - k8s
  variables:
    RANCHER_CLUSTER: 'k8s-dev-common1'
    RANCHER_PROJECT: 'c-m-nh6klrxk'
    RANCHER_PROJECT_NAME: 'ad'
    RANCHER_NAMESPACE: 'ad'
    RANCHER_WORKLOAD: 'youxuan-test'
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_TEST_NAME
  <<: *deploy_def
  only:
    - test
  except:
    - api
  environment:
    name: test
    url: http://youxuan-server-test.inner.youdao.com/v1.0.0/swagger-ui/
  dependencies:
    - job:docker-build-test

# 定义 job，沙箱环境部署
job:deploy-sandbox:
  stage: deploy
  tags:
    - k8s
  variables:
    RANCHER_CLUSTER: 'k8s-dev-common1'
    RANCHER_PROJECT: 'c-m-nh6klrxk'
    RANCHER_PROJECT_NAME: 'ad'
    RANCHER_NAMESPACE: 'ad'
    RANCHER_WORKLOAD: 'youxuan-sandbox'
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_SANDBOX_NAME
  <<: *deploy_def
  only:
    - master
    - sandbox
  except:
    - api
  environment:
    name: sandbox
    url: http://youxuan-server-sandbox.inner.youdao.com/v1.0.0/swagger-ui/
  dependencies:
    - job:docker-build-sandbox

# 定义 job，一键拉起环境部署
job:deploy-temp:
  stage: deploy
  tags:
    - k8s
  variables:
    RANCHER_CLUSTER: $K8S_CLUSTER
    RANCHER_PROJECT: $K8S_PROJECT
    RANCHER_PROJECT_NAME: $K8S_PROJECT
    RANCHER_NAMESPACE: $K8S_NAMESPACE
    RANCHER_WORKLOAD: $WORKLOAD_BACKEND
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_TEMP_NAME
  <<: *deploy_def
  only:
    variables:
      - $WORKLOAD_BACKEND
  dependencies:
    - job:docker-build-temp

# 定义 job，api test环境部署
job:deploy-apiTest:
  stage: deploy
  tags:
    - k8s
  variables:
    RANCHER_CLUSTER: 'k8s-dev-common1'
    RANCHER_PROJECT: 'c-m-nh6klrxk'
    RANCHER_PROJECT_NAME: 'ad'
    RANCHER_NAMESPACE: 'ad'
    RANCHER_WORKLOAD: 'youxuan-apitest'
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_AUTO_NAME
  <<: *deploy_def
  only:
    - master
    - add-apiTest
  except:
    - triggers
    - api
  dependencies:
    - job:docker-build-apiTest
    
# 定义 job，线上环境部署
job:deploy-online:
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  stage: deploy
  tags:
    - k8s
  variables:
    RANCHER_CLUSTER: 'k8s-prod-common1'
    RANCHER_PROJECT: 'c-m-tfstzpcf'
    RANCHER_PROJECT_NAME: 'ad'
    RANCHER_NAMESPACE: 'ad-web'
    RANCHER_WORKLOAD: 'youxuan-backend'
  <<: *deploy_online_def
  only:
    variables:
      - $RUN_NIGHTLY_ONLINE
  dependencies:
    - job:docker-build-online

check_deploy:
  stage: check
  image: harbor-registry.inner.youdao.com/ead-test/centos7-maven3.5.4:jdk1.8.0_40
  tags:
    - k8s
  only:
    - master
    - add-apiTest
  except:
    - triggers
    - api
  script:
    - sh bin/waitHostPortReConnect.sh ************* 12000


job:api-test:
  image: harbor-registry.inner.youdao.com/devops/python:3.7.4
  stage: api-test
  tags:
    - k8s
  script:
    - pip install requests
    - python goapiTest.py
  only:
    - master
    - add-apiTest
  except:
    - triggers
    - api  

job:notify_success:
  stage: notify
  tags:
    - k8s
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  variables:
    MSG: 'CI PIPELINE SUCCESS'
  script:
  <<: *notify_def
  only:
    - triggers

job:notify_failure:
  stage: notify
  tags:
    - k8s
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  when: on_failure
  variables:
    MSG: 'CI PIPELINE FAIL'
  <<: *notify_def
  only:
    - triggers
    
job:notify_popo_success:
  stage: notify
  tags:
    - k8s
  variables:
    MSG: 'YOUXUAN CI PIPELINE SUCCESS'
  <<: *notify_popo_def
  only:
    - dev
    - test
    - sandbox
    - master
    - add-apiTest    
  except:
    - triggers

job:notify_popo_failure:
  stage: notify
  tags:
    - k8s
  when: on_failure
  variables:
    MSG: 'YOUXUAN CI PIPELINE FAIL'
  <<: *notify_popo_def
  only:
    - dev
    - test
    - sandbox
    - master
    - add-apiTest
  except:
    - triggers    

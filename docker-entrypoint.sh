#!/bin/sh

cat /tmp/hosts >> /etc/hosts
echo "Asia/Shanghai" > /etc/timezone

# Check arguments
# if [[ $# -ne 1 ]]; then
#     echo "Param error";
#     exit 1;
# fi

JAVA_HOME=/usr/java/jdk1.8.0_40
export PATH=$JAVA_HOME/bin:$PATH
export CLASSPATH=.:$JAVA_HOME/lib/*

if [ ! -d "$JAVA_HOME" ];then
    echo "$JAVA_HOME does not exist"; >&1
    exit 1;
fi

remote_debug_options="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8749"
env=""
echo $1
if [[ $1 == 'online' ]] ; then
    env="--spring.profiles.active=online"
    remote_debug_options=""
elif [[ $1 == 'test' ]] ; then
    env="--spring.profiles.active=test"
elif [[ $1 == 'sandbox' ]] ; then
    env="--spring.profiles.active=sandbox"
else
    env="--spring.profiles.active=dev"
fi

service_name="youxuan";
proj_path=$(dirname $0);
pid_file="$proj_path/$service_name.pid";
port_file="$proj_path/$service_name.port";
stdout_file="$proj_path/logs/stdout";
stderr_file="$proj_path/logs/stderr";


echo 12000 > $port_file;
# create logs dir if not exists
if [[ ! -d "$proj_path/logs" ]]
then
    mkdir "$proj_path/logs"
fi

# backup stdout_file and stderr_file if they are not empty
[[ -s $stdout_file ]] && mv $stdout_file $stdout_file.$(date +%Y-%m-%d.%H%M)
[[ -s $stderr_file ]] && mv $stderr_file $stderr_file.$(date +%Y-%m-%d.%H%M)

service_vm_args="";
memory_options="-Xms2g -Xmx2g";
gc_options="-server -XX:+UseG1GC -XX:MaxGCPauseMillis=50 -verbose:gc -XX:+PrintGCDetails -XX:+PrintAdaptiveSizePolicy -XX:+PrintTenuringDistribution -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=3 -XX:GCLogFileSize=200M -XX:+PrintGCDateStamps -Xloggc:logs/gc.log -XX:-OmitStackTraceInFastThrow -XX:+ParallelRefProcEnabled";
jmx_options=" -Dcom.sun.management.jmxremote.port=12001 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false"
service_vm_args="-Dservername=youxuan $memory_options $gc_options $jmx_options $remote_debug_options";


# invoke the server.
vm_args="$vm_args $service_vm_args"
nohup $JAVA_HOME/bin/java $vm_args -jar $proj_path/target/youxuan.jar $env --server.port=12000>$stdout_file 2>$stderr_file &

chmod 644 $stdout_file
chmod 644 $stderr_file


pid=$!;
if [[ -z $pid ]]; then
    echo "Error: cannot start the service." > $proj_path/stderr;
    exit 1;
fi
echo $pid > $pid_file;

tail -f $stdout_file

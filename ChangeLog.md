# Youxuan变更记录
# 2.54.2
* bugfix
  - 推广组保存图片格式错误，添加日志；上传图片接口，添加响应日志

# 2.54.1
* bugfix
  - 优选平台素材库服务:将生成的URL从http替换为https

# 2.54.0
* 优化
  - 支持三合一交互类型
  - jira: https://jira.inner.youdao.com/browse/YOUXUAN-1557

# 2.53.0
* 优化
  - 优选平台素材库服务
  - jira: https://confluence.inner.youdao.com/x/i3KAFg
  - wiki: https://confluence.inner.youdao.com/x/mSmlFg

# 2.52.1
* 优化
  - 重构自调用部分
  - LongListTypeHandler 改为 LongSetTypeHandler
  - [技术文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=379936799)

# 2.52.0
* 需求
  - 优选支持联合频控
  - [Jira]()
  - [PRD](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=373140068)
  - [相关文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=373159819)

# 2.51.1
* 优化
  - mongodb uri 更新
  - jira: https://jira.inner.youdao.com/browse/EADSYS-138

# 2.51.0
* 需求
  - 调整人群包大小上限为1G
  - [jira](https://jira.inner.youdao.com/browse/YOUXUAN-1510)

# 2.50.6
* 需求
  - 支持优选平台配置全屏点击
  - jira：https://jira.inner.youdao.com/browse/YOUXUAN-1489
  - ALTER TABLE `AdGroup`
    ADD COLUMN `FULL_CLICK_IS_OPEN` TINYINT(1) DEFAULT 0 COMMENT '全屏点击：0表示关，1表示开',
    ADD COLUMN `FULL_CLICK_CLASS_CITIES` VARCHAR(24) DEFAULT '' COMMENT '全屏点击的城市等级列表,-1表示不限制地域';

    ALTER TABLE `Style`
    ADD COLUMN `FULL_CLICK` TINYINT(1) DEFAULT 0 COMMENT '支持全屏点击：0表示关，1表示开';
  
# 2.50.5
* 需求
  * 支持YDID
  * JIRA: https://jira.inner.youdao.com/browse/ZHIXUAN-5828

# 2.50.4
* 优化
  * 调整DruidQuery查询超时时间为20s
  * [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5852)

# 2.50.3
* 优化
  - 新建/修改组的事务超时时间调整至200s
  - [jira](https://jira.inner.youdao.com/browse/YOUXUAN-1472)

# 2.50.2
* fix
  - 批量创建计划赋值修正

# 2.50.1
* fix
  - 非连续区间改为前闭后闭

# 2.50.0
* 需求
  - [批量创建支持多段日期](https://jira.inner.youdao.com/browse/YOUXUAN-1463)

# 2.49.0
* 需求
  - [支持全球发音广告](https://jira.inner.youdao.com/browse/YOUXUAN-1448)
* 优化
  - 延长jta事务时间，用于支持批量创建推广计划
  - 文件上传 唯一标识改为 md5(file byteArray)

# 2.48.0
* 需求
  - [批量创建计划和组工具化](https://jira.inner.youdao.com/browse/YOUXUAN-1461)

# 2.47.2
* 需求
  - title元素长度限制调整为40

# 2.47.1
* 优化
  - AreaUtil内部方法改为private
  - Atomikos分布式事务超时时间调长，用于支持批量创建推广计划

# 2.47.0
* 需求
  - [支持批量创建投放计划和组](https://jira.inner.youdao.com/browse/YOUXUAN-1444)

# 2.46.1
* bug fix
  - 修复审核运营人员无法查看所有橱窗组件的问题

# 2.46.0
* 需求
  - [全链路投放](https://jira.inner.youdao.com/browse/YOUXUAN-1429?filter=-1)

# 2.45.2
* 安全
  - 数据越权操作修正
* 优化
  - 全局异常捕获时，打印用户id
  - 修正ci输出的k8s url

# 2.45.1
* 需求
  - [配合 oImage 实现域名级读写分离](https://jira.inner.youdao.com/browse/YOUXUAN-1439)
* 优化
  - 写入改为youdao，读取用ydstatic

# 2.45.0
* 需求
  - [焦点图、信息流、查词结果广告位中部分样式线上数据中添加摇一摇和clkText](https://jira.inner.youdao.com/browse/YOUXUAN-1435?filter=-1)
  - 放开clkText字段长度限制
* bug fix
  - AdMappingGroup修改保存时，级联AdMapping状态未设置

# 2.44.4
* bug fix
  - zk版本回滚

# 2.44.3
* bug fix
  - 报表排序修正

# 2.44.2
* bug fix
  - AdGroup DO基础类型改为包装类型，否则MyBatis写库时，如果没有设置该基础类型，会写入该数据的默认值

# 2.44.1
* bug fix
  - AdMappingGroup修改保存时，级联AdMapping状态未设置
* 优化
  - 词典服务错误堆栈缺失补全

# 2.44.0
* 优化
  - 统计报表接口新增all选项

# 2.43.0
* 需求
  - [优选新增地域定向生效范围控制开关](https://jira.inner.youdao.com/browse/YOUXUAN-1420)
* 优化
  - 抽取公共配置，减少冗余配置

# 2.42.2
* bug fix
  - 事务注解修正

# 2.42.1
* 优化
  - 优化注解写法

# 2.42.0
* 需求
  - [支持过滤无设备号流量功能中排除caid](https://jira.inner.youdao.com/browse/YOUXUAN-1405)

# 2.41.0
* 需求
  - [新增cpm广告消费类型和加速系数字段](https://jira.inner.youdao.com/browse/YOUXUAN-1389)

# 2.40.1
* 优化
   - 更新uniondb端口

# 2.40.0
* 需求
  - [小程序来源优化一期](https://jira.inner.youdao.com/browse/YOUXUAN-1377)

# 2.39.6
* 优化
    - 数据库改为FQDN。
    - mongodb链接串改为贵州节点。

# 2.39.5
* bug fix
    - TreeSet遍历时添加对象报错

# 2.39.4
* 优化
    - [存储视频物料时，强制将视频的文件扩展名修改为小写，防止sdk无法播放](https://jira.inner.youdao.com/browse/YOUXUAN-1369)

# 2.39.3
* 优化
    - 定时任务调度失败，打印错误日志

# 2.39.2
* 优化
    - 计划状态更新任务逻辑优化，加快任务速度

# 2.39.1
* bug fix
    - npe fix
# 2.39.0
* 需求
  - [年龄格式修改为具体值](https://jira.inner.youdao.com/browse/YOUXUAN-1315)
  
# 2.38.1
* bug fix
    - 图片上传接口bug fix

# 2.38.0
* 优化
    - [提供图片上传接口](https://jira.inner.youdao.com/browse/YOUXUAN-1339)
    - Dockerfile支持yum源，加速构建
* bug fix
    - 修复依赖覆盖导致excel导出失败的问题

# 2.37.1
* bug fix
  - 未知元素聚合覆盖问题修复

# 2.37.0
* 需求
  - [V1.12有道优选样式增加扩展元素](https://jira.inner.youdao.com/browse/YOUXUAN-1318)
* 文档
  - [V1.12有道优选样式增加扩展元素](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=283888018)

# 2.36.0
* 需求
  - [优选支持自定义人群包投放](https://jira.inner.youdao.com/browse/YOUXUAN-1290)
* 文档
  - [支持自定义人群包投放](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=281827393)

# 2.35.0
* 需求
  - [新增开屏扭一扭广告](https://jira.inner.youdao.com/browse/YOUXUAN-1281)
* 文档
  - [新增开屏扭一扭广告](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=279706566)

# 2.34.0
* 需求
  - [品牌广告流量转换器V1.2](https://jira.inner.youdao.com/browse/YOUXUAN-1253)
* 文档
  - [优选品牌广告流量转换器V1.2](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=276052166)

# 2.33.1
* 需求
  - 测试设备直达权限bugfix

# 2.33.0
* 需求
  - [支持测试设备直达](https://jira.inner.youdao.com/browse/YOUXUAN-1233)

# 2.32.0
* 需求
  - [优选支持微信小程序定投](https://jira.inner.youdao.com/browse/YOUXUAN-1225)
* 文档
  - [优选支持微信小程序定投开发文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=270879580)

# 2.31.0
* 需求
  - 在上传视频后对视频进行校验，判断其编码格式配置是否符合要求
  - jira：https://jira.inner.youdao.com/browse/YOUXUAN-1236

# 2.30.0
* 需求
    - [品牌广告下发CPM价格](https://jira.inner.youdao.com/browse/YOUXUAN-1219)
* 文档
    - [品牌广告下发CPM价格](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=268678699)

# 2.29.0
* 需求
  - [品牌广告流量转换器V1.0](https://jira.inner.youdao.com/browse/YOUXUAN-1192)
* 文档
  - [流量转化器](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=266574192)

# 2.28.0
* 优化
  - [使用xxl-job](https://jira.inner.youdao.com/browse/YOUXUAN-977)

# 2.27.0
* 需求
  - [海外流量定投](https://jira.inner.youdao.com/browse/YOUXUAN-1173)

# 2.26.1
* 配置修改
    - 修改日志配置，减少磁盘空间占用

# 2.26.0
* 需求
  - [支持APP应用定向投放](https://jira.inner.youdao.com/browse/YOUXUAN-1152)

# 2.25.0
* 需求
  - [开屏新增多link类型](https://jira.inner.youdao.com/browse/YOUXUAN-1138)
* 文档
  - [优选 开屏新增多link类型](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=258560000)

# 2.24.2
* bug fix
    - 修改推广计划和推广组时role id不应该被修改

# 2.24.1
* 优化
    - 非本地环境logger改为INFO

# 2.24.0
* 优化
    - 测试+线上使用百川的Redis.

# 2.23.0

* 需求
    - [【投放后台】新增开屏文案参数支持接口下发](https://jira.inner.youdao.com/browse/YOUXUAN-1090)
* 文档
    - [优选2.8 开屏文案支持配置下发](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=245352685)
* bug fix
    - 修复一处npe


# 2.22.0

* 需求
  - [V1.15-优选支持广告频控功能](https://jira.inner.youdao.com/browse/YOUXUAN-1063)


# 2.21.0

* 需求
    - [V2.8-广告主与机构创作者间关系绑定方式优化](https://jira.inner.youdao.com/browse/YOUXUAN-1079)
* 优化
    - 简化系统配置文件
    - 优化资源排期接口性能
* 文档
    - [优选接口性能优化](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=242738090)

# 2.20.0

* 需求
    - [V1.14-设备信息资源库的建立和利用](https://jira.inner.youdao.com/browse/YOUXUAN-1033)
* 文档
    - https://confluence.inner.youdao.com/pages/viewpage.action?pageId=228897595

# 2.19.2

* bug fix
    - 修复死循环导致系统变慢的问题
* revert
    - 回调本地缓存

# 2.19.1

* bug fix
    - 优选系统响应慢，尝试移除本地缓存

# 2.19.0

* 需求
    - [V2.7-帖子内容与商品匹配策略1.0](https://jira.inner.youdao.com/browse/YOUXUAN-1057)
* 文档
    - [优选2.7 帖子内容与商品匹配策略1.0](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=235347822)
* 优化
    - 支持二级缓存，优化接口性能
    - 新增自动挂窗商品预热任务
    - 重构自动挂窗代码逻辑

# 2.18.2
    - [【橱窗优化】达人好物推荐和猜你喜欢过滤图片尺寸为700*700（不含）以下的商品](https://jira.inner.youdao.com/browse/YOUXUAN-1044)

# 2.18.1

* bug fix
    - [创作者多角色分页查询时出错](https://jira.inner.youdao.com/browse/YOUXUAN-1028)
    - [达人推荐好物npe](https://jira.inner.youdao.com/browse/YOUXUAN-1043)

# 2.18.0

* 需求
    - [有道词典橱窗优化三期](https://jira.inner.youdao.com/browse/YOUXUAN-1007)
* 文档
    - [优选2.7 帖子橱窗优化](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=221551045)

# 2.17.0

* 需求
    - [V1.4-优选支持下发多视频元素的广告样式](https://jira.inner.youdao.com/browse/YOUXUAN-1003)
* 文档
    - https://confluence.inner.youdao.com/pages/viewpage.action?pageId=223981769
* refactor
    - 优化部分代码写法
    - 升级部分组件依赖包

# 2.16.0

* 需求
    - [V1.5-品牌广告支持设备号的md5类型宏参数替换 & 优选后台增加推送“无设备信息”类流量开关](https://jira.inner.youdao.com/browse/YOUXUAN-1008)
* 文档
    - [优选1.5 品牌广告支持设备号的md5类型宏参数替换 & 优选后台增加推送“无设备信息”类流量开关](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=223970966)

## 2.15.2

* bug fix
    - 全局异常拦截器修复
* 优化
    - swagger 支持 https 请求

## 2.15.1

* bugfix
    - 修复京东商品定时任务NPE-2
    - 更新橱窗物料数据需要将审核数据也更新

## 2.15.0

* 需求
    -[V1.13-接入词典人群标签数据 & 增加精准人群定向和扩展人群定向2种定向模式（鹰眼计划V1.0）](https://jira.inner.youdao.com/browse/YOUXUAN-994)
* 文档
    - https://confluence.inner.youdao.com/pages/viewpage.action?pageId=218514010

# 2.14.0

* 需求
  - [【后台】优选开屏和信息流广告位增加滑动互动类型](https://jira.inner.youdao.com/browse/YOUXUAN-970)

# 2.13.1

* bug fix
    - 投稿任务需要查询父任务的橱窗id，加缓存时不慎改错

# 2.13.0

* 需求
    - [V2.6-帖子与橱窗自动关联](https://jira.inner.youdao.com/browse/YOUXUAN-982)
* 文档
    - [prd](https://tfouqb.axshare.com/)
    - [confluence](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=215908378)
* 优化
    - 注解 JmxReport -> Report

## 2.12.3

* 需求
    - [京东联盟商品定时任务支持更新商品图&不再更新引导文案](https://jira.inner.youdao.com/browse/YOUXUAN-990)

## 2.12.2

* bugfix
    - 修复京东商品定时任务NPE
* jira
    - [京东商品定时任务NPE排查](https://jira.inner.youdao.com/browse/YOUXUAN-976)

# 2.12.1

* perf
    - [添加jmx监控]

# 2.12.0

* 需求
    - [【后台】优选投放后台增加年龄、身份定向&性别定向优化](https://jira.inner.youdao.com/browse/YOUXUAN-917)

## 2.11.2

* bugfix
    - 修复保存橱窗时可能出现的NPE和保存不成功的问题。

## 2.11.1

* bugfix
    - 橱窗划线价支持修改为null
* jira
    - [【组件编辑】编辑组件划线价为空，审核后还是展示编辑前的划线价](https://jira.inner.youdao.com/browse/YOUXUAN-953)

## 2.11.0

* 需求
    - [V2.3-优选注册登录流程优化&支持词典账户注册登录优选](https://jira.inner.youdao.com/browse/YOUXUAN-852)
* 文档
    - [优选2.2 注册登陆](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=194528360)
* 优化
    - jwt升级，各个环境token不再通用
    - spring security用法修正
    - @AccessControl、@LDAP、@YoudaoIntranet 支持注解继承，支持元注解
    - @LDAP 默认 @AccessControl(Exclude=true)
* 重构
    - 将注册相关的接口全部移到RegisterService
    - 将模板批量导入相关的接口全部移到TemplateService

## 2.10.3

* 需求
  - 添加更多的词典测可见的投稿任务广告主ID(302562)

## 2.10.2

* bugfix
    - 修复atomikos在并发下借出无效连接的问题
* 文档
    - [优选线上数据库连接失效报错](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=207405886)

# 2.10.1
* 需求
    - 添加更多的词典测可见的投稿任务广告主ID

# 2.10.0

* 需求
    - [优选提供初级版“批量审核”功能](https://jira.inner.youdao.com/browse/YOUXUAN-946)

# 2.9.0

* 需求
    - [V2.5-批量导入京东联盟商品小工具](https://jira.inner.youdao.com/browse/YOUXUAN-938)

2.8.0

* 需求
    - [优选支持创建CPA类型任务&支持创建0元商品](https://jira.inner.youdao.com/browse/YOUXUAN-932)

# 2.7.0

* 需求
    - [【后台】词典视频开屏支持摇一摇](https://jira.inner.youdao.com/browse/YOUXUAN-906)
* 文档
    - [优选2.4 视频开屏摇一摇优先+投放](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=205559904)

## 2.6.1

* bug fix
    - 分布式锁失效修复
    - 详情见[分布式锁失效问题](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=205582780)

## 2.6.0

* 需求
    - [V2.3-词典直播版本接入优选的橱窗和任务场景](https://jira.inner.youdao.com/browse/YOUXUAN-879)
* 优化
    - 使用zk重写分布式锁
    - 支持分布式锁注解
    - 定时任务优化，删除事务注解，新增定时任务计时

## 2.5.5

* bug fix
  - 修复修改橱窗组件广告标识不生效的问题。

## 2.5.4

* 优化
  - 定时任务执行异常报警仅在online环境下生效.

## 2.5.3

* 需求
  - 指派任务备注列需支持创作者可见
  - [jira](https://jira.inner.youdao.com/browse/YOUXUAN-883)

## 2.5.2

* bug fix
    - 2.3.4定时任务补偿机制导致的bug  定时任务异常导致无限选主
    - [优选线上推广组推广计划无法保存成功排查](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=200298652)
* 优化
    - RestTemplate打印请求响应日志
    - 添加异步错误拦截器

## 2.5.1

* bug fix
    - 推广组投放日期没有返回

## 2.5.0

* 需求
    - [V1.9-支持按照有无设备号过滤广告请求流量](https://jira.inner.youdao.com/browse/YOUXUAN-806)
    - [V1.10-地域定向场景下支持定向查词广告投放](https://jira.inner.youdao.com/browse/YOUXUAN-844)

## 2.4.1

* bug fix
    - 词典uid自动生成账户的时候，词典重复请求，没有加锁导致生成多条重复数据

## 2.4.0

* 需求
    - [【后台】V2.2-优选接入词典橱窗](https://jira.inner.youdao.com/browse/YOUXUAN-742)
* 优化
    - 增强 @AccessControl 功能，最近匹配原则 + exclude
    - 接入 Ldap 校验
* bugfix
    - MyMetaObjectHandler.java#insertFill 修正用法

## 2.3.4

* 优化
    - 定时任务执行失败，新增手动重跑，新增补偿机制

## 2.3.3

* 需求
    - [优选保存视频素材时不要携带宽高参数](https://jira.inner.youdao.com/browse/YOUXUAN-733)

## 2.3.2

* 需求
    - [优选针对词典橱窗接口变动做适应性调整](https://jira.inner.youdao.com/browse/YOUXUAN-731)

## 2.3.1

* 调整
    - master代码合并后自动部署sandbox环境

## 2.3.0

* 需求
    - [优选推广组广告位搜索框逻辑优化](https://jira.inner.youdao.com/browse/YOUXUAN-729)

## 2.2.0

* 需求
    - [【后端】V1.8 优选后台筛选优化+地域定向支持多项搜索等](https://jira.inner.youdao.com/browse/YOUXUAN-707?filter=-1)
* 优化
    - 优化报表查询速度

## 2.1.0

* 需求
    - [【后端】V1.7优选新增人群性别定向](https://jira.inner.youdao.com/browse/YOUXUAN-690)

## 2.0.6

* 需求
    - [优选后台支持多主图样式](https://jira.inner.youdao.com/browse/YOUXUAN-703)
    - [达人数据导入时，性别改为非必填项](https://jira.inner.youdao.com/browse/YOUXUAN-709)
* bug fix
    - 词典批量数据接口上限100个用户

## 2.0.5

* 需求
    - [支持视频流广告点赞、评论及转发](https://jira.inner.youdao.com/browse/YOUXUAN-601?filter=-1)
* bug fix
    - 修复一处NPE

## 2.0.4

* 需求
    - [微博推广任务增加更多类型](https://jira.inner.youdao.com/browse/YOUXUAN-685?filter=-1)
* 优化
    - 优选达人模板优先从mfs_ead下载，这样更新模板时不需要重新部署项目

## 2.0.3

* 优化
    - 支持测试环境、沙箱环境 CI/CD

## 2.0.2

* bug fix
    - 修复编辑品牌号组件编辑后不送审的问题。

## 2.0.1

* 需求
    - [个人创作者绑定词典账号后不向词典服务端品牌号相关接口推送相关信息](https://jira.inner.youdao.com/browse/YOUXUAN-693)
* bug fix
    - 修复绑定和恢复绑定词典账户时未对个人创作者和品牌创作者做判断，造成将品牌号标识赋给个人创作者的问题。
    - 更新邮箱正则校验
    - 补全sql缺失字段

## 2.0.0

* 需求
    - [【后台】有道优选2.0](https://jira.inner.youdao.com/browse/YOUXUAN-374)
    - [优选后台统计报表显示异常问题修复](https://jira.inner.youdao.com/browse/YOUXUAN-553)
* 功能
    - 新增角色权限模块，隔离用户数据
    - 支持内容营销功能
* 优化
    - 简化项目配置
    - 全局拦截新增ServletException，用于处理外部错误请求
    - 容器部署支持远程Debug
* bug fix
    - 后台统计报表数据映射修复，过滤请求上报数据

## 1.6.6

* 需求
    - [优选系统里城市名旧称”襄樊“改为新称”襄阳“](https://jira.inner.youdao.com/browse/YOUXUAN-380)

## 1.6.5

* bug fix
    - 数据源未设置存活时间导致异常
    - 关闭mybatis 输出执行sql

## 1.6.4

* 需求
    - [【有道优选】非信息流广告位下的样式不应展示接收开屏回收属性](https://jira.inner.youdao.com/browse/YOUXUAN-364)
* bug fix
    - 配合前端修复1.6显示bug

# 1.6.3

* 需求
    - [跨数据库写入需要支持分布式事务](https://jira.inner.youdao.com/browse/YOUXUAN-341?filter=-1)
* 优化
    - 引入分布式事务，保证多实例写入事务

## 1.6.2

* 需求
    - [【有道优选】推广组列表历史数据报错](https://jira.inner.youdao.com/browse/YOUXUAN-358?filter=-1)
* bug fix
    - Jackson序列化，未知属性报错，设置为不报错

## 1.6.1

* 需求
    - [优选后台资源排期推广组创意图片加载错误图片问题修复](https://jira.inner.youdao.com/browse/YOUXUAN-337)
* bug fix
    - 修正资源排期详情页预览图错误的问题

## 1.6.0

* 需求
    - [【后台】V1.1-不同尺寸/样式数据按照广告位聚合](http://jira.inner.youdao.com/browse/YOUXUAN-250)
* 功能
    - 样式层级元素支持多尺寸
    - 样式新增是否全屏属性
* 数据库变动
    - alter table Style add FULL_SCREEN TINYINT default 0 null comment '是否全屏，0-否，1-是';
    - element新增gifimage
* 优化
    - fix max-lifetime，线上数据库为600s，导致hikari warn

## 1.5.0

* 需求
    - [【后端】V1.5-有道优选广告位映射逻辑优化](https://jira.inner.youdao.com/browse/YOUXUAN-322)
    - [V1.5.2-优选程序化广告写YEX时，投放开始时间逻辑优化](https://jira.inner.youdao.com/browse/YOUXUAN-339)
* 改动
    - 样式新增开屏回收、开屏首刷、接收开屏回收三个属性
    - 移除centralDogma映射查找逻辑
    - yexPmpDeal起始时间前推7天
* 数据库变动
    - [sql](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=157975716)

## 1.4.0

* 需求
    - [【后台】有道词典青少年模式屏蔽部分广告](http://jira.inner.youdao.com/browse/YOUXUAN-304)
* 数据库变动
    - alter table AdGroup add YOUTH_MODE tinyint default 0 null comment '是否屏蔽青少年，0-否，1-是';
    - alter table Media add YOUTH_MODE tinyint default 0 null comment '是否屏蔽青少年，0-否，1-是';
* 优化
  - log4j版本存在安全漏洞，升级log4j至2.17.1
  - 升级部分依赖版本

## 1.3.0

* 需求
    - [【后台】V1.4-有道优选后台页面逻辑优化](http://jira.inner.youdao.com/browse/YOUXUAN-241)
* 优化
    - 推广计划起始时间优化，之前全部存成0点，现在根据具体的时间进行存储
    - 推广组日展示量支持任意修改
    - 预览图展示逻辑优化，根据给出的优先级顺序进行展示
    - videourl与video同尺寸时，自动将video的url赋值给videourl的url
    - 样式创建元素列表展示顺序优化
    - SpringSecurity 全局异常处理优化
    - adContent、adContentRelation、yexPmpDeal三张表物理删除改为逻辑删除
* 功能
    - 新增推广管理层级列表接口
    - 新增资源多层级列表接口
    - 推广计划支持删除未来未被占用的时间
* 依赖升级
    - log4j2 2.15.1-snapshot切换为正式版本2.15.0
* bug fix
    - 修正1.2.5 security代码更新导致token失效时返回结果跨域问题

## 1.2.6 (2021-12-10)

* 优化
    - log4j版本存在安全漏洞，升级log4j至2.15.1-snapshot

## 1.2.5(2021-12-08)

* 需求
    - [底价为null时，设置为100](http://jira.inner.youdao.com/browse/YOUXUAN-260)
* bug fix
    - 底价为空时，设置底价为100，否则投放端会出问题
    - Security 配置修正

## 1.2.4(2021-11-23)

* 需求
    - [【后台】优选存储创意内容时 ，需要把创意类型由大写改为小写](http://jira.inner.youdao.com/browse/YOUXUAN-231)
* bug fix
    - 投放不支持大写文件格式，改为小写
* 优化
    - 文件夹名称修改 domain ->  entity

## 1.2.3(2021-11-22)

* 优化
    - 广告位轮播数上下限调整
    - 修改部分日志级别

## 1.2.2(2021-11-16)

* 优化
    - 彩色日志导致部分日志乱码，重新配置日志格式

## 1.2.1(2021-11-12)

* 优化
    - centralDogma 广告位映射数据不全，切换conf，往新conf里写映射数据

## 1.1.0-1.2.0(2021-11-3)

* 需求
    - [【后台】有道优选V1.0.1-优选支持程序化投放逻辑优化](http://jira.inner.youdao.com/browse/YOUXUAN-120)
    - [【后台】V1.0.2-优选推广管理和资源管理各层级编辑逻辑优化](http://jira.inner.youdao.com/browse/YOUXUAN-101)
* 功能
    - 新增推广计划，推广组，媒体，广告位，样式编辑校验逻辑。
    - 新增程序化投放逻辑
    - 样式支持摇一摇功能

* 优化
    - 保存样式时，会将封面图属性赋予视频，简化推广组初始化逻辑。

## 1.0.1(2021-11-11)

* bug fix
    - druid查询granularity使用错误
        - period的时间分段，没指定origin

## 1.0.0(2021-10-29)

* 需求
    - [【后台】有道优选1.0](http://jira.inner.youdao.com/browse/YOUXUAN-8)
* 功能
    - 投放后台：登录，用户管理，推广管理，统计报表，资源管理，资源排期
    - 官网：预约咨询

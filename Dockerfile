FROM harbor-registry.inner.youdao.com/ead-test/centos7-maven3.5.4:jdk1.8.0_40
ENV LC_ALL   en_US.UTF-8
WORKDIR /youxuan
COPY . .

# 下载 ffmpeg 需要的库
RUN wget -O /etc/yum.repos.d/CentOS-Base.repo http://mirrors.aliyun.com/repo/Centos-7.repo && \
    yum clean all && \
    yum makecache && \
    yum install libxcb alsa-lib-devel -y && \
    chmod +x /youxuan/docker-entrypoint.sh

EXPOSE 12000
EXPOSE 8749
ARG ENV
ENV ENV=$ENV
ENTRYPOINT ["sh", "-c", "/youxuan/docker-entrypoint.sh ${ENV}"]


# coding:utf-8
import requests
import time
import datetime
import json
import hashlib
import sys

PROJECT_ID = 622
MODULE_ID = 38680
TOKEN = '7fa648ee-0825-492d-b96f-80c744616aa4'
KEY = 'aefd73cd5cc49897'
SUITE_ID = 13084  # 执行集id
CID = '机房网络'  # 执行器id
# 域名替换
OLD_DOMAIN_ID = 24972 # 旧域名id, http://youxuan-server-test.inner.youdao.com
NEW_DOMAIN_ID = 27425 # 新域名id, http://youxuan-server-apitest.inner.youdao.com
# 测试报告邮件通知
EMAILS = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

def execute_task():
  '''
    执行测试任务
  '''
  url = 'https://gotest.hz.netease.com/open/api/task/suite/run'
  timestamp = int(round(time.time() * 1000))
  data = {
      "timestamp": timestamp,
      "id": SUITE_ID,
      "cid": CID,
      "execMode": 0,  # 串行执行
      "replace": "true",
      "domains": [
      {
        "oldDomainId": OLD_DOMAIN_ID,
        "newDomainId": NEW_DOMAIN_ID
      }],
      "notify": "true",
      "notifyEmails": EMAILS
  }

  src = json.dumps(data) + "|" + KEY
  m2 = hashlib.md5()
  m2.update(src.encode("utf8"))  # 指定编码格式，否则会报错

  sign = m2.hexdigest()
  headers = {
      "token": TOKEN,
      "sign": sign,
  }
  print("发送http请求给goapi，执行测试")
  print("request body: "+json.dumps(data))
  print("request sign: "+sign)
  r = requests.post(url, json=data, headers=headers)
  print('goapi执行结果', r.text)
  taskId = r.json()["data"][0]["taskId"]
  print("response message: " + r.json()["msg"])
  print("response taskId: " + taskId)
  return taskId

def query_task_status(task_id):
  '''
    获取执行结果
  '''
  url = 'https://gotest.hz.netease.com/open/api/history/status'
  timestamp = int(round(time.time() * 1000))
  data = {
      "timestamp": timestamp,
      "id": task_id,
  }

  src = json.dumps(data) + "|" + KEY
  m2 = hashlib.md5()
  m2.update(src.encode("utf8"))
  sign = m2.hexdigest()
  headers = {
      "token": TOKEN,
      "sign": sign,
  }
  print("发送http请求给goapi，查询执行结果")
  r = requests.post(url, json=data, headers=headers).json()
#   print('goapi返回结果', r)
  return r


if __name__ == '__main__':
  # 触发执行case
  task_id = execute_task()

  taskUrl = '''https://gotest.hz.netease.com/web/#/home/<USER>/history-suite-detail?projectId=%s&moduleid=%s&taskId=%s''' % (
      PROJECT_ID, MODULE_ID, task_id)
  print("【用例执行详情】")
  print(taskUrl)

  # 轮询执行结果
  status = None
  unfinishedStatus = ["ready", "processing"]
  while True:
    status = query_task_status(task_id=task_id)
    taskStatus = status["data"]["taskStatus"]
    print("当前任务状态：" + taskStatus)
    if taskStatus not in unfinishedStatus:
      break

    print("测试任务执行中... sleep 10s")
    time.sleep(10)

  taskStatus = status["data"]["taskStatus"]
  checkResult = status["data"]["checkResult"]
  if taskStatus != "complete" and checkResult != 1:
    print("执行结果(checkResult)：" + checkResult)
    print("测试任务执行失败，请去goapi上查看具体原因")
    sys.exit(1)
  print("测试结果如下：")
  responseData = status["data"]
  for key, value in responseData.items():
    if key.find("Count") > 0:
      print(key + ":" + str(value))
      if key.find("FailCount") > 0 and value != 0:
        print("测试用例执行失败")
        sys.exit(1)
  sys.exit(0)
